<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Grad<PERSON> or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>net.kyori</groupId>
  <artifactId>adventure-bom</artifactId>
  <version>4.16.0</version>
  <packaging>pom</packaging>
  <name>adventure-bom</name>
  <description>A user-interface library for Minecraft: Java Edition.</description>
  <url>https://github.com/KyoriPowered/adventure</url>
  <licenses>
    <license>
      <name>The MIT License</name>
      <url>https://opensource.org/licenses/MIT</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>kashike</id>
      <timezone>America/Vancouver</timezone>
    </developer>
    <developer>
      <id>lucko</id>
      <name>Luck</name>
      <email>************</email>
      <url>https://lucko.me</url>
    </developer>
    <developer>
      <id>zml</id>
      <name>zml</name>
      <timezone>America/Vancouver</timezone>
    </developer>
    <developer>
      <id>Electroid</id>
    </developer>
    <developer>
      <id>minidigger</id>
      <name>MiniDigger</name>
    </developer>
    <developer>
      <id>kezz</id>
    </developer>
    <developer>
      <id>broccolai</id>
    </developer>
    <developer>
      <id>rymiel</id>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/KyoriPowered/adventure.git</connection>
    <developerConnection>scm:git:ssh://**************/KyoriPowered/adventure.git</developerConnection>
    <url>https://github.com/KyoriPowered/adventure</url>
  </scm>
  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/KyoriPowered/adventure/issues</url>
  </issueManagement>
  <ciManagement>
    <system>GitHub Actions</system>
    <url>https://github.com/KyoriPowered/adventure/actions</url>
  </ciManagement>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>net.kyori</groupId>
        <artifactId>adventure-api</artifactId>
        <version>4.16.0</version>
      </dependency>
      <dependency>
        <groupId>net.kyori</groupId>
        <artifactId>adventure-annotation-processors</artifactId>
        <version>4.16.0</version>
      </dependency>
      <dependency>
        <groupId>net.kyori</groupId>
        <artifactId>adventure-extra-kotlin</artifactId>
        <version>4.16.0</version>
      </dependency>
      <dependency>
        <groupId>net.kyori</groupId>
        <artifactId>adventure-key</artifactId>
        <version>4.16.0</version>
      </dependency>
      <dependency>
        <groupId>net.kyori</groupId>
        <artifactId>adventure-nbt</artifactId>
        <version>4.16.0</version>
      </dependency>
      <dependency>
        <groupId>net.kyori</groupId>
        <artifactId>adventure-serializer-configurate4</artifactId>
        <version>4.16.0</version>
      </dependency>
      <dependency>
        <groupId>net.kyori</groupId>
        <artifactId>adventure-text-logger-slf4j</artifactId>
        <version>4.16.0</version>
      </dependency>
      <dependency>
        <groupId>net.kyori</groupId>
        <artifactId>adventure-text-minimessage</artifactId>
        <version>4.16.0</version>
      </dependency>
      <dependency>
        <groupId>net.kyori</groupId>
        <artifactId>adventure-text-serializer-ansi</artifactId>
        <version>4.16.0</version>
      </dependency>
      <dependency>
        <groupId>net.kyori</groupId>
        <artifactId>adventure-text-serializer-gson</artifactId>
        <version>4.16.0</version>
      </dependency>
      <dependency>
        <groupId>net.kyori</groupId>
        <artifactId>adventure-text-serializer-json</artifactId>
        <version>4.16.0</version>
      </dependency>
      <dependency>
        <groupId>net.kyori</groupId>
        <artifactId>adventure-text-serializer-json-legacy-impl</artifactId>
        <version>4.16.0</version>
      </dependency>
      <dependency>
        <groupId>net.kyori</groupId>
        <artifactId>adventure-text-serializer-legacy</artifactId>
        <version>4.16.0</version>
      </dependency>
      <dependency>
        <groupId>net.kyori</groupId>
        <artifactId>adventure-text-serializer-plain</artifactId>
        <version>4.16.0</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
