<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.mongodb</groupId>
  <artifactId>bson</artifactId>
  <version>4.8.1</version>
  <name>BSON</name>
  <description>The BSON library</description>
  <url>https://bsonspec.org</url>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>Various</name>
      <organization>MongoDB</organization>
    </developer>
  </developers>
  <scm>
    <connection>scm:https://github.com/mongodb/mongo-java-driver.git</connection>
    <developerConnection>scm:**************:mongodb/mongo-java-driver.git</developerConnection>
    <url>https://github.com/mongodb/mongo-java-driver</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.6</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
  </dependencies>
</project>
