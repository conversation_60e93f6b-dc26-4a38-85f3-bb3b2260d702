<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.jetbrains</groupId>
  <artifactId>annotations</artifactId>
  <version>22.0.0</version>
  <name>JetBrains Java Annotations</name>
  <description>A set of annotations used for code inspection support and code documentation.</description>
  <url>https://github.com/JetBrains/java-annotations</url>
  <scm>
    <url>https://github.com/JetBrains/java-annotations</url>
    <connection>scm:git:git://github.com/JetBrains/java-annotations.git</connection>
    <developerConnection>scm:git:ssh://github.com:JetBrains/java-annotations.git</developerConnection>
  </scm>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>JetBrains</id>
      <name>JetBrains Team</name>
      <organization>JetBrains</organization>
      <organizationUrl>https://www.jetbrains.com</organizationUrl>
    </developer>
  </developers>
</project>
