<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<parent>
		<groupId>org.sonatype.oss</groupId>
		<artifactId>oss-parent</artifactId>
		<version>7</version>
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<packaging>jar</packaging>
	<groupId>redis.clients</groupId>
	<artifactId>jedis</artifactId>
	<version>4.3.1</version>
	<name>Jedis</name>
	<description>Jed<PERSON> is a blazingly small and sane Redis java client.</description>
	<url>https://github.com/redis/jedis</url>

	<mailingLists>
		<mailingList>
			<name>Jedis Mailing List</name>
			<post><EMAIL></post>
			<archive>
				http://groups.google.com/group/jedis_redis
			</archive>
		</mailingList>
	</mailingLists>

	<licenses>
		<license>
			<name>MIT</name>
			<url>http://github.com/redis/jedis/raw/master/LICENSE.txt</url>
			<distribution>repo</distribution>
		</license>
	</licenses>

	<issueManagement>
		<system>github</system>
		<url>http://github.com/redis/jedis/issues</url>
	</issueManagement>

	<scm>
		<connection>scm:git:**************:redis/jedis.git</connection>
		<url>scm:git:**************:redis/jedis.git</url>
		<developerConnection>scm:git:**************:redis/jedis.git</developerConnection>
		<tag>jedis-3.4.1</tag>
	</scm>

	<properties>
		<github.global.server>github</github.global.server>
		<slf4j.version>1.7.36</slf4j.version>
		<jedis.module.name>redis.clients.jedis</jedis.module.name>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>${slf4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
			<version>2.11.1</version>
		</dependency>
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20220320</version>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.8.9</version>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.13.2</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-library</artifactId>
			<version>1.3</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-simple</artifactId>
			<version>${slf4j.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.kohlschutter.junixsocket</groupId>
			<artifactId>junixsocket-core</artifactId>
			<version>2.4.0</version>
			<type>pom</type>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-inline</artifactId>
			<version>3.12.4</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<distributionManagement>
		<snapshotRepository>
			<id>ossrh</id>
			<url>https://oss.sonatype.org/content/repositories/snapshots</url>
		</snapshotRepository>
		<repository>
			<id>ossrh</id>
			<url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
		</repository>
	</distributionManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.5</version>
				<executions>
					<execution>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>report</id>
						<phase>test</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.22.2</version>
				<configuration>
					<systemPropertyVariables>
						<redis-hosts>${redis-hosts}</redis-hosts>
					</systemPropertyVariables>
					<!--<trimStackTrace>false</trimStackTrace>-->
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.2.1</version>
				<configuration>
					<attach>true</attach>
				</configuration>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>2.10.4</version>
				<configuration>
					<aggregate>true</aggregate>
					<additionalparam>-Xdoclint:none</additionalparam>
				</configuration>
				<executions>
					<execution>
						<id>attach-javadoc</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-release-plugin</artifactId>
				<version>2.5.3</version>
			</plugin>
			<plugin>
				<groupId>org.sonatype.plugins</groupId>
				<artifactId>nexus-staging-maven-plugin</artifactId>
				<version>1.6.8</version>
				<extensions>true</extensions>
				<configuration>
					<serverId>ossrh</serverId>
					<nexusUrl>https://oss.sonatype.org/</nexusUrl>
					<autoReleaseAfterClose>true</autoReleaseAfterClose>
				</configuration>
			</plugin>
			<plugin>
				<groupId>com.googlecode.maven-java-formatter-plugin</groupId>
				<artifactId>maven-java-formatter-plugin</artifactId>
				<version>0.4</version>
				<configuration>
					<configFile>${project.basedir}/hbase-formatter.xml</configFile>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.0.2</version>
				<configuration>
					<archive>
						<manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
						<manifestEntries>
							<Automatic-Module-Name>${jedis.module.name}</Automatic-Module-Name>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.felix</groupId>
				<artifactId>maven-bundle-plugin</artifactId>
				<version>4.2.1</version>
				<executions>
					<execution>
						<id>bundle-manifest</id>
						<phase>process-classes</phase>
						<goals>
							<goal>manifest</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
	<profiles>
		<profile>
			<id>release</id>
			<build>
				<plugins>
					<!--Sign the components - this is required by maven central for releases -->
					<plugin>
						<artifactId>maven-gpg-plugin</artifactId>
						<version>3.0.1</version>
						<configuration>
							<gpgArguments>
								<arg>--pinentry-mode</arg>
								<arg>loopback</arg>
							</gpgArguments>
						</configuration>
						<executions>
							<execution>
								<id>sign-artifacts</id>
								<phase>verify</phase>
								<goals>
									<goal>sign</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>
</project>
