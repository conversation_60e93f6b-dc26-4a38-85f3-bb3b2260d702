---- Minecraft Crash Report ----
// Don't be sad, have a hug! <3

Time: 2025-07-11 16:06:59
Description: Exception in server tick loop

java.lang.IllegalStateException: Failed to bind to port
	at net.minecraft.server.dedicated.DedicatedServer.initServer(DedicatedServer.java:286)
	at net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:1215)
	at net.minecraft.server.MinecraftServer.lambda$spin$0(MinecraftServer.java:330)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.BindException: Address already in use: bind
	at java.base/sun.nio.ch.Net.bind0(Native Method)
	at java.base/sun.nio.ch.Net.bind(Net.java:565)
	at java.base/sun.nio.ch.ServerSocketChannelImpl.netBind(ServerSocketChannelImpl.java:344)
	at java.base/sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:301)
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334)
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600)
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579)
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973)
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260)
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:356)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:167)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	... 1 more


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- System Details --
Details:
	Minecraft Version: 1.21
	Minecraft Version ID: 1.21
	Operating System: Windows 10 (amd64) version 10.0
	Java Version: 21.0.3, Oracle Corporation
	Java VM Version: Java HotSpot(TM) 64-Bit Server VM (mixed mode, sharing), Oracle Corporation
	Memory: 133601040 bytes (127 MiB) / 545259520 bytes (520 MiB) up to 4223664128 bytes (4028 MiB)
	CPUs: 16
	Processor Vendor: GenuineIntel
	Processor Name: 13th Gen Intel(R) Core(TM) i5-13500H
	Identifier: Intel64 Family 6 Model 186 Stepping 2
	Microarchitecture: Raptor Lake
	Frequency (GHz): 3.19
	Number of physical packages: 1
	Number of physical CPUs: 12
	Number of logical CPUs: 16
	Graphics card #0 name: Intel(R) Iris(R) Xe Graphics
	Graphics card #0 vendor: Intel Corporation
	Graphics card #0 VRAM (MiB): 2048.00
	Graphics card #0 deviceId: VideoController1
	Graphics card #0 versionInfo: 32.0.101.6737
	Memory slot #0 capacity (MiB): 2048.00
	Memory slot #0 clockSpeed (GHz): 7.40
	Memory slot #0 type: Unknown
	Memory slot #1 capacity (MiB): 2048.00
	Memory slot #1 clockSpeed (GHz): 7.40
	Memory slot #1 type: Unknown
	Memory slot #2 capacity (MiB): 2048.00
	Memory slot #2 clockSpeed (GHz): 7.40
	Memory slot #2 type: Unknown
	Memory slot #3 capacity (MiB): 2048.00
	Memory slot #3 clockSpeed (GHz): 7.40
	Memory slot #3 type: Unknown
	Memory slot #4 capacity (MiB): 2048.00
	Memory slot #4 clockSpeed (GHz): 7.40
	Memory slot #4 type: Unknown
	Memory slot #5 capacity (MiB): 2048.00
	Memory slot #5 clockSpeed (GHz): 7.40
	Memory slot #5 type: Unknown
	Memory slot #6 capacity (MiB): 2048.00
	Memory slot #6 clockSpeed (GHz): 7.40
	Memory slot #6 type: Unknown
	Memory slot #7 capacity (MiB): 2048.00
	Memory slot #7 clockSpeed (GHz): 7.40
	Memory slot #7 type: Unknown
	Virtual memory max (MiB): 23276.87
	Virtual memory used (MiB): 16728.86
	Swap memory total (MiB): 7168.00
	Swap memory used (MiB): 1056.30
	Space in storage for jna.tmpdir (MiB): <path not set>
	Space in storage for org.lwjgl.system.SharedLibraryExtractPath (MiB): <path not set>
	Space in storage for io.netty.native.workdir (MiB): <path not set>
	Space in storage for java.io.tmpdir (MiB): available: 56987.99, total: 200000.00
	Space in storage for workdir (MiB): available: 110366.48, total: 775349.00
	JVM Flags: 0 total; 
	CraftBukkit Information: 
   BrandInfo: Paper (papermc:paper) version 1.21-130-master@b1b5d4c (2024-08-10T10:02:42Z)
   Running: Paper version 1.21-130-b1b5d4c (MC: 1.21) (Implementing API version 1.21-R0.1-SNAPSHOT) true
   Plugins: {}
   Warnings: DEFAULT
   Reload Count: 0
   Threads: { RUNNABLE Signal Dispatcher: [], WAITING RegionFile I/O Thread #0: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221), ca.spottedleaf.concurrentutil.executor.standard.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:97)], RUNNABLE Netty Server IO #0: [java.base@21.0.3/sun.nio.ch.WEPoll.wait(Native Method), java.base@21.0.3/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114), java.base@21.0.3/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130), java.base@21.0.3/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:147), io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68), io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879), io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526), io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997), io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74), java.base@21.0.3/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.3/java.lang.Thread.run(Thread.java:1583)], WAITING Paper Common Worker #0: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221), ca.spottedleaf.concurrentutil.executor.standard.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:97)], TIMED_WAITING JNA Cleaner: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.3/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1847), java.base@21.0.3/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:71), java.base@21.0.3/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:143), java.base@21.0.3/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:218), com.sun.jna.internal.Cleaner$CleanerThread.run(Cleaner.java:154)], TIMED_WAITING Craft Scheduler Thread - 1: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.3/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.3/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.3/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:233), java.base@21.0.3/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:336), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.3/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.3/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Craft Scheduler Thread - 0: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.3/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.3/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.3/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:233), java.base@21.0.3/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:336), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.3/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.3/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Common-Cleaner: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.3/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1847), java.base@21.0.3/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:71), java.base@21.0.3/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:143), java.base@21.0.3/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:218), java.base@21.0.3/jdk.internal.ref.CleanerImpl.run(CleanerImpl.java:140), java.base@21.0.3/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.3/java.lang.Thread.run(Thread.java:1583), java.base@21.0.3/jdk.internal.misc.InnocuousThread.run(InnocuousThread.java:186)], WAITING Worker-Main-6: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.3/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.3/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.3/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.3/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707), java.base@21.0.3/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.3/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.3/java.lang.Thread.run(Thread.java:1583)], RUNNABLE DestroyJavaVM: [], WAITING Log4j2-AsyncAppenderEventDispatcher-1-Async: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.3/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.3/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.3/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.3/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707), java.base@21.0.3/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420), org.apache.logging.log4j.core.appender.AsyncAppenderEventDispatcher.dispatchAll(AsyncAppenderEventDispatcher.java:81), org.apache.logging.log4j.core.appender.AsyncAppenderEventDispatcher.run(AsyncAppenderEventDispatcher.java:73)], WAITING Worker-Main-5: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.3/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.3/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.3/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.3/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707), java.base@21.0.3/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.3/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.3/java.lang.Thread.run(Thread.java:1583)], RUNNABLE Attach Listener: [], TIMED_WAITING Craft Scheduler Thread - 2: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.3/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.3/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.3/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:233), java.base@21.0.3/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:336), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.3/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.3/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Keep-Alive-Timer: [java.base@21.0.3/java.lang.Thread.sleep0(Native Method), java.base@21.0.3/java.lang.Thread.sleep(Thread.java:509), java.base@21.0.3/sun.net.www.http.KeepAliveCache.run(KeepAliveCache.java:238), java.base@21.0.3/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.3/java.lang.Thread.run(Thread.java:1583), java.base@21.0.3/jdk.internal.misc.InnocuousThread.run(InnocuousThread.java:186)], TIMED_WAITING ForkJoinPool.commonPool-worker-1: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.parkUntil(LockSupport.java:449), java.base@21.0.3/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1891), java.base@21.0.3/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.3/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING Paper Common Worker #3: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221), ca.spottedleaf.concurrentutil.executor.standard.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:97)], WAITING Worker-Main-3: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.3/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.3/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.3/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.3/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707), java.base@21.0.3/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.3/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.3/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Yggdrasil Key Fetcher: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.3/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758), java.base@21.0.3/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.3/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.3/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.3/java.lang.Thread.run(Thread.java:1583)], WAITING Worker-Main-2: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.3/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.3/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.3/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.3/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707), java.base@21.0.3/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.3/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.3/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Timer hack thread: [java.base@21.0.3/java.lang.Thread.sleep0(Native Method), java.base@21.0.3/java.lang.Thread.sleep(Thread.java:509), net.minecraft.Util$7.run(Util.java:785)], RUNNABLE Reference Handler: [java.base@21.0.3/java.lang.ref.Reference.waitForReferencePendingList(Native Method), java.base@21.0.3/java.lang.ref.Reference.processPendingReferences(Reference.java:246), java.base@21.0.3/java.lang.ref.Reference$ReferenceHandler.run(Reference.java:208)], TIMED_WAITING Craft Scheduler Thread - 3: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.3/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.3/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.3/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:233), java.base@21.0.3/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:336), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.3/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.3/java.lang.Thread.run(Thread.java:1583)], WAITING Finalizer: [java.base@21.0.3/java.lang.Object.wait0(Native Method), java.base@21.0.3/java.lang.Object.wait(Object.java:366), java.base@21.0.3/java.lang.Object.wait(Object.java:339), java.base@21.0.3/java.lang.ref.NativeReferenceQueue.await(NativeReferenceQueue.java:48), java.base@21.0.3/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:158), java.base@21.0.3/java.lang.ref.NativeReferenceQueue.remove(NativeReferenceQueue.java:89), java.base@21.0.3/java.lang.ref.Finalizer$FinalizerThread.run(Finalizer.java:173)], RUNNABLE Notification Thread: [], WAITING Worker-Main-1: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.3/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.3/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.3/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.3/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707), java.base@21.0.3/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.3/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.3/java.lang.Thread.run(Thread.java:1583)], WAITING Paper Common Worker #1: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221), ca.spottedleaf.concurrentutil.executor.standard.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:97)], RUNNABLE WindowsStreamPump: [org.fusesource.jansi.internal.Kernel32.WaitForSingleObject(Native Method), org.jline.terminal.impl.jansi.win.JansiWinSysTerminal.processConsoleInput(JansiWinSysTerminal.java:138), org.jline.terminal.impl.AbstractWindowsTerminal.pump(AbstractWindowsTerminal.java:460), org.jline.terminal.impl.AbstractWindowsTerminal$$Lambda/0x000001bf0112b328.run(Unknown Source), java.base@21.0.3/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.3/java.lang.Thread.run(Thread.java:1583)], RUNNABLE Server thread: [java.base@21.0.3/java.lang.Thread.dumpThreads(Native Method), java.base@21.0.3/java.lang.Thread.getAllStackTraces(Thread.java:2521), org.bukkit.craftbukkit.CraftCrashReport.get(CraftCrashReport.java:35), org.bukkit.craftbukkit.CraftCrashReport.get(CraftCrashReport.java:17), net.minecraft.SystemReport.setDetail(SystemReport.java:71), net.minecraft.CrashReport.<init>(CrashReport.java:40), net.minecraft.server.MinecraftServer.constructOrExtractCrashReport(MinecraftServer.java:1421), net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:1338), net.minecraft.server.MinecraftServer.lambda$spin$0(MinecraftServer.java:330), net.minecraft.server.MinecraftServer$$Lambda/0x000001bf01ce6bb0.run(Unknown Source), java.base@21.0.3/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.3/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Paper Watchdog Thread: [java.base@21.0.3/java.lang.Thread.sleep0(Native Method), java.base@21.0.3/java.lang.Thread.sleep(Thread.java:509), org.spigotmc.WatchdogThread.run(WatchdogThread.java:240)], TIMED_WAITING Server console handler: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.3/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1847), org.jline.utils.NonBlockingPumpReader.read(NonBlockingPumpReader.java:78), org.jline.utils.NonBlockingReader.read(NonBlockingReader.java:57), org.jline.keymap.BindingReader.readCharacter(BindingReader.java:160), org.jline.keymap.BindingReader.readBinding(BindingReader.java:110), org.jline.keymap.BindingReader.readBinding(BindingReader.java:61), org.jline.reader.impl.LineReaderImpl.doReadBinding(LineReaderImpl.java:923), org.jline.reader.impl.LineReaderImpl.readBinding(LineReaderImpl.java:956), org.jline.reader.impl.LineReaderImpl.readLine(LineReaderImpl.java:651), org.jline.reader.impl.LineReaderImpl.readLine(LineReaderImpl.java:468), net.minecrell.terminalconsole.SimpleTerminalConsole.readCommands(SimpleTerminalConsole.java:158), net.minecrell.terminalconsole.SimpleTerminalConsole.start(SimpleTerminalConsole.java:141), net.minecraft.server.dedicated.DedicatedServer$1.run(DedicatedServer.java:117)], WAITING Worker-Main-4: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.3/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.3/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.3/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.3/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707), java.base@21.0.3/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.3/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.3/java.lang.Thread.run(Thread.java:1583)], WAITING Paper Common Worker #2: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221), ca.spottedleaf.concurrentutil.executor.standard.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:97)], TIMED_WAITING pool-8-thread-1: [java.base@21.0.3/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.3/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.3/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758), java.base@21.0.3/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.3/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.3/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.3/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.3/java.lang.Thread.run(Thread.java:1583)],}
   
   Force Loaded Chunks: {}
	Server Running: true
	Player Count: 0 / 20; []
	Active Data Packs: vanilla, file/bukkit, paper (incompatible)
	Available Data Packs: bundle, file/bukkit, paper (incompatible), trade_rebalance, vanilla
	Enabled Feature Flags: minecraft:vanilla
	World Generation: Experimental
	World Seed: -7914350190436031977
	Is Modded: Definitely; Server brand changed to 'Paper'
	Type: Dedicated Server (map_server.txt)