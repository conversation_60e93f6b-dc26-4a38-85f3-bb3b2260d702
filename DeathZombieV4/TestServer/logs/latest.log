[13:37:07] [ServerMain/INFO]: [bootstrap] Running Java 21 (Java HotSpot(TM) 64-Bit Server VM 21.0.3+7-LTS-152; Oracle Corporation null) on Windows 10 10.0 (amd64)
[13:37:07] [ServerMain/INFO]: [bootstrap] Loading Paper 1.21-130-master@b1b5d4c (2024-08-10T10:02:42Z) for Minecraft 1.21
[13:37:08] [ServerMain/INFO]: [PluginInitializerManager] Initializing plugins...
[13:37:08] [ServerMain/INFO]: [PluginInitializerManager] Initialized 14 plugins
[13:37:08] [ServerMain/INFO]: [PluginInitializerManager] Bukkit plugins (14):
 - <PERSON><PERSON><PERSON>ommands (4.0.5), Citizens (2.0.35-SNAPSHOT (build 3598)), CommandNPC (1.7.2), DeathZombieV4 (1.2), DecentHolograms (2.8.16), <PERSON>izen (1.3.0-<PERSON><PERSON><PERSON><PERSON><PERSON> (build 1803-REL)), FastAsyncWorldEdit (2.13.1-SNAPSHOT-1113;145d077), LuckPerms (5.4.131), Multiverse-Core (4.3.17-SNAPSHOT), Sentinel (2.9.2-SNAPSHOT (build 527)), Shoot (1.5), ViaBackwards (5.2.1), ViaRewind (4.0.5), ViaVersion (5.2.1)
[13:37:12] [ServerMain/INFO]: [STDERR]: You have used the Spigot command line EULA agreement flag.
[13:37:12] [ServerMain/INFO]: [STDERR]: By using this setting you are indicating your agreement to Mojang's EULA (https://account.mojang.com/documents/minecraft_eula).
[13:37:12] [ServerMain/INFO]: [STDERR]: If you do not agree to the above EULA please stop your server and remove this flag immediately.
[13:37:12] [ServerMain/INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
[13:37:14] [ServerMain/INFO]: Loaded 1290 recipes
[13:37:14] [ServerMain/INFO]: Loaded 1399 advancements
[13:37:14] [Server thread/INFO]: Starting minecraft server version 1.21
[13:37:14] [Server thread/WARN]: ****************************
[13:37:14] [Server thread/WARN]: YOU ARE RUNNING THIS SERVER AS AN ADMINISTRATIVE OR ROOT USER. THIS IS NOT ADVISED.
[13:37:14] [Server thread/WARN]: YOU ARE OPENING YOURSELF UP TO POTENTIAL RISKS WHEN DOING THIS.
[13:37:14] [Server thread/WARN]: FOR MORE INFORMATION, SEE https://madelinemiller.dev/blog/root-minecraft-server/
[13:37:14] [Server thread/WARN]: ****************************
[13:37:14] [Server thread/INFO]: Loading properties
[13:37:14] [Server thread/INFO]: This server is running Paper version 1.21-130-master@b1b5d4c (2024-08-10T10:02:42Z) (Implementing API version 1.21-R0.1-SNAPSHOT)
[13:37:15] [Server thread/INFO]: [spark] This server bundles the spark profiler. For more information please visit https://docs.papermc.io/paper/profiling
[13:37:15] [Server thread/INFO]: Using 4 threads for Netty based IO
[13:37:15] [Server thread/INFO]: Server Ping Player Sample Count: 12
[13:37:16] [Server thread/INFO]: [ChunkTaskScheduler] Chunk system is using 1 I/O threads, 4 worker threads, and population gen parallelism of 4 threads
[13:37:16] [Server thread/INFO]: Default game type: SURVIVAL
[13:37:16] [Server thread/INFO]: Generating keypair
[13:37:16] [Server thread/INFO]: Starting Minecraft server on *:25565
[13:37:16] [Server thread/INFO]: Using default channel type
[13:37:16] [Server thread/INFO]: Paper: Using Java compression from Velocity.
[13:37:16] [Server thread/INFO]: Paper: Using Java cipher from Velocity.
[13:37:17] [Server thread/WARN]: [org.bukkit.craftbukkit.legacy.CraftLegacy] Initializing Legacy Material Support. Unless you have legacy plugins and/or data this is a bug!
[13:37:25] [Server thread/WARN]: Legacy plugin CommandNPC v1.7.2 does not specify an api-version.
[13:37:26] [Server thread/INFO]: [SpigotLibraryLoader] [Denizen] Loading 2 libraries... please wait
[13:37:26] [Server thread/INFO]: [SpigotLibraryLoader] [Denizen] Loaded library D:\all-projeck\Spigot plugins\DeathZombieV4 - 副本 (Shoot合并) - 副本\DeathZombieV4\TestServer\libraries\org\mongodb\mongodb-driver-sync\4.8.1\mongodb-driver-sync-4.8.1.jar
[13:37:26] [Server thread/INFO]: [SpigotLibraryLoader] [Denizen] Loaded library D:\all-projeck\Spigot plugins\DeathZombieV4 - 副本 (Shoot合并) - 副本\DeathZombieV4\TestServer\libraries\org\mongodb\bson\4.8.1\bson-4.8.1.jar
[13:37:26] [Server thread/INFO]: [SpigotLibraryLoader] [Denizen] Loaded library D:\all-projeck\Spigot plugins\DeathZombieV4 - 副本 (Shoot合并) - 副本\DeathZombieV4\TestServer\libraries\org\mongodb\mongodb-driver-core\4.8.1\mongodb-driver-core-4.8.1.jar
[13:37:26] [Server thread/INFO]: [SpigotLibraryLoader] [Denizen] Loaded library D:\all-projeck\Spigot plugins\DeathZombieV4 - 副本 (Shoot合并) - 副本\DeathZombieV4\TestServer\libraries\org\mongodb\bson-record-codec\4.8.1\bson-record-codec-4.8.1.jar
[13:37:26] [Server thread/INFO]: [SpigotLibraryLoader] [Denizen] Loaded library D:\all-projeck\Spigot plugins\DeathZombieV4 - 副本 (Shoot合并) - 副本\DeathZombieV4\TestServer\libraries\redis\clients\jedis\4.3.1\jedis-4.3.1.jar
[13:37:26] [Server thread/INFO]: [SpigotLibraryLoader] [Denizen] Loaded library D:\all-projeck\Spigot plugins\DeathZombieV4 - 副本 (Shoot合并) - 副本\DeathZombieV4\TestServer\libraries\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar
[13:37:26] [Server thread/INFO]: [SpigotLibraryLoader] [Denizen] Loaded library D:\all-projeck\Spigot plugins\DeathZombieV4 - 副本 (Shoot合并) - 副本\DeathZombieV4\TestServer\libraries\org\apache\commons\commons-pool2\2.11.1\commons-pool2-2.11.1.jar
[13:37:26] [Server thread/INFO]: [SpigotLibraryLoader] [Denizen] Loaded library D:\all-projeck\Spigot plugins\DeathZombieV4 - 副本 (Shoot合并) - 副本\DeathZombieV4\TestServer\libraries\org\json\json\20220320\json-20220320.jar
[13:37:26] [Server thread/INFO]: [SpigotLibraryLoader] [Denizen] Loaded library D:\all-projeck\Spigot plugins\DeathZombieV4 - 副本 (Shoot合并) - 副本\DeathZombieV4\TestServer\libraries\com\google\code\gson\gson\2.8.9\gson-2.8.9.jar
[13:37:26] [Server thread/INFO]: [ViaVersion] Loading server plugin ViaVersion v5.2.1
[13:37:26] [Server thread/INFO]: [ViaVersion] ViaVersion 5.2.1 is now loaded. Registering protocol transformers and injecting...
[13:37:27] [Via-Mappingloader-0/INFO]: [ViaVersion] Loading block connection mappings ...
[13:37:27] [Via-Mappingloader-0/INFO]: [ViaVersion] Using FastUtil Long2ObjectOpenHashMap for block connections
[13:37:28] [Server thread/INFO]: [ViaBackwards] Loading translations...
[13:37:28] [Server thread/INFO]: [ViaBackwards] Registering protocols...
[13:37:29] [Server thread/INFO]: [ViaRewind] Registering protocols...
[13:37:29] [Server thread/INFO]: [DecentHolograms] Loading server plugin DecentHolograms v2.8.16
[13:37:29] [Server thread/INFO]: [Citizens] Loading server plugin Citizens v2.0.35-SNAPSHOT (build 3598)
[13:37:29] [Server thread/INFO]: [ViaBackwards] Loading server plugin ViaBackwards v5.2.1
[13:37:29] [Server thread/INFO]: [Shoot] Loading server plugin Shoot v1.5
[13:37:29] [Server thread/INFO]: [CommandNPC] Loading server plugin CommandNPC v1.7.2
[13:37:29] [Server thread/INFO]: [ViaRewind] Loading server plugin ViaRewind v4.0.5
[13:37:29] [Server thread/INFO]: [Sentinel] Loading server plugin Sentinel v2.9.2-SNAPSHOT (build 527)
[13:37:29] [Server thread/INFO]: [Multiverse-Core] Loading server plugin Multiverse-Core v4.3.17-SNAPSHOT
[13:37:29] [Server thread/INFO]: [LuckPerms] Loading server plugin LuckPerms v5.4.131
[13:37:29] [Server thread/INFO]: [FastAsyncWorldEdit] Loading server plugin FastAsyncWorldEdit v2.13.1-SNAPSHOT-1113;145d077
[13:37:29] [Server thread/WARN]: 
**********************************************
** You are using the Spigot-mapped FAWE jar on a modern Paper version.
** This will result in slower first-run times and wasted disk space from plugin remapping.
** Download the Paper FAWE jar from Modrinth to avoid this: https://modrinth.com/plugin/fastasyncworldedit/
**********************************************
[13:37:31] [Server thread/INFO]: Got request to register class com.sk89q.worldedit.bukkit.BukkitServerInterface with WorldEdit [com.sk89q.worldedit.extension.platform.PlatformManager@116b0730]
[13:37:31] [Server thread/INFO]: [Denizen] Loading server plugin Denizen v1.3.0-SNAPSHOT (build 1803-REL)
[13:37:31] [Server thread/INFO]: [DeathZombieV4] Loading server plugin DeathZombieV4 v1.2
[13:37:31] [Server thread/INFO]: [ChestCommands] Loading server plugin ChestCommands v4.0.5
[13:37:31] [Server thread/INFO]: Server permissions file permissions.yml is empty, ignoring it
[13:37:31] [Server thread/INFO]: [ViaRewind] Enabling ViaRewind v4.0.5
[13:37:31] [Server thread/INFO]: [LuckPerms] Enabling LuckPerms v5.4.131
[13:37:32] [Server thread/INFO]:         __    
[13:37:32] [Server thread/INFO]:   |    |__)   LuckPerms v5.4.131
[13:37:32] [Server thread/INFO]:   |___ |      Running on Bukkit - Paper
[13:37:32] [Server thread/INFO]: 
[13:37:32] [Server thread/INFO]: [LuckPerms] Loading configuration...
[13:37:32] [Server thread/INFO]: [LuckPerms] Loading storage provider... [H2]
[13:37:32] [Server thread/INFO]: [LuckPerms] Loading internal permission managers...
[13:37:32] [Server thread/INFO]: [LuckPerms] Performing initial data load...
[13:37:32] [Server thread/INFO]: [LuckPerms] Successfully enabled. (took 1108ms)
[13:37:32] [Server thread/INFO]: [FastAsyncWorldEdit] Enabling FastAsyncWorldEdit v2.13.1-SNAPSHOT-1113;145d077
[13:37:33] [Server thread/INFO]: [com.fastasyncworldedit.core.Fawe] LZ4 Compression Binding loaded successfully
[13:37:33] [Server thread/INFO]: [com.fastasyncworldedit.core.Fawe] ZSTD Compression Binding loaded successfully
[13:37:33] [Server thread/INFO]: Registering commands with com.sk89q.worldedit.bukkit.BukkitServerInterface
[13:37:33] [Server thread/INFO]: WEPIF: Using the Bukkit Permissions API.
[13:37:33] [Server thread/INFO]: Using com.sk89q.worldedit.bukkit.adapter.impl.fawe.v1_21_R1.PaperweightFaweAdapter as the Bukkit adapter
[13:37:34] [Server thread/INFO]: Preparing level "world"
[13:37:34] [Server thread/INFO]: -------- World Settings For [world] --------
[13:37:34] [Server thread/INFO]: Simulation Distance: 10
[13:37:34] [Server thread/INFO]: View Distance: 10
[13:37:34] [Server thread/INFO]: Allow Zombie Pigmen to spawn from portal blocks: true
[13:37:34] [Server thread/INFO]: Zombie Aggressive Towards Villager: true
[13:37:34] [Server thread/INFO]: Item Merge Radius: 2.5
[13:37:34] [Server thread/INFO]: Experience Merge Radius: 3.0
[13:37:34] [Server thread/INFO]: Hopper Transfer: 8 Hopper Check: 1 Hopper Amount: 1 Hopper Can Load Chunks: false
[13:37:34] [Server thread/INFO]: Nerfing mobs spawned from spawners: false
[13:37:34] [Server thread/INFO]: Entity Activation Range: An 32 / Mo 32 / Ra 48 / Mi 16 / Tiv true / Isa false
[13:37:34] [Server thread/INFO]: Entity Tracking Range: Pl 48 / An 48 / Mo 48 / Mi 32 / Di 128 / Other 64
[13:37:34] [Server thread/INFO]: Cactus Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Cane Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Melon Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Mushroom Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Pumpkin Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Sapling Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Beetroot Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Carrot Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Potato Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: TorchFlower Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Wheat Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: NetherWart Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Vine Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Cocoa Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Bamboo Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: SweetBerry Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Kelp Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: TwistingVines Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: WeepingVines Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: CaveVines Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: GlowBerry Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: PitcherPlant Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Custom Map Seeds:  Village: 10387312 Desert: 14357617 Igloo: 14357618 Jungle: 14357619 Swamp: 14357620 Monument: 10387313 Ocean: 14357621 Shipwreck: 165745295 End City: 10387313 Slime: 987234911 Nether: 30084232 Mansion: 10387319 Fossil: 14357921 Portal: 34222645
[13:37:34] [Server thread/INFO]: Arrow Despawn Rate: 1200 Trident Respawn Rate:1200
[13:37:34] [Server thread/INFO]: Tile Max Tick Time: 50ms Entity max Tick Time: 50ms
[13:37:34] [Server thread/INFO]: Max TNT Explosions: 100
[13:37:34] [Server thread/INFO]: Mob Spawn Range: 6
[13:37:34] [Server thread/INFO]: Item Despawn Rate: 6000
[13:37:34] [Server thread/INFO]: -------- World Settings For [world_nether] --------
[13:37:34] [Server thread/INFO]: Simulation Distance: 10
[13:37:34] [Server thread/INFO]: View Distance: 10
[13:37:34] [Server thread/INFO]: Allow Zombie Pigmen to spawn from portal blocks: true
[13:37:34] [Server thread/INFO]: Zombie Aggressive Towards Villager: true
[13:37:34] [Server thread/INFO]: Item Merge Radius: 2.5
[13:37:34] [Server thread/INFO]: Experience Merge Radius: 3.0
[13:37:34] [Server thread/INFO]: Hopper Transfer: 8 Hopper Check: 1 Hopper Amount: 1 Hopper Can Load Chunks: false
[13:37:34] [Server thread/INFO]: Nerfing mobs spawned from spawners: false
[13:37:34] [Server thread/INFO]: Entity Activation Range: An 32 / Mo 32 / Ra 48 / Mi 16 / Tiv true / Isa false
[13:37:34] [Server thread/INFO]: Entity Tracking Range: Pl 48 / An 48 / Mo 48 / Mi 32 / Di 128 / Other 64
[13:37:34] [Server thread/INFO]: Cactus Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Cane Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Melon Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Mushroom Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Pumpkin Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Sapling Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Beetroot Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Carrot Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Potato Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: TorchFlower Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Wheat Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: NetherWart Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Vine Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Cocoa Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Bamboo Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: SweetBerry Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Kelp Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: TwistingVines Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: WeepingVines Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: CaveVines Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: GlowBerry Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: PitcherPlant Growth Modifier: 100%
[13:37:34] [Server thread/INFO]: Custom Map Seeds:  Village: 10387312 Desert: 14357617 Igloo: 14357618 Jungle: 14357619 Swamp: 14357620 Monument: 10387313 Ocean: 14357621 Shipwreck: 165745295 End City: 10387313 Slime: 987234911 Nether: 30084232 Mansion: 10387319 Fossil: 14357921 Portal: 34222645
[13:37:34] [Server thread/INFO]: Arrow Despawn Rate: 1200 Trident Respawn Rate:1200
[13:37:34] [Server thread/INFO]: Tile Max Tick Time: 50ms Entity max Tick Time: 50ms
[13:37:34] [Server thread/INFO]: Max TNT Explosions: 100
[13:37:34] [Server thread/INFO]: Mob Spawn Range: 6
[13:37:34] [Server thread/INFO]: Item Despawn Rate: 6000
[13:37:35] [Server thread/INFO]: -------- World Settings For [world_the_end] --------
[13:37:35] [Server thread/INFO]: Simulation Distance: 10
[13:37:35] [Server thread/INFO]: View Distance: 10
[13:37:35] [Server thread/INFO]: Allow Zombie Pigmen to spawn from portal blocks: true
[13:37:35] [Server thread/INFO]: Zombie Aggressive Towards Villager: true
[13:37:35] [Server thread/INFO]: Item Merge Radius: 2.5
[13:37:35] [Server thread/INFO]: Experience Merge Radius: 3.0
[13:37:35] [Server thread/INFO]: Hopper Transfer: 8 Hopper Check: 1 Hopper Amount: 1 Hopper Can Load Chunks: false
[13:37:35] [Server thread/INFO]: Nerfing mobs spawned from spawners: false
[13:37:35] [Server thread/INFO]: Entity Activation Range: An 32 / Mo 32 / Ra 48 / Mi 16 / Tiv true / Isa false
[13:37:35] [Server thread/INFO]: Entity Tracking Range: Pl 48 / An 48 / Mo 48 / Mi 32 / Di 128 / Other 64
[13:37:35] [Server thread/INFO]: Cactus Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: Cane Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: Melon Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: Mushroom Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: Pumpkin Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: Sapling Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: Beetroot Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: Carrot Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: Potato Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: TorchFlower Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: Wheat Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: NetherWart Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: Vine Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: Cocoa Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: Bamboo Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: SweetBerry Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: Kelp Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: TwistingVines Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: WeepingVines Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: CaveVines Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: GlowBerry Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: PitcherPlant Growth Modifier: 100%
[13:37:35] [Server thread/INFO]: Custom Map Seeds:  Village: 10387312 Desert: 14357617 Igloo: 14357618 Jungle: 14357619 Swamp: 14357620 Monument: 10387313 Ocean: 14357621 Shipwreck: 165745295 End City: 10387313 Slime: 987234911 Nether: 30084232 Mansion: 10387319 Fossil: 14357921 Portal: 34222645
[13:37:35] [Server thread/INFO]: Arrow Despawn Rate: 1200 Trident Respawn Rate:1200
[13:37:35] [Server thread/INFO]: Tile Max Tick Time: 50ms Entity max Tick Time: 50ms
[13:37:35] [Server thread/INFO]: Max TNT Explosions: 100
[13:37:35] [Server thread/INFO]: Mob Spawn Range: 6
[13:37:35] [Server thread/INFO]: Item Despawn Rate: 6000
[13:37:35] [Server thread/INFO]: Preparing start region for dimension minecraft:overworld
[13:37:35] [ForkJoinPool.commonPool-worker-1/WARN]: [com.fastasyncworldedit.core.util.UpdateNotification] An update for FastAsyncWorldEdit is available. You are 24 build(s) out of date.
You are running build 1113, the latest version is build 1137.
Update at https://ci.athion.net/job/FastAsyncWorldEdit
[13:37:35] [Server thread/INFO]: Time elapsed: 605 ms
[13:37:35] [Server thread/INFO]: Preparing start region for dimension minecraft:the_nether
[13:37:35] [Server thread/INFO]: Time elapsed: 82 ms
[13:37:35] [Server thread/INFO]: Preparing start region for dimension minecraft:the_end
[13:37:35] [Server thread/INFO]: Time elapsed: 76 ms
[13:37:35] [Server thread/INFO]: [ViaVersion] Enabling ViaVersion v5.2.1
[13:37:35] [Server thread/INFO]: [ViaVersion] ViaVersion detected server version: 1.21-1.21.1 (767)
[13:37:35] [Server thread/INFO]: [DecentHolograms] Enabling DecentHolograms v2.8.16
[13:37:36] [Server thread/INFO]: [NBTAPI] [NBTAPI] Found Minecraft: 1.21! Trying to find NMS support
[13:37:36] [Server thread/INFO]: [NBTAPI] [NBTAPI] NMS support 'MC1_21_R1' loaded!
[13:37:36] [Server thread/INFO]: [NBTAPI] [NBTAPI] Using the plugin 'DecentHolograms' to create a bStats instance!
[13:37:36] [Server thread/INFO]: [Citizens] Enabling Citizens v2.0.35-SNAPSHOT (build 3598)
[13:37:36] [Server thread/INFO]: [Citizens] Loading external libraries
[13:37:36] [Server thread/INFO]: [Citizens] 检测到系统语言 [[zh]]. 如果您有兴趣，欢迎通过我们的 Discord 为 Citizens 贡献翻译！https://discord.gg/Q6pZGSR
[13:37:36] [Server thread/INFO]: [Citizens] Using mojmapped server, avoiding server package checks
[13:37:36] [Server thread/INFO]: [Citizens] Loaded 0 templates.
[13:37:37] [Server thread/INFO]: [ViaBackwards] Enabling ViaBackwards v5.2.1
[13:37:37] [Server thread/INFO]: [Shoot] Enabling Shoot v1.5
[13:37:37] [Server thread/INFO]: [Shoot] §a成功初始化ParticleHelper
[13:37:37] [Server thread/INFO]: §a[ParticleEffect] 成功初始化ParticleHelper
[13:37:37] [Server thread/INFO]: [Shoot] §a成功初始化ParticleEffect兼容层
[13:37:37] [Server thread/INFO]: [Shoot] §aShoot 插件已成功加载！
[13:37:37] [Server thread/INFO]: [Shoot] §a粒子效果辅助类已初始化！
[13:37:37] [Server thread/INFO]: [Shoot] 已加载 20 个枪支材质
[13:37:37] [Server thread/INFO]: [Shoot] 已加载玩家枪支限制数据
[13:37:37] [Server thread/INFO]: [Shoot] 成功连接到DeathZombieV4插件，将同步显示设置
[13:37:37] [Server thread/INFO]: [Shoot] Shoot 插件已启用！
[13:37:37] [Server thread/INFO]: [CommandNPC] Enabling CommandNPC v1.7.2*
[13:37:37] [Server thread/INFO]: Injecting command information into Citizens.
[13:37:37] [Server thread/INFO]: [Sentinel] Enabling Sentinel v2.9.2-SNAPSHOT (build 527)
[13:37:37] [Server thread/INFO]: [Sentinel] Sentinel loading...
[13:37:37] [Server thread/INFO]: [Sentinel] Running on java version: 21.0.3
[13:37:37] [Server thread/INFO]: [Sentinel] Sentinel loaded on a fully supported Minecraft version. If you encounter any issues or need to ask a question, please join our Discord at https://discord.gg/Q6pZGSR and post in the '#sentinel' channel.
[13:37:37] [Server thread/WARN]: [Sentinel] Sentinel Configuration value 'random.spectral sound' is set to an invalid sound name. This is usually an ignorable issue.
[13:37:37] [Server thread/INFO]: [Sentinel] Sentinel loaded!
[13:37:37] [Server thread/INFO]: [Multiverse-Core] Enabling Multiverse-Core v4.3.17-SNAPSHOT
[13:37:37] [Server thread/WARN]: [Multiverse-Core] "Multiverse-Core v4.3.17-SNAPSHOT" has registered a listener for org.bukkit.event.entity.EntityCreatePortalEvent on method "public void com.onarandombox.MultiverseCore.listeners.MVPortalListener.entityPortalCreate(org.bukkit.event.entity.EntityCreatePortalEvent)", but the event is Deprecated. "Server performance will be affected"; please notify the authors [dumptruckman, Rigby, fernferret, lithium3141, main--].
[13:37:37] [Server thread/INFO]: [Multiverse-Core] §aWe are aware of the warning about the deprecated event. There is no alternative that allows us to do what we need to do and performance impact is negligible. It is safe to ignore.
[13:37:37] [Server thread/INFO]: -------- World Settings For [111] --------
[13:37:37] [Server thread/INFO]: Simulation Distance: 10
[13:37:37] [Server thread/INFO]: View Distance: 10
[13:37:37] [Server thread/INFO]: Allow Zombie Pigmen to spawn from portal blocks: true
[13:37:37] [Server thread/INFO]: Zombie Aggressive Towards Villager: true
[13:37:37] [Server thread/INFO]: Item Merge Radius: 2.5
[13:37:37] [Server thread/INFO]: Experience Merge Radius: 3.0
[13:37:37] [Server thread/INFO]: Hopper Transfer: 8 Hopper Check: 1 Hopper Amount: 1 Hopper Can Load Chunks: false
[13:37:37] [Server thread/INFO]: Nerfing mobs spawned from spawners: false
[13:37:37] [Server thread/INFO]: Entity Activation Range: An 32 / Mo 32 / Ra 48 / Mi 16 / Tiv true / Isa false
[13:37:37] [Server thread/INFO]: Entity Tracking Range: Pl 48 / An 48 / Mo 48 / Mi 32 / Di 128 / Other 64
[13:37:37] [Server thread/INFO]: Cactus Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Cane Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Melon Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Mushroom Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Pumpkin Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Sapling Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Beetroot Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Carrot Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Potato Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: TorchFlower Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Wheat Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: NetherWart Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Vine Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Cocoa Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Bamboo Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: SweetBerry Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Kelp Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: TwistingVines Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: WeepingVines Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: CaveVines Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: GlowBerry Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: PitcherPlant Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Custom Map Seeds:  Village: 10387312 Desert: 14357617 Igloo: 14357618 Jungle: 14357619 Swamp: 14357620 Monument: 10387313 Ocean: 14357621 Shipwreck: 165745295 End City: 10387313 Slime: 987234911 Nether: 30084232 Mansion: 10387319 Fossil: 14357921 Portal: 34222645
[13:37:37] [Server thread/INFO]: Arrow Despawn Rate: 1200 Trident Respawn Rate:1200
[13:37:37] [Server thread/INFO]: Tile Max Tick Time: 50ms Entity max Tick Time: 50ms
[13:37:37] [Server thread/INFO]: Max TNT Explosions: 100
[13:37:37] [Server thread/INFO]: Mob Spawn Range: 6
[13:37:37] [Server thread/INFO]: Item Despawn Rate: 6000
[13:37:37] [Server thread/INFO]: Preparing start region for dimension minecraft:111
[13:37:37] [Server thread/INFO]: Time elapsed: 98 ms
[13:37:37] [Server thread/INFO]: -------- World Settings For [Lobby] --------
[13:37:37] [Server thread/INFO]: Simulation Distance: 10
[13:37:37] [Server thread/INFO]: View Distance: 10
[13:37:37] [Server thread/INFO]: Allow Zombie Pigmen to spawn from portal blocks: true
[13:37:37] [Server thread/INFO]: Zombie Aggressive Towards Villager: true
[13:37:37] [Server thread/INFO]: Item Merge Radius: 2.5
[13:37:37] [Server thread/INFO]: Experience Merge Radius: 3.0
[13:37:37] [Server thread/INFO]: Hopper Transfer: 8 Hopper Check: 1 Hopper Amount: 1 Hopper Can Load Chunks: false
[13:37:37] [Server thread/INFO]: Nerfing mobs spawned from spawners: false
[13:37:37] [Server thread/INFO]: Entity Activation Range: An 32 / Mo 32 / Ra 48 / Mi 16 / Tiv true / Isa false
[13:37:37] [Server thread/INFO]: Entity Tracking Range: Pl 48 / An 48 / Mo 48 / Mi 32 / Di 128 / Other 64
[13:37:37] [Server thread/INFO]: Cactus Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Cane Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Melon Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Mushroom Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Pumpkin Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Sapling Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Beetroot Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Carrot Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Potato Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: TorchFlower Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Wheat Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: NetherWart Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Vine Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Cocoa Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Bamboo Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: SweetBerry Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Kelp Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: TwistingVines Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: WeepingVines Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: CaveVines Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: GlowBerry Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: PitcherPlant Growth Modifier: 100%
[13:37:37] [Server thread/INFO]: Custom Map Seeds:  Village: 10387312 Desert: 14357617 Igloo: 14357618 Jungle: 14357619 Swamp: 14357620 Monument: 10387313 Ocean: 14357621 Shipwreck: 165745295 End City: 10387313 Slime: 987234911 Nether: 30084232 Mansion: 10387319 Fossil: 14357921 Portal: 34222645
[13:37:37] [Server thread/INFO]: Arrow Despawn Rate: 1200 Trident Respawn Rate:1200
[13:37:37] [Server thread/INFO]: Tile Max Tick Time: 50ms Entity max Tick Time: 50ms
[13:37:37] [Server thread/INFO]: Max TNT Explosions: 100
[13:37:37] [Server thread/INFO]: Mob Spawn Range: 6
[13:37:37] [Server thread/INFO]: Item Despawn Rate: 6000
[13:37:38] [Server thread/INFO]: Preparing start region for dimension minecraft:lobby
[13:37:38] [Server thread/INFO]: Time elapsed: 69 ms
[13:37:38] [Server thread/INFO]: -------- World Settings For [Peak-Time] --------
[13:37:38] [Server thread/INFO]: Simulation Distance: 10
[13:37:38] [Server thread/INFO]: View Distance: 10
[13:37:38] [Server thread/INFO]: Allow Zombie Pigmen to spawn from portal blocks: true
[13:37:38] [Server thread/INFO]: Zombie Aggressive Towards Villager: true
[13:37:38] [Server thread/INFO]: Item Merge Radius: 2.5
[13:37:38] [Server thread/INFO]: Experience Merge Radius: 3.0
[13:37:38] [Server thread/INFO]: Hopper Transfer: 8 Hopper Check: 1 Hopper Amount: 1 Hopper Can Load Chunks: false
[13:37:38] [Server thread/INFO]: Nerfing mobs spawned from spawners: false
[13:37:38] [Server thread/INFO]: Entity Activation Range: An 32 / Mo 32 / Ra 48 / Mi 16 / Tiv true / Isa false
[13:37:38] [Server thread/INFO]: Entity Tracking Range: Pl 48 / An 48 / Mo 48 / Mi 32 / Di 128 / Other 64
[13:37:38] [Server thread/INFO]: Cactus Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Cane Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Melon Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Mushroom Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Pumpkin Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Sapling Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Beetroot Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Carrot Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Potato Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: TorchFlower Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Wheat Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: NetherWart Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Vine Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Cocoa Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Bamboo Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: SweetBerry Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Kelp Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: TwistingVines Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: WeepingVines Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: CaveVines Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: GlowBerry Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: PitcherPlant Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Custom Map Seeds:  Village: 10387312 Desert: 14357617 Igloo: 14357618 Jungle: 14357619 Swamp: 14357620 Monument: 10387313 Ocean: 14357621 Shipwreck: 165745295 End City: 10387313 Slime: 987234911 Nether: 30084232 Mansion: 10387319 Fossil: 14357921 Portal: 34222645
[13:37:38] [Server thread/INFO]: Arrow Despawn Rate: 1200 Trident Respawn Rate:1200
[13:37:38] [Server thread/INFO]: Tile Max Tick Time: 50ms Entity max Tick Time: 50ms
[13:37:38] [Server thread/INFO]: Max TNT Explosions: 100
[13:37:38] [Server thread/INFO]: Mob Spawn Range: 6
[13:37:38] [Server thread/INFO]: Item Despawn Rate: 6000
[13:37:38] [Server thread/INFO]: Preparing start region for dimension minecraft:peak-time
[13:37:38] [Server thread/INFO]: Time elapsed: 53 ms
[13:37:38] [Server thread/INFO]: -------- World Settings For [testWorld] --------
[13:37:38] [Server thread/INFO]: Simulation Distance: 10
[13:37:38] [Server thread/INFO]: View Distance: 10
[13:37:38] [Server thread/INFO]: Allow Zombie Pigmen to spawn from portal blocks: true
[13:37:38] [Server thread/INFO]: Zombie Aggressive Towards Villager: true
[13:37:38] [Server thread/INFO]: Item Merge Radius: 2.5
[13:37:38] [Server thread/INFO]: Experience Merge Radius: 3.0
[13:37:38] [Server thread/INFO]: Hopper Transfer: 8 Hopper Check: 1 Hopper Amount: 1 Hopper Can Load Chunks: false
[13:37:38] [Server thread/INFO]: Nerfing mobs spawned from spawners: false
[13:37:38] [Server thread/INFO]: Entity Activation Range: An 32 / Mo 32 / Ra 48 / Mi 16 / Tiv true / Isa false
[13:37:38] [Server thread/INFO]: Entity Tracking Range: Pl 48 / An 48 / Mo 48 / Mi 32 / Di 128 / Other 64
[13:37:38] [Server thread/INFO]: Cactus Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Cane Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Melon Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Mushroom Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Pumpkin Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Sapling Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Beetroot Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Carrot Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Potato Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: TorchFlower Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Wheat Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: NetherWart Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Vine Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Cocoa Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Bamboo Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: SweetBerry Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Kelp Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: TwistingVines Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: WeepingVines Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: CaveVines Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: GlowBerry Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: PitcherPlant Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Custom Map Seeds:  Village: 10387312 Desert: 14357617 Igloo: 14357618 Jungle: 14357619 Swamp: 14357620 Monument: 10387313 Ocean: 14357621 Shipwreck: 165745295 End City: 10387313 Slime: 987234911 Nether: 30084232 Mansion: 10387319 Fossil: 14357921 Portal: 34222645
[13:37:38] [Server thread/INFO]: Arrow Despawn Rate: 1200 Trident Respawn Rate:1200
[13:37:38] [Server thread/INFO]: Tile Max Tick Time: 50ms Entity max Tick Time: 50ms
[13:37:38] [Server thread/INFO]: Max TNT Explosions: 100
[13:37:38] [Server thread/INFO]: Mob Spawn Range: 6
[13:37:38] [Server thread/INFO]: Item Despawn Rate: 6000
[13:37:38] [Server thread/INFO]: Preparing start region for dimension minecraft:testworld
[13:37:38] [Server thread/INFO]: Time elapsed: 38 ms
[13:37:38] [Server thread/INFO]: -------- World Settings For [CenterRoad] --------
[13:37:38] [Server thread/INFO]: Simulation Distance: 10
[13:37:38] [Server thread/INFO]: View Distance: 10
[13:37:38] [Server thread/INFO]: Allow Zombie Pigmen to spawn from portal blocks: true
[13:37:38] [Server thread/INFO]: Zombie Aggressive Towards Villager: true
[13:37:38] [Server thread/INFO]: Item Merge Radius: 2.5
[13:37:38] [Server thread/INFO]: Experience Merge Radius: 3.0
[13:37:38] [Server thread/INFO]: Hopper Transfer: 8 Hopper Check: 1 Hopper Amount: 1 Hopper Can Load Chunks: false
[13:37:38] [Server thread/INFO]: Nerfing mobs spawned from spawners: false
[13:37:38] [Server thread/INFO]: Entity Activation Range: An 32 / Mo 32 / Ra 48 / Mi 16 / Tiv true / Isa false
[13:37:38] [Server thread/INFO]: Entity Tracking Range: Pl 48 / An 48 / Mo 48 / Mi 32 / Di 128 / Other 64
[13:37:38] [Server thread/INFO]: Cactus Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Cane Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Melon Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Mushroom Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Pumpkin Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Sapling Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Beetroot Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Carrot Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Potato Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: TorchFlower Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Wheat Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: NetherWart Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Vine Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Cocoa Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Bamboo Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: SweetBerry Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Kelp Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: TwistingVines Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: WeepingVines Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: CaveVines Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: GlowBerry Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: PitcherPlant Growth Modifier: 100%
[13:37:38] [Server thread/INFO]: Custom Map Seeds:  Village: 10387312 Desert: 14357617 Igloo: 14357618 Jungle: 14357619 Swamp: 14357620 Monument: 10387313 Ocean: 14357621 Shipwreck: 165745295 End City: 10387313 Slime: 987234911 Nether: 30084232 Mansion: 10387319 Fossil: 14357921 Portal: 34222645
[13:37:38] [Server thread/INFO]: Arrow Despawn Rate: 1200 Trident Respawn Rate:1200
[13:37:38] [Server thread/INFO]: Tile Max Tick Time: 50ms Entity max Tick Time: 50ms
[13:37:38] [Server thread/INFO]: Max TNT Explosions: 100
[13:37:38] [Server thread/INFO]: Mob Spawn Range: 6
[13:37:38] [Server thread/INFO]: Item Despawn Rate: 6000
[13:37:38] [Server thread/INFO]: Preparing start region for dimension minecraft:centerroad
[13:37:38] [Server thread/INFO]: Time elapsed: 51 ms
[13:37:38] [Server thread/INFO]: [Multiverse-Core] 8 - World(s) loaded.
[13:37:38] [Server thread/WARN]: [Multiverse-Core] Buscript failed to load! The script command will be disabled! If you would like not to see this message, use `/mv conf enablebuscript false` to disable Buscript from loading.
[13:37:38] [Server thread/INFO]: [Multiverse-Core] Version 4.3.17-SNAPSHOT (API v24) Enabled - By dumptruckman, Rigby, fernferret, lithium3141 and main--
[13:37:38] [Server thread/INFO]: [Denizen] Enabling Denizen v1.3.0-SNAPSHOT (build 1803-REL)
[13:37:38] [Server thread/INFO]: +> [DenizenCore] Initializing Denizen Core v1.91.0-SNAPSHOT (Build 1374), impl for Spigot v1.3.0-SNAPSHOT (build 1803-REL) 
[13:37:39] [Server thread/INFO]: +> [Denizen] Running on java version: 21.0.3 
[13:37:39] [Server thread/INFO]: +> [Denizen] Running on unrecognized (future?) Java version. May or may not work. 
[13:37:39] [Server thread/WARN]: [Denizen] -------------------------------------
[13:37:39] [Server thread/WARN]: [Denizen] This build of Denizen is not compatible with this Spigot version! Deactivating Denizen!
[13:37:39] [Server thread/WARN]: [Denizen] -------------------------------------
[13:37:39] [Server thread/INFO]: [Denizen] Disabling Denizen v1.3.0-SNAPSHOT (build 1803-REL)
[13:37:39] [Server thread/INFO]: [DeathZombieV4] Enabling DeathZombieV4 v1.2
[13:37:39] [Server thread/WARN]: java.lang.IllegalArgumentException: The embedded resource 'zombie.yml' cannot be found in plugins\.paper-remapped\deathzombiev4-1.2.jar
[13:37:39] [Server thread/WARN]: 	at org.bukkit.plugin.java.JavaPlugin.saveResource(JavaPlugin.java:215)
[13:37:39] [Server thread/WARN]: 	at deathzombiev4-1.2.jar//org.Ver_zhzh.customZombie.UserCustomZombie.UserCustomZombie.initializeConfig(UserCustomZombie.java:99)
[13:37:39] [Server thread/WARN]: 	at deathzombiev4-1.2.jar//org.Ver_zhzh.customZombie.UserCustomZombie.UserCustomZombie.<init>(UserCustomZombie.java:83)
[13:37:39] [Server thread/WARN]: 	at deathzombiev4-1.2.jar//org.Ver_zhzh.customZombie.DualZombieSystemManager.<init>(DualZombieSystemManager.java:44)
[13:37:39] [Server thread/WARN]: 	at deathzombiev4-1.2.jar//org.Ver_zhzh.deathZombieV4.utils.ZombieHelper.initialize(ZombieHelper.java:90)
[13:37:39] [Server thread/WARN]: 	at deathzombiev4-1.2.jar//org.Ver_zhzh.deathZombieV4.utils.ZombieHelper.<init>(ZombieHelper.java:59)
[13:37:39] [Server thread/WARN]: 	at deathzombiev4-1.2.jar//org.Ver_zhzh.deathZombieV4.DeathZombieV4.onEnable(DeathZombieV4.java:137)
[13:37:39] [Server thread/WARN]: 	at org.bukkit.plugin.java.JavaPlugin.setEnabled(JavaPlugin.java:288)
[13:37:39] [Server thread/WARN]: 	at io.papermc.paper.plugin.manager.PaperPluginInstanceManager.enablePlugin(PaperPluginInstanceManager.java:202)
[13:37:39] [Server thread/WARN]: 	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.enablePlugin(PaperPluginManagerImpl.java:109)
[13:37:39] [Server thread/WARN]: 	at org.bukkit.plugin.SimplePluginManager.enablePlugin(SimplePluginManager.java:520)
[13:37:39] [Server thread/WARN]: 	at org.bukkit.craftbukkit.CraftServer.enablePlugin(CraftServer.java:640)
[13:37:39] [Server thread/WARN]: 	at org.bukkit.craftbukkit.CraftServer.enablePlugins(CraftServer.java:589)
[13:37:39] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.loadWorld0(MinecraftServer.java:754)
[13:37:39] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.loadLevel(MinecraftServer.java:516)
[13:37:39] [Server thread/WARN]: 	at net.minecraft.server.dedicated.DedicatedServer.initServer(DedicatedServer.java:329)
[13:37:39] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:1215)
[13:37:39] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.lambda$spin$0(MinecraftServer.java:330)
[13:37:39] [Server thread/WARN]: 	at java.base/java.lang.Thread.run(Thread.java:1583)
[13:37:42] [Server thread/INFO]: Done preparing level "world" (7.482s)
[13:37:42] [Server thread/INFO]: Running delayed init tasks
[13:37:42] [Craft Scheduler Thread - 5 - DecentHolograms/INFO]: 
A newer version of DecentHolograms is available. Download it from: 
- https://www.spigotmc.org/resources/96927/
- https://modrinth.com/plugin/decentholograms
[13:37:42] [Server thread/INFO]: Done (35.627s)! For help, type "help"
