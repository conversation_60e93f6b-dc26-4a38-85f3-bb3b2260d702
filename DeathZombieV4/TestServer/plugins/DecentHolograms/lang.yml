prefix: '&8[&3DecentHolograms&8] &7'
no_perm: '{prefix}&c你不可以使用这个.'
only_player: '{prefix}&c该命令仅可被玩家执行.'
reloaded: '{prefix}&a成功重载, 耗时 %1$d ms!'
command:
  use_help: '{prefix}使用命令 &b/holograms help&7 来浏览可用命令.'
  usage: '{prefix}命令用法: &b%1$s'
  unknown_sub_command: '{prefix}未知子命令.'
hologram:
  does_not_exist: '{prefix}&c该名称的悬浮字不存在.'
  already_exists: '{prefix}&c该名称的悬浮字已存在.'
  invalid_name: '{prefix}&c无效名称 ''%1$s'', 只可以使用英文字母, 下划线和横杠.'
  created: '{prefix}悬浮字已成功创建!'
  cloned: '{prefix}悬浮字已成功复制!'
  deleted: '{prefix}悬浮字已成功删除!'
  updated: '{prefix}悬浮字已成功刷新!'
  renamed: '{prefix}悬浮字已成功重命名! &7(&b%1$s&7 -> &b%2$s&7)'
  teleported: '{prefix}已传送至该位置!'
  moved: '{prefix}悬浮字已成功移动!'
  aligned: '{prefix}悬浮字已被成功对齐!'
  align_self: '{prefix}不能对被操作悬浮字本身进行对齐!'
  align_axis: '{prefix}该操作不存在!'
  down_origin_set: '{prefix}原点 属性 已被设置为 &b''%1$s''&7!'
  down_origin_does_not_exist: '{prefix}原点 属性 只能填入 true 或 false!'
  always_face_player_set: '{prefix}总是面朝玩家 属性 已被设置为 &b''%1$s''&7!'
  always_face_player_does_not_exist: '{prefix}总是面朝玩家 属性 只能填入 true 或 false!'
  facing_set: '{prefix}面朝方向已设置!'
  flag_add: '{prefix}标签 &b"%1$s"&7 已被设置!'
  flag_remove: '{prefix}标签 &b"%1$s"&7 已被移除!'
  permission_set: '{prefix}权限已设置!'
  permission_removed: '{prefix}权限已移除!'
  display_range_set: '{prefix}显示范围已设置!'
  update_range_set: '{prefix}显示范围已移除!'
  update_interval_set: '{prefix}更新间隔已被设置!'
  disabled: '{prefix}悬浮字已被禁止显示!'
  already_disabled: '{prefix}悬浮字已经是禁止显示状态了!'
  enabled: '{prefix}悬浮字已显示!'
  already_enabled: '{prefix}悬浮字已经是显示状态了!'
  save_failed: '{prefix}&c悬浮字保存出现错误. 请检查服务器后台以获取更多信息!'
page:
  added: '{prefix}新的一页已成功添加!'
  add_failed: '{prefix}该页已成功删除!'
  inserted: '{prefix}该页已成功插入!'
  insert_failed: '{prefix}该页插入失败!'
  deleted: '{prefix}该页已成功删除!'
  swapped: '{prefix}该页已成功交换!'
  swap_self: '{prefix}&c该页不可与它本身进行交换!'
  swap_failed: '{prefix}&c交换页面失败.'
  does_not_exist: '{prefix}&c该页不存在.'
action:
  click_type_does_not_exist: '{prefix}&c点击类型不存在.'
  does_not_exist: '{prefix}&c动作类型不存在.'
  added: '{prefix}已成功添加操作.'
  removed: '{prefix}已成功删除操作.'
  cleared: '{prefix}已成功清除操作列表.'
  no_actions: '{prefix}当前点击类型无已设置的操作.'
line:
  added: '{prefix}成功添加一行文本!'
  add_failed: '{prefix}&c添加文本行失败.'
  set: '{prefix}文本行已被成功设置!'
  edit: '{prefix}&a&l&n点击此处编辑该行文本!'
  edit_hover: '&r%1$s'
  inserted: '{prefix}已成功插入该行文本!'
  insert_failed: '{prefix}&c插入该行文本失败.'
  removed: '{prefix}该行文本已成功移除!'
  swapped: '{prefix}该行文本已成功交换!'
  swap_self: '{prefix}&c不能将该行文本与它自身交换!'
  swap_failed: '{prefix}&c该行文本交换失败.'
  aligned: '{prefix}该行文本已被成功对齐!'
  align_self: '{prefix}该行文本不能对齐至它本身!'
  align_axis: '{prefix}该坐标不存在!'
  height_set: '{prefix}该行文本高度已成功设置!'
  offsetx_set: '{prefix}该行文本的 X轴偏移量 已被成功设置!'
  offsetz_set: '{prefix}该行文本的 Z轴偏移量 已被成功设置!'
  flag_added: '{prefix}标签 &b"%1$s"&7 已被成功添加!'
  flag_removed: '{prefix}标签 &b"%1$s"&7 已被成功移除!'
  does_not_exist: '{prefix}&c该行文本不存在.'
  permission_set: '{prefix}权限已设置!'
  permission_removed: '{prefix}权限已移除!'
  facing_set: '{prefix}面朝方向已设置!'
feature:
  does_not_exist: '{prefix}&c特性 "%1$s" 不存在.'
  enabled: '{prefix}特性 &b"%1$s"&7 已被启用!'
  already_enabled: '{prefix}&c特性 "%1$s" is 已被是启用状态!'
  disabled: '{prefix}特性 &b"%1$s"&7 已被禁用!'
  already_disabled: '{prefix}&c特性 "%1$s" 已经是禁用状态!'
  reloaded: '{prefix}特性 &b"%1$s"&7 已成功重载!'