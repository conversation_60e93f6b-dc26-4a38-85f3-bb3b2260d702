luckperms.logs.actionlog-prefix=LOG
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=EKSPORTER
luckperms.commandsystem.available-commands=Brug {0} for at se tilgÃ¦ngelige kommandoer
luckperms.commandsystem.command-not-recognised=Kommando ikke genkendt
luckperms.commandsystem.no-permission=Du har ikke tilladelse til at bruge denne kommando\!
luckperms.commandsystem.no-permission-subcommands=Du har ikke tilladelse til at bruge nogen underkommandoer
luckperms.commandsystem.already-executing-command=En anden kommando udfÃ¸res; afventer dens fÃ¦rdiggÃ¸relse...
luckperms.commandsystem.usage.sub-commands-header=Underkommandoer
luckperms.commandsystem.usage.usage-header=Kommando brug
luckperms.commandsystem.usage.arguments-header=Argumenter
luckperms.first-time.no-permissions-setup=Det lader til, at ingen tilladelser er blevet opsat endnu\!
luckperms.first-time.use-console-to-give-access=FÃ¸r du kan bruge nogen af LuckPerms kommandoerne i spillet, skal du bruge konsollen til at give dig selv adgang
luckperms.first-time.console-command-prompt=Ãbn din konsol og kÃ¸r
luckperms.first-time.next-step=NÃ¥r du har gjort dette, kan du begynde at definere dine tilladelser og grupper
luckperms.first-time.wiki-prompt=Ved ikke, hvor du skal starte? Tjek her\: {0}
luckperms.login.try-again=PrÃ¸v igen senere
luckperms.login.loading-database-error=En database fejl opstod under indlÃ¦sning af tilladelses data
luckperms.login.server-admin-check-console-errors=Hvis du er en serveradministrator, sÃ¥ tjek venligst konsollen for eventuelle fejl
luckperms.login.server-admin-check-console-info=Tjek venligst serverkonsollen for mere information
luckperms.login.data-not-loaded-at-pre=Tilladelses data for din bruger blev ikke indlÃ¦st i prÃ¦-login fasen
luckperms.login.unable-to-continue=kunne ikke fortsÃ¦tte
luckperms.login.craftbukkit-offline-mode-error=dette skyldes sandsynligvis en konflikt mellem CraftBukkit og online-mode indstillingen
luckperms.login.unexpected-error=En uventet fejl opstod under opsÃ¦tning af dine tilladelses data
luckperms.opsystem.disabled=Vanilla OP-systemet er deaktiveret pÃ¥ denne server
luckperms.opsystem.sponge-warning=BemÃ¦rk, at Server Operator status ikke har nogen effekt pÃ¥ Sponge tilladelse kontrol, nÃ¥r et tilladelse plugin er installeret, skal du redigere brugerdata direkte
luckperms.duration.unit.years.plural={0} Ã¥r
luckperms.duration.unit.years.singular={0} Ã¥r
luckperms.duration.unit.years.short={0}Ã¥r
luckperms.duration.unit.months.plural={0} mÃ¥neder
luckperms.duration.unit.months.singular={0} mÃ¥ned
luckperms.duration.unit.months.short={0}mÃ¥ned
luckperms.duration.unit.weeks.plural={0} uger
luckperms.duration.unit.weeks.singular={0} uge
luckperms.duration.unit.weeks.short={0}u
luckperms.duration.unit.days.plural={0} dage
luckperms.duration.unit.days.singular={0} dag
luckperms.duration.unit.days.short={0}d
luckperms.duration.unit.hours.plural={0} timer
luckperms.duration.unit.hours.singular={0} time
luckperms.duration.unit.hours.short={0}t
luckperms.duration.unit.minutes.plural={0} minutter
luckperms.duration.unit.minutes.singular={0} minut
luckperms.duration.unit.minutes.short={0}min
luckperms.duration.unit.seconds.plural={0} sekunder
luckperms.duration.unit.seconds.singular={0} sekund
luckperms.duration.unit.seconds.short={0}s
luckperms.duration.since={0} siden
luckperms.command.misc.invalid-code=Ugyldig kode
luckperms.command.misc.response-code-key=svarkode
luckperms.command.misc.error-message-key=besked
luckperms.command.misc.bytebin-unable-to-communicate=Kunne ikke kommunikere med bytebin
luckperms.command.misc.webapp-unable-to-communicate=Kunne ikke kommunikere med web-appen
luckperms.command.misc.check-console-for-errors=Tjek konsollen for fejl
luckperms.command.misc.file-must-be-in-data=Filen {0} skal vÃ¦re et direkte barn af datamappen
luckperms.command.misc.wait-to-finish=Vent venligst pÃ¥ at den er fÃ¦rdig og prÃ¸v igen
luckperms.command.misc.invalid-priority=Ugyldig prioritet {0}
luckperms.command.misc.expected-number=Forventede et tal
luckperms.command.misc.date-parse-error=Kunne ikke parse datoen {0}
luckperms.command.misc.date-in-past-error=Du kan ikke sÃ¦tte en dato fra fortiden\!
luckperms.command.misc.page=side {0} af {1}
luckperms.command.misc.page-entries={0} poster
luckperms.command.misc.none=Ingen
luckperms.command.misc.loading.error.unexpected=Der opstod en uventet fejl
luckperms.command.misc.loading.error.user=Bruger ikke indlÃ¦st
luckperms.command.misc.loading.error.user-specific=Kunne ikke indlÃ¦se mÃ¥lbrugeren {0}
luckperms.command.misc.loading.error.user-not-found=En bruger for {0} kunne ikke findes
luckperms.command.misc.loading.error.user-save-error=Der var en fejl med at gemme brugerdata for {0}
luckperms.command.misc.loading.error.user-not-online=Brugeren {0} er ikke online
luckperms.command.misc.loading.error.user-invalid={0} er ikke et gyldigt brugernavn eller UUID
luckperms.command.misc.loading.error.user-not-uuid=MÃ¥lbrugeren {0} er ikke et gyldigt uuid
luckperms.command.misc.loading.error.group=Gruppen blev ikke indlÃ¦st
luckperms.command.misc.loading.error.all-groups=Kunne ikke indlÃ¦se alle grupper
luckperms.command.misc.loading.error.group-not-found=En gruppe navngivet {0} kunne ikke blive fundet
luckperms.command.misc.loading.error.group-save-error=Der var en fejl med at gemme gruppedata for {0}
luckperms.command.misc.loading.error.group-invalid={0} er ikke et gyldigt gruppenavn
luckperms.command.misc.loading.error.track=Sporet er ikke indlÃ¦st
luckperms.command.misc.loading.error.all-tracks=Kunne ikke indlÃ¦se alle spor
luckperms.command.misc.loading.error.track-not-found=Et spor navngivet {0} blev ikke fundet
luckperms.command.misc.loading.error.track-save-error=Der var en fejl med at gemme spordataen {0}
luckperms.command.misc.loading.error.track-invalid={0} er ikke et gyldigt spornavn
luckperms.command.editor.no-match=Kan ikke Ã¥bne editor, ingen objekter matchede den Ã¸nskede type
luckperms.command.editor.start=Forbereder en ny redigeringssession, vent venligst...
luckperms.command.editor.url=Klik pÃ¥ linket nedenfor for at Ã¥bne editoren
luckperms.command.editor.unable-to-communicate=Kunne ikke kommunikere med redigeringsvÃ¦rktÃ¸jet
luckperms.command.editor.apply-edits.success=Data fra redigeringsvÃ¦rktÃ¸jet blev anvendt til {0} {1} med succes
luckperms.command.editor.apply-edits.success-summary={0} {1} og {2} {3}
luckperms.command.editor.apply-edits.success.additions=tillÃ¦gninger
luckperms.command.editor.apply-edits.success.additions-singular=tillÃ¦gning
luckperms.command.editor.apply-edits.success.deletions=sletninger
luckperms.command.editor.apply-edits.success.deletions-singular=sletning
luckperms.command.editor.apply-edits.no-changes=Ingen Ã¦ndringer blev anvendt fra webeditoren, de returnerede data indeholdt ingen redigeringer
luckperms.command.editor.apply-edits.unknown-type=Kan ikke anvende redigering pÃ¥ den angivne objekttype
luckperms.command.editor.apply-edits.unable-to-read=Kan ikke lÃ¦se data med den givne kode
luckperms.command.search.searching.permission=SÃ¸ger efter brugere og grupper med {0}
luckperms.command.search.searching.inherit=SÃ¸ger efter brugere og grupper som arver fra {0}
luckperms.command.search.result=Fandt {0} poster fra {1} brugere og {2} grupper
luckperms.command.search.result.default-notice=BemÃ¦rk\: nÃ¥r der sÃ¸ges efter medlemmer af standardgruppen, vil offline spillere uden andre tilladelser ikke blive vist\!
luckperms.command.search.showing-users=Viser brugerposter
luckperms.command.search.showing-groups=Viser gruppeposter
luckperms.command.tree.start=Genererer tilladelsestrÃ¦. Vent venligst...
luckperms.command.tree.empty=Ikke i stand til at generere trÃ¦, ingen resultater blev fundet
luckperms.command.tree.url=TilladelsestrÃ¦ URL
luckperms.command.verbose.invalid-filter={0} er ikke et gyldigt verbose filter
luckperms.command.verbose.enabled=Detaljeret logning {0} for tjek der matcher {1}
luckperms.command.verbose.command-exec=Tvinger {0} til at udfÃ¸re kommando {1} og rapportere alle Ã¦ndringer foretaget...
luckperms.command.verbose.off=Detaljeret logfÃ¸ring {0}
luckperms.command.verbose.command-exec-complete=Kommandoen blev udfÃ¸rt
luckperms.command.verbose.command.no-checks=Kommandoen udfÃ¸rt, men ingen tilladelse tjek blev foretaget
luckperms.command.verbose.command.possibly-async=Dette kan skyldes, at pluginet kÃ¸rer kommandoer i baggrunden (async)
luckperms.command.verbose.command.try-again-manually=Du kan stadig bruge detaljeret manuelt til at opdage kontrol foretaget pÃ¥ denne mÃ¥de
luckperms.command.verbose.enabled-recording=Detaljeret optagelse {0} for tjek der matcher {1}
luckperms.command.verbose.uploading=Detaljeret logning {0}, uploader resultater...
luckperms.command.verbose.url=Detaljeret resultaters URL
luckperms.command.verbose.enabled-term=aktiveret
luckperms.command.verbose.disabled-term=slÃ¥et fra
luckperms.command.verbose.query-any=ALLE
luckperms.command.info.running-plugin=KÃ¸rer
luckperms.command.info.platform-key=Platform
luckperms.command.info.server-brand-key=ServermÃ¦rke
luckperms.command.info.server-version-key=Server Version
luckperms.command.info.storage-key=Lager
luckperms.command.info.storage-type-key=Type
luckperms.command.info.storage.meta.split-types-key=Typer
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=Tilsluttet
luckperms.command.info.storage.meta.file-size-key=FilstÃ¸rrelse
luckperms.command.info.extensions-key=Udvidelser
luckperms.command.info.messaging-key=Beskeder
luckperms.command.info.instance-key=Instans
luckperms.command.info.static-contexts-key=Statiske kontekster
luckperms.command.info.online-players-key=Online Spillere
luckperms.command.info.online-players-unique={0} unikke
luckperms.command.info.uptime-key=Oppetid
luckperms.command.info.local-data-key=Lokal Data
luckperms.command.info.local-data={0} brugere, {1} grupper, {2} spor
luckperms.command.generic.create.success={0} blev oprettet uden problemer
luckperms.command.generic.create.error=Der opstod en fejl under oprettelsen af {0}
luckperms.command.generic.create.error-already-exists={0} eksisterer allerede\!
luckperms.command.generic.delete.success={0} er blevet slettet uden problemer
luckperms.command.generic.delete.error=Der opstod en fejl under sletningen af {0}
luckperms.command.generic.delete.error-doesnt-exist={0} eksisterer ikke\!
luckperms.command.generic.rename.success={0} blev omdÃ¸bt til {1}
luckperms.command.generic.clone.success={0} blev klonet pÃ¥ {1}
luckperms.command.generic.info.parent.title=Overordnet Gruppe
luckperms.command.generic.info.parent.temporary-title=Midlertidige Overordnede Grupper
luckperms.command.generic.info.expires-in=udlÃ¸ber om
luckperms.command.generic.info.inherited-from=nedarvet fra
luckperms.command.generic.info.inherited-from-self=selv
luckperms.command.generic.show-tracks.title={0}''s Spor
luckperms.command.generic.show-tracks.empty={0} er ikke pÃ¥ nogen spor
luckperms.command.generic.clear.node-removed={0} indholdselementer blev fjernet
luckperms.command.generic.clear.node-removed-singular={0} indholdselement blev fjernet
luckperms.command.generic.clear={0}''s indholdselementer blev ryddet i kontekst {1}
luckperms.command.generic.permission.info.title={0}''s Tilladelser
luckperms.command.generic.permission.info.empty={0} har ingen rettigheder angivet
luckperms.command.generic.permission.info.click-to-remove=Klik for at fjerne dette indholdselement fra {0}
luckperms.command.generic.permission.check.info.title=Tilladelsesoplysninger for {0}
luckperms.command.generic.permission.check.info.directly={0} har {1} indstillet til {2} i kontekst {3}
luckperms.command.generic.permission.check.info.inherited={0} arver {1} indstillet til {2} fra {3} i kontekst {4}
luckperms.command.generic.permission.check.info.not-directly={0} har ikke {1} indstillet
luckperms.command.generic.permission.check.info.not-inherited={0} arver ikke {1}
luckperms.command.generic.permission.check.result.title=Tilladelsestjek for {0}
luckperms.command.generic.permission.check.result.result-key=Resultat
luckperms.command.generic.permission.check.result.processor-key=Processor
luckperms.command.generic.permission.check.result.cause-key=Ãrsag
luckperms.command.generic.permission.check.result.context-key=Kontekst
luckperms.command.generic.permission.set=SÃ¦t {0} til {1} for {2} i kontekst {3}
luckperms.command.generic.permission.already-has={0} har allerede {1} sat i kontekst {2}
luckperms.command.generic.permission.set-temp=SÃ¦t {0} til {1} for {2} for en varighed af {3} i kontekst {4}
luckperms.command.generic.permission.already-has-temp={0} har allerede {1} midlertidigt indstillet i kontekst {2}
luckperms.command.generic.permission.unset=Fjern {0} for {1} i kontekst {2}
luckperms.command.generic.permission.doesnt-have={0} har ikke {1} sat i kontekst {2}
luckperms.command.generic.permission.unset-temp=Fjern midlertidig tilladelse {0} for {1} i kontekst {2}
luckperms.command.generic.permission.subtract=SÃ¦t {0} til {1} for {2} for en varighed af {3} i kontekst {4}, {5} mindre end fÃ¸r
luckperms.command.generic.permission.doesnt-have-temp={0} har ikke {1} indstillet midlertidigt i kontekst {2}
luckperms.command.generic.permission.clear={0}''s rettigheder blev ryddet i kontekst {1}
luckperms.command.generic.parent.info.title={0}''s ForÃ¦ldre
luckperms.command.generic.parent.info.empty={0} har ingen forÃ¦ldre defineret
luckperms.command.generic.parent.info.click-to-remove=Klik for at fjerne denne forÃ¦lder fra {0}
luckperms.command.generic.parent.add={0} arver nu tilladelser fra {1} i kontekst {2}
luckperms.command.generic.parent.add-temp={0} arver nu tilladelser fra {1} for en varighed af {2} i kontekst {3}
luckperms.command.generic.parent.set={0} fik deres eksisterende forÃ¦ldre gruppe fjernet, og arver kun {1} i kontekst {2}
luckperms.command.generic.parent.set-track={0} fik deres eksisterende forÃ¦ldre gruppe fjernet, pÃ¥ track {1}, og arver kun {2} i kontekst {3}
luckperms.command.generic.parent.remove={0} arver ikke lÃ¦ngere tilladelser fra {1} i kontekst {2}
luckperms.command.generic.parent.remove-temp={0} arver ikke lÃ¦ngere midlertidigt tilladelser fra {1} i kontekst {2}
luckperms.command.generic.parent.subtract={0} vil arve tilladelser fra {1} for en varighed af {2} i kontekst {3}, {4} mindre end fÃ¸r
luckperms.command.generic.parent.clear={0}''s forÃ¦ldre blev ryddet i kontekst {1}
luckperms.command.generic.parent.clear-track={0}''s forÃ¦ldre pÃ¥ sporet {1} blev ryddet i kontekst {2}
luckperms.command.generic.parent.already-inherits={0} har allerede {1} sat i kontekst {2}
luckperms.command.generic.parent.doesnt-inherit={0} arver ikke fra {1} i kontekst {2}
luckperms.command.generic.parent.already-temp-inherits={0} arver allerede midlertidigt fra {1} i kontekst {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} arver ikke midlertidigt fra {1} i kontekst {2}
luckperms.command.generic.chat-meta.info.title-prefix={0}''s PrÃ¦fikses
luckperms.command.generic.chat-meta.info.title-suffix={0}''s Suffikses
luckperms.command.generic.chat-meta.info.none-prefix={0} har ingen prÃ¦fikser
luckperms.command.generic.chat-meta.info.none-suffix={0} har ingen suffikser
luckperms.command.generic.chat-meta.info.click-to-remove=Klik for at fjerne denne {0} fra {1}
luckperms.command.generic.chat-meta.already-has={0} har allerede {1} {2} sat til en prioritet pÃ¥ {3} i kontekst {4}
luckperms.command.generic.chat-meta.already-has-temp={0} har allerede {1} {2} midlertidigt sat til en prioritet pÃ¥ {3} i kontekst {4}
luckperms.command.generic.chat-meta.doesnt-have={0} har ikke {1} {2} sat til en prioritet pÃ¥ {3} i kontekst {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} har ikke {1} {2} sat til en midlertidigt prioritet pÃ¥ {3} i kontekst {4}
luckperms.command.generic.chat-meta.add={0} havde {1} {2} sat til en prioritet pÃ¥ {3} i kontekst {4}
luckperms.command.generic.chat-meta.add-temp={0} havde {1} {2} sat til en prioritet pÃ¥ {3} for en varighed af {4} i kontekst {5}
luckperms.command.generic.chat-meta.remove={0} havde {1} {2} i prioritet {3} fjernet i kontekst {4}
luckperms.command.generic.chat-meta.remove-bulk={0} havde alle {1} med prioritet {2} fjernet i kontekst {3}
luckperms.command.generic.chat-meta.remove-temp={0} havde midlertidig {1} {2} med prioritet {3} fjernet i kontekst {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} havde alle midlertidige {1} med prioritet {2} fjernet i kontekst {3}
luckperms.command.generic.meta.info.title={0}''s Meta
luckperms.command.generic.meta.info.none={0} har ingen meta
luckperms.command.generic.meta.info.click-to-remove=Klik for at fjerne denne meta node fra {0}
luckperms.command.generic.meta.already-has={0} har allerede metanÃ¸gle {1} sat til {2} i kontekst {3}
luckperms.command.generic.meta.already-has-temp={0} har allerede metanÃ¸gle {1} midlertidigt indstillet til {2} i kontekst {3}
luckperms.command.generic.meta.doesnt-have={0} har ikke metanÃ¸gle {1} sat i kontekst {2}
luckperms.command.generic.meta.doesnt-have-temp={0} har ikke metanÃ¸gle {1} sat midlertidigt i kontekst {2}
luckperms.command.generic.meta.set=SÃ¦t metanÃ¸gle {0} til {1} for {2} i kontekst {3}
luckperms.command.generic.meta.set-temp=SÃ¦t metanÃ¸gle {0} til {1} for {2} for en varighed af {3} i kontekst {4}
luckperms.command.generic.meta.unset=Fjern metanÃ¸gle {0} for {1} i kontekst {2}
luckperms.command.generic.meta.unset-temp=Fjern midlertidig metanÃ¸gle {0} for {1} i kontekst {2}
luckperms.command.generic.meta.clear={0}''s meta matchende type {1} blev ryddet i kontekst {2}
luckperms.command.generic.contextual-data.title=Kontekstuel Data
luckperms.command.generic.contextual-data.mode.key=tilstand
luckperms.command.generic.contextual-data.mode.server=server
luckperms.command.generic.contextual-data.mode.active-player=aktiv spiller
luckperms.command.generic.contextual-data.contexts-key=Kontekster
luckperms.command.generic.contextual-data.prefix-key=PrÃ¦fiks
luckperms.command.generic.contextual-data.suffix-key=Suffiks
luckperms.command.generic.contextual-data.primary-group-key=PrimÃ¦r Gruppe
luckperms.command.generic.contextual-data.meta-key=Meta
luckperms.command.generic.contextual-data.null-result=Ingen
luckperms.command.user.info.title=Brugeroplysninger
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=type
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=offline
luckperms.command.user.info.status-key=Status
luckperms.command.user.info.status.online=Online
luckperms.command.user.info.status.offline=Offline
luckperms.command.user.removegroup.error-primary=Du kan ikke fjerne en bruger fra deres primÃ¦re gruppe
luckperms.command.user.primarygroup.not-member={0} Var ikke et medlem af {1}, tilfÃ¸jer dem nu
luckperms.command.user.primarygroup.already-has={0} har allerede {1} sat som deres primÃ¦re gruppe
luckperms.command.user.primarygroup.warn-option=Advarsel\: Den primÃ¦re gruppeberegningsmetode, der anvendes af denne server ({0}) afspejler muligvis ikke denne Ã¦ndring
luckperms.command.user.primarygroup.set={0}''s primÃ¦re gruppe blev sat til {1}
luckperms.command.user.track.error-not-contain-group={0} er ikke i nogen grupper pÃ¥ {1}
luckperms.command.user.track.unsure-which-track=Usikker pÃ¥, hvilken spor der skal bruges, angiv venligst det som et argument
luckperms.command.user.track.missing-group-advice=Opret enten gruppen eller fjern den fra sporet og prÃ¸v igen
luckperms.command.user.promote.added-to-first={0} er ikke i nogen grupper pÃ¥ {1}, sÃ¥ de blev fÃ¸jet til den fÃ¸rste gruppe, {2} i kontekst {3}
luckperms.command.user.promote.not-on-track={0} er ikke i nogen grupper pÃ¥ {1}, sÃ¥ brugeren blev ikke forfremmet
luckperms.command.user.promote.success=Forfremmer {0} langs sporet {1} fra {2} til {3} i forbindelse {4}
luckperms.command.user.promote.end-of-track=Slutningen af sporet {0} blev nÃ¥et, kunne ikke forfremme {1}
luckperms.command.user.promote.next-group-deleted=Den nÃ¦ste gruppe pÃ¥ sporet, {0}, findes ikke lÃ¦ngere
luckperms.command.user.promote.unable-to-promote=Kan ikke forfremme brugeren
luckperms.command.user.demote.success=Nedrykker {0} langs sporet {1} fra {2} til {3} i kontekst {4}
luckperms.command.user.demote.end-of-track=Slutningen af sporet {0} blev nÃ¥et, sÃ¥ {1} blev fjernet fra {2}
luckperms.command.user.demote.end-of-track-not-removed=Slutningen af sporet {0} blev nÃ¥et, men {1} blev ikke fjernet fra den fÃ¸rste gruppe
luckperms.command.user.demote.previous-group-deleted=Den forrige gruppe pÃ¥ sporet, {0}, findes ikke lÃ¦ngere
luckperms.command.user.demote.unable-to-demote=Kan ikke degradere brugeren
luckperms.command.group.list.title=Grupper
luckperms.command.group.delete.not-default=Du kan ikke slette standardgruppen
luckperms.command.group.info.title=Gruppeinfo
luckperms.command.group.info.display-name-key=Visningsnavn
luckperms.command.group.info.weight-key=VÃ¦gt
luckperms.command.group.setweight.set=SÃ¦t vÃ¦gt til {0} for gruppe {1}
luckperms.command.group.setdisplayname.doesnt-have={0} har ikke et visningsnavn sat
luckperms.command.group.setdisplayname.already-has={0} har allerede et visningsnavn pÃ¥ {1}
luckperms.command.group.setdisplayname.already-in-use=Visningsnavnet {0} bruges allerede af {1}
luckperms.command.group.setdisplayname.set=SÃ¦t visningsnavn til {0} for gruppe {1} i kontekst {2}
luckperms.command.group.setdisplayname.removed=Fjernede visningsnavn for gruppe {0} i kontekst {1}
luckperms.command.track.list.title=Spor
luckperms.command.track.path.empty=Ingen
luckperms.command.track.info.showing-track=Viser Spor
luckperms.command.track.info.path-property=Sti
luckperms.command.track.clear={0}''s gruppespor er blevet ryddet
luckperms.command.track.append.success=Gruppe {0} blev tilfÃ¸jet til spor {1}
luckperms.command.track.insert.success=Gruppe {0} blev indsat i sporet {1} pÃ¥ position {2}
luckperms.command.track.insert.error-number=Forventede nummer, men modtog\: {0}
luckperms.command.track.insert.error-invalid-pos=Kan ikke indsÃ¦tte pÃ¥ position {0}
luckperms.command.track.insert.error-invalid-pos-reason=ugyldig position
luckperms.command.track.remove.success=Gruppen {0} blev fjernet fra sporet {1}
luckperms.command.track.error-empty={0} kan ikke bruges, da det er tomt eller kun indeholder en gruppe
luckperms.command.track.error-multiple-groups={0} er medlem af flere grupper pÃ¥ dette spor
luckperms.command.track.error-ambiguous=Kan ikke afgÃ¸re deres placering
luckperms.command.track.already-contains={0} indeholder allerede {1}
luckperms.command.track.doesnt-contain={0} indeholder ikke {1}
luckperms.command.log.load-error=Loggen kunne ikke indlÃ¦ses
luckperms.command.log.invalid-page=Ugyldigt sidenummer
luckperms.command.log.invalid-page-range=Angiv en vÃ¦rdi mellem {0} og {1}
luckperms.command.log.empty=Ingen log poster at vise
luckperms.command.log.notify.error-console=Kan ikke slÃ¥ notifikationer til for konsollen
luckperms.command.log.notify.enabled-term=SlÃ¥et til
luckperms.command.log.notify.disabled-term=SlÃ¥et fra
luckperms.command.log.notify.changed-state={0} logger udput
luckperms.command.log.notify.already-on=Du modtager allerede notifikationer
luckperms.command.log.notify.already-off=Du modtager ikke notifikationer i Ã¸jeblikket
luckperms.command.log.notify.invalid-state=Ukendt tilstand. Forventer {0} eller {1}
luckperms.command.log.show.search=Viser seneste handlinger for forespÃ¸rgsel {0}
luckperms.command.log.show.recent=Viser seneste handlinger
luckperms.command.log.show.by=Viser seneste handlinger af {0}
luckperms.command.log.show.history=Viser historik for {0} {1}
luckperms.command.export.error-term=Fejl
luckperms.command.export.already-running=En anden eksportproces kÃ¸rer allerede
luckperms.command.export.file.already-exists=Filen {0} findes allerede
luckperms.command.export.file.not-writable=Filen {0} er ikke skrivbar
luckperms.command.export.file.success=Eksporteret succesfuldt til {0}
luckperms.command.export.file-unexpected-error-writing=Der opstod en uventet fejl under skrivning til filen
luckperms.command.export.web.export-code=EksportÃ©r kode
luckperms.command.export.web.import-command-description=Brug fÃ¸lgende kommando til at importere
luckperms.command.import.term=Importer
luckperms.command.import.error-term=Fejl
luckperms.command.import.already-running=En anden importproces kÃ¸rer allerede
luckperms.command.import.file.doesnt-exist=Filen {0} eksisterer ikke
luckperms.command.import.file.not-readable=Filen {0} er ikke lÃ¦sbar
luckperms.command.import.file.unexpected-error-reading=En uventet fejl opstod under lÃ¦sning fra importfilen
luckperms.command.import.file.correct-format=er det, det korrekte format?
luckperms.command.import.web.unable-to-read=Kan ikke lÃ¦se data med den givne kode
luckperms.command.import.progress.percent={0}% fÃ¦rdig
luckperms.command.import.progress.operations={0}/{1} handlinger fuldfÃ¸rt
luckperms.command.import.starting=Starter importeringsprocessen
luckperms.command.import.completed=FULDFÃRT
luckperms.command.import.duration=tog {0} sekunder
luckperms.command.bulkupdate.must-use-console=Masseopdaterings kommandoen kan kun bruges fra konsollen
luckperms.command.bulkupdate.invalid-data-type=Ugyldig type, forventede {0}
luckperms.command.bulkupdate.invalid-constraint=Ugyldig begrÃ¦nsning {0}
luckperms.command.bulkupdate.invalid-constraint-format=Restriktioner skal vÃ¦re i formatet {0}
luckperms.command.bulkupdate.invalid-comparison=Ugyldig sammenligningsudbyder {0}
luckperms.command.bulkupdate.invalid-comparison-format=Forventede en af fÃ¸lgende\: {0}
luckperms.command.bulkupdate.queued=Masseopdateringshandlingen blev sat i kÃ¸
luckperms.command.bulkupdate.confirm=KÃ¸r {0} for at kÃ¸re opdateringen
luckperms.command.bulkupdate.unknown-id=Handlingen med id''et {0} eksisterer ikke eller er udlÃ¸bet
luckperms.command.bulkupdate.starting=KÃ¸rer masseopdatering
luckperms.command.bulkupdate.success=Masseopdatering gennemfÃ¸rt
luckperms.command.bulkupdate.success.statistics.nodes=I alt pÃ¥virkede knudepunkter
luckperms.command.bulkupdate.success.statistics.users=PÃ¥virkede brugere i alt
luckperms.command.bulkupdate.success.statistics.groups=I alt pÃ¥virkede grupper
luckperms.command.bulkupdate.failure=Masseopdatering fejlede, tjek konsollen for fejl
luckperms.command.update-task.request=Der er anmodet om en opdateringsopgave, vent venligst
luckperms.command.update-task.complete=Opdateringsopgave fuldfÃ¸rt
luckperms.command.update-task.push.attempting=ForsÃ¸ger nu at skubbe ud til andre servere
luckperms.command.update-task.push.complete=Andre servere blev underrettet via {0}
luckperms.command.update-task.push.error=Fejl under trykning af Ã¦ndringer til andre servere
luckperms.command.update-task.push.error-not-setup=Kan ikke sende Ã¦ndringer til andre servere, da en besked tjeneste ikke er konfigureret
luckperms.command.reload-config.success=Konfigurationsfilen blev genindlÃ¦st
luckperms.command.reload-config.restart-note=nogle indstillinger vil kun gÃ¦lde efter serveren er genstartet
luckperms.command.translations.searching=SÃ¸ger efter tilgÃ¦ngelige oversÃ¦ttelser, vent venligst...
luckperms.command.translations.searching-error=Kan ikke hente en liste over tilgÃ¦ngelige oversÃ¦ttelser
luckperms.command.translations.installed-translations=Installerede OversÃ¦ttelser
luckperms.command.translations.available-translations=TilgÃ¦ngelige OversÃ¦ttelser
luckperms.command.translations.percent-translated={0}% oversat
luckperms.command.translations.translations-by=af
luckperms.command.translations.installing=Installerer oversÃ¦ttelser, vent venligst...
luckperms.command.translations.download-error=Kunne ikke downloade oversÃ¦ttelse til {0}
luckperms.command.translations.installing-specific=Installerer sprog {0}...
luckperms.command.translations.install-complete=Installation gennemfÃ¸rt
luckperms.command.translations.download-prompt=Brug {0} til at downloade og installere opdaterede versioner af disse oversÃ¦ttelser fra fÃ¦llesskabet
luckperms.command.translations.download-override-warning=BemÃ¦rk, at dette vil overskrive de Ã¦ndringer, du har foretaget for disse sprog
luckperms.usage.user.description=Et sÃ¦t kommandoer til hÃ¥ndtering af brugere i LuckPerms. (En ''bruger'' i LuckPerms er blot en spiller, og kan henvise til et UUID eller brugernavn)
luckperms.usage.group.description=Et sÃ¦t kommandoer til hÃ¥ndtering af grupper i LuckPerms. Grupper er er blot samlinger af tilladelser, der kan gives til brugere. Nye grupper laves ved hjÃ¦lp af ''creategroup''-kommandoen.
luckperms.usage.track.description=Et sÃ¦t kommandoer til styring af spor i LuckPerms. Spor er en ordnet samling af grupper, som kan bruges til at definere forfremmelser og demoteringer.
luckperms.usage.log.description=Et sÃ¦t kommandoer til styring af logning funktionalitet i LuckPerms.
luckperms.usage.sync.description=GenindlÃ¦ser alle data fra plugins lagring i hukommelsen, og anvender eventuelle Ã¦ndringer, der registreres.
luckperms.usage.info.description=Udskriver generel information om den aktive plugin instans.
luckperms.usage.editor.description=Opretter en ny webredigeringssession
luckperms.usage.editor.argument.type=typer der skal indlÃ¦ses i editoren. (''alle'', ''brugere'' eller ''grupper'')
luckperms.usage.editor.argument.filter=tilladelse til at filtrere brugerindgange efter
luckperms.usage.verbose.description=Kontrollerer plugins verbose tilladelse kontrol overvÃ¥gningssystem.
luckperms.usage.verbose.argument.action=om logning skal aktiveres/deaktivere eller uploade logget output
luckperms.usage.verbose.argument.filter=filteret der skal matches poster mod
luckperms.usage.verbose.argument.commandas=spilleren/kommandoen der skal kÃ¸res
luckperms.usage.tree.description=Genererer en trÃ¦visning (sorteret liste hierarki) af alle tilladelser kendt af LuckPerms.
luckperms.usage.tree.argument.scope=roden af trÃ¦et. Angiv "." for at inkludere alle tilladelser
luckperms.usage.tree.argument.player=navnet pÃ¥ en online-spiller, der skal tjekkes mod
luckperms.usage.search.description=SÃ¸ger efter alle bruger/grupper med en bestemt tilladelse
luckperms.usage.search.argument.permission=tilladelsen at sÃ¸ge efter
luckperms.usage.search.argument.page=siden der skal vises
luckperms.usage.network-sync.description=Synkroniser Ã¦ndringer med lageret og anmoder om, at alle andre servere pÃ¥ netvÃ¦rket gÃ¸r det samme
luckperms.usage.import.description=Importdata fra en (tidligere oprettet) eksportfil
luckperms.usage.import.argument.file=filen der skal importeres fra
luckperms.usage.import.argument.replace=erstat eksisterende data i stedet for at fusionere
luckperms.usage.import.argument.upload=upload data fra en tidligere eksport
luckperms.usage.export.description=Eksporterer alle tilladelser data til en ''eksport''-fil. Kan genimporteres pÃ¥ et senere tidspunkt.
luckperms.usage.export.argument.file=filen der skal eksporteres til
luckperms.usage.export.argument.without-users=udelukke brugere fra eksporten
luckperms.usage.export.argument.without-groups=ekskludere grupper fra eksport
luckperms.usage.export.argument.upload=Upload alle tilladelsesdata til webeditoren. Kan genimporteres pÃ¥ et senere tidspunkt.
luckperms.usage.reload-config.description=GenindlÃ¦s nogle af konfigurationsindstillingerne
luckperms.usage.bulk-update.description=KÃ¸r masseÃ¦ndringsforespÃ¸rgsler pÃ¥ alle data
luckperms.usage.bulk-update.argument.data-type=den type data, der skal Ã¦ndres. (''alle'', ''brugere'' eller ''grupper'')
luckperms.usage.bulk-update.argument.action=den handling, der skal udfÃ¸res pÃ¥ dataene. (''update'' eller ''slette'')
luckperms.usage.bulk-update.argument.action-field=feltet der skal ageres pÃ¥, krÃ¦ves kun ved ''opdatering''. (''tilladelse'', ''server'' eller ''verden'')
luckperms.usage.bulk-update.argument.action-value=den vÃ¦rdi der skal erstattes med. krÃ¦ves kun for ''opdatering''.
luckperms.usage.bulk-update.argument.constraint=de nÃ¸dvendige der krÃ¦ves for opdateringen
luckperms.usage.translations.description=Administrer oversÃ¦ttelser
luckperms.usage.translations.argument.install=underkommando til at installere oversÃ¦ttelser
luckperms.usage.apply-edits.description=Anvender tilladelses Ã¦ndringer foretaget fra webeditoren
luckperms.usage.apply-edits.argument.code=den unikke kode til dataen
luckperms.usage.apply-edits.argument.target=hvem dataen pÃ¥virker
luckperms.usage.create-group.description=Opret en ny gruppe
luckperms.usage.create-group.argument.name=navn pÃ¥ gruppe
luckperms.usage.create-group.argument.weight=vÃ¦gten af gruppen
luckperms.usage.create-group.argument.display-name=visningsnavnet for gruppen
luckperms.usage.delete-group.description=Slet gruppe
luckperms.usage.delete-group.argument.name=navnet pÃ¥ gruppen
luckperms.usage.list-groups.description=Vis alle grupper pÃ¥ platformen
luckperms.usage.create-track.description=Opret et nyt spor
luckperms.usage.create-track.argument.name=navnet pÃ¥ sporet
luckperms.usage.delete-track.description=Slet et spor
luckperms.usage.delete-track.argument.name=navnet pÃ¥ sporet
luckperms.usage.list-tracks.description=Liste over alle spor pÃ¥ platformen
luckperms.usage.user-info.description=Viser information om brugeren
luckperms.usage.user-switchprimarygroup.description=Skifter brugerens primÃ¦re gruppe
luckperms.usage.user-switchprimarygroup.argument.group=gruppen der skal skiftes til
luckperms.usage.user-promote.description=Forfremmer brugeren op ad sporet
luckperms.usage.user-promote.argument.track=sporet til at promovere brugeren op
luckperms.usage.user-promote.argument.context=konteksten brugeren skal forfremmes i
luckperms.usage.user-promote.argument.dont-add-to-first=kun promover brugeren, hvis den allerede er pÃ¥ sporet
luckperms.usage.user-demote.description=Nedgraderer brugeren et spor ned
luckperms.usage.user-demote.argument.track=sporet til at degraderer brugeren ned
luckperms.usage.user-demote.argument.context=konteksten brugeren skal degraderes i
luckperms.usage.user-demote.argument.dont-remove-from-first=forhindre brugeren i at blive fjernet fra den fÃ¸rste gruppe
luckperms.usage.user-clone.description=Klon brugeren
luckperms.usage.user-clone.argument.user=navn/uuid pÃ¥ brugeren til at klone pÃ¥
luckperms.usage.group-info.description=Giver info om gruppen
luckperms.usage.group-listmembers.description=Vis brugere/grupper som arver fra denne gruppe
luckperms.usage.group-listmembers.argument.page=siden der skal vises
luckperms.usage.group-setweight.description=Indstil gruppernes vÃ¦gt
luckperms.usage.group-setweight.argument.weight=den vÃ¦gt, der skal angives
luckperms.usage.group-set-display-name.description=Angiv gruppernes visningsnavn
luckperms.usage.group-set-display-name.argument.name=navnet der skal angives
luckperms.usage.group-set-display-name.argument.context=den kontekst navnet skal indsÃ¦ttes i
luckperms.usage.group-rename.description=OmdÃ¸b gruppen
luckperms.usage.group-rename.argument.name=det nye navn
luckperms.usage.group-clone.description=Klon gruppen
luckperms.usage.group-clone.argument.name=navnet pÃ¥ den gruppe, der skal klones pÃ¥
luckperms.usage.holder-editor.description=Ãbner webtilladelseseditoren
luckperms.usage.holder-showtracks.description=Viser en liste over spor som objektet er pÃ¥
luckperms.usage.holder-clear.description=Fjerner alle tilladelser, forÃ¦ldre og meta
luckperms.usage.holder-clear.argument.context=de kontekster, der skal filtreres efter
luckperms.usage.permission.description=Rediger rettigheder
luckperms.usage.parent.description=Rediger arv
luckperms.usage.meta.description=Rediger metadata vÃ¦rdier
luckperms.usage.permission-info.description=Viser en liste over tilladelser objektet har
luckperms.usage.permission-info.argument.page=siden der skal vises
luckperms.usage.permission-info.argument.sort-mode=hvordan man sorterer posterne
luckperms.usage.permission-set.description=Indstiller en tilladelse for objektet
luckperms.usage.permission-set.argument.node=den tilladelsesnÃ¸gle der skal indstilles
luckperms.usage.permission-set.argument.value=vÃ¦rdien af noden
luckperms.usage.permission-set.argument.context=konteksten tilladelsen skal indstilles i
luckperms.usage.permission-unset.description=Fjerner en tilladelse fra objektet
luckperms.usage.permission-unset.argument.node=den tilladelsesnÃ¸gle der skal fjernes
luckperms.usage.permission-unset.argument.context=konteksterne til at fjerne tilladelsen i
luckperms.usage.permission-settemp.description=Indstiller en tilladelse for objektet midlertidigt
luckperms.usage.permission-settemp.argument.node=den tilladelsesnÃ¸gle der skal indstilles
luckperms.usage.permission-settemp.argument.value=vÃ¦rdien af noden
luckperms.usage.permission-settemp.argument.duration=varigheden indtil tilladelsesnoden udlÃ¸ber
luckperms.usage.permission-settemp.argument.temporary-modifier=hvordan den midlertidige tilladelse bÃ¸r anvendes
luckperms.usage.permission-settemp.argument.context=konteksten tilladelsen skal indstilles i
luckperms.usage.permission-unsettemp.description=Fjern midlertidig tilladelse fra objektet
luckperms.usage.permission-unsettemp.argument.node=den tilladelsesnÃ¸gle der skal fjernes
luckperms.usage.permission-unsettemp.argument.duration=varigheden der skal trÃ¦kkes fra
luckperms.usage.permission-unsettemp.argument.context=konteksten tilladelsen skal fjernes fra
luckperms.usage.permission-check.description=Kontrollerer om objektet har en bestemt tilladelse
luckperms.usage.permission-check.argument.node=tilladelsen der skal tjekkes efter
luckperms.usage.permission-clear.description=Ryd alle tilladelser
luckperms.usage.permission-clear.argument.context=de kontekster, der skal filtreres efter
luckperms.usage.parent-info.description=Viser en liste over grupper som dette objekt arver fra
luckperms.usage.parent-info.argument.page=siden der skal vises
luckperms.usage.parent-info.argument.sort-mode=hvordan man sorterer posterne
luckperms.usage.parent-set.description=Fjerner alle andre grupper objektet der allerede arves og tilfÃ¸jer dem til den givne
luckperms.usage.parent-set.argument.group=gruppen der skal sÃ¦ttes
luckperms.usage.parent-set.argument.context=konteksten gruppen skal sÃ¦ttes i
luckperms.usage.parent-add.description=SÃ¦tter en anden gruppe for objektet til at arve tilladelser fra
luckperms.usage.parent-add.argument.group=den gruppe, der skal arves fra
luckperms.usage.parent-add.argument.context=konteksten gruppen skal arves i
luckperms.usage.parent-remove.description=Fjerner en tidligere indstillet arve regel
luckperms.usage.parent-remove.argument.group=gruppen der skal fjernes
luckperms.usage.parent-remove.argument.context=konteksten gruppen skal fjernes fra
luckperms.usage.parent-set-track.description=Fjerner alle andre grupper objektet allerede arver fra og tilfÃ¸jer dem til den givne
luckperms.usage.parent-set-track.argument.track=sporet der skal indsÃ¦ttes pÃ¥
luckperms.usage.parent-set-track.argument.group=den gruppe, der skal angives, eller et nummer, der vedrÃ¸rer gruppens position pÃ¥ det givne spor
luckperms.usage.parent-set-track.argument.context=konteksten gruppen skal sÃ¦ttes i
luckperms.usage.parent-add-temp.description=SÃ¦tter en anden gruppe for objektet til at arve tilladelser fra midlertidigt
luckperms.usage.parent-add-temp.argument.group=den gruppe, der skal arves fra
luckperms.usage.parent-add-temp.argument.duration=varigheden af gruppemedlemskabet
luckperms.usage.parent-add-temp.argument.temporary-modifier=hvordan den midlertidige tilladelse bÃ¸r anvendes
luckperms.usage.parent-add-temp.argument.context=konteksten gruppen skal sÃ¦ttes i
luckperms.usage.parent-remove-temp.description=Fjerner en tidligere indstillet midlertidig arv regel
luckperms.usage.parent-remove-temp.argument.group=gruppen der skal fjernes
luckperms.usage.parent-remove-temp.argument.duration=varigheden der skal trÃ¦kkes fra
luckperms.usage.parent-remove-temp.argument.context=konteksten gruppen skal fjernes fra
luckperms.usage.parent-clear.description=Rydder alle forÃ¦ldre
luckperms.usage.parent-clear.argument.context=de kontekster, der skal filtreres efter
luckperms.usage.parent-clear-track.description=Rydder alle forÃ¦ldre pÃ¥ et givet spor
luckperms.usage.parent-clear-track.argument.track=sporet der skal fjernes pÃ¥
luckperms.usage.parent-clear-track.argument.context=de kontekster, der skal filtreres efter
luckperms.usage.meta-info.description=Viser alle chat meta
luckperms.usage.meta-set.description=Indstiller en metavÃ¦rdi
luckperms.usage.meta-set.argument.key=nÃ¸glen der skal angives
luckperms.usage.meta-set.argument.value=vÃ¦rdien der skal angives
luckperms.usage.meta-set.argument.context=de kontekster, der skal tilfÃ¸jes metapar i
luckperms.usage.meta-unset.description=Fjern en metavÃ¦rdi
luckperms.usage.meta-unset.argument.key=nÃ¸glen der skal fravÃ¦lges
luckperms.usage.meta-unset.argument.context=de kontekster, der skal fjernes metapar i
luckperms.usage.meta-settemp.description=Indstiller en metavÃ¦rdi midlertidigt
luckperms.usage.meta-settemp.argument.key=nÃ¸glen der skal angives
luckperms.usage.meta-settemp.argument.value=vÃ¦rdien der skal angives
luckperms.usage.meta-settemp.argument.duration=varigheden indtil metavÃ¦rdien udlÃ¸ber
luckperms.usage.meta-settemp.argument.context=de kontekster, der skal tilfÃ¸jes metapar i
luckperms.usage.meta-unsettemp.description=Fjern en midlertidig metavÃ¦rdi
luckperms.usage.meta-unsettemp.argument.key=nÃ¸glen der skal fravÃ¦lges
luckperms.usage.meta-unsettemp.argument.context=de kontekster, der skal fjernes metapar i
luckperms.usage.meta-addprefix.description=TilfÃ¸jer et prÃ¦fiks
luckperms.usage.meta-addprefix.argument.priority=den prioritet prÃ¦fikset skal sÃ¦ttes ved
luckperms.usage.meta-addprefix.argument.prefix=prÃ¦fiks strengen
luckperms.usage.meta-addprefix.argument.context=den prioritet prÃ¦fikset skal sÃ¦ttes ved
luckperms.usage.meta-addsuffix.description=TilfÃ¸jer et suffiks
luckperms.usage.meta-addsuffix.argument.priority=den prioritet prÃ¦fikset skal sÃ¦ttes ved
luckperms.usage.meta-addsuffix.argument.suffix=suffiks strengen
luckperms.usage.meta-addsuffix.argument.context=konteksten suffixet skal sÃ¦ttes i
luckperms.usage.meta-setprefix.description=SÃ¦tter et prÃ¦fiks
luckperms.usage.meta-setprefix.argument.priority=den prioritet prÃ¦fikset skal sÃ¦ttes ved
luckperms.usage.meta-setprefix.argument.prefix=prÃ¦fiks strengen
luckperms.usage.meta-setprefix.argument.context=konteksten prÃ¦fikset skal sÃ¦ttes i
luckperms.usage.meta-setsuffix.description=SÃ¦tter et suffiks
luckperms.usage.meta-setsuffix.argument.priority=den prioritet prÃ¦fikset skal sÃ¦ttes ved
luckperms.usage.meta-setsuffix.argument.suffix=suffiks strengen
luckperms.usage.meta-setsuffix.argument.context=konteksten suffixet skal sÃ¦ttes i
luckperms.usage.meta-removeprefix.description=Fjerner et prÃ¦fiks
luckperms.usage.meta-removeprefix.argument.priority=den prioritet prÃ¦fikset skal fjernes ved
luckperms.usage.meta-removeprefix.argument.prefix=prÃ¦fiks strengen
luckperms.usage.meta-removeprefix.argument.context=konteksten prÃ¦fikset skal fjernes ved
luckperms.usage.meta-removesuffix.description=Fjerner et suffiks
luckperms.usage.meta-removesuffix.argument.priority=den prioritet prÃ¦fikset skal fjernes ved
luckperms.usage.meta-removesuffix.argument.suffix=suffiks strengen
luckperms.usage.meta-removesuffix.argument.context=konteksterne at fjerne suffikset i
luckperms.usage.meta-addtemp-prefix.description=TilfÃ¸jer et prÃ¦fiks midlertidigt
luckperms.usage.meta-addtemp-prefix.argument.priority=den prioritet prÃ¦fikset skal sÃ¦ttes ved
luckperms.usage.meta-addtemp-prefix.argument.prefix=prÃ¦fiks strengen
luckperms.usage.meta-addtemp-prefix.argument.duration=varigheden indtil prÃ¦fikset udlÃ¸ber
luckperms.usage.meta-addtemp-prefix.argument.context=konteksten prÃ¦fikset skal sÃ¦ttes i
luckperms.usage.meta-addtemp-suffix.description=TilfÃ¸jer et suffiks midlertidigt
luckperms.usage.meta-addtemp-suffix.argument.priority=den prioritet suffiks skal sÃ¦ttes ved
luckperms.usage.meta-addtemp-suffix.argument.suffix=suffiks strengen
luckperms.usage.meta-addtemp-suffix.argument.duration=varigheden indtil suffiks udlÃ¸ber
luckperms.usage.meta-addtemp-suffix.argument.context=konteksten suffiks skal sÃ¦ttes i
luckperms.usage.meta-settemp-prefix.description=TilfÃ¸jer et prÃ¦fiks midlertidigt
luckperms.usage.meta-settemp-prefix.argument.priority=den prioritet prÃ¦fikset skal sÃ¦ttes ved
luckperms.usage.meta-settemp-prefix.argument.prefix=prÃ¦fiks strengen
luckperms.usage.meta-settemp-prefix.argument.duration=varigheden indtil prÃ¦fikset udlÃ¸ber
luckperms.usage.meta-settemp-prefix.argument.context=konteksten prÃ¦fikset skal sÃ¦ttes i
luckperms.usage.meta-settemp-suffix.description=TilfÃ¸jer et suffiks midlertidigt
luckperms.usage.meta-settemp-suffix.argument.priority=den prioritet suffikset skal sÃ¦ttes ved
luckperms.usage.meta-settemp-suffix.argument.suffix=suffiks strengen
luckperms.usage.meta-settemp-suffix.argument.duration=varigheden indtil suffiket udlÃ¸ber
luckperms.usage.meta-settemp-suffix.argument.context=konteksten suffikset skal sÃ¦ttes i
luckperms.usage.meta-removetemp-prefix.description=Fjerner et midlertidigt prÃ¦fiks
luckperms.usage.meta-removetemp-prefix.argument.priority=den prioritet prÃ¦fikset skal fjernes ved
luckperms.usage.meta-removetemp-prefix.argument.prefix=prÃ¦fiks strengen
luckperms.usage.meta-removetemp-prefix.argument.context=konteksten prÃ¦fikset skal fjernes ved
luckperms.usage.meta-removetemp-suffix.description=Fjerner et midlertidigt suffiks
luckperms.usage.meta-removetemp-suffix.argument.priority=den prioritet suffikset skal fjernes ved
luckperms.usage.meta-removetemp-suffix.argument.suffix=suffiks strengen
luckperms.usage.meta-removetemp-suffix.argument.context=konteksterne suffikset skal fjernes ved
luckperms.usage.meta-clear.description=Rydder alle meta data
luckperms.usage.meta-clear.argument.type=den metatype der skal fjernes
luckperms.usage.meta-clear.argument.context=de kontekster, der skal filtreres efter
luckperms.usage.track-info.description=Giver info om sporet
luckperms.usage.track-editor.description=Ãbner web-editoren for tilladelser
luckperms.usage.track-append.description=TilfÃ¸jer en gruppe til slutningen af sporet
luckperms.usage.track-append.argument.group=gruppen som skal tilfÃ¸jes
luckperms.usage.track-insert.description=IndsÃ¦tter en gruppe pÃ¥ en given position langs sporet
luckperms.usage.track-insert.argument.group=den gruppe der skal indsÃ¦ttes
luckperms.usage.track-insert.argument.position=positionen til at indsÃ¦tte gruppen i (den fÃ¸rste position pÃ¥ sporet er 1)
luckperms.usage.track-remove.description=Fjerner en gruppe fra sporet
luckperms.usage.track-remove.argument.group=gruppen der skal fjernes
luckperms.usage.track-clear.description=Fjerner grupperne pÃ¥ sporet
luckperms.usage.track-rename.description=OmdÃ¸b sporet
luckperms.usage.track-rename.argument.name=det nye navn
luckperms.usage.track-clone.description=Klon sporet
luckperms.usage.track-clone.argument.name=navnet pÃ¥ det spor, der skal klones pÃ¥
luckperms.usage.log-recent.description=Vis seneste handlinger
luckperms.usage.log-recent.argument.user=navn/uuid pÃ¥ brugeren der skal sÃ¸ges efter
luckperms.usage.log-recent.argument.page=sidenummeret der skal vises
luckperms.usage.log-search.description=SÃ¸g i loggen efter en post
luckperms.usage.log-search.argument.query=forespÃ¸rgslen der skal sÃ¸ges efter
luckperms.usage.log-search.argument.page=sidenummeret der skal vises
luckperms.usage.log-notify.description=SlÃ¥ log notifikationer til/fra
luckperms.usage.log-notify.argument.toggle=om den skal slÃ¥s til eller fra
luckperms.usage.log-user-history.description=Se en brugers historik
luckperms.usage.log-user-history.argument.user=navn/uuid pÃ¥ brugeren
luckperms.usage.log-user-history.argument.page=sidenummeret der skal vises
luckperms.usage.log-group-history.description=Vis en gruppes historik
luckperms.usage.log-group-history.argument.group=navnet pÃ¥ gruppen
luckperms.usage.log-group-history.argument.page=sidenummeret der skal vises
luckperms.usage.log-track-history.description=Se et spors historik
luckperms.usage.log-track-history.argument.track=navnet pÃ¥ sporet
luckperms.usage.log-track-history.argument.page=sidenummeret der skal vises
luckperms.usage.sponge.description=Rediger ekstra Sponge data
luckperms.usage.sponge.argument.collection=den samling der skal sÃ¸ges pÃ¥
luckperms.usage.sponge.argument.subject=det emne der skal Ã¦ndres
luckperms.usage.sponge-permission-info.description=Viser info om emnets tilladelser
luckperms.usage.sponge-permission-info.argument.contexts=de kontekster, der skal filtreres efter
luckperms.usage.sponge-permission-set.description=Indstiller en tilladelse for objektet
luckperms.usage.sponge-permission-set.argument.node=den tilladelsesnÃ¸gle der skal indstilles
luckperms.usage.sponge-permission-set.argument.tristate=vÃ¦rdien som tilladelsen skal sÃ¦ttes til
luckperms.usage.sponge-permission-set.argument.contexts=konteksten til at indstille indstillingen i
luckperms.usage.sponge-permission-clear.description=Rydder emneindstillingerne
luckperms.usage.sponge-permission-clear.argument.contexts=konteksten der skal ryddes for rettigheder
luckperms.usage.sponge-parent-info.description=Viser info om emnets forÃ¦ldre
luckperms.usage.sponge-parent-info.argument.contexts=de kontekster, der skal filtreres efter
luckperms.usage.sponge-parent-add.description=TilfÃ¸jer en forÃ¦lder til emnet
luckperms.usage.sponge-parent-add.argument.collection=samlingen hvor forÃ¦ldre emnerne er
luckperms.usage.sponge-parent-add.argument.subject=navnet pÃ¥ det overordnede emne
luckperms.usage.sponge-parent-add.argument.contexts=de kontekster, der skal tilfÃ¸jes forÃ¦lder i
luckperms.usage.sponge-parent-remove.description=Fjerner en forÃ¦lder fra emnet
luckperms.usage.sponge-parent-remove.argument.collection=samlingen hvor forÃ¦ldre emnerne er
luckperms.usage.sponge-parent-remove.argument.subject=navnet pÃ¥ det overordnede emne
luckperms.usage.sponge-parent-remove.argument.contexts=de kontekster, der skal fjernes forÃ¦lder fra
luckperms.usage.sponge-parent-clear.description=Rydder emneforÃ¦ldre
luckperms.usage.sponge-parent-clear.argument.contexts=de kontekster der skal ryddes forÃ¦ldre i
luckperms.usage.sponge-option-info.description=Viser info om emnets indstillinger
luckperms.usage.sponge-option-info.argument.contexts=de kontekster der skal filtreres efter
luckperms.usage.sponge-option-set.description=Indstiller en mulighed for emnet
luckperms.usage.sponge-option-set.argument.key=nÃ¸glen der skal angives
luckperms.usage.sponge-option-set.argument.value=vÃ¦rdien som nÃ¸glen skal sÃ¦ttes til
luckperms.usage.sponge-option-set.argument.contexts=konteksterne til at indstille indstillingen i
luckperms.usage.sponge-option-unset.description=Fjerner end instilling for emnet
luckperms.usage.sponge-option-unset.argument.key=nÃ¸glen der skal fravÃ¦lges
luckperms.usage.sponge-option-unset.argument.contexts=den kontekst nÃ¸glen skal fjernes fra
luckperms.usage.sponge-option-clear.description=Rydder emneindstillingerne
luckperms.usage.sponge-option-clear.argument.contexts=konteksten som valgmuligheden skal fjernes fra
