luckperms.logs.actionlog-prefix=ã­ã°
luckperms.logs.verbose-prefix=æ¨©éè©³ç´°
luckperms.logs.export-prefix=æ¨©éåºå
luckperms.commandsystem.available-commands=å©ç¨å¯è½ãªã³ãã³ããè¡¨ç¤ºããã«ã¯ {0} ãä½¿ç¨ãã¦ãã ãã
luckperms.commandsystem.command-not-recognised=ã³ãã³ããèªè­ããã¾ãã
luckperms.commandsystem.no-permission=ãã®ã³ãã³ããå®è¡ããæ¨©éãããã¾ãã\!
luckperms.commandsystem.no-permission-subcommands=ãµãã³ãã³ããå®è¡ããæ¨©éãããã¾ãã
luckperms.commandsystem.already-executing-command=å¥ã®ã³ãã³ããå®è¡ããã¦ãã¾ããå®äºãå¾ã£ã¦ãã¾ã...
luckperms.commandsystem.usage.sub-commands-header=ãµãã³ãã³ã
luckperms.commandsystem.usage.usage-header=ã³ãã³ãã®ä½¿ç¨æ³
luckperms.commandsystem.usage.arguments-header=å¼æ°
luckperms.first-time.no-permissions-setup=ã¾ã æ¨©éãè¨­å®ããã¦ãã¾ãã\!
luckperms.first-time.use-console-to-give-access=ã²ã¼ã åã§ LuckPerms ã³ãã³ããä½¿ç¨ããåã«ãã³ã³ã½ã¼ã«ãä½¿ç¨ãã¦èªåèªèº«ã«ã¢ã¯ã»ã¹ãè¨±å¯ããå¿è¦ãããã¾ã
luckperms.first-time.console-command-prompt=ã³ã³ã½ã¼ã«ãéãã¦å®è¡
luckperms.first-time.next-step=ãããå®è¡ãããã¨ãæ¨©éã®å²ãå½ã¦ãã°ã«ã¼ããå®ç¾©ã§ãã¾ã
luckperms.first-time.wiki-prompt=ã©ãããå§ããã°ããããããã¾ããã? ãã¡ãããè¦§ãã ãã\: {0}
luckperms.login.try-again=æéãç½®ãã¦ããä¸åº¦å®è¡ãã¦ãã ãã
luckperms.login.loading-database-error=æ¨©éãã¼ã¿ã®èª­ã¿è¾¼ã¿ä¸­ã«ãã¼ã¿ãã¼ã¹ã¨ã©ã¼ãçºçãã¾ãã
luckperms.login.server-admin-check-console-errors=ãµã¼ãã¼ç®¡çèã®å ´åã¯ãã³ã³ã½ã¼ã«ã§ã¨ã©ã¼ãç¢ºèªãã¦ãã ãã
luckperms.login.server-admin-check-console-info=è©³ç´°ã¯ãµã¼ãã¼ã³ã³ã½ã¼ã«ãç¢ºèªãã¦ãã ãã
luckperms.login.data-not-loaded-at-pre=ãã¬-ã­ã°ã¤ã³ã®æ®µéã§ã¯ããªãã®æ¨©éãã¼ã¿ãèª­ã¿è¾¼ã¾ãã¾ããã§ãã
luckperms.login.unable-to-continue=ç¶è¡ã§ãã¾ãã
luckperms.login.craftbukkit-offline-mode-error=CraftBukkit ã¨ online-mode ã®è¨­å®ã§ç«¶åãã¦ããå¯è½æ§ãããã¾ã
luckperms.login.unexpected-error=æ¨©éãã¼ã¿ã®è¨­å®ä¸­ã«äºæããªãã¨ã©ã¼ãçºçãã¾ãã
luckperms.opsystem.disabled=ãã®ãµã¼ãã¼ã§ã¯ããã©ã® OP ã·ã¹ãã ã¯ç¡å¹åããã¦ãã¾ã
luckperms.opsystem.sponge-warning=æ¨©éãã©ã°ã¤ã³ãå°å¥ããã¦ããå ´åãServer Operator ã®ç¶æã¯ Sponge ã®æ¨©éãã§ãã¯ã«å½±é¿ããªãã®ã§æ³¨æãã¦ãã ãããç´æ¥ã¦ã¼ã¶ã¼ãã¼ã¿ãç·¨éããå¿è¦ãããã¾ã
luckperms.duration.unit.years.plural={0} å¹´
luckperms.duration.unit.years.singular={0} å¹´
luckperms.duration.unit.years.short={0}å¹´
luckperms.duration.unit.months.plural={0} ã¶æ
luckperms.duration.unit.months.singular={0} ã¶æ
luckperms.duration.unit.months.short={0}ã¶æ
luckperms.duration.unit.weeks.plural={0} é±
luckperms.duration.unit.weeks.singular={0} é±
luckperms.duration.unit.weeks.short={0}é±
luckperms.duration.unit.days.plural={0} æ¥
luckperms.duration.unit.days.singular={0} æ¥
luckperms.duration.unit.days.short={0}æ¥
luckperms.duration.unit.hours.plural={0} æé
luckperms.duration.unit.hours.singular={0} æé
luckperms.duration.unit.hours.short={0}æé
luckperms.duration.unit.minutes.plural={0} å
luckperms.duration.unit.minutes.singular={0} å
luckperms.duration.unit.minutes.short={0}å
luckperms.duration.unit.seconds.plural={0} ç§
luckperms.duration.unit.seconds.singular={0} ç§
luckperms.duration.unit.seconds.short={0}ç§
luckperms.duration.since={0} å
luckperms.command.misc.invalid-code=ç¡å¹ãªã³ã¼ã
luckperms.command.misc.response-code-key=ã¬ã¹ãã³ã¹ã»ã³ã¼ã
luckperms.command.misc.error-message-key=ã¡ãã»ã¼ã¸
luckperms.command.misc.bytebin-unable-to-communicate=bytebin ã¨éä¿¡ã§ãã¾ãã
luckperms.command.misc.webapp-unable-to-communicate=ã¦ã§ãã¢ããªã¨éä¿¡ã§ãã¾ãã
luckperms.command.misc.check-console-for-errors=ã³ã³ã½ã¼ã«ã§ã¨ã©ã¼ãç¢ºèªãã¦ãã ãã
luckperms.command.misc.file-must-be-in-data=ãã¡ã¤ã« {0} ã¯ãã¼ã¿ãã£ã¬ã¯ããªã®ç´ä¸ã«ããå¿è¦ãããã¾ã
luckperms.command.misc.wait-to-finish=å®äºããã¾ã§å¾ã£ã¦ããããä¸åº¦ããç´ãã¦ãã ãã
luckperms.command.misc.invalid-priority=ç¡å¹ãªåªååº¦\: {0}
luckperms.command.misc.expected-number=æå¾ãããæ°å¤
luckperms.command.misc.date-parse-error=æ¥æ {0} ãè§£æã§ãã¾ãã
luckperms.command.misc.date-in-past-error=éå»ã®æ¥æã¯æå®ã§ãã¾ãã\!
luckperms.command.misc.page={0} / {1} ãã¼ã¸
luckperms.command.misc.page-entries={0} åã®é ç®
luckperms.command.misc.none=ãªã
luckperms.command.misc.loading.error.unexpected=äºæããªãã¨ã©ã¼ãçºçãã¾ãã
luckperms.command.misc.loading.error.user=ã¦ã¼ã¶ã¼ãèª­ã¿è¾¼ã¾ãã¦ãã¾ãã
luckperms.command.misc.loading.error.user-specific=æå®ããã¦ã¼ã¶ã¼ {0} ãèª­ã¿è¾¼ãã¾ãã
luckperms.command.misc.loading.error.user-not-found=ã¦ã¼ã¶ã¼ {0} ã¯è¦ã¤ããã¾ããã§ãã
luckperms.command.misc.loading.error.user-save-error=ã¦ã¼ã¶ã¼ {0} ã®ãã¼ã¿ä¿å­ä¸­ã«ã¨ã©ã¼ãçºçãã¾ãã
luckperms.command.misc.loading.error.user-not-online=ã¦ã¼ã¶ã¼ {0} ã¯ãªãã©ã¤ã³ã§ã
luckperms.command.misc.loading.error.user-invalid={0} ã¯æå¹ãªã¦ã¼ã¶ã¼åã¾ãã¯ UUID ã§ã¯ããã¾ãã
luckperms.command.misc.loading.error.user-not-uuid=æå®ããã¦ã¼ã¶ã¼ {0} ã¯æå¹ãª UUID ã§ã¯ããã¾ãã
luckperms.command.misc.loading.error.group=ã°ã«ã¼ããèª­ã¿è¾¼ã¾ãã¦ãã¾ãã
luckperms.command.misc.loading.error.all-groups=ãã¹ã¦ã®ã°ã«ã¼ããèª­ã¿è¾¼ããã¨ãã§ãã¾ããã§ãã
luckperms.command.misc.loading.error.group-not-found={0} ã¨ããã°ã«ã¼ãã¯è¦ã¤ããã¾ããã§ãã
luckperms.command.misc.loading.error.group-save-error=ã°ã«ã¼ã {0} ã®ãã¼ã¿ä¿å­ä¸­ã«ã¨ã©ã¼ãçºçãã¾ãã
luckperms.command.misc.loading.error.group-invalid={0} ã¯ä¸æ­£ãªã°ã«ã¼ãåã§ã
luckperms.command.misc.loading.error.track=ãã©ãã¯ãèª­ã¿è¾¼ã¾ãã¦ãã¾ãã
luckperms.command.misc.loading.error.all-tracks=ãã¹ã¦ã®ãã©ãã¯ãèª­ã¿è¾¼ããã¨ãã§ãã¾ããã§ãã
luckperms.command.misc.loading.error.track-not-found={0} ã¨ãããã©ãã¯ã¯è¦ã¤ããã¾ããã§ãã
luckperms.command.misc.loading.error.track-save-error=ãã©ãã¯ {0} ã®ãã¼ã¿ä¿å­ä¸­ã«ã¨ã©ã¼ãçºçãã¾ãã
luckperms.command.misc.loading.error.track-invalid={0} ã¯ä¸æ­£ãªãã©ãã¯åã§ã
luckperms.command.editor.no-match=ã¨ãã£ã¿ã¼ãéããã¨ãã§ãã¾ãããç®çã®ã¿ã¤ãã«ä¸è´ãããªãã¸ã§ã¯ããããã¾ãã
luckperms.command.editor.start=æ°ããã¨ãã£ã¿ã»ãã·ã§ã³ãæºåãã¦ãã¾ãããå¾ã¡ãã ãã...
luckperms.command.editor.url=ä¸ã®ãªã³ã¯ãã¯ãªãã¯ãã¦ã¨ãã£ã¿ãéãã¾ã
luckperms.command.editor.unable-to-communicate=ã¨ãã£ã¿ã¨éä¿¡ã§ãã¾ãã
luckperms.command.editor.apply-edits.success=ã¦ã§ãã¨ãã£ã¿ã¼ã®ãã¼ã¿ã {0} {1} ã«æ­£å¸¸ã«é©ç¨ããã¾ãã
luckperms.command.editor.apply-edits.success-summary={0} {1} ã¨ {2} {3}
luckperms.command.editor.apply-edits.success.additions=è¿½å 
luckperms.command.editor.apply-edits.success.additions-singular=è¿½å 
luckperms.command.editor.apply-edits.success.deletions=åé¤
luckperms.command.editor.apply-edits.success.deletions-singular=åé¤
luckperms.command.editor.apply-edits.no-changes=ã¦ã§ãã¨ãã£ã¿ã®ãã¼ã¿ãç·¨éããã¦ããªãã£ããããä½ãå¤æ´ããã¾ããã§ãã
luckperms.command.editor.apply-edits.unknown-type=æå®ããããªãã¸ã§ã¯ãã¿ã¤ãã«å¤æ´ãé©ç¨ã§ãã¾ãã
luckperms.command.editor.apply-edits.unable-to-read=æå®ãããã³ã¼ããä½¿ç¨ãã¦ãã¼ã¿ãèª­ã¿åããã¨ã¯ã§ãã¾ããã§ãã
luckperms.command.search.searching.permission={0} ã®æ¨©éãæã¤ã¦ã¼ã¶ã¼ã¾ãã¯ã°ã«ã¼ããæ¤ç´¢ä¸­
luckperms.command.search.searching.inherit=ã°ã«ã¼ã {0} ãç¶æ¿ããã¦ã¼ã¶ã¼ã¾ãã¯ã°ã«ã¼ããæ¤ç´¢ä¸­
luckperms.command.search.result={0} äººã®ã¦ã¼ã¶ã¼ã¨ {1} ã°ã«ã¼ããã {2} ä»¶ã®é ç®ãè¦ã¤ããã¾ãã
luckperms.command.search.result.default-notice=æ³¨æ\: ããã©ã«ãã°ã«ã¼ãã®ã¡ã³ãã¼ãæ¤ç´¢ããå ´åãä»ã®æ¨©éãæããªããªãã©ã¤ã³ãã¬ã¤ã¤ã¼ã¯è¡¨ç¤ºããã¾ãã\!
luckperms.command.search.showing-users=ã¦ã¼ã¶ã¼ã®ã¿è¡¨ç¤º
luckperms.command.search.showing-groups=ã°ã«ã¼ãã®ã¿è¡¨ç¤º
luckperms.command.tree.start=æ¨©éã®ããªã¼æ§é ãçæä¸­ã§ãããã°ãããå¾ã¡ãã ãã...
luckperms.command.tree.empty=æ¨©éã®ããªã¼æ§é ã®è¦ç´ ãå­å¨ããªããããããªã¼æ§é ãçæã§ãã¾ããã§ãã
luckperms.command.tree.url=æ¨©éã®ããªã¼æ§é ã®URL
luckperms.command.verbose.invalid-filter={0} ã¯æ¨©éã®è©³ç´°ãè¡¨ç¤ºããããã®æå¹ãªãã£ã«ã¿ã¼ã§ã¯ããã¾ãã
luckperms.command.verbose.enabled={1} ã«ä¸è´ããè©³ç´°ãªã­ã®ã³ã°ã {0} ãã¾ãã
luckperms.command.verbose.command-exec=ã³ãã³ã {1} ã {0} ã«å¼·å¶å®è¡ãããã¹ã¦ã®ãã§ãã¯ãã¬ãã¼ããã¾ã....
luckperms.command.verbose.off=è©³ç´°ãªã­ã®ã³ã°ã {0} ãã¾ãã
luckperms.command.verbose.command-exec-complete=ã³ãã³ãã®å®è¡ãå®äºãã¾ãã
luckperms.command.verbose.command.no-checks=ã³ãã³ãã®å®è¡ã¯å®äºãã¾ããããæ¨©éãã§ãã¯ã¯è¡ããã¾ããã§ãã
luckperms.command.verbose.command.possibly-async=ããã¯ãã©ã°ã¤ã³ãããã¯ã°ã©ã¦ã³ã (éåæ) ã§ã³ãã³ããå®è¡ãã¦ããããããããã¾ãã
luckperms.command.verbose.command.try-again-manually=ãã®ããã«è¡ããããã§ãã¯ãæåã§æ¤åºããããã« verbose ãä½¿ç¨ã§ãã¾ã
luckperms.command.verbose.enabled-recording={1} ã«ä¸è´ããè©³ç´°ãªè¨é²ã {0} ãã¾ãã
luckperms.command.verbose.uploading=è©³ç´°ãªã­ã®ã³ã°ã {0} ãã¾ãããçµæãã¢ããã­ã¼ããã¦ãã¾ã...
luckperms.command.verbose.url=Verbose ã®çµæã® URL
luckperms.command.verbose.enabled-term=æå¹å
luckperms.command.verbose.disabled-term=ç¡å¹å
luckperms.command.verbose.query-any=ä»»æ
luckperms.command.info.running-plugin=å®è¡ä¸­
luckperms.command.info.platform-key=ãã©ãããã©ã¼ã 
luckperms.command.info.server-brand-key=ãµã¼ãã¼ãã©ã³ã
luckperms.command.info.server-version-key=ãµã¼ãã¼ãã¼ã¸ã§ã³
luckperms.command.info.storage-key=ã¹ãã¬ã¼ã¸
luckperms.command.info.storage-type-key=ã¿ã¤ã
luckperms.command.info.storage.meta.split-types-key=ã¿ã¤ã
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=æ¥ç¶æ¸ã¿
luckperms.command.info.storage.meta.file-size-key=ãã¡ã¤ã«ãµã¤ãº
luckperms.command.info.extensions-key=æ¡å¼µæ©è½
luckperms.command.info.messaging-key=ã¡ãã»ã¼ã¸ã³ã°
luckperms.command.info.instance-key=ã¤ã³ã¹ã¿ã³ã¹
luckperms.command.info.static-contexts-key=éçã³ã³ãã­ã¹ã
luckperms.command.info.online-players-key=æ¥ç¶ä¸­ã®ãã¬ã¤ã¤ã¼
luckperms.command.info.online-players-unique=ç´¯è¨æ¥ç¶æ° {0}
luckperms.command.info.uptime-key=ç¨¼åæé
luckperms.command.info.local-data-key=ã­ã¼ã«ã«ãã¼ã¿
luckperms.command.info.local-data={0} ã¦ã¼ã¶ã¼, {1} ã°ã«ã¼ã, {2} ãã©ãã¯
luckperms.command.generic.create.success={0} ã¯æ­£å¸¸ã«ä½æããã¾ãã
luckperms.command.generic.create.error={0} ã®ä½æä¸­ã«ã¨ã©ã¼ãçºçãã¾ãã
luckperms.command.generic.create.error-already-exists={0} ã¯ãã§ã«å­å¨ãã¦ãã¾ã\!
luckperms.command.generic.delete.success={0} ã¯æ­£å¸¸ã«åé¤ããã¾ãã
luckperms.command.generic.delete.error={0} ã®åé¤ä¸­ã«ã¨ã©ã¼ãçºçãã¾ãã
luckperms.command.generic.delete.error-doesnt-exist={0} ã¯å­å¨ãã¦ãã¾ãã\!
luckperms.command.generic.rename.success={0} ã¯ {1} ã«æ­£å¸¸ã«åç§°å¤æ´ããã¾ãã
luckperms.command.generic.clone.success={0} ã¯ {1} ã«æ­£å¸¸ã«è¤è£½ããã¾ãã
luckperms.command.generic.info.parent.title=è¦ªã°ã«ã¼ã
luckperms.command.generic.info.parent.temporary-title=ä¸æçãªè¦ªã°ã«ã¼ã
luckperms.command.generic.info.expires-in=æå¹æé\:
luckperms.command.generic.info.inherited-from=ç¶æ¿å\:
luckperms.command.generic.info.inherited-from-self=èªèº«
luckperms.command.generic.show-tracks.title={0} ã®ãã©ãã¯
luckperms.command.generic.show-tracks.empty={0} ã¯ãã©ãã¯ä¸ã«ããã¾ãã
luckperms.command.generic.clear.node-removed={0} åã®ãã¼ããåé¤ããã¾ãã
luckperms.command.generic.clear.node-removed-singular={0} åã®ãã¼ããåé¤ããã¾ãã
luckperms.command.generic.clear={0} ã®ãã¼ãã¯ã³ã³ãã­ã¹ã {1} ã§ã¯ãªã¢ããã¾ãã
luckperms.command.generic.permission.info.title={0} ã®æ¨©é
luckperms.command.generic.permission.info.empty={0} ã¯æ¨©éãæã£ã¦ãã¾ãã
luckperms.command.generic.permission.info.click-to-remove=ãã®ãã¼ãã {0} ããåé¤ããã«ã¯ã¯ãªãã¯ãã¦ãã ãã
luckperms.command.generic.permission.check.info.title={0} ã®æ¨©éæå ±
luckperms.command.generic.permission.check.info.directly={0} ã¯ {1} ãã³ã³ãã­ã¹ã {3} ã§ {2} ã«è¨­å®ããã¦ãã¾ã
luckperms.command.generic.permission.check.info.inherited={0} ã¯ {3} ãã {1} ã {2} ã«è¨­å®ããã¦ãã {4} ãç¶æ¿ãã¦ãã¾ã
luckperms.command.generic.permission.check.info.not-directly={0} ã¯ {1} ãè¨­å®ããã¦ãã¾ãã
luckperms.command.generic.permission.check.info.not-inherited={0} ã¯ {1} ãç¶æ¿ãã¦ãã¾ãã
luckperms.command.generic.permission.check.result.title={0} ã®æ¨©éãã§ãã¯
luckperms.command.generic.permission.check.result.result-key=çµæ
luckperms.command.generic.permission.check.result.processor-key=ãã­ã»ããµã¼
luckperms.command.generic.permission.check.result.cause-key=åå 
luckperms.command.generic.permission.check.result.context-key=ã³ã³ãã­ã¹ã
luckperms.command.generic.permission.set=ã³ã³ãã­ã¹ã {3} ã§ {2} ã® {0} ã {1} ã«è¨­å®ãã¾ãã
luckperms.command.generic.permission.already-has={0} ã¯ãã§ã«ã³ã³ãã­ã¹ã {2} ã§ {1} ãè¨­å®ããã¦ãã¾ã
luckperms.command.generic.permission.set-temp=ã³ã³ãã­ã¹ã {4}, æå¹æé {3} ã§ {2} ã® {0} ã {1} ã«è¨­å®ãã¾ãã
luckperms.command.generic.permission.already-has-temp={0} ã¯ãã§ã«ã³ã³ãã­ã¹ã {2} ã§ {1} ãä¸æçã«è¨­å®ããã¦ãã¾ã
luckperms.command.generic.permission.unset=ã³ã³ãã­ã¹ã {2} ã§ {1} ãã {0} ã®è¨­å®ãè§£é¤ãã¾ãã
luckperms.command.generic.permission.doesnt-have={0} ã¯ã³ã³ãã­ã¹ã {2} ã§ {1} ãè¨­å®ããã¦ãã¾ãã
luckperms.command.generic.permission.unset-temp=ã³ã³ãã­ã¹ã {2} ã§ {1} ã®ä¸æçãªæ¨©é {0} ãè§£é¤ãã¾ãã
luckperms.command.generic.permission.subtract=ã³ã³ãã­ã¹ã {4} ã§ {2} ã«å¯¾ãã¦ {3} ã®æéã{0} ã {1} ã«è¨­å®ãã¾ãããããã¯ä»¥åãã {5} å°ãªããªãã¾ã
luckperms.command.generic.permission.doesnt-have-temp={0} ã¯ã³ã³ãã­ã¹ã {2} ã§ {1} ãä¸æçã«è¨­å®ããã¦ãã¾ãã
luckperms.command.generic.permission.clear={0} ã®æ¨©éã¯ã³ã³ãã­ã¹ã {1} ã§ã¯ãªã¢ããã¾ãã
luckperms.command.generic.parent.info.title={0} ã®è¦ª
luckperms.command.generic.parent.info.empty={0} ã«ã¯è¦ªãå®ç¾©ããã¦ãã¾ãã
luckperms.command.generic.parent.info.click-to-remove=ã¯ãªãã¯ãã¦ {0} ãããã®è¦ªãåé¤ãã¾ã
luckperms.command.generic.parent.add={0} ã¯ã³ã³ãã­ã¹ã {2} ã§ {1} ããæ¨©éãç¶æ¿ãã¦ãã¾ã
luckperms.command.generic.parent.add-temp={0} ã¯ã³ã³ãã­ã¹ã {3}, æå¹æé {2} ã§ {1} ããæ¨©éãç¶æ¿ãã¦ãã¾ã
luckperms.command.generic.parent.set={0} ã¯æ¢å­ã®è¦ªã°ã«ã¼ããã¯ãªã¢ããã³ã³ãã­ã¹ã {2} ã§ {1} ã®ã¿ãç¶æ¿ãã¾ã
luckperms.command.generic.parent.set-track={0} ã¯ãã©ãã¯ {1} ä¸ã®æ¢å­ã®è¦ªã°ã«ã¼ããã¯ãªã¢ããã³ã³ãã­ã¹ã {3} ã§ {2} ã®ã¿ãç¶æ¿ãã¾ã
luckperms.command.generic.parent.remove={0} ã¯ã³ã³ãã­ã¹ã {2} ã§ {1} ããæ¨©éãç¶æ¿ãã¾ãã
luckperms.command.generic.parent.remove-temp={0} ã¯ã³ã³ãã­ã¹ã {2} ã§ {1} ããã®æ¨©éãä¸æçã«ç¶æ¿ãã¾ãã
luckperms.command.generic.parent.subtract={0} ã¯ã³ã³ãã­ã¹ã {3} ã§æå¹æéãä»¥åãã {4} å°ãªã {2} ã®é {1} ããæ¨©éãç¶æ¿ãã¾ã
luckperms.command.generic.parent.clear={0} ã®è¦ªã¯ã³ã³ãã­ã¹ã {1} ã§ã¯ãªã¢ããã¾ãã
luckperms.command.generic.parent.clear-track=ãã©ãã¯ {1} ä¸ã® {0} ã®è¦ªã¯ã³ã³ãã­ã¹ã {2} ã§ã¯ãªã¢ããã¾ãã
luckperms.command.generic.parent.already-inherits={0} ã¯ãã§ã«ã³ã³ãã­ã¹ã {2} ã§ {1} ããç¶æ¿ãã¦ãã¾ã
luckperms.command.generic.parent.doesnt-inherit={0} ã¯ã³ã³ãã­ã¹ã {2} ã§ {1} ããç¶æ¿ãã¦ãã¾ãã
luckperms.command.generic.parent.already-temp-inherits={0} ã¯ãã§ã«ã³ã³ãã­ã¹ã {2} ã§ {1} ããä¸æçã«ç¶æ¿ãã¦ãã¾ã
luckperms.command.generic.parent.doesnt-temp-inherit={0} ã¯ã³ã³ãã­ã¹ã {2} ã§ {1} ããä¸æçã«ç¶æ¿ãã¦ãã¾ãã
luckperms.command.generic.chat-meta.info.title-prefix={0} ã®ãã¬ãã£ãã¯ã¹
luckperms.command.generic.chat-meta.info.title-suffix={0} ã®ãµãã£ãã¯ã¹
luckperms.command.generic.chat-meta.info.none-prefix={0} ã¯ãã¬ãã£ãã¯ã¹ãæã£ã¦ãã¾ãã
luckperms.command.generic.chat-meta.info.none-suffix={0} ã¯ãµãã£ãã¯ã¹ãæã£ã¦ãã¾ãã
luckperms.command.generic.chat-meta.info.click-to-remove=ã¯ãªãã¯ãã¦ {1} ãããã® {0} ãåé¤ãã¾ã
luckperms.command.generic.chat-meta.already-has={0} ã¯ãã§ã«ã³ã³ãã­ã¹ã {4} ã§ {1} {2} ãåªååº¦ {3} ã«è¨­å®ããã¦ãã¾ã
luckperms.command.generic.chat-meta.already-has-temp={0} ã¯ãã§ã«ã³ã³ãã­ã¹ã {4} ã§ {1} {2} ãä¸æçã«åªååº¦ {3} ã«è¨­å®ããã¦ãã¾ã
luckperms.command.generic.chat-meta.doesnt-have={0} ã¯ã³ã³ãã­ã¹ã {4} ã§ {1} {2} ãåªååº¦ {3} ã§è¨­å®ããã¦ãã¾ãã
luckperms.command.generic.chat-meta.doesnt-have-temp={0} ã¯ã³ã³ãã­ã¹ã {4} ã§ {1} {2} ãä¸æçã«åªååº¦ {3} ã§è¨­å®ããã¦ãã¾ãã
luckperms.command.generic.chat-meta.add={0} ã«ã³ã³ãã­ã¹ã {4} ã§ {1} {2} ãåªååº¦ {3} ã§è¨­å®ãã¾ãã
luckperms.command.generic.chat-meta.add-temp={0} ã«ã³ã³ãã­ã¹ã {5} ã§ {1} {2} ãåªååº¦ {3}, æå¹æé {4} ã§è¨­å®ãã¾ãã
luckperms.command.generic.chat-meta.remove={0} ããã³ã³ãã­ã¹ã {4} ã§åªååº¦ {3} ã® {1} {2} ãåé¤ãã¾ãã
luckperms.command.generic.chat-meta.remove-bulk={0} ããã³ã³ãã­ã¹ã {3} ã§åªååº¦ {2} ã®ãã¹ã¦ã® {1} ãåé¤ãã¾ãã
luckperms.command.generic.chat-meta.remove-temp={0} ããã³ã³ãã­ã¹ã {4} ã§åªååº¦ {3} ã®ä¸æçãª {1} {2} ãåé¤ãã¾ãã
luckperms.command.generic.chat-meta.remove-temp-bulk={0} ããã³ã³ãã­ã¹ã {3} ã§åªååº¦ {2} ã®ãã¹ã¦ã®ä¸æçãª {1} ãåé¤ãã¾ãã
luckperms.command.generic.meta.info.title={0} ã®ã¡ã¿
luckperms.command.generic.meta.info.none={0} ã¯ã¡ã¿ãæã£ã¦ãã¾ãã
luckperms.command.generic.meta.info.click-to-remove=ã¯ãªãã¯ãã¦ {0} ãããã®ã¡ã¿ãåé¤ãã¾ã
luckperms.command.generic.meta.already-has={0} ã¯ãã§ã«ã³ã³ãã­ã¹ã {3} ã§ã¡ã¿ã­ã¼ {1} ã« {2} ãè¨­å®ããã¦ãã¾ã
luckperms.command.generic.meta.already-has-temp={0} ã¯ãã§ã«ã³ã³ãã­ã¹ã {3} ã§ã¡ã¿ã­ã¼ {1} ã« {2} ãä¸æçã«è¨­å®ããã¦ãã¾ã
luckperms.command.generic.meta.doesnt-have={0} ã¯ã³ã³ãã­ã¹ã {2} ã§ã¡ã¿ã­ã¼ {1} ãè¨­å®ããã¦ãã¾ãã
luckperms.command.generic.meta.doesnt-have-temp={0} ã¯ã³ã³ãã­ã¹ã {2} ã§ã¡ã¿ã­ã¼ {1} ãä¸æçã«è¨­å®ããã¦ãã¾ãã
luckperms.command.generic.meta.set=ã³ã³ãã­ã¹ã {3} ã§ {2} ã«ã¡ã¿ã­ã¼ {0} ã {1} ã§è¨­å®ãã¾ãã
luckperms.command.generic.meta.set-temp=ã³ã³ãã­ã¹ã {4} ã§ {2} ã®ã¡ã¿ã­ã¼ {0} ãæé {3} ã®é {1} ã«è¨­å®ãã¾ãã
luckperms.command.generic.meta.unset=ã³ã³ãã­ã¹ã {2} ã§ {1} ã®ã¡ã¿ã­ã¼ {0} ã®è¨­å®ãè§£é¤ãã¾ãã
luckperms.command.generic.meta.unset-temp=ã³ã³ãã­ã¹ã {2} ã§ {1} ã®ä¸æçãªã¡ã¿ã­ã¼ {0} ãè§£é¤ãã¾ãã
luckperms.command.generic.meta.clear=ã³ã³ãã­ã¹ã {2} ã§ {0} ã®ã¿ã¤ã {1} ã«ä¸è´ããã¡ã¿ãã¯ãªã¢ããã¾ãã
luckperms.command.generic.contextual-data.title=ã³ã³ãã­ã¹ããã¼ã¿
luckperms.command.generic.contextual-data.mode.key=ã¢ã¼ã
luckperms.command.generic.contextual-data.mode.server=ãµã¼ãã¼
luckperms.command.generic.contextual-data.mode.active-player=ã¢ã¯ãã£ããã¬ã¤ã¤ã¼
luckperms.command.generic.contextual-data.contexts-key=ã³ã³ãã­ã¹ã
luckperms.command.generic.contextual-data.prefix-key=ãã¬ãã£ãã¯ã¹
luckperms.command.generic.contextual-data.suffix-key=ãµãã£ãã¯ã¹
luckperms.command.generic.contextual-data.primary-group-key=ãã©ã¤ããªã¼ã°ã«ã¼ã
luckperms.command.generic.contextual-data.meta-key=ã¡ã¿
luckperms.command.generic.contextual-data.null-result=ãªã
luckperms.command.user.info.title=ã¦ã¼ã¶ã¼æå ±
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=ã¿ã¤ã
luckperms.command.user.info.uuid-type.mojang=å¬å¼
luckperms.command.user.info.uuid-type.not-mojang=ãªãã©ã¤ã³
luckperms.command.user.info.status-key=ã¹ãã¼ã¿ã¹
luckperms.command.user.info.status.online=ãªã³ã©ã¤ã³
luckperms.command.user.info.status.offline=ãªãã©ã¤ã³
luckperms.command.user.removegroup.error-primary=ãã©ã¤ããªã°ã«ã¼ãããã¦ã¼ã¶ã¼ãåé¤ãããã¨ã¯ã§ãã¾ãã
luckperms.command.user.primarygroup.not-member={0} ã¯ã¾ã  {1} ã®ã¡ã³ãã¼ã§ã¯ããã¾ãããä»ããè¿½å ãã¾ã
luckperms.command.user.primarygroup.already-has={0} ã¯ãã§ã« {1} ããã©ã¤ããªã°ã«ã¼ãã¨ãã¦è¨­å®ããã¦ãã¾ã
luckperms.command.user.primarygroup.warn-option=è­¦å\: ãã®ãµã¼ãã¼ ({0}) ã§ä½¿ç¨ããã¦ãããã©ã¤ããªã°ã«ã¼ãã®è¨ç®æ¹æ³ã¯ããã®å¤æ´ãåæ ãã¦ããªãå¯è½æ§ãããã¾ã
luckperms.command.user.primarygroup.set={0} ã®ãã©ã¤ããªã°ã«ã¼ãã¯ {1} ã«è¨­å®ããã¾ãã
luckperms.command.user.track.error-not-contain-group={0} ã¯ {1} ã®ã©ã®ã°ã«ã¼ãã«ãå±ãã¦ãã¾ãã
luckperms.command.user.track.unsure-which-track=ã©ã®ãã©ãã¯ãä½¿ç¨ãããããããªãå ´åã¯ãå¼æ°ã¨ãã¦æå®ãã¦ãã ããã
luckperms.command.user.track.missing-group-advice=ã°ã«ã¼ããä½æãããããã©ãã¯ããåé¤ãã¦åè©¦è¡ãã¦ãã ãã
luckperms.command.user.promote.added-to-first={0} ã¯ {1} ã®ã©ã®ã°ã«ã¼ãã«ãå±ãã¦ããªããããæåã®ã°ã«ã¼ã {2} ã«ã³ã³ãã­ã¹ã {3} ã§è¿½å ããã¾ãã
luckperms.command.user.promote.not-on-track={0} ã¯ {1} ã®ã©ã®ã°ã«ã¼ãã«ãå±ãã¦ããªããããææ ¼ããã¾ããã§ãã
luckperms.command.user.promote.success=ãã©ãã¯ {1} ã«å¾ã£ã¦ {0} ãã³ã³ãã­ã¹ã {4} ã§ {2} ãã {3} ã«ææ ¼ãã¾ãã
luckperms.command.user.promote.end-of-track=ãã©ãã¯ {0} ãçµäºãã¾ããã{1} ãææ ¼ã§ãã¾ãã
luckperms.command.user.promote.next-group-deleted=ãã©ãã¯ã®æ¬¡ã®ã°ã«ã¼ã {0} ã¯å­å¨ãã¾ãã
luckperms.command.user.promote.unable-to-promote=ã¦ã¼ã¶ã¼ãææ ¼ã§ãã¾ãã
luckperms.command.user.demote.success=ãã©ãã¯ {1} ã«å¾ã£ã¦ {0} ãã³ã³ãã­ã¹ã {4} ã§ {2} ãã {3} ã«éæ ¼ãã¾ãã
luckperms.command.user.demote.end-of-track=ãã©ãã¯ {0} ãçµäºããã®ã§ã{1} ã¯ {2} ããåé¤ããã¾ãã
luckperms.command.user.demote.end-of-track-not-removed=ãã©ãã¯ {0} ã¯çµäºãã¾ãããã{1} ã¯æåã®ã°ã«ã¼ãããåé¤ããã¾ããã§ãã
luckperms.command.user.demote.previous-group-deleted=ãã©ãã¯ã®åã®ã°ã«ã¼ã {0} ã¯å­å¨ãã¾ãã
luckperms.command.user.demote.unable-to-demote=ã¦ã¼ã¶ã¼ãéæ ¼ã§ãã¾ãã
luckperms.command.group.list.title=ã°ã«ã¼ã
luckperms.command.group.delete.not-default=ããã©ã«ãã®ã°ã«ã¼ãã¯åé¤ã§ãã¾ãã
luckperms.command.group.info.title=ã°ã«ã¼ãæå ±
luckperms.command.group.info.display-name-key=è¡¨ç¤ºå
luckperms.command.group.info.weight-key=ã¦ã§ã¤ã
luckperms.command.group.setweight.set=ã°ã«ã¼ã {1} ã®ã¦ã§ã¤ãã {0} ã«è¨­å®ãã¾ãã
luckperms.command.group.setdisplayname.doesnt-have={0} ã«è¡¨ç¤ºåã¯è¨­å®ããã¦ãã¾ãã
luckperms.command.group.setdisplayname.already-has={0} ã¯ãã§ã«è¡¨ç¤ºåã {1} ã§ã
luckperms.command.group.setdisplayname.already-in-use=è¡¨ç¤ºå {0} ã¯ãã§ã« {1} ã«ä½¿ç¨ããã¦ãã¾ã
luckperms.command.group.setdisplayname.set=ã°ã«ã¼ã {1} ã®è¡¨ç¤ºåãã³ã³ãã­ã¹ã {2} ã§ {0} ã«è¨­å®ãã¾ãã
luckperms.command.group.setdisplayname.removed=ã°ã«ã¼ã {0} ã®è¡¨ç¤ºåãã³ã³ãã­ã¹ã {1} ã§åé¤ãã¾ãã
luckperms.command.track.list.title=ãã©ãã¯
luckperms.command.track.path.empty=ãªã
luckperms.command.track.info.showing-track=ãã©ãã¯ãè¡¨ç¤º
luckperms.command.track.info.path-property=ãã¹
luckperms.command.track.clear={0} ã®ã°ã«ã¼ããã©ãã¯ãã¯ãªã¢ãã¾ãã
luckperms.command.track.append.success=ã°ã«ã¼ã {0} ã¯ãã©ãã¯ {1} ã«è¿½å ããã¾ãã
luckperms.command.track.insert.success=ã°ã«ã¼ã {0} ã¯ãã©ãã¯ {1} ã® {2} çªç®ã«æ¿å¥ããã¾ãã
luckperms.command.track.insert.error-number=æ°å¤ãå¿è¦ã§ãããä»£ããã« {0} ãå¥åããã¾ããã
luckperms.command.track.insert.error-invalid-pos={0} çªç®ã«æ¿å¥ã§ãã¾ããã§ãã
luckperms.command.track.insert.error-invalid-pos-reason=ç¡å¹ãªä½ç½®
luckperms.command.track.remove.success=ã°ã«ã¼ã {0} ã¯ãã©ãã¯ {1} ããåé¤ããã¾ãã
luckperms.command.track.error-empty={0} ã¯ç©ºã¾ãã¯1ã¤ã®ã°ã«ã¼ãããå«ã¾ãã¦ããªãããä½¿ç¨ã§ãã¾ãã
luckperms.command.track.error-multiple-groups={0} ã¯ãã®ãã©ãã¯ä¸ã®è¤æ°ã®ã°ã«ã¼ãã®ã¡ã³ãã¼ã§ã
luckperms.command.track.error-ambiguous=ä½ç½®ãç¹å®ã§ãã¾ãã
luckperms.command.track.already-contains={0} ã¯ãã§ã« {1} ãå«ãã§ãã¾ã
luckperms.command.track.doesnt-contain={0} ã¯ {1} ãå«ãã§ãã¾ãã
luckperms.command.log.load-error=ã­ã°ãèª­ã¿è¾¼ãã¾ããã§ãã
luckperms.command.log.invalid-page=ç¡å¹ãªãã¼ã¸çªå·ã§ã
luckperms.command.log.invalid-page-range={0} ãã {1} ã®éã§å¤ãå¥åãã¦ãã ãã
luckperms.command.log.empty=è¡¨ç¤ºããã­ã°ã¨ã³ããªãããã¾ãã
luckperms.command.log.notify.error-console=ã³ã³ã½ã¼ã«ã¸ã®éç¥ã¯åãæ¿ãããã¾ãã
luckperms.command.log.notify.enabled-term=æå¹å
luckperms.command.log.notify.disabled-term=ç¡å¹å
luckperms.command.log.notify.changed-state=ã­ã°åºåã {0} ãã¾ãã
luckperms.command.log.notify.already-on=ãã§ã«éç¥ãåä¿¡ãã¦ãã¾ã
luckperms.command.log.notify.already-off=ç¾å¨éç¥ãåä¿¡ãã¦ãã¾ãã
luckperms.command.log.notify.invalid-state={0} ã {1} ãæå®ãã¦ãã ãã
luckperms.command.log.show.search=ã¯ã¨ãª {0} ã®æè¿ã®æä½ãè¡¨ç¤ºãã
luckperms.command.log.show.recent=æè¿ã®æä½ãè¡¨ç¤ºãã
luckperms.command.log.show.by={0} ã«ããæè¿ã®æä½ãè¡¨ç¤ºãã
luckperms.command.log.show.history={0} {1} ã®å±¥æ­´ãè¡¨ç¤ºãã¦ãã¾ã
luckperms.command.export.error-term=ã¨ã©ã¼
luckperms.command.export.already-running=ä»ã®ã¨ã¯ã¹ãã¼ããã­ã»ã¹ãå®è¡ããã¦ãã¾ã
luckperms.command.export.file.already-exists=ãã¡ã¤ã« {0} ã¯ãã§ã«å­å¨ãã¦ãã¾ã
luckperms.command.export.file.not-writable=ãã¡ã¤ã« {0} ã¯æ¸ãè¾¼ã¿ç¦æ­¢ç¶æã§ã
luckperms.command.export.file.success={0} ã¸ã®ã¨ã¯ã¹ãã¼ããæ­£å¸¸ã«å®äºãã¾ãã
luckperms.command.export.file-unexpected-error-writing=ãã¡ã¤ã«ã¸ã®æ¸ãè¾¼ã¿ä¸­ã«äºæããªãã¨ã©ã¼ãçºçãã¾ãã
luckperms.command.export.web.export-code=ã¨ã¯ã¹ãã¼ãã³ã¼ã
luckperms.command.export.web.import-command-description=ã¤ã³ãã¼ãããã«ã¯æ¬¡ã®ã³ãã³ããä½¿ç¨ãã¦ãã ãã
luckperms.command.import.term=ã¤ã³ãã¼ã
luckperms.command.import.error-term=ã¨ã©ã¼
luckperms.command.import.already-running=ä»ã®ã¤ã³ãã¼ããã­ã»ã¹ãæ¢ã«å®è¡ä¸­ã§ã
luckperms.command.import.file.doesnt-exist=ãã¡ã¤ã« {0} ã¯å­å¨ãã¾ãã
luckperms.command.import.file.not-readable=ãã¡ã¤ã« {0} ãèª­ã¿è¾¼ãã¾ãã
luckperms.command.import.file.unexpected-error-reading=ãã¡ã¤ã«ã®èª­ã¿è¾¼ã¿ä¸­ã«äºæããªãã¨ã©ã¼ãçºçãã¾ãã
luckperms.command.import.file.correct-format=æ­£ããå½¢å¼ã§ãã?
luckperms.command.import.web.unable-to-read=æå®ãããã³ã¼ããä½¿ç¨ãã¦ãã¼ã¿ãèª­ã¿åãã¾ããã§ãã
luckperms.command.import.progress.percent={0}% å®äº
luckperms.command.import.progress.operations={0}/{1} å¦çå®äº
luckperms.command.import.starting=ã¤ã³ãã¼ãå¦çãéå§ãã¦ãã¾ã
luckperms.command.import.completed=å®äºãã¾ãã
luckperms.command.import.duration=æè¦æé {0} ç§
luckperms.command.bulkupdate.must-use-console=ä¸æ¬æ´æ°ã³ãã³ãã¯ã³ã³ã½ã¼ã«ããã®ã¿ä½¿ç¨ã§ãã¾ã
luckperms.command.bulkupdate.invalid-data-type=ç¡å¹ãªã¿ã¤ãã§ãã{0} ãæå®ã§ãã¾ã
luckperms.command.bulkupdate.invalid-constraint={0} ã¯ç¡å¹ãªå¶ç´ã§ã
luckperms.command.bulkupdate.invalid-constraint-format=å¶ç´ã¯ {0} ã®ãã©ã¼ãããã§ããå¿è¦ãããã¾ã
luckperms.command.bulkupdate.invalid-comparison=ç¡å¹ãªæ¯è¼æ¼ç®å­ {0}
luckperms.command.bulkupdate.invalid-comparison-format=æ¬¡ã®ããããã§ããå¿è¦ãããã¾ã\: {0}
luckperms.command.bulkupdate.queued=ãã«ã¯ã¢ãããã¼ããã­ã¥ã¼ã«è¿½å ããã¾ãã
luckperms.command.bulkupdate.confirm={0} ãå®è¡ãã¦æ´æ°ãè¡ãã¾ã
luckperms.command.bulkupdate.unknown-id=id {0} ã®æä½ã¯å­å¨ããªãããæéåãã§ã
luckperms.command.bulkupdate.starting=ãã«ã¯ã¢ãããã¼ããå®è¡ä¸­
luckperms.command.bulkupdate.success=ãã«ã¯ã¢ãããã¼ããæ­£å¸¸ã«å®äºãã¾ãã
luckperms.command.bulkupdate.success.statistics.nodes=å¤æ´ããããã¼ãæ°
luckperms.command.bulkupdate.success.statistics.users=å¤æ´ãããã¦ã¼ã¶ã¼æ°
luckperms.command.bulkupdate.success.statistics.groups=å¤æ´ãããã°ã«ã¼ãæ°
luckperms.command.bulkupdate.failure=ãã«ã¯ã¢ãããã¼ãã«å¤±æãã¾ãããã³ã³ã½ã¼ã«ã§ã¨ã©ã¼ãç¢ºèªãã¦ãã ãã
luckperms.command.update-task.request=ã¢ãããã¼ããè¦æ±ããã¾ããããã°ãããå¾ã¡ãã ãã
luckperms.command.update-task.complete=ã¢ãããã¼ããå®äºãã¾ãã
luckperms.command.update-task.push.attempting=ç¾å¨ä»ã®ãµã¼ãã¼ã«é©ç¨ãããã¨ãã¦ãã¾ã
luckperms.command.update-task.push.complete=ä»ã®ãµã¼ãã¼ã¯ {0} çµç±ã§æ­£å¸¸ã«é©ç¨ããã¾ãã
luckperms.command.update-task.push.error=ä»ã®ãµã¼ãã¼ã¸ã®å¤æ´ã®é©ç¨ä¸­ã«ã¨ã©ã¼ãçºçãã¾ãã
luckperms.command.update-task.push.error-not-setup=ã¡ãã»ã¼ã¸ã³ã°ãµã¼ãã¹ãè¨­å®ããã¦ããªããããä»ã®ãµã¼ãã¼ã«å¤æ´ãé©ç¨ã§ãã¾ãã
luckperms.command.reload-config.success=è¨­å®ãã¡ã¤ã«ãåèª­ã¿è¾¼ã¿ããã¾ãã
luckperms.command.reload-config.restart-note=ããã¤ãã®è¨­å®ã¯ãµã¼ãã¼ã®åèµ·åå¾ã«é©ç¨ããã¾ã
luckperms.command.translations.searching=å©ç¨å¯è½ãªç¿»è¨³ãæ¤ç´¢ãã¦ãã¾ãããå¾ã¡ãã ãã...
luckperms.command.translations.searching-error=å©ç¨å¯è½ãªç¿»è¨³ã®ãªã¹ããåå¾ã§ãã¾ãã
luckperms.command.translations.installed-translations=ã¤ã³ã¹ãã¼ã«æ¸ã¿ã®ç¿»è¨³
luckperms.command.translations.available-translations=å©ç¨å¯è½ãªç¿»è¨³
luckperms.command.translations.percent-translated={0}% ç¿»è¨³æ¸ã¿
luckperms.command.translations.translations-by=ä½æè
luckperms.command.translations.installing=ç¿»è¨³ãã¤ã³ã¹ãã¼ã«ãã¦ãã¾ãããå¾ã¡ãã ãã...
luckperms.command.translations.download-error={0} ã®ç¿»è¨³ããã¦ã³ã­ã¼ãã§ãã¾ãã
luckperms.command.translations.installing-specific=è¨èª {0} ãã¤ã³ã¹ãã¼ã«ä¸­...
luckperms.command.translations.install-complete=ã¤ã³ã¹ãã¼ã«å®äº
luckperms.command.translations.download-prompt=ã³ãã¥ããã£ã¼ãæä¾ããææ°ã®ç¿»è¨³ããã¦ã³ã­ã¼ããã¦ã¤ã³ã¹ãã¼ã«ããã«ã¯ {0} ãä½¿ç¨ãã¦ãã ãã
luckperms.command.translations.download-override-warning=ãããã®è¨èªã«å¯¾ããå¤æ´ã¯ä¸æ¸ãããã¾ãã®ã§æ³¨æãã¦ãã ãã
luckperms.usage.user.description=LuckPerms åã§ã¦ã¼ã¶ã¼ãç®¡çããããã®ã³ãã³ãã§ã (LuckPerms åã® `user` ã¯ãã¬ã¤ã¤ã¼ã§ãããUUID ãã¦ã¼ã¶ã¼åãåç§ã§ãã¾ã)
luckperms.usage.group.description=LuckPerms åã§ã°ã«ã¼ããç®¡çããããã®ã³ãã³ãã§ããã°ã«ã¼ãã¯ã¦ã¼ã¶ã¼ã«ä¸ããããæ¨©éå²ãå½ã¦ã®åãªãã³ã¬ã¯ã·ã§ã³ã§ããæ°ããã°ã«ã¼ãã¯ `creategroup` ã³ãã³ããä½¿ç¨ãã¦ä½æããã¾ãã
luckperms.usage.track.description=LuckPerms åã§ãã©ãã¯ãç®¡çããããã®ã³ãã³ãã§ãããã©ãã¯ã¯ãææ ¼ãåºè§ã®å®ç¾©ã«ä½¿ç¨ã§ããã°ã«ã¼ãã®é åºä»ãã³ã¬ã¯ã·ã§ã³ã§ãã
luckperms.usage.log.description=LuckPerms åã§ã­ã®ã³ã°æ©è½ãç®¡çããããã®ã³ãã³ãã§ãã
luckperms.usage.sync.description=ãã©ã°ã¤ã³ã®ã¹ãã¬ã¼ã¸ãããã¹ã¦ã®ãã¼ã¿ãã¡ã¢ãªã«åèª­ã¿è¾¼ã¿ããæ¤åºãããå¤æ´ãé©ç¨ãã¾ã
luckperms.usage.info.description=ã¢ã¯ãã£ããªãã©ã°ã¤ã³ã¤ã³ã¹ã¿ã³ã¹ã«é¢ããä¸è¬æå ±ãåºåãã¾ã
luckperms.usage.editor.description=æ°ããã¦ã§ãã¨ãã£ã¿ã¼ãä½æãã¾ã
luckperms.usage.editor.argument.type=ã¨ãã£ã¿ã«èª­ã¿è¾¼ãã¿ã¤ã (''all'', ''users'' or ''groups'')
luckperms.usage.editor.argument.filter=ã¦ã¼ã¶ã¼ããã£ã«ã¿ãªã³ã°ããæ¨©é
luckperms.usage.verbose.description=ãã©ã°ã¤ã³ã®æ¨©éãã§ãã¯ãç£è¦ããã·ã¹ãã ãå¶å¾¡ãã¾ã
luckperms.usage.verbose.argument.action=ã­ã°ã®æå¹/ç¡å¹ãåãæ¿ããããã­ã°åºåãã¢ããã­ã¼ããããã©ãã
luckperms.usage.verbose.argument.filter=æ¬¡ã«ä¸è´ããã¨ã³ããªã®ãã£ã«ã¿
luckperms.usage.verbose.argument.commandas=å®è¡ãããã¬ã¤ã¤ã¼/ã³ãã³ã
luckperms.usage.tree.description=LuckPerms ãææ¡ãã¦ãããã¹ã¦ã®æ¨©éã®ããªã¼ãã¥ã¼ (é åºä»ããªã¹ãéå±¤) ãçæãã¾ã
luckperms.usage.tree.argument.scope=ããªã¼ã®ã«ã¼ãã§ãããã¹ã¦ã®æ¨©éãå«ããã«ã¯ "." ãæå®ãã¦ãã ãã
luckperms.usage.tree.argument.player=ç¢ºèªãããªã³ã©ã¤ã³ãã¬ã¤ã¤ã¼ã®åå
luckperms.usage.search.description=æå®ããæ¨©éãæã¤ãã¹ã¦ã®ã¦ã¼ã¶ã¼/ã°ã«ã¼ããæ¤ç´¢ãã¾ã
luckperms.usage.search.argument.permission=æ¤ç´¢ããæ¨©é
luckperms.usage.search.argument.page=è¡¨ç¤ºãããã¼ã¸
luckperms.usage.network-sync.description=å¤æ´ãã¹ãã¬ã¼ã¸ã«åæããããããã¯ã¼ã¯ä¸ã®ä»ã®ãã¹ã¦ã®ãµã¼ãã¼ã«ãåæãè¦æ±ãã¾ã
luckperms.usage.import.description=(ä»¥åä½æãã) ã¨ã¯ã¹ãã¼ããã¡ã¤ã«ãããã¼ã¿ãã¤ã³ãã¼ããã
luckperms.usage.import.argument.file=ã¤ã³ãã¼ããããã¡ã¤ã«
luckperms.usage.import.argument.replace=ãã¼ã¸ã®ä»£ããã«æ¢å­ã®ãã¼ã¿ãç½®ãæãã
luckperms.usage.import.argument.upload=ä»¥åã®ã¨ã¯ã¹ãã¼ããããã¼ã¿ãã¢ããã­ã¼ã
luckperms.usage.export.description=ãã¡ã¤ã«ã«ãã¹ã¦ã®æ¨©éãã¼ã¿ãã¨ã¯ã¹ãã¼ããã¾ãããã¨ããåã¤ã³ãã¼ãã§ãã¾ãã
luckperms.usage.export.argument.file=ã¨ã¯ã¹ãã¼ããããã¡ã¤ã«
luckperms.usage.export.argument.without-users=ã¨ã¯ã¹ãã¼ãããã¦ã¼ã¶ã¼ãé¤å¤
luckperms.usage.export.argument.without-groups=ã¨ã¯ã¹ãã¼ãããã°ã«ã¼ããé¤å¤ãã
luckperms.usage.export.argument.upload=ã¦ã§ãã¨ãã£ã¿ã¼ã«ãã¹ã¦ã®æ¨©éãã¼ã¿ãã¢ããã­ã¼ããã¾ãããã¨ããåã¤ã³ãã¼ãã§ãã¾ãã
luckperms.usage.reload-config.description=è¨­å®ãªãã·ã§ã³ãåèª­ã¿è¾¼ã¿ãã
luckperms.usage.bulk-update.description=ãã¹ã¦ã®ãã¼ã¿ã«å¯¾ãã¦ä¸æ¬å¤æ´ã¯ã¨ãªãå®è¡ãã
luckperms.usage.bulk-update.argument.data-type=å¤æ´ããããã¼ã¿ã®ã¿ã¤ã (''all'', ''users'' or ''groups'')
luckperms.usage.bulk-update.argument.action=ãã¼ã¿ä¸ã§å®è¡ããã¢ã¯ã·ã§ã³ (''update'' ã¾ãã¯ ''delete'')
luckperms.usage.bulk-update.argument.action-field=å®è¡ããå¯¾è±¡ã''update'' æã®ã¿å¿è¦ã§ãã(''permission'', ''server'' ã¾ãã¯ ''world'')
luckperms.usage.bulk-update.argument.action-value=ç½®ãæãå¾ã®å¤ã''update'' ã«ã®ã¿å¿è¦ã§ãã
luckperms.usage.bulk-update.argument.constraint=æ´æ°ã«å¿è¦ãªå¶ç´
luckperms.usage.translations.description=ç¿»è¨³ãç®¡çãã
luckperms.usage.translations.argument.install=ç¿»è¨³ãã¤ã³ã¹ãã¼ã«ãããµãã³ãã³ã
luckperms.usage.apply-edits.description=ã¦ã§ãã¨ãã£ã¿ã¼ã§å¤æ´ããæ¨©éãé©ç¨ãã¾ã
luckperms.usage.apply-edits.argument.code=ãã¼ã¿ã®ä¸æãªã³ã¼ã
luckperms.usage.apply-edits.argument.target=ãã¼ã¿ã®é©ç¨å
luckperms.usage.create-group.description=æ°ããã°ã«ã¼ãã®ä½æ
luckperms.usage.create-group.argument.name=ã°ã«ã¼ãå
luckperms.usage.create-group.argument.weight=ã°ã«ã¼ãã®éã¿
luckperms.usage.create-group.argument.display-name=ã°ã«ã¼ãã®è¡¨ç¤ºå
luckperms.usage.delete-group.description=ã°ã«ã¼ãã®åé¤
luckperms.usage.delete-group.argument.name=ã°ã«ã¼ãå
luckperms.usage.list-groups.description=ãã©ãããã©ã¼ã ä¸ã®ãã¹ã¦ã®ã°ã«ã¼ãã®ä¸è¦§
luckperms.usage.create-track.description=æ°ãããã©ãã¯ã®ä½æ
luckperms.usage.create-track.argument.name=ãã©ãã¯å
luckperms.usage.delete-track.description=ãã©ãã¯ã®åé¤
luckperms.usage.delete-track.argument.name=ãã©ãã¯å
luckperms.usage.list-tracks.description=ãã©ãããã©ã¼ã ä¸ã®ãã¹ã¦ã®ãã©ãã¯ã®ä¸è¦§
luckperms.usage.user-info.description=ã¦ã¼ã¶ã¼ã«é¢ããæå ±ãè¡¨ç¤ºãã
luckperms.usage.user-switchprimarygroup.description=ã¦ã¼ã¶ã¼ã®ãã©ã¤ããªã°ã«ã¼ããåãæ¿ãã
luckperms.usage.user-switchprimarygroup.argument.group=ã°ã«ã¼ãã®åãæ¿ãå
luckperms.usage.user-promote.description=ã¦ã¼ã¶ã¼ããã©ãã¯ã«å¾ã£ã¦ææ ¼ããã
luckperms.usage.user-promote.argument.track=ã¦ã¼ã¶ã¼ãææ ¼ãããããã®ãã©ãã¯
luckperms.usage.user-promote.argument.context=ã¦ã¼ã¶ã¼ãææ ¼ãããã³ã³ãã­ã¹ã
luckperms.usage.user-promote.argument.dont-add-to-first=ã¦ã¼ã¶ã¼ããã©ãã¯ä¸ã«ããå ´åã®ã¿ææ ¼ããã
luckperms.usage.user-demote.description=ã¦ã¼ã¶ã¼ããã©ãã¯ã«å¾ã£ã¦éæ ¼ããã
luckperms.usage.user-demote.argument.track=ã¦ã¼ã¶ã¼ãéæ ¼ãããããã®ãã©ãã¯
luckperms.usage.user-demote.argument.context=ã¦ã¼ã¶ã¼ãéæ ¼ãããã³ã³ãã­ã¹ã
luckperms.usage.user-demote.argument.dont-remove-from-first=ã¦ã¼ã¶ã¼ãæåã®ã°ã«ã¼ãããåé¤ãããªãããã«ãã
luckperms.usage.user-clone.description=ã¦ã¼ã¶ã¼ãè¤è£½ãã
luckperms.usage.user-clone.argument.user=è¤è£½åã®ã¦ã¼ã¶ã¼ã®ååã¾ãã¯ UUID
luckperms.usage.group-info.description=ã°ã«ã¼ãã«ã¤ãã¦ã®æå ±ãè¡¨ç¤ºãã
luckperms.usage.group-listmembers.description=ãã®ã°ã«ã¼ãããç¶æ¿ãã¦ããã¦ã¼ã¶ã¼ãã°ã«ã¼ããè¡¨ç¤ºãã
luckperms.usage.group-listmembers.argument.page=è¡¨ç¤ºãããã¼ã¸
luckperms.usage.group-setweight.description=ã°ã«ã¼ãã®ã¦ã§ã¤ããè¨­å®ãã
luckperms.usage.group-setweight.argument.weight=è¨­å®ããã¦ã§ã¤ã
luckperms.usage.group-set-display-name.description=ã°ã«ã¼ãã®è¡¨ç¤ºåãè¨­å®ãã
luckperms.usage.group-set-display-name.argument.name=è¨­å®ããåå
luckperms.usage.group-set-display-name.argument.context=ååãè¨­å®ããã³ã³ãã­ã¹ã
luckperms.usage.group-rename.description=ã°ã«ã¼ãåãå¤æ´ãã
luckperms.usage.group-rename.argument.name=æ°ããåå
luckperms.usage.group-clone.description=ã°ã«ã¼ãã®è¤è£½
luckperms.usage.group-clone.argument.name=è¤è£½åã®ã°ã«ã¼ãã®åå
luckperms.usage.holder-editor.description=Web æ¨©éã¨ãã£ã¿ã¼ãéã
luckperms.usage.holder-showtracks.description=ãªãã¸ã§ã¯ããå±ãããã©ãã¯ã®ä¸è¦§ãè¡¨ç¤ºãã
luckperms.usage.holder-clear.description=ãã¹ã¦ã®æ¨©éãè¦ªãã¡ã¿ãåé¤ãã
luckperms.usage.holder-clear.argument.context=ãã£ã«ã¿ãªã³ã°ããã³ã³ãã­ã¹ã
luckperms.usage.permission.description=æ¨©éãå¤æ´ãã
luckperms.usage.parent.description=ç¶æ¿ãå¤æ´ãã
luckperms.usage.meta.description=ã¡ã¿ãã¼ã¿ã®å¤ãå¤æ´ãã
luckperms.usage.permission-info.description=ãªãã¸ã§ã¯ããæã¤æ¨©éãã¼ãã®ä¸è¦§ãè¡¨ç¤ºãã
luckperms.usage.permission-info.argument.page=è¡¨ç¤ºãããã¼ã¸
luckperms.usage.permission-info.argument.sort-mode=ã¨ã³ããªã®ã½ã¼ãæ¹æ³
luckperms.usage.permission-set.description=ãªãã¸ã§ã¯ãã«æ¨©éãè¨­å®ãã
luckperms.usage.permission-set.argument.node=è¨­å®ããæ¨©éãã¼ã
luckperms.usage.permission-set.argument.value=ãã¼ãã®å¤
luckperms.usage.permission-set.argument.context=æ¨©éãè¿½å ããã³ã³ãã­ã¹ã
luckperms.usage.permission-unset.description=ãªãã¸ã§ã¯ãããæ¨©éãè§£é¤ãã
luckperms.usage.permission-unset.argument.node=è§£é¤ããæ¨©éãã¼ã
luckperms.usage.permission-unset.argument.context=æ¨©éãè§£é¤ããã³ã³ãã­ã¹ã
luckperms.usage.permission-settemp.description=ãªãã¸ã§ã¯ãã«æ¨©éãä¸æçã«è¨­å®ãã
luckperms.usage.permission-settemp.argument.node=è¨­å®ããæ¨©éãã¼ã
luckperms.usage.permission-settemp.argument.value=ãã¼ãã®å¤
luckperms.usage.permission-settemp.argument.duration=æ¨©éãã¼ãã®æå¹æé
luckperms.usage.permission-settemp.argument.temporary-modifier=ä¸æçãªæ¨©éã®é©ç¨æ¹æ³
luckperms.usage.permission-settemp.argument.context=æ¨©éãè¿½å ããã³ã³ãã­ã¹ã
luckperms.usage.permission-unsettemp.description=ãªãã¸ã§ã¯ãããä¸æçãªæ¨©éãè§£é¤ãã
luckperms.usage.permission-unsettemp.argument.node=è§£é¤ããæ¨©éãã¼ã
luckperms.usage.permission-unsettemp.argument.duration=æ¸ç®ããæé
luckperms.usage.permission-unsettemp.argument.context=æ¨©éãè§£é¤ããã³ã³ãã­ã¹ã
luckperms.usage.permission-check.description=ãªãã¸ã§ã¯ãã«ç¹å®ã®æ¨©éãã¼ãããããç¢ºèªãã
luckperms.usage.permission-check.argument.node=ç¢ºèªããæ¨©éãã¼ã
luckperms.usage.permission-clear.description=ãã¹ã¦ã®æ¨©éãæ¶å»ãã
luckperms.usage.permission-clear.argument.context=ãã£ã«ã¿ãªã³ã°ããã³ã³ãã­ã¹ã
luckperms.usage.parent-info.description=ãã®ãªãã¸ã§ã¯ããç¶æ¿ããã°ã«ã¼ãã®ä¸è¦§
luckperms.usage.parent-info.argument.page=è¡¨ç¤ºãããã¼ã¸
luckperms.usage.parent-info.argument.sort-mode=ã¨ã³ããªã®ã½ã¼ãæ¹æ³
luckperms.usage.parent-set.description=ãªãã¸ã§ã¯ããæ¢ã«ç¶æ¿ãã¦ããä»ã®ãã¹ã¦ã®ã°ã«ã¼ããåé¤ããæå®ããã°ã«ã¼ãã«è¿½å ãã¾ã
luckperms.usage.parent-set.argument.group=è¨­å®ããã°ã«ã¼ã
luckperms.usage.parent-set.argument.context=ã°ã«ã¼ããè¨­å®ããã³ã³ãã­ã¹ã
luckperms.usage.parent-add.description=ãªãã¸ã§ã¯ããæ¨©éãç¶æ¿ããä»ã®ã°ã«ã¼ããè¨­å®ãã
luckperms.usage.parent-add.argument.group=ç¶æ¿åã®ã°ã«ã¼ã
luckperms.usage.parent-add.argument.context=ã°ã«ã¼ããç¶æ¿ããã³ã³ãã­ã¹ã
luckperms.usage.parent-remove.description=ä»¥åè¨­å®ããç¶æ¿ã«ã¼ã«ãåé¤ãã
luckperms.usage.parent-remove.argument.group=åé¤ããã°ã«ã¼ã
luckperms.usage.parent-remove.argument.context=ã°ã«ã¼ããåé¤ããã³ã³ãã­ã¹ã
luckperms.usage.parent-set-track.description=æå®ããããã©ãã¯ä¸ãããªãã¸ã§ã¯ããç¶æ¿ãããã¹ã¦ã®ã°ã«ã¼ããåé¤ããæå®ããããã©ãã¯ã«è¿½å ãã¾ã
luckperms.usage.parent-set-track.argument.track=è¨­å®ãããã©ãã¯
luckperms.usage.parent-set-track.argument.group=è¨­å®ããã°ã«ã¼ããã¾ãã¯æå®ããããã©ãã¯ä¸ã§ã®ã°ã«ã¼ãã®ä½ç½®ã«é¢é£ããçªå·
luckperms.usage.parent-set-track.argument.context=ã°ã«ã¼ããè¨­å®ããã³ã³ãã­ã¹ã
luckperms.usage.parent-add-temp.description=ãªãã¸ã§ã¯ããæ¨©éãä¸æçã«ç¶æ¿ããä»ã®ã°ã«ã¼ããè¨­å®ãã
luckperms.usage.parent-add-temp.argument.group=ç¶æ¿åã®ã°ã«ã¼ã
luckperms.usage.parent-add-temp.argument.duration=ã°ã«ã¼ãã®ã¡ã³ãã¼ã§ããæé
luckperms.usage.parent-add-temp.argument.temporary-modifier=ä¸æçãªæ¨©éã®é©ç¨æ¹æ³
luckperms.usage.parent-add-temp.argument.context=ã°ã«ã¼ããç¶æ¿ããã³ã³ãã­ã¹ã
luckperms.usage.parent-remove-temp.description=ä»¥åè¨­å®ããä¸æçãªç¶æ¿ã«ã¼ã«ãåé¤ãã
luckperms.usage.parent-remove-temp.argument.group=åé¤ããã°ã«ã¼ã
luckperms.usage.parent-remove-temp.argument.duration=æ¸ç®ããæé
luckperms.usage.parent-remove-temp.argument.context=ã°ã«ã¼ããåé¤ããã³ã³ãã­ã¹ã
luckperms.usage.parent-clear.description=ãã¹ã¦ã®è¦ªãæ¶å»ãã
luckperms.usage.parent-clear.argument.context=ãã£ã«ã¿ãªã³ã°ããã³ã³ãã­ã¹ã
luckperms.usage.parent-clear-track.description=æå®ãããã©ãã¯ä¸ã®ãã¹ã¦ã®è¦ªãæ¶å»ãã
luckperms.usage.parent-clear-track.argument.track=æ¶å»ãããã©ãã¯
luckperms.usage.parent-clear-track.argument.context=ãã£ã«ã¿ãªã³ã°ããã³ã³ãã­ã¹ã
luckperms.usage.meta-info.description=ãã¹ã¦ã®ãã£ããã¡ã¿ãè¡¨ç¤ºãã
luckperms.usage.meta-set.description=ã¡ã¿å¤ãè¨­å®ãã
luckperms.usage.meta-set.argument.key=è¨­å®ããã­ã¼
luckperms.usage.meta-set.argument.value=è¨­å®ããå¤
luckperms.usage.meta-set.argument.context=ã¡ã¿ãè¿½å ããã³ã³ãã­ã¹ã
luckperms.usage.meta-unset.description=ã¡ã¿å¤ãè§£é¤ãã
luckperms.usage.meta-unset.argument.key=è§£é¤ããã­ã¼
luckperms.usage.meta-unset.argument.context=ã¡ã¿ãè§£é¤ããã³ã³ãã­ã¹ã
luckperms.usage.meta-settemp.description=ä¸æçãªã¡ã¿å¤ãè¨­å®ãã
luckperms.usage.meta-settemp.argument.key=è¨­å®ããã­ã¼
luckperms.usage.meta-settemp.argument.value=è¨­å®ããå¤
luckperms.usage.meta-settemp.argument.duration=ã¡ã¿å¤ã®æå¹æé
luckperms.usage.meta-settemp.argument.context=ã¡ã¿ãè¿½å ããã³ã³ãã­ã¹ã
luckperms.usage.meta-unsettemp.description=ä¸æçãªã¡ã¿å¤ãè§£é¤ãã
luckperms.usage.meta-unsettemp.argument.key=è§£é¤ããã­ã¼
luckperms.usage.meta-unsettemp.argument.context=ã¡ã¿ãè§£é¤ããã³ã³ãã­ã¹ã
luckperms.usage.meta-addprefix.description=ãã¬ãã£ãã¯ã¹ãè¿½å 
luckperms.usage.meta-addprefix.argument.priority=è¿½å ãããã¬ãã£ãã¯ã¹ã®åªååº¦
luckperms.usage.meta-addprefix.argument.prefix=ãã¬ãã£ãã¯ã¹ã®æå­å
luckperms.usage.meta-addprefix.argument.context=ãã¬ãã£ãã¯ã¹ãè¿½å ããã³ã³ãã­ã¹ã
luckperms.usage.meta-addsuffix.description=ãµãã£ãã¯ã¹ãè¿½å ãã
luckperms.usage.meta-addsuffix.argument.priority=è¿½å ãããµãã£ãã¯ã¹ã®åªååº¦
luckperms.usage.meta-addsuffix.argument.suffix=ãµãã£ãã¯ã¹ã®æå­å
luckperms.usage.meta-addsuffix.argument.context=ãµãã£ãã¯ã¹ãè¿½å ããã³ã³ãã­ã¹ã
luckperms.usage.meta-setprefix.description=ãã¬ãã£ãã¯ã¹ãã»ãããã
luckperms.usage.meta-setprefix.argument.priority=ã»ãããããã¬ãã£ãã¯ã¹ã®åªååº¦
luckperms.usage.meta-setprefix.argument.prefix=ãã¬ãã£ãã¯ã¹ã®æå­å
luckperms.usage.meta-setprefix.argument.context=ãã¬ãã£ãã¯ã¹ãã»ããããã³ã³ãã­ã¹ã
luckperms.usage.meta-setsuffix.description=ãµãã£ãã¯ã¹ãã»ãããã
luckperms.usage.meta-setsuffix.argument.priority=ã»ãããããµãã£ãã¯ã¹ã®åªååº¦
luckperms.usage.meta-setsuffix.argument.suffix=ãµãã£ãã¯ã¹ã®æå­å
luckperms.usage.meta-setsuffix.argument.context=ãµãã£ãã¯ã¹ãã»ããããã³ã³ãã­ã¹ã
luckperms.usage.meta-removeprefix.description=ãã¬ãã£ãã¯ã¹ãåé¤ãã
luckperms.usage.meta-removeprefix.argument.priority=åé¤ãããã¬ãã£ãã¯ã¹ã®åªååº¦
luckperms.usage.meta-removeprefix.argument.prefix=ãã¬ãã£ãã¯ã¹ã®æå­å
luckperms.usage.meta-removeprefix.argument.context=ãã¬ãã£ãã¯ã¹ãåé¤ããã³ã³ãã­ã¹ã
luckperms.usage.meta-removesuffix.description=ãµãã£ãã¯ã¹ãåé¤ãã
luckperms.usage.meta-removesuffix.argument.priority=åé¤ãããµãã£ãã¯ã¹ã®åªååº¦
luckperms.usage.meta-removesuffix.argument.suffix=ãµãã£ãã¯ã¹ã®æå­å
luckperms.usage.meta-removesuffix.argument.context=ãµãã£ãã¯ã¹ãåé¤ããã³ã³ãã­ã¹ã
luckperms.usage.meta-addtemp-prefix.description=ãã¬ãã£ãã¯ã¹ãä¸æçã«è¿½å ãã
luckperms.usage.meta-addtemp-prefix.argument.priority=è¿½å ãããã¬ãã£ãã¯ã¹ã®åªååº¦
luckperms.usage.meta-addtemp-prefix.argument.prefix=ãã¬ãã£ãã¯ã¹ã®æå­å
luckperms.usage.meta-addtemp-prefix.argument.duration=ãã¬ãã£ãã¯ã¹ã®æå¹æé
luckperms.usage.meta-addtemp-prefix.argument.context=ãã¬ãã£ãã¯ã¹ãè¿½å ããã³ã³ãã­ã¹ã
luckperms.usage.meta-addtemp-suffix.description=ãµãã£ãã¯ã¹ãä¸æçã«è¿½å ãã
luckperms.usage.meta-addtemp-suffix.argument.priority=è¿½å ãããµãã£ãã¯ã¹ã®åªååº¦
luckperms.usage.meta-addtemp-suffix.argument.suffix=ãµãã£ãã¯ã¹ã®æå­å
luckperms.usage.meta-addtemp-suffix.argument.duration=ãµãã£ãã¯ã¹ã®æå¹æé
luckperms.usage.meta-addtemp-suffix.argument.context=ãµãã£ãã¯ã¹ãè¿½å ããã³ã³ãã­ã¹ã
luckperms.usage.meta-settemp-prefix.description=ä¸æçãªãã¬ãã£ãã¯ã¹ãã»ãããã
luckperms.usage.meta-settemp-prefix.argument.priority=ã»ãããããã¬ãã£ãã¯ã¹ã®åªååº¦
luckperms.usage.meta-settemp-prefix.argument.prefix=ãã¬ãã£ãã¯ã¹ã®æå­å
luckperms.usage.meta-settemp-prefix.argument.duration=ãã¬ãã£ãã¯ã¹ã®æå¹æé
luckperms.usage.meta-settemp-prefix.argument.context=ãã¬ãã£ãã¯ã¹ãã»ããããã³ã³ãã­ã¹ã
luckperms.usage.meta-settemp-suffix.description=ä¸æçãªãµãã£ãã¯ã¹ãã»ãããã
luckperms.usage.meta-settemp-suffix.argument.priority=ã»ãããããµãã£ãã¯ã¹ã®åªååº¦
luckperms.usage.meta-settemp-suffix.argument.suffix=ãµãã£ãã¯ã¹ã®æå­å
luckperms.usage.meta-settemp-suffix.argument.duration=ãµãã£ãã¯ã¹ã®æå¹æé
luckperms.usage.meta-settemp-suffix.argument.context=ãµãã£ãã¯ã¹ãã»ããããã³ã³ãã­ã¹ã
luckperms.usage.meta-removetemp-prefix.description=ä¸æçãªãã¬ãã£ãã¯ã¹ãåé¤ãã
luckperms.usage.meta-removetemp-prefix.argument.priority=åé¤ãããã¬ãã£ãã¯ã¹ã®åªååº¦
luckperms.usage.meta-removetemp-prefix.argument.prefix=ãã¬ãã£ãã¯ã¹ã®æå­å
luckperms.usage.meta-removetemp-prefix.argument.context=ãã¬ãã£ãã¯ã¹ãåé¤ããã³ã³ãã­ã¹ã
luckperms.usage.meta-removetemp-suffix.description=ä¸æçãªãµãã£ãã¯ã¹ãåé¤ãã
luckperms.usage.meta-removetemp-suffix.argument.priority=åé¤ãããµãã£ãã¯ã¹ã®åªååº¦
luckperms.usage.meta-removetemp-suffix.argument.suffix=ãµãã£ãã¯ã¹ã®æå­å
luckperms.usage.meta-removetemp-suffix.argument.context=ãµãã£ãã¯ã¹ãåé¤ããã³ã³ãã­ã¹ã
luckperms.usage.meta-clear.description=ãã¹ã¦ã®ã¡ã¿ãæ¶å»ãã
luckperms.usage.meta-clear.argument.type=åé¤ããã¡ã¿ã®ç¨®é¡
luckperms.usage.meta-clear.argument.context=ãã£ã«ã¿ãªã³ã°ããã³ã³ãã­ã¹ã
luckperms.usage.track-info.description=ãã©ãã¯ã«ã¤ãã¦ã®æå ±ãè¡¨ç¤ºãã
luckperms.usage.track-editor.description=Web æ¨©éã¨ãã£ã¿ã¼ãéã
luckperms.usage.track-append.description=ãã©ãã¯ã®æ«å°¾ã«ã°ã«ã¼ããè¿½å ãã
luckperms.usage.track-append.argument.group=è¿½å ããã°ã«ã¼ã
luckperms.usage.track-insert.description=ãã©ãã¯ä¸ã®æå®ä½ç½®ã«ã°ã«ã¼ããæ¿å¥ãã¾ãã
luckperms.usage.track-insert.argument.group=æ¿å¥ããã°ã«ã¼ã
luckperms.usage.track-insert.argument.position=ã°ã«ã¼ãã®æ¿å¥ä½ç½® (ãã©ãã¯ä¸ã®æåã®ä½ç½®ã¯ 1)
luckperms.usage.track-remove.description=ãã©ãã¯ããã°ã«ã¼ããåé¤ãã
luckperms.usage.track-remove.argument.group=åé¤ããã°ã«ã¼ã
luckperms.usage.track-clear.description=ãã©ãã¯ãããã¹ã¦ã®ã°ã«ã¼ããåé¤ãã
luckperms.usage.track-rename.description=ãã©ãã¯åãå¤æ´ãã
luckperms.usage.track-rename.argument.name=æ°ããåå
luckperms.usage.track-clone.description=ãã©ãã¯ã®è¤è£½
luckperms.usage.track-clone.argument.name=è¤è£½åã®ãã©ãã¯ã®åå
luckperms.usage.log-recent.description=æè¿ã®æä½ãè¡¨ç¤ºãã
luckperms.usage.log-recent.argument.user=ãã£ã«ã¿ãªã³ã°ããã¦ã¼ã¶ã¼ã®ååã¾ãã¯ UUID
luckperms.usage.log-recent.argument.page=è¡¨ç¤ºãããã¼ã¸ã®çªå·
luckperms.usage.log-search.description=ã¨ã³ããªã®ã­ã°ãæ¤ç´¢ãã
luckperms.usage.log-search.argument.query=æ¤ç´¢ããã¯ã¨ãª
luckperms.usage.log-search.argument.page=è¡¨ç¤ºãããã¼ã¸ã®çªå·
luckperms.usage.log-notify.description=ã­ã°éç¥ã®åãæ¿ã
luckperms.usage.log-notify.argument.toggle=on ã¾ãã¯ off
luckperms.usage.log-user-history.description=ã¦ã¼ã¶ã¼ã®å±¥æ­´ãè¡¨ç¤ºãã
luckperms.usage.log-user-history.argument.user=ã¦ã¼ã¶ã¼ã®ååã¾ãã¯ UUID
luckperms.usage.log-user-history.argument.page=è¡¨ç¤ºãããã¼ã¸ã®çªå·
luckperms.usage.log-group-history.description=ã°ã«ã¼ãã®å±¥æ­´ãè¡¨ç¤ºãã
luckperms.usage.log-group-history.argument.group=ã°ã«ã¼ãå
luckperms.usage.log-group-history.argument.page=è¡¨ç¤ºãããã¼ã¸ã®çªå·
luckperms.usage.log-track-history.description=ãã©ãã¯ã®å±¥æ­´ãè¡¨ç¤ºãã
luckperms.usage.log-track-history.argument.track=ãã©ãã¯å
luckperms.usage.log-track-history.argument.page=è¡¨ç¤ºãããã¼ã¸ã®çªå·
luckperms.usage.sponge.description=è¿½å ã® Sponge ãã¼ã¿ãç·¨éãã
luckperms.usage.sponge.argument.collection=ã¯ã¨ãªããã³ã¬ã¯ã·ã§ã³
luckperms.usage.sponge.argument.subject=å¤æ´ãã Subject
luckperms.usage.sponge-permission-info.description=Subject ã®æ¨©éã«ã¤ãã¦ã®æå ±ãè¡¨ç¤ºãã
luckperms.usage.sponge-permission-info.argument.contexts=ãã£ã«ã¿ãªã³ã°ããã³ã³ãã­ã¹ã
luckperms.usage.sponge-permission-set.description=Subject ã«æ¨©éãè¨­å®ãã
luckperms.usage.sponge-permission-set.argument.node=è¨­å®ããæ¨©éãã¼ã
luckperms.usage.sponge-permission-set.argument.tristate=è¨­å®ããæ¨©éã®å¤
luckperms.usage.sponge-permission-set.argument.contexts=æ¨©éãè¨­å®ããã³ã³ãã­ã¹ã
luckperms.usage.sponge-permission-clear.description=Subject ã®æ¨©éãæ¶å»ãã
luckperms.usage.sponge-permission-clear.argument.contexts=æ¨©éãæ¶å»ããã³ã³ãã­ã¹ã
luckperms.usage.sponge-parent-info.description=Subject ã®è¦ªã«ã¤ãã¦ã®æå ±ãè¡¨ç¤ºãã
luckperms.usage.sponge-parent-info.argument.contexts=ãã£ã«ã¿ãªã³ã°ããã³ã³ãã­ã¹ã
luckperms.usage.sponge-parent-add.description=Subject ã«è¦ªãè¿½å ãã
luckperms.usage.sponge-parent-add.argument.collection=è¦ª Subject ãå­å¨ãã Subject ã®ã³ã¬ã¯ã·ã§ã³
luckperms.usage.sponge-parent-add.argument.subject=è¦ª Subject ã®åå
luckperms.usage.sponge-parent-add.argument.contexts=è¦ªãè¿½å ããã³ã³ãã­ã¹ã
luckperms.usage.sponge-parent-remove.description=Subject ããè¦ªãåé¤ãã
luckperms.usage.sponge-parent-remove.argument.collection=è¦ª Subject ãå­å¨ãã Subject ã®ã³ã¬ã¯ã·ã§ã³
luckperms.usage.sponge-parent-remove.argument.subject=è¦ª Subject ã®åå
luckperms.usage.sponge-parent-remove.argument.contexts=è¦ªãåé¤ããã³ã³ãã­ã¹ã
luckperms.usage.sponge-parent-clear.description=Subject ã®è¦ªãæ¶å»ãã
luckperms.usage.sponge-parent-clear.argument.contexts=è¦ªãæ¶å»ããã³ã³ãã­ã¹ã
luckperms.usage.sponge-option-info.description=Subject ã®ãªãã·ã§ã³ã®æå ±ãè¡¨ç¤ºãã
luckperms.usage.sponge-option-info.argument.contexts=ãã£ã«ã¿ãªã³ã°ããã³ã³ãã­ã¹ã
luckperms.usage.sponge-option-set.description=Subject ã®ãªãã·ã§ã³ãè¨­å®ãã
luckperms.usage.sponge-option-set.argument.key=è¨­å®ããã­ã¼
luckperms.usage.sponge-option-set.argument.value=ã­ã¼ã«è¨­å®ããå¤
luckperms.usage.sponge-option-set.argument.contexts=ãªãã·ã§ã³ãè¨­å®ããã³ã³ãã­ã¹ã
luckperms.usage.sponge-option-unset.description=Subject ãããªãã·ã§ã³ãè§£é¤ãã
luckperms.usage.sponge-option-unset.argument.key=è§£é¤ããã­ã¼
luckperms.usage.sponge-option-unset.argument.contexts=ãªãã·ã§ã³ãè§£é¤ããã³ã³ãã­ã¹ã
luckperms.usage.sponge-option-clear.description=Subject ãããªãã·ã§ã³ãæ¶å»ãã
luckperms.usage.sponge-option-clear.argument.contexts=ãªãã·ã§ã³ãæ¶å»ããã³ã³ãã­ã¹ã
