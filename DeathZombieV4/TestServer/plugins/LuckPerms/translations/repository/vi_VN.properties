luckperms.logs.actionlog-prefix=NHáº¬T KÃ
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=XUáº¤T
luckperms.commandsystem.available-commands=DÃ¹ng {0} Äá» xem mÃ£ lá»nh cÃ³ sáºµn
luckperms.commandsystem.command-not-recognised=Lá»nh khÃ´ng ÄÆ°á»£c ghi nháº­n
luckperms.commandsystem.no-permission=Báº¡n khÃ´ng cÃ³ quyá»n Äá» thá»±c hiá»n lá»nh nÃ y\!
luckperms.commandsystem.no-permission-subcommands=Báº¡n khÃ´ng cÃ³ quyá»n Äá» thá»±c hiá»n mÃ£ lá»nh con nÃ y
luckperms.commandsystem.already-executing-command=Má»t lá»nh khÃ¡c Äang ÄÆ°á»£c thá»±c thi, Äang chá» lá»nh ÄÆ°á»£c hoÃ n thÃ nh...
luckperms.commandsystem.usage.sub-commands-header=Lá»nh con
luckperms.commandsystem.usage.usage-header=Sá»­ dá»¥ng lá»nh
luckperms.commandsystem.usage.arguments-header=Tham sá»
luckperms.first-time.no-permissions-setup=CÃ³ váº» nhÆ° chÆ°a cÃ³ quyá»n nÃ o ÄÆ°á»£c thiáº¿t láº­p\!
luckperms.first-time.use-console-to-give-access=TrÆ°á»c khi cÃ³ thá» sá»­ dá»¥ng báº¥t ká»³ lá»nh LuckPerms nÃ o trong trÃ² chÆ¡i, báº¡n cáº§n sá»­ dá»¥ng báº£ng Äiá»u khiá»n Äá» cáº¥p quyá»n truy cáº­p cho mÃ¬nh
luckperms.first-time.console-command-prompt=Má» báº£ng Äiá»u khiá»n cá»§a báº¡n vÃ  cháº¡y
luckperms.first-time.next-step=Sau khi hoÃ n thÃ nh viá»c nÃ y, báº¡n cÃ³ thá» báº¯t Äáº§u xÃ¡c Äá»nh cÃ¡c nhiá»m vá»¥ cáº¥p quyá»n vÃ  nhÃ³m cá»§a mÃ¬nh
luckperms.first-time.wiki-prompt=KhÃ´ng biáº¿t báº¯t Äáº§u tá»« ÄÃ¢u? Xem táº¡i ÄÃ¢y\: {0}
luckperms.login.try-again=Vui loÌng thÆ°Ì laÌ£i sau
luckperms.login.loading-database-error=ÄÃ£ xáº£y ra lá»i trong dá»¯ liá»u khi táº£i dá»¯ liá»u quyá»n cáº¥p
luckperms.login.server-admin-check-console-errors=Náº¿u báº¡n lÃ  quáº£n trá» viÃªn cá»§a mÃ¡y chá»§, vui lÃ²ng kiá»m tra báº£ng Äiá»u khiá»n xem cÃ³ lá»i nÃ o khÃ´ng
luckperms.login.server-admin-check-console-info=Vui lÃ²ng kiá»m tra báº£ng Äiá»u khiá»n cá»§a mÃ¡y chá»§ Äá» biáº¿t thÃªm thÃ´ng tin
luckperms.login.data-not-loaded-at-pre=Dá»¯ liá»u quyá»n cho ngÆ°á»i dÃ¹ng cá»§a báº¡n khÃ´ng ÄÆ°á»£c xá»­ lÃ­ trong giai Äoáº¡n ÄÄng nháº­p trÆ°á»c
luckperms.login.unable-to-continue=khÃ´ng thá» tiáº¿p tá»¥c ÄÆ°á»£c
luckperms.login.craftbukkit-offline-mode-error=Äiá»u nÃ y cÃ³ thá» do xung Äá»t giá»¯a CraftBukkit vÃ  cháº¿ Äá» trá»±c tuyáº¿n
luckperms.login.unexpected-error=ÄÃ£ xáº£y ra lá»i khÃ´ng mong muá»n trong khi thiáº¿t láº­p dá»¯ liá»u quyá»n cáº¥p cá»§a báº¡n
luckperms.opsystem.disabled=Há» thá»ng Vanilla OP bá» táº¯t trÃªn mÃ¡y chá»§ nÃ y
luckperms.opsystem.sponge-warning=Xin lÆ°u Ã½ ráº±ng tráº¡ng thÃ¡i cá»§a MÃ¡y chá»§ khÃ´ng áº£nh hÆ°á»ng Äáº¿n viá»c kiá»m tra quyá»n cá»§a Sponge khi plugin cáº¥p quyá»n ÄÆ°á»£c cÃ i Äáº·t, nÃªn báº¡n pháº£i chá»nh sá»­a dá»¯ liá»u ngÆ°á»i dÃ¹ng trá»±c tiáº¿p
luckperms.duration.unit.years.plural={0} nÄm
luckperms.duration.unit.years.singular={0} nÄm
luckperms.duration.unit.years.short={0}y
luckperms.duration.unit.months.plural={0} thÃ¡ng
luckperms.duration.unit.months.singular={0} thÃ¡ng
luckperms.duration.unit.months.short={0}mo
luckperms.duration.unit.weeks.plural={0} tuáº§n
luckperms.duration.unit.weeks.singular={0} tuáº§n
luckperms.duration.unit.weeks.short={0}w
luckperms.duration.unit.days.plural={0} ngÃ y
luckperms.duration.unit.days.singular={0} ngÃ y
luckperms.duration.unit.days.short={0}d
luckperms.duration.unit.hours.plural={0} giá»
luckperms.duration.unit.hours.singular={0} giá»
luckperms.duration.unit.hours.short={0}h
luckperms.duration.unit.minutes.plural={0} phÃºt
luckperms.duration.unit.minutes.singular={0} phÃºt
luckperms.duration.unit.minutes.short={0}m
luckperms.duration.unit.seconds.plural={0} giÃ¢y
luckperms.duration.unit.seconds.singular={0} giÃ¢y
luckperms.duration.unit.seconds.short={0}s
luckperms.duration.since=cÃ¡ch ÄÃ¢y {0}
luckperms.command.misc.invalid-code=MÃ£ khÃ´ng há»£p lá»
luckperms.command.misc.response-code-key=mÃ£ pháº£n há»i
luckperms.command.misc.error-message-key=tin-nháº¯n
luckperms.command.misc.bytebin-unable-to-communicate=KhÃ´ng thá» tÆ°Æ¡ng tÃ¡c vá»i bytebin
luckperms.command.misc.webapp-unable-to-communicate=KhÃ´ng thá» tÆ°Æ¡ng tÃ¡c vá»i á»©ng dá»¥ng web
luckperms.command.misc.check-console-for-errors=Vui lÃ²ng nhÃ¬n vÃ o báº£ng Äiá»u khiá»n Äá» xem lá»i
luckperms.command.misc.file-must-be-in-data=Tá»p tin {0} pháº£i lÃ  tá»p trá»±c tiáº¿p vá»i dá»¯ liá»u thÆ° má»¥c
luckperms.command.misc.wait-to-finish=Vui lÃ²ng Äá»£i cho Äáº¿n khi hoÃ n thÃ nh rá»i thá»­ láº¡i
luckperms.command.misc.invalid-priority=MÃ£ Æ°u tiÃªn {0} khÃ´ng há»£p lá»
luckperms.command.misc.expected-number=Cáº§n má»t con sá»
luckperms.command.misc.date-parse-error=KhÃ´ng thá» phÃ¢n tÃ­ch cÃº phÃ¡p {0}
luckperms.command.misc.date-in-past-error=Báº¡n khÃ´ng thá» Äáº·t thá»i gian trong quÃ¡ khá»©\!
luckperms.command.misc.page=trang {0} trong {1}
luckperms.command.misc.page-entries={0} má»¥c
luckperms.command.misc.none=KhÃ´ng cÃ³
luckperms.command.misc.loading.error.unexpected=ÄÃ£ xáº£y ra lá»i khÃ´ng mong muá»n
luckperms.command.misc.loading.error.user=NgÆ°á»i dÃ¹ng khÃ´ng xá»­ lÃ­ ÄÆ°á»£c
luckperms.command.misc.loading.error.user-specific=KhÃ´ng thá» xá»­ lÃ­ ngÆ°á»i dÃ¹ng xÃ¡c Äá»nh {0}
luckperms.command.misc.loading.error.user-not-found=KhÃ´ng thá» tÃ¬m tháº¥y ngÆ°á»i dÃ¹ng cÃ³ tÃªn {0}
luckperms.command.misc.loading.error.user-save-error=CÃ³ má»t lá»i xáº£y ra khi lÆ°u dá»¯ liá»u ngÆ°á»i dÃ¹ng cho {0}
luckperms.command.misc.loading.error.user-not-online=NgÆ°á»i dÃ¹ng {0} khÃ´ng á» trá»±c tuyáº¿n
luckperms.command.misc.loading.error.user-invalid={0} khÃ´ng pháº£i lÃ  tÃªn ngÆ°á»i dÃ¹ng hoáº·c UUID há»£p lá»
luckperms.command.misc.loading.error.user-not-uuid=NgÆ°á»i dÃ¹ng xÃ¡c Äá»nh {0} cÃ³ UUID khÃ´ng há»£p lá»
luckperms.command.misc.loading.error.group=NhÃ³m chÆ°a ÄÆ°á»£c xá»­ lÃ­
luckperms.command.misc.loading.error.all-groups=KhÃ´ng thá» xá»­ lÃ­ táº¥t cáº£ cÃ¡c nhÃ³m
luckperms.command.misc.loading.error.group-not-found=KhÃ´ng thá» tÃ¬m tháº¥y nhÃ³m cÃ³ tÃªn {0}
luckperms.command.misc.loading.error.group-save-error=CÃ³ má»t lá»i xáº£y ra khi lÆ°u dá»¯ liá»u nhÃ³m cho {0}
luckperms.command.misc.loading.error.group-invalid={0} khÃ´ng pháº£i lÃ  má»t tÃªn nhÃ³m há»£p lá»
luckperms.command.misc.loading.error.track=Thang chÆ°a ÄÆ°á»£c xá»­ lÃ­
luckperms.command.misc.loading.error.all-tracks=KhÃ´ng thá» xá»­ lÃ­ táº¥t cáº£ cÃ¡c thang
luckperms.command.misc.loading.error.track-not-found=KhÃ´ng thá» tÃ¬m tháº¥y thang cÃ³ tÃªn {0}
luckperms.command.misc.loading.error.track-save-error=CÃ³ má»t lá»i ÄÃ£ xáº£y ra khi lÆ°u dá»¯ liá»u thang cho {0}
luckperms.command.misc.loading.error.track-invalid={0} khÃ´ng pháº£i lÃ  má»t tÃªn thang há»£p lá»
luckperms.command.editor.no-match=KhÃ´ng thá» má» trÃ¬nh chá»nh sá»­a, khÃ´ng cÃ³ gÃ¬ trÃ¹ng vá»i kiá»u mong muá»n
luckperms.command.editor.start=Äang chuáº©n bá» báº£n chá»nh sá»­a má»i, xin Äá»£i...
luckperms.command.editor.url=Nháº¥p vÃ o ÄÆ°á»ng dáº«n phÃ­a dÆ°á»i Äá» má» trÃ¬nh chá»nh sá»­a
luckperms.command.editor.unable-to-communicate=KhÃ´ng thá» káº¿t ná»i vá»i báº£n chá»nh sá»­a
luckperms.command.editor.apply-edits.success=Dá»¯ liá»u trong trÃ¬nh chá»nh sá»­a web ÄÆ°á»£c Ã¡p dá»¥ng cho {0} {1} thÃ nh cÃ´ng
luckperms.command.editor.apply-edits.success-summary={0} {1} vÃ  {2} {3}
luckperms.command.editor.apply-edits.success.additions=pháº§n bá» sung
luckperms.command.editor.apply-edits.success.additions-singular=pháº§n bá» sung
luckperms.command.editor.apply-edits.success.deletions=má»¥c xÃ³a
luckperms.command.editor.apply-edits.success.deletions-singular=má»¥c xÃ³a
luckperms.command.editor.apply-edits.no-changes=KhÃ´ng cÃ³ thay Äá»i nÃ o ÄÆ°á»£c Ã¡p dá»¥ng tá»« trÃ¬nh chá»nh sá»­a web, dá»¯ liá»u tráº£ vá» khÃ´ng chá»©a báº¥t ká»³ chá»nh sá»­a nÃ o
luckperms.command.editor.apply-edits.unknown-type=KhÃ´ng thá» Ã¡p dá»¥ng báº£n chá»nh sá»­a cho loáº¡i Äá»i tÆ°á»£ng ÄÆ°á»£c xÃ¡c Äá»nh
luckperms.command.editor.apply-edits.unable-to-read=KhÃ´ng thá» Äá»c dá»¯ liá»u báº±ng mÃ£ ÄÃ£ cho
luckperms.command.search.searching.permission=Äang tÃ¬m ngÆ°á»i dÃ¹ng vÃ  nhÃ³m vá»i {0}
luckperms.command.search.searching.inherit=Äang tÃ¬m kiáº¿m ngÆ°á»i dÃ¹ng vÃ  nhÃ³m káº¿ thá»«a tá»« {0}
luckperms.command.search.result=ÄÃ£ tÃ¬m tháº¥y {0} má»¥c tá»« {1} ngÆ°á»i dÃ¹ng vÃ  {2} nhÃ³m
luckperms.command.search.result.default-notice=LÆ°u Ã½\: khi tÃ¬m kiáº¿m thÃ nh viÃªn cá»§a nhÃ³m máº·c Äá»nh, nhá»¯ng ngÆ°á»i chÆ¡i ngoáº¡i tuyáº¿n khÃ´ng cÃ³ quyá»n khÃ¡c sáº½ khÃ´ng ÄÆ°á»£c hiá»n thá»\!
luckperms.command.search.showing-users=Hiá»n thá» cÃ¡c má»¥c ngÆ°á»i dÃ¹ng
luckperms.command.search.showing-groups=Hiá»n thá» cÃ¡c má»¥c nhÃ³m
luckperms.command.tree.start=Äang táº¡o quyá»n thang báº­c, vui lÃ²ng Äá»£i...
luckperms.command.tree.empty=KhÃ´ng thá» táº¡o thang báº­c, nÃªn khÃ´ng tÃ¬m tháº¥y káº¿t quáº£
luckperms.command.tree.url=Quyá»n thang báº­c URL
luckperms.command.verbose.invalid-filter={0} khÃ´ng pháº£i lÃ  má»t bá» lá»c há»£p lá»
luckperms.command.verbose.enabled=Äang ghi bá» nháº­t kÃ½ {0} cho cÃ¡c sÃ©c khá»p vá»i {1}
luckperms.command.verbose.command-exec=Äang buá»c {0} pháº£i thá»±c hiá»n lá»nh {1} vÃ  bÃ¡o cÃ¡o táº¥t cáº£ cÃ¡c láº§n kiá»m tra ÄÃ£ thá»±c hiá»n...
luckperms.command.verbose.off=Bá» ghi nháº­t kÃ½ {0}
luckperms.command.verbose.command-exec-complete=Thá»±c hiá»n lá»nh hoÃ n táº¥t
luckperms.command.verbose.command.no-checks=Lá»nh dÆ°á»£c thá»±c thi thÃ nh cÃ´ng, nhÆ°ng khÃ´ng cÃ³ quyá»n nÃ o ÄÆ°á»£c kiá»m tra
luckperms.command.verbose.command.possibly-async=ÄÃ¢y cÃ³ thá» lÃ  do plugin cháº¡y lá»nh á» ná»n sau (async)
luckperms.command.verbose.command.try-again-manually=Báº¡n váº«n cÃ³ thá» dÃ¹ng bá» nháº­t kÃ½ thá»§ cÃ´ng Äá» phÃ¡t hiá»n cÃ¡c viá»c kiá»m tra nhÆ° sau
luckperms.command.verbose.enabled-recording=Äang ghi láº¡i chi tiáº¿t {0} Äá» kiá»m tra trÃ¹ng khá»p vá»i {1}
luckperms.command.verbose.uploading=Ghi nháº­t kÃ½ {0}, táº£i lÃªn káº¿t quáº£...
luckperms.command.verbose.url=Káº¿t quáº£ bá» nháº­t kÃ­ URL
luckperms.command.verbose.enabled-term=kÃ­ch hoáº¡t
luckperms.command.verbose.disabled-term=táº¯t
luckperms.command.verbose.query-any=Báº¤T KÃ
luckperms.command.info.running-plugin=Ãang cháº¡y
luckperms.command.info.platform-key=Ná»n táº£ng
luckperms.command.info.server-brand-key=ThÆ°Æ¡ng hiá»u mÃ¡y chá»§
luckperms.command.info.server-version-key=PhiÃªn báº£n mÃ¡y chá»§
luckperms.command.info.storage-key=Bá» nhá»
luckperms.command.info.storage-type-key=Dáº¡ng
luckperms.command.info.storage.meta.split-types-key=Dáº¡ng
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=ÄÃ£ káº¿t ná»i
luckperms.command.info.storage.meta.file-size-key=KÃ­ch thÆ°á»c táº­p tin
luckperms.command.info.extensions-key=Tiá»n Ã­ch má» rá»ng
luckperms.command.info.messaging-key=Tin nháº¯n
luckperms.command.info.instance-key=Äá»i tÆ°á»£ng
luckperms.command.info.static-contexts-key=TrÆ°á»ng chá»t
luckperms.command.info.online-players-key=NgÆ°á»i chÆ¡i trá»±c tuyáº¿n
luckperms.command.info.online-players-unique={0} Äa dáº¡ng
luckperms.command.info.uptime-key=Thá»i gian hoáº¡t Äá»ng
luckperms.command.info.local-data-key=Dá»¯ liá»u cá»¥c bá»
luckperms.command.info.local-data={0} ngÆ°á»i dÃ¹ng, {1} nhÃ³m, {2} thang
luckperms.command.generic.create.success={0} ÄÃ£ ÄÆ°á»£c táº¡o thÃ nh cÃ´ng
luckperms.command.generic.create.error=ÄÃ£ xáº£y ra lá»i khi táº¡o {0}
luckperms.command.generic.create.error-already-exists={0} ÄÃ£ tá»n táº¡i\!
luckperms.command.generic.delete.success={0} ÄÃ£ ÄÆ°á»£c xÃ³a thÃ nh cÃ´ng
luckperms.command.generic.delete.error=ÄÃ£ xáº£y ra lá»i khi xÃ³a {0}
luckperms.command.generic.delete.error-doesnt-exist={0} khÃ´ng tá»n táº¡i\!
luckperms.command.generic.rename.success={0} ÄÃ£ ÄÆ°á»£c thay Äá»i tÃªn Äáº¿n {1}
luckperms.command.generic.clone.success={0} ÄÃ£ ÄÆ°á»£c sao lÆ°u Äáº¿n {1}
luckperms.command.generic.info.parent.title=NhÃ³m chÃ­nh
luckperms.command.generic.info.parent.temporary-title=NhÃ³m chÃ­nh táº¡m thá»i
luckperms.command.generic.info.expires-in=háº¿t háº¡n trong
luckperms.command.generic.info.inherited-from=thá»«a hÆ°á»ng tá»«
luckperms.command.generic.info.inherited-from-self=báº£n thÃ¢n
luckperms.command.generic.show-tracks.title=Thang cá»§a {0}
luckperms.command.generic.show-tracks.empty={0} khÃ´ng á» trong thang nÃ o
luckperms.command.generic.clear.node-removed={0} máº©u ÄÆ°á»£c gá»¡ bá»
luckperms.command.generic.clear.node-removed-singular={0} máº©u ÄÆ°á»£c gá»¡ bá»
luckperms.command.generic.clear={0} máº©u ÄÆ°á»£c gá»¡ bá» trong trÆ°á»ng {1}
luckperms.command.generic.permission.info.title=Quyá»n cáº¥p cá»§a {0}
luckperms.command.generic.permission.info.empty={0} khÃ´ng cÃ³ quyá»n nÃ o ÄÆ°á»£c sáº¯p Äáº·t
luckperms.command.generic.permission.info.click-to-remove=Nháº¥p Äá» gá»¡ máº©u khá»i {0}
luckperms.command.generic.permission.check.info.title=ThÃ´ng tin quyá»n vá» {0}
luckperms.command.generic.permission.check.info.directly={0} cÃ³ {1} ÄÆ°á»£c Äáº·t vá» {2} á» trÆ°á»ng {3}
luckperms.command.generic.permission.check.info.inherited={0} káº¿ thá»«a tá»« {1} ÄÆ°á»£c Äáº·t vá» {2} tá»« {3} á» trÆ°á»ng {4}
luckperms.command.generic.permission.check.info.not-directly={0} cÃ³ {1} chÆ°a ÄÆ°á»£c Äáº·t
luckperms.command.generic.permission.check.info.not-inherited={0} khÃ´ng káº¿ thá»«a {1}
luckperms.command.generic.permission.check.result.title=Kiá»m tra quyá»n cá»§a {0}
luckperms.command.generic.permission.check.result.result-key=Káº¿t quáº£
luckperms.command.generic.permission.check.result.processor-key=Bá» xá»­ lÃ½
luckperms.command.generic.permission.check.result.cause-key=LÃ½ do
luckperms.command.generic.permission.check.result.context-key=TrÆ°á»ng
luckperms.command.generic.permission.set=Äáº·t {0} thÃ nh {1} cho {2} trong trÆ°á»ng {3}
luckperms.command.generic.permission.already-has={0} ÄÃ£ cÃ³ {1} Äáº·t trong trÆ°á»ng {2}
luckperms.command.generic.permission.set-temp=ÄÃ£ Äáº·t {0} thÃ nh {1} cho {2} trong khoáº£ng thá»i gian {3} trong trÆ°á»ng {4}
luckperms.command.generic.permission.already-has-temp={0} ÄÃ£ cÃ³ {1} Äáº·t táº¡m thá»i trong trÆ°á»ng {2}
luckperms.command.generic.permission.unset=ÄÃ£ gá»¡ {0} cho {1} trong trÆ°á»ng {2}
luckperms.command.generic.permission.doesnt-have={0} khÃ´ng cÃ³ {1} Äáº·t trong trÆ°á»ng {2}
luckperms.command.generic.permission.unset-temp=ÄÃ£ gá»¡ quyá»n cáº¥p táº¡m thá»i {0} cho {1} trong trÆ°á»ng {2}
luckperms.command.generic.permission.subtract=ÄÃ£ Äáº·t {0} thÃ nh {1} cho {2} trong khoáº£ng thá»i gian {3} trong trÆ°á»ng {4}, tháº¥p hÆ¡n {5}
luckperms.command.generic.permission.doesnt-have-temp={0} khÃ´ng cÃ³ {1} Äáº·t táº¡m thá»i trong trÆ°á»ng {2}
luckperms.command.generic.permission.clear=CÃ¡c quyá»n cá»§a {0} ÄÃ£ ÄÆ°á»£c xÃ³a khá»i trÆ°á»ng {1}
luckperms.command.generic.parent.info.title=Chá»t chÃ­nh cá»§a {0}
luckperms.command.generic.parent.info.empty={0} khÃ´ng cÃ³ chá»t chÃ­nh sáº¯p Äáº·t
luckperms.command.generic.parent.info.click-to-remove=Nháº¥p Äá» gá»¡ chá»t chÃ­nh nÃ y khá»i {0}
luckperms.command.generic.parent.add={0} bÃ¢y giá» ÄÆ°á»£c thá»«a hÆ°á»ng quyá»n cáº¥p tá»« {1} trong trÆ°á»ng {2}
luckperms.command.generic.parent.add-temp={0} bÃ¢y giá» ÄÆ°á»£c thá»«a hÆ°á»ng quyá»n cáº¥p tá»« {1} trong khoáº£ng thá»i gian {2} trong trÆ°á»ng {3}
luckperms.command.generic.parent.set={0} ÄÃ£ cÃ³ nhÃ³m chÃ­nh cÃ³ sáºµn bá» xÃ³a, nÃªn ÄÆ°á»£c thá»«a hÆ°á»ng tá»« {1} trong trÆ°á»ng {2}
luckperms.command.generic.parent.set-track={0} ÄÃ£ cÃ³ nhÃ³m chÃ­nh cÃ³ sáºµn trong thang {1} bá» xÃ³a, nÃªn giá» ÄÆ°á»£c thá»«a hÆ°á»ng tá»« {2} trong trÆ°á»ng {3}
luckperms.command.generic.parent.remove={0} khÃ´ng cÃ²n thá»«a hÆ°á»ng quyá»n cáº¥p tá»« {1} trong trÆ°á»ng {2}
luckperms.command.generic.parent.remove-temp={0} khÃ´ng cÃ²n thá»«a hÆ°á»ng quyá»n cáº¥p táº¡m thá»i tá»« {1} trong trÆ°á»ng {2}
luckperms.command.generic.parent.subtract={0} sáº½ ÄÆ°á»£c thá»«a hÆ°á»ng quyá»n cáº¥p tá»« {1} trong khoáº£ng thá»i gian {2} in trong trÆ°á»ng {3}, tháº¥p hÆ¡n má»t khoáº£ng {4}
luckperms.command.generic.parent.clear=Chá»t chÃ­nh cá»§a {0} ÄÃ£ ÄÆ°á»£c xÃ³a khá»i trÆ°á»ng {1}
luckperms.command.generic.parent.clear-track=Chá»t chÃ­nh cá»§a {0} trong thang {1} ÄÃ£ bá» xÃ³a trong trÆ°á»ng {2}
luckperms.command.generic.parent.already-inherits={0} ÄÃ£ ÄÆ°á»£c káº¿ thá»«a tá»« {1} trong trÆ°á»ng {2}
luckperms.command.generic.parent.doesnt-inherit={0} khÃ´ng thá»«a hÆ°á»ng tá»« {1} trong trÆ°á»ng {2}
luckperms.command.generic.parent.already-temp-inherits={0} khÃ´ng cÃ²n thá»«a hÆ°á»ng quyá»n cáº¥p táº¡m thá»i tá»« {1} trong trÆ°á»ng {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} táº¡m thá»i khÃ´ng káº¿ thá»«a tá»« {1} trong trÆ°á»ng {2}
luckperms.command.generic.chat-meta.info.title-prefix=Tiá»n tá» cá»§a {0}
luckperms.command.generic.chat-meta.info.title-suffix=Háº­u tá» cá»§a {0}
luckperms.command.generic.chat-meta.info.none-prefix={0} khÃ´ng cÃ³ tiá»n tá» nÃ o
luckperms.command.generic.chat-meta.info.none-suffix={0} khÃ´ng cÃ³ háº­u tá» nÃ o
luckperms.command.generic.chat-meta.info.click-to-remove=Nháº¥p Äá» gá»¡ {0} nÃ y khá»i {1}
luckperms.command.generic.chat-meta.already-has={0} ÄÃ£ cÃ³ {1} {2} Äáº·t á» má»©c Æ°u tiÃªn {3} trong trÆ°á»ng {4}
luckperms.command.generic.chat-meta.already-has-temp={0} ÄÃ£ cÃ³ {1} {2} Äáº·t á» má»©c Æ°u tiÃªn táº¡m thá»i {3} trong trÆ°á»ng {4}
luckperms.command.generic.chat-meta.doesnt-have={0} khÃ´ng cÃ³ {1} {2} Äáº·t á» má»©c Æ°u tiÃªn {3} trong trÆ°á»ng {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} khÃ´ng cÃ³ {1} {2} Äáº·t á» má»©c Æ°u tiÃªn táº¡m thá»i {3} trong trÆ°á»ng {4}
luckperms.command.generic.chat-meta.add={0} ÄÃ£ cÃ³ {1} {2} Äáº·t á» má»©c Æ°u tiÃªn {3} trong trÆ°á»ng {4}
luckperms.command.generic.chat-meta.add-temp={0} ÄÃ£ cÃ³ {1} {2} Äáº·t á» má»©c Æ°u tiÃªn {3} trong khoáº£ng thá»i gian {4} cá»§a trÆ°á»ng {5}
luckperms.command.generic.chat-meta.remove={0} ÄÃ£ cÃ³ {1} {2} á» má»©c Æ°u tiÃªn {3} bá» xÃ³a trong trÆ°á»ng {4}
luckperms.command.generic.chat-meta.remove-bulk={0} ÄÃ£ cÃ³ táº¥t cáº£ {1} á» má»©c Æ°u tiÃªn {2} bá» xÃ³a trong trÆ°á»ng {3}
luckperms.command.generic.chat-meta.remove-temp={0} ÄÃ£ cÃ³ {1} {2} táº¡m thá»i á» má»©c Æ°u tiÃªn {3} bá» xÃ³a trong trÆ°á»ng {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} ÄÃ£ cÃ³ táº¥t cáº£ {1} táº¡m thá»i á» má»©c Æ°u tiÃªn {2} bá» xÃ³a trong trÆ°á»ng {3}
luckperms.command.generic.meta.info.title=Dá»¯ liá»u Äá»i tÆ°á»£ng cá»§a {0}
luckperms.command.generic.meta.info.none={0} khÃ´ng cÃ³ Äá»i tÆ°á»£ng dá»¯ liá»u nÃ o
luckperms.command.generic.meta.info.click-to-remove=Nháº¥p Äá» xÃ³a máº©u dá»¯ liá»u nÃ y khá»i {0}
luckperms.command.generic.meta.already-has={0} ÄÃ£ cÃ³ khÃ³a dá»¯ liá»u {1} Äáº·t thÃ nh {2} trong trÆ°á»ng {3}
luckperms.command.generic.meta.already-has-temp={0} ÄÃ£ cÃ³ khÃ³a dá»¯ liá»u {1} táº¡m thá»i Äáº·t thÃ nh {2} trong trÆ°á»ng {3}
luckperms.command.generic.meta.doesnt-have={0} khÃ´ng cÃ³ khÃ³a dá»¯ liá»u {1} trong trÆ°á»ng {2}
luckperms.command.generic.meta.doesnt-have-temp={0} khÃ´ng cÃ³ khÃ³a dá»¯ liá»u {1} Äáº·t táº¡m thá»i trong trÆ°á»ng {2}
luckperms.command.generic.meta.set=ÄÃ£ Äáº·t khÃ³a Äá»i tÆ°á»£ng dá»¯ liá»u {0} thÃ nh {1} cho {2} trong trÆ°á»ng {3}
luckperms.command.generic.meta.set-temp=ÄÃ£ Äáº·t khÃ³a meta {0} thÃ nh {1} cho {2} trong khoáº£ng thá»i gian {3} cá»§a trÆ°á»ng {4}
luckperms.command.generic.meta.unset=ÄÃ£ gá»¡ khÃ³a Äá»i tÆ°á»£ng dá»¯ liá»u {0} cho {1} trong trÆ°á»ng {2}
luckperms.command.generic.meta.unset-temp=ÄÃ£ gá»¡ khÃ³a Äá»i tÆ°á»£ng dá»¯ liá»u táº¡m thá»i {0} cho {1} trong trÆ°á»ng {2}
luckperms.command.generic.meta.clear=Äá»i tÆ°á»£ng dá»¯ liá»u {0} cÃ³ loáº¡i trÃ¹ng khá»p {1} bá» xÃ³a trong trÆ°á»ng {2}
luckperms.command.generic.contextual-data.title=Dá»¯ liá»u cá»§a trÆ°á»ng
luckperms.command.generic.contextual-data.mode.key=cháº¿ Äá»
luckperms.command.generic.contextual-data.mode.server=mÃ¡y chá»§
luckperms.command.generic.contextual-data.mode.active-player=cÃ¡c ngÆ°á»i chÆ¡i hoáº¡t Äá»ng
luckperms.command.generic.contextual-data.contexts-key=TrÆ°á»ng
luckperms.command.generic.contextual-data.prefix-key=Tiá»n tá»
luckperms.command.generic.contextual-data.suffix-key=Háº­u tá»
luckperms.command.generic.contextual-data.primary-group-key=NhÃ³m chÃ­nh
luckperms.command.generic.contextual-data.meta-key=Dá»¯ liá»u Äá»i tÆ°á»£ng
luckperms.command.generic.contextual-data.null-result=KhÃ´ng cÃ³
luckperms.command.user.info.title=ThÃ´ng tin ngÆ°á»i dÃ¹ng
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=dáº¡ng
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=ngoáº¡i tuyáº¿n
luckperms.command.user.info.status-key=Tráº¡ng thÃ¡i
luckperms.command.user.info.status.online=Trá»±c tuyáº¿n
luckperms.command.user.info.status.offline=Ngoáº¡i tuyáº¿n
luckperms.command.user.removegroup.error-primary=Báº¡n khÃ´ng thá» xÃ³a ngÆ°á»i dÃ¹ng khá»i nhÃ³m chÃ­nh cá»§a há»
luckperms.command.user.primarygroup.not-member={0} chÆ°a pháº£i lÃ  thÃ nh viÃªn cá»§a {1}, Äang thÃªm há» bÃ¢y giá»
luckperms.command.user.primarygroup.already-has={0} ÄÃ£ Äáº·t {1} lÃ m nhÃ³m chÃ­nh cá»§a há»
luckperms.command.user.primarygroup.warn-option=Cáº£nh bÃ¡o\: PhÆ°Æ¡ng phÃ¡p tÃ­nh toÃ¡n nhÃ³m chÃ­nh Äang ÄÆ°á»£c mÃ¡y chá»§ nÃ y ({0}) sá»­ dá»¥ng cÃ³ thá» khÃ´ng pháº£n láº¡i thay Äá»i nÃ y
luckperms.command.user.primarygroup.set=NhÃ³m chÃ­nh cá»§a {0} ÄÃ£ ÄÆ°á»£c Äáº·t thÃ nh {1}
luckperms.command.user.track.error-not-contain-group={0} chÆ°a cÃ³ trong báº¥t ká»³ nhÃ³m nÃ o trÃªn {1}
luckperms.command.user.track.unsure-which-track=Khi khÃ´ng cháº¯c cháº¯n nÃªn sá»­ dá»¥ng thang nÃ o, vui lÃ²ng chá» Äá»nh nÃ³ trong tham biáº¿n
luckperms.command.user.track.missing-group-advice=CÃ³ thá» táº¡o nhÃ³m hoáº·c xÃ³a nhÃ³m khá»i thang vÃ  thá»­ láº¡i
luckperms.command.user.promote.added-to-first={0} khÃ´ng cÃ³ trong báº¥t ká»³ nhÃ³m nÃ o trÃªn {1}, vÃ¬ váº­y chÃºng ÄÃ£ ÄÆ°á»£c thÃªm vÃ o nhÃ³m Äáº§u tiÃªn, {2} trong trÆ°á»ng {3}
luckperms.command.user.promote.not-on-track={0} khÃ´ng thuá»c báº¥t ká»³ nhÃ³m nÃ o trÃªn {1}, vÃ¬ váº­y ÄÃ£ khÃ´ng ÄÆ°á»£c lÃªn cáº¥p
luckperms.command.user.promote.success=ÄÃ£ lÃªn cáº¥p {0} theo thang {1} tá»« {2} Äáº¿n {3} trong trÆ°á»ng {4}
luckperms.command.user.promote.end-of-track=ÄÃ£ Äáº¿n Äiá»m káº¿t thÃºc cá»§a thang {0}, khÃ´ng thá» lÃªn cáº¥p {1} ÄÆ°á»£c ná»¯a
luckperms.command.user.promote.next-group-deleted=NhÃ³m tiáº¿p theo trÃªn thang {0} khÃ´ng cÃ²n tá»n táº¡i ná»¯a
luckperms.command.user.promote.unable-to-promote=KhÃ´ng thá» thÄng cáº¥p ngÆ°á»i dÃ¹ng
luckperms.command.user.demote.success=ÄÃ£ xuá»ng cáº¥p {0} theo thang {1} tá»« {2} Äáº¿n {3} trong trÆ°á»ng {4}
luckperms.command.user.demote.end-of-track=ÄÃ£ Äáº¿n Äiá»m káº¿t thÃºc cá»§a thang {0}, vÃ¬ váº­y {1} ÄÃ£ bá» xÃ³a khá»i {2}
luckperms.command.user.demote.end-of-track-not-removed=ÄÃ£ Äáº¿n Äiá»m káº¿t thÃºc cá»§a thang {0}, nhÆ°ng {1} khÃ´ng bá» xÃ³a khá»i nhÃ³m Äáº§u tiÃªn
luckperms.command.user.demote.previous-group-deleted=NhÃ³m trÆ°á»c trÃªn thang {0} khÃ´ng cÃ²n tá»n táº¡i ná»¯a
luckperms.command.user.demote.unable-to-demote=KhÃ´ng thá» xuá»ng cáº¥p ngÆ°á»i dÃ¹ng
luckperms.command.group.list.title=NhÃ³m
luckperms.command.group.delete.not-default=Báº¡n khÃ´ng thá» xÃ³a nhÃ³m máº·c Äá»nh
luckperms.command.group.info.title=ThÃ´ng tin NhÃ³m
luckperms.command.group.info.display-name-key=TÃªn hiá»n thá»
luckperms.command.group.info.weight-key=Trá»ng sá»
luckperms.command.group.setweight.set=Äáº·t trá»ng sá» thÃ nh {0} cho nhÃ³m {1}
luckperms.command.group.setdisplayname.doesnt-have={0} khÃ´ng cÃ³ tÃªn hiá»n thá» nÃ o ÄÆ°á»£c Äáº·t
luckperms.command.group.setdisplayname.already-has={0} ÄÃ£ cÃ³ tÃªn hiá»n thá» ÄÆ°á»£c Äáº·t lÃ  {1}
luckperms.command.group.setdisplayname.already-in-use=TÃªn hiá»n thá» {0} ÄÃ£ ÄÆ°á»£c {1} sá»­ dá»¥ng
luckperms.command.group.setdisplayname.set=Äáº·t tÃªn hiá»n thá» thÃ nh {0} cho nhÃ³m {1} trong trÆ°á»ng {2}
luckperms.command.group.setdisplayname.removed=ÄÃ£ xÃ³a tÃªn hiá»n thá» cho nhÃ³m {0} trong trÆ°á»ng {1}
luckperms.command.track.list.title=Thang
luckperms.command.track.path.empty=KhÃ´ng cÃ³
luckperms.command.track.info.showing-track=Hiá»n thá» thang
luckperms.command.track.info.path-property=ÄÆ°á»ng dáº«n
luckperms.command.track.clear=NhÃ³m thang cá»§a {0} ÄÃ£ bá» xÃ³a
luckperms.command.track.append.success=NhÃ³m {0} ÄÃ£ ÄÆ°á»£c thÃªm vÃ o thang {1}
luckperms.command.track.insert.success=NhÃ³m {0} ÄÃ£ ÄÆ°á»£c chÃ¨n vÃ o trong thang {1} táº¡i vá» trÃ­ {2}
luckperms.command.track.insert.error-number=Äang cáº§n con sá» nhÆ°ng láº¡i nháº­n ÄÆ°á»£c\: {0}
luckperms.command.track.insert.error-invalid-pos=KhÃ´ng thá» chÃ¨n á» vá» trÃ­ {0}
luckperms.command.track.insert.error-invalid-pos-reason=vá» trÃ­ khÃ´ng há»£p lá»
luckperms.command.track.remove.success=NhÃ³m {0} ÄÃ£ ÄÆ°á»£c gá»¡ bá» tá»« thang {1}
luckperms.command.track.error-empty=KhÃ´ng thá» sá»­ dá»¥ng {0} vÃ¬ nÃ³ trá»ng hoáº·c chá» chá»©a má»t nhÃ³m
luckperms.command.track.error-multiple-groups={0} lÃ  thÃ nh viÃªn cá»§a nhiá»u nhÃ³m trÃªn thang nÃ y
luckperms.command.track.error-ambiguous=KhÃ´ng thá» xÃ¡c Äá»nh vá» trÃ­ ÄÆ°á»£c
luckperms.command.track.already-contains={0} ÄÃ£ chá»©a {1}
luckperms.command.track.doesnt-contain={0} khÃ´ng chá»©a {1}
luckperms.command.log.load-error=Nháº­t kÃ­ khÃ´ng thá» xá»­ lÃ­ ÄÆ°á»£c
luckperms.command.log.invalid-page=Sá» trang khÃ´ng há»£p lá»
luckperms.command.log.invalid-page-range=Vui lÃ²ng nháº­p giÃ¡ trá» tá»« {0} Äáº¿n {1}
luckperms.command.log.empty=KhÃ´ng cÃ³ má»¥c nháº­t kÃ½ nÃ o Äá» hiá»n thá»
luckperms.command.log.notify.error-console=KhÃ´ng thá» chuyá»n Äá»i cháº¿ Äá» thÃ´ng bÃ¡o cho báº£ng Äiá»u khiá»n
luckperms.command.log.notify.enabled-term=ÄÃ£ báº­t
luckperms.command.log.notify.disabled-term=ÄÃ£ táº¯t
luckperms.command.log.notify.changed-state={0} káº¿t quáº£ nháº­t kÃ­
luckperms.command.log.notify.already-on=Báº¡n ÄÃ£ ÄÆ°á»£c nháº­n ÄÆ°á»£c thÃ´ng bÃ¡o
luckperms.command.log.notify.already-off=Báº¡n hiá»n khÃ´ng nháº­n ÄÆ°á»£c thÃ´ng bÃ¡o nÃ o
luckperms.command.log.notify.invalid-state=Tráº¡ng thÃ¡i khÃ´ng xÃ¡c Äá»nh. Äang cáº§n {0} hoáº·c {1}
luckperms.command.log.show.search=Hiá»n thá» cÃ¡c hÃ nh Äá»ng gáº§n ÄÃ¢y cho máº«u {0}
luckperms.command.log.show.recent=Äáº¡ng xem hÃ nh Äá»ng gáº§n ÄÃ¢y
luckperms.command.log.show.by=Äáº¡ng xem hÃ nh Äá»ng gáº§n ÄÃ¢y trong {0}
luckperms.command.log.show.history=Hiá»n thá» lá»ch sá»­ cho {0} {1}
luckperms.command.export.error-term=Lá»i
luckperms.command.export.already-running=CÃ³ má»t quÃ¡ trÃ¬nh xuáº¥t hiá»n Äang cháº¡y
luckperms.command.export.file.already-exists=Táº­p tin {0} ÄÃ£ tá»n táº¡i
luckperms.command.export.file.not-writable=Táº­p tin {0} khÃ´ng thá» ghi ÄÆ°á»£c
luckperms.command.export.file.success=Xuáº¥t thÃ nh cÃ´ng sang {0}
luckperms.command.export.file-unexpected-error-writing=ÄÃ£ cÃ³ lá»i khÃ´ng mong muá»n khi Äang ghi vÃ o táº­p tin
luckperms.command.export.web.export-code=Xuáº¥t mÃ£
luckperms.command.export.web.import-command-description=Sá»­ dá»¥ng lá»nh sau Äá» nháº­p vÃ o
luckperms.command.import.term=Nháº­p
luckperms.command.import.error-term=Lá»i
luckperms.command.import.already-running=CÃ³ má»t quÃ¡ trÃ¬nh nháº­p vÃ o hiá»n Äang cháº¡y
luckperms.command.import.file.doesnt-exist=Táº­p tin {0} khÃ´ng tá»n táº¡i
luckperms.command.import.file.not-readable=Táº­p tin {0} khÃ´ng thá» Äá»c ÄÆ°á»£c
luckperms.command.import.file.unexpected-error-reading=ÄÃ£ cÃ³ lá»i khÃ´ng xÃ¡c Äá»nh khi Äá»c tá»« táº­p tin nháº­p vÃ o
luckperms.command.import.file.correct-format=nÃ³ cÃ³ pháº£i lÃ  Äá»nh dáº¡ng chÃ­nh xÃ¡c khÃ´ng?
luckperms.command.import.web.unable-to-read=KhÃ´ng thá» Äá»c dá»¯ liá»u báº±ng mÃ£ ÄÃ£ cho
luckperms.command.import.progress.percent={0}% ÄÃ£ hoÃ n thÃ nh
luckperms.command.import.progress.operations={0}/{1} hoáº¡t Äá»ng hoÃ n táº¥t
luckperms.command.import.starting=Äang báº¯t Äáº§u quÃ¡ trÃ¬nh nháº­p vÃ o
luckperms.command.import.completed=HOÃN THÃNH
luckperms.command.import.duration=máº¥t {0} giÃ¢y
luckperms.command.bulkupdate.must-use-console=Lá»nh cáº­p nháº­t hÃ ng loáº¡t chá» cÃ³ thá» ÄÆ°á»£c sá»­ dá»¥ng tá»« báº£ng Äiá»u khiá»n
luckperms.command.bulkupdate.invalid-data-type=Loáº¡i khÃ´ng há»£p lá», Äang cáº§n {0}
luckperms.command.bulkupdate.invalid-constraint=RÃ ng buá»c {0} khÃ´ng há»£p lá»
luckperms.command.bulkupdate.invalid-constraint-format=Sá»± cÆ°á»¡ng Ã©p nÃªn dÆ°á»£c Äáº·t trong Äá»nh dáº¡ng {0}
luckperms.command.bulkupdate.invalid-comparison=TrÃ¬nh hoáº¡t Äá»ng so sÃ¡nh khÃ´ng há»£p lá» {0}
luckperms.command.bulkupdate.invalid-comparison-format=Äang cáº§n má»t trong nhá»¯ng yáº¿u tá» sau\: {0}
luckperms.command.bulkupdate.queued=QuÃ¡ trÃ¬nh cáº­p nháº­t hoáº¡t Äá»ng ÄÃ£ ÄÆ°á»£c xáº¿p vÃ o hÃ ng Äá»£i
luckperms.command.bulkupdate.confirm=Cháº¡y {0} Äá» thá»±c hiá»n cáº­p nháº­t
luckperms.command.bulkupdate.unknown-id=QuÃ¡ trÃ¬nh vá»i id {0} khÃ´ng tá»n táº¡i hoáº·c ÄÃ£ háº¿t háº¡n
luckperms.command.bulkupdate.starting=Äang cháº¡y khá»i cáº­p nháº­t
luckperms.command.bulkupdate.success=Khá»i cáº­p nháº­t ÄÃ£ hoÃ n thÃ nh
luckperms.command.bulkupdate.success.statistics.nodes=Tá»ng sá» máº©u bá» áº£nh hÆ°á»ng
luckperms.command.bulkupdate.success.statistics.users=Tá»ng sá» ngÆ°á»i dÃ¹ng bá» áº£nh hÆ°á»ng
luckperms.command.bulkupdate.success.statistics.groups=Tá»ng sá» nhÃ³m bá» áº£nh hÆ°á»ng
luckperms.command.bulkupdate.failure=Cáº­p nháº­t khá»i khÃ´ng thÃ nh cÃ´ng, hÃ£y kiá»m tra báº£ng Äiá»u khiá»n Äá» tÃ¬m lá»i
luckperms.command.update-task.request=Má»t tÃ¡c vá»¥ cáº­p nháº­t ÄÃ£ ÄÆ°á»£c yÃªu cáº§u, vui lÃ²ng Äá»£i
luckperms.command.update-task.complete=Cáº­p nháº­t tÃ¡c vá»¥ hoÃ n táº¥t
luckperms.command.update-task.push.attempting=Hiá»n Äang cá» gáº¯ng Äáº©y sang cÃ¡c mÃ¡y chá»§ khÃ¡c
luckperms.command.update-task.push.complete=CÃ¡c mÃ¡y chá»§ khÃ¡c ÄÃ£ ÄÆ°á»£c thÃ´ng bÃ¡o qua {0} thÃ nh cÃ´ng
luckperms.command.update-task.push.error=ÄÃ£ cÃ³ lá»i khi Äáº©y cÃ¡c thay Äá»i Äáº¿n cÃ¡c mÃ¡y chá»§ khÃ¡c
luckperms.command.update-task.push.error-not-setup=KhÃ´ng thá» Äáº©y cÃ¡c thay Äá»i Äáº¿n cÃ¡c mÃ¡y chá»§ khÃ¡c vÃ¬ dá»ch vá»¥ tin nháº¯n chÆ°a ÄÆ°á»£c cáº¥u hÃ¬nh
luckperms.command.reload-config.success=Tá»p cáº¥u hÃ¬nh ÄÃ£ ÄÆ°á»£c táº£i láº¡i
luckperms.command.reload-config.restart-note=má»t sá» tÃ¹y chá»n sáº½ chá» Ã¡p dá»¥ng sau khi mÃ¡y chá»§ ÄÃ£ khá»i Äá»ng láº¡i
luckperms.command.translations.searching=Äang tÃ¬m kiáº¿m cÃ¡c báº£n dá»ch cÃ³ sáºµn, vui lÃ²ng Äá»£i...
luckperms.command.translations.searching-error=KhÃ´ng thá» láº¥y danh sÃ¡ch cÃ¡c báº£n dá»ch cÃ³ sáºµn
luckperms.command.translations.installed-translations=Nhá»¯ng báº£n dá»ch ÄÃ£ ÄÆ°á»£c cÃ i
luckperms.command.translations.available-translations=CÃ¡c báº£n dá»ch cÃ³ sáºµn
luckperms.command.translations.percent-translated={0}% ÄÃ£ dá»ch
luckperms.command.translations.translations-by=bá»i
luckperms.command.translations.installing=Äang cÃ i Äáº·t báº£n dá»ch, vui lÃ²ng Äá»£i...
luckperms.command.translations.download-error=KhÃ´ng thá» táº£i xuá»ng báº£n dá»ch cho tiáº¿ng {0}
luckperms.command.translations.installing-specific=Äang cÃ i ngÃ´n ngá»¯ {0}...
luckperms.command.translations.install-complete=CÃ i Äáº·t hoÃ n táº¥t
luckperms.command.translations.download-prompt=Sá»­ dá»¥ng {0} Äá» táº£i xuá»ng vÃ  cÃ i Äáº·t cÃ¡c phiÃªn báº£n cáº­p nháº­t cá»§a cÃ¡c báº£n dá»ch nÃ y do cá»ng Äá»ng cung cáº¥p
luckperms.command.translations.download-override-warning=Xin lÆ°u Ã½ ráº±ng Äiá»u nÃ y sáº½ ghi ÄÃ¨ báº¥t ká»³ thay Äá»i nÃ o báº¡n ÄÃ£ thá»±c hiá»n cho cÃ¡c ngÃ´n ngá»¯ nÃ y
luckperms.usage.user.description=Má»t nhÃ³m cÃ¡c lá»nh Äá» quáº£n lÃ½ ngÆ°á»i dÃ¹ng trong LuckPerms. (''NgÆ°á»i dÃ¹ng'' trong LuckPerms chá» lÃ  má»t ngÆ°á»i chÆ¡i vÃ  cÃ³ thá» tham chiáº¿u Äáº¿n UUID hoáº·c tÃªn ngÆ°á»i dÃ¹ng)
luckperms.usage.group.description=Má»t nhÃ³m cÃ¡c lá»nh Äá» quáº£n lÃ½ cÃ¡c nhÃ³m trong LuckPerms. NhÃ³m chá» lÃ  táº­p há»£p cÃ¡c tÃ¡c vá»¥ quyá»n cÃ³ thá» ÄÆ°á»£c cáº¥p cho ngÆ°á»i dÃ¹ng. CÃ¡c nhÃ³m má»i ÄÆ°á»£c táº¡o báº±ng lá»nh ''creategroup''.
luckperms.usage.track.description=Má»t nhÃ³m cÃ¡c lá»nh Äá» quáº£n lÃ½ cÃ¡c thang trong LuckPerms. Thang báº­c lÃ  má»t táº­p há»£p cÃ¡c nhÃ³m cÃ³ thá»© tá»± cÃ³ thá» ÄÆ°á»£c sá»­ dá»¥ng Äá» xÃ¡c Äá»nh cÃ¡c viá»c lÃªn cáº¥p hay xuá»ng cáº¥p.
luckperms.usage.log.description=Má»t nhÃ³m cÃ¡c lá»nh Äá» quáº£n lÃ½ chá»©c nÄng ghi nháº­t kÃ½ trong LuckPerms.
luckperms.usage.sync.description=Táº£i láº¡i táº¥t cáº£ dá»¯ liá»u tá»« bá» nhá» plugin vÃ o bá» nhá» vÃ  Ã¡p dá»¥ng báº¥t ká»³ thay Äá»i nÃ o ÄÆ°á»£c phÃ¡t hiá»n.
luckperms.usage.info.description=In vÃ o nhá»¯ng thÃ´ng tin chung vá» Äá»i tÆ°á»£ng plugin Äang hoáº¡t Äá»ng.
luckperms.usage.editor.description=Táº¡o trÃ¬nh chá»nh sá»­a web má»i
luckperms.usage.editor.argument.type=cÃ¡c loáº¡i cáº§n táº£i vÃ o trÃ¬nh chá»nh sá»­a. (''táº¥t cáº£'', ''ngÆ°á»i dÃ¹ng'' hoáº·c ''nhÃ³m'')
luckperms.usage.editor.argument.filter=quyá»n Äá» lá»c má»¥c cá»§a ngÆ°á»i dÃ¹ng theo
luckperms.usage.verbose.description=Kiá»m soÃ¡t há» thá»ng giÃ¡m sÃ¡t kiá»m tra quyá»n cá»§a plugin.
luckperms.usage.verbose.argument.action=khi muá»n báº­t/táº¯t viá»c in nháº­t kÃ½ hoáº·c táº£i lÃªn káº¿t quáº£ ÄÃ£ ghi
luckperms.usage.verbose.argument.filter=bá» lá»c Äá» khá»p cÃ¡c má»¥c nháº­p vá»i
luckperms.usage.verbose.argument.commandas=ngÆ°á»i chÆ¡i /lá»nh Äá» cháº¡y
luckperms.usage.tree.description=Táº¡o cháº¿ Äá» xem dáº¡ng thang báº­c (thá»© tá»± danh sÃ¡ch há» thá»ng phÃ¢n cáº¥p) cá»§a táº¥t cáº£ cÃ¡c quyá»n mÃ  LuckPerms ÄÃ£ biáº¿t.
luckperms.usage.tree.argument.scope=gá»c cá»§a thang báº­c. chá» Äá»nh "." Äá» bao gá»m táº¥t cáº£ cÃ¡c quyá»n
luckperms.usage.tree.argument.player=tÃªn cá»§a má»t ngÆ°á»i chÆ¡i trá»±c tuyáº¿n Äá» kiá»m tra
luckperms.usage.search.description=TÃ¬m táº¥t cáº£ ngÆ°á»i dÃ¹ng/nhÃ³m vá»i má»t quyá»n cá»¥ thá»
luckperms.usage.search.argument.permission=quyá»n cáº¥p cho tÃ¬m kiáº¿m
luckperms.usage.search.argument.page=trang Äá» xem
luckperms.usage.network-sync.description=Äá»ng bá» hÃ³a cÃ¡c thay Äá»i vá»i bá» nhá» vÃ  yÃªu cáº§u mÃ  táº¥t cáº£ cÃ¡c mÃ¡y chá»§ khÃ¡c trÃªn máº¡ng káº¿t ná»i Äang thá»±c hiá»n tÆ°Æ¡ng tá»±
luckperms.usage.import.description=Nháº­p dá»¯ liá»u tá»« tá»p xuáº¥t (ÄÃ£ táº¡o trÆ°á»c ÄÃ³)
luckperms.usage.import.argument.file=chá»n tá»p Äá» nháº­p tá»«
luckperms.usage.import.argument.replace=thay tháº¿ dá»¯ liá»u hiá»n cÃ³ thay vÃ¬ káº¿t há»£p
luckperms.usage.import.argument.upload=táº£i dá»¯ liá»u lÃªn tá»« láº§n xuáº¥t trÆ°á»c
luckperms.usage.export.description=Xuáº¥t táº¥t cáº£ dá»¯ liá»u quyá»n sang tá»p ''xuáº¥t''. CÃ³ thá» ÄÆ°á»£c nháº­p láº¡i sau ÄÃ³.
luckperms.usage.export.argument.file=tá»p tin Äá» xuáº¥t sang
luckperms.usage.export.argument.without-users=loáº¡i trá»« ngÆ°á»i dÃ¹ng khá»i quÃ¡ trÃ¬nh xuáº¥t
luckperms.usage.export.argument.without-groups=loáº¡i trá»« nhÃ³m khá»i quÃ¡ trÃ¬nh xuáº¥t
luckperms.usage.export.argument.upload=Táº£i lÃªn táº¥t cáº£ dá»¯ liá»u quyá»n cáº¥p sang trÃ¬nh chá»nh sá»­a web. CÃ³ thá» ÄÆ°á»£c nháº­p láº¡i sau ÄÃ³.
luckperms.usage.reload-config.description=Táº£i láº¡i má»t sá» tÃ¹y chá»n cáº¥u hÃ¬nh
luckperms.usage.bulk-update.description=Thá»±c hiá»n cÃ¡c thay Äá»i khá»i trÃªn táº¥t cáº£ dá»¯ liá»u
luckperms.usage.bulk-update.argument.data-type=loáº¡i dá»¯ liá»u Äang ÄÆ°á»£c thay Äá»i. (''táº¥t cáº£'', ''ngÆ°á»i dÃ¹ng'' hoáº·c ''nhÃ³m'')
luckperms.usage.bulk-update.argument.action=cÃ¡c hÃ nh Äá»ng cáº§n thá»±c hiá»n trÃªn dá»¯ liá»u. (''cáº­p nháº­t'' hoáº·c ''xÃ³a'')
luckperms.usage.bulk-update.argument.action-field=vÃ¹ng Äá» hÃ nh Äá»ng. chá» cáº§n thiáº¿t cho ''cáº­p nháº­t''. (''quyá»n'', ''mÃ¡y chá»§'' hoáº·c ''tháº¿ giá»i'')
luckperms.usage.bulk-update.argument.action-value=giÃ¡ trá» Äá» thay tháº¿. chá» cáº§n thiáº¿t cho ''cáº­p nháº­t''.
luckperms.usage.bulk-update.argument.constraint=cÃ¡c giá»i háº¡n cáº§n thiáº¿t cho báº£n cáº­p nháº­t
luckperms.usage.translations.description=Quáº£n lÃ½ chuyá»n ngá»¯
luckperms.usage.translations.argument.install=lá»nh con Äá» cÃ i Äáº·t báº£n dá»ch
luckperms.usage.apply-edits.description=Ãp dá»¥ng cÃ¡c thay Äá»i quyá»n cáº¥p ÄÆ°á»£c thá»±c hiá»n tá»« trÃ¬nh chá»nh sá»­a web
luckperms.usage.apply-edits.argument.code=mÃ£ duy nháº¥t cho dá»¯ liá»u
luckperms.usage.apply-edits.argument.target=ngÆ°á»i cáº§n ÄÆ°á»£c Ã¡p dá»¥ng dá»¯ liá»u
luckperms.usage.create-group.description=Táº¡o má»t nhÃ³m má»i
luckperms.usage.create-group.argument.name=tÃªn nhÃ³m
luckperms.usage.create-group.argument.weight=trá»ng sá» cá»§a nhÃ³m
luckperms.usage.create-group.argument.display-name=tÃªn hiá»n thá» cá»§a nhÃ³m
luckperms.usage.delete-group.description=XoÃ¡ má»t nhÃ³m
luckperms.usage.delete-group.argument.name=tÃªn nhÃ³m
luckperms.usage.list-groups.description=Danh sÃ¡ch táº¥t cáº£ cÃ¡c nhÃ³m trÃªn ná»n táº£ng
luckperms.usage.create-track.description=Táº¡o má»t thang má»i
luckperms.usage.create-track.argument.name=tÃªn thang
luckperms.usage.delete-track.description=XÃ³a má»t thang
luckperms.usage.delete-track.argument.name=tÃªn thang
luckperms.usage.list-tracks.description=Danh sÃ¡ch táº¥t cáº£ cÃ¡c thang trÃªn ná»n táº£ng
luckperms.usage.user-info.description=Hiá»n thá» thÃ´ng tin vá» ngÆ°á»i dÃ¹ng
luckperms.usage.user-switchprimarygroup.description=Chuyá»n Äá»i nhÃ³m chÃ­nh cá»§a ngÆ°á»i dÃ¹ng
luckperms.usage.user-switchprimarygroup.argument.group=nhÃ³m Äá» chuyá»n sang
luckperms.usage.user-promote.description=ThÄng cáº¥p ngÆ°á»i dÃ¹ng lÃªn má»t thang
luckperms.usage.user-promote.argument.track=thang Äá» thÄng cáº¥p ngÆ°á»i dÃ¹ng lÃªn
luckperms.usage.user-promote.argument.context=trÆ°á»ng Äá» thÄng cáº¥p ngÆ°á»i dÃ¹ng lÃªn
luckperms.usage.user-promote.argument.dont-add-to-first=chá» thÄng cáº¥p ngÆ°á»i dÃ¹ng náº¿u há» ÄÃ£ á» trong thang
luckperms.usage.user-demote.description=Háº¡ cáº¥p ngÆ°á»i dÃ¹ng xuá»ng má»t thang
luckperms.usage.user-demote.argument.track=thang Äá» háº¡ cáº¥p ngÆ°á»i dÃ¹ng xuá»ng
luckperms.usage.user-demote.argument.context=trÆ°á»ng Äá» háº¡ cáº¥p ngÆ°á»i dÃ¹ng xuá»ng
luckperms.usage.user-demote.argument.dont-remove-from-first=ngÄn ngÆ°á»i dÃ¹ng khá»i viá»c bá» xÃ³a khá»i nhÃ³m Äáº§u tiÃªn
luckperms.usage.user-clone.description=Sao chÃ©p ngÆ°á»i dÃ¹ng
luckperms.usage.user-clone.argument.user=tÃªn/uuid cá»§a ngÆ°á»i dÃ¹ng sao chÃ©p vÃ o
luckperms.usage.group-info.description=Cung cáº¥p thÃ´ng tin vá» nhÃ³m
luckperms.usage.group-listmembers.description=Hiá»n thá» nhá»¯ng ngÆ°á»i dÃ¹ng/nhÃ³m káº¿ thá»«a tá»« nhÃ³m nÃ y
luckperms.usage.group-listmembers.argument.page=trang Äá» xem
luckperms.usage.group-setweight.description=Äáº·t trá»ng sá» cá»§a nhÃ³m
luckperms.usage.group-setweight.argument.weight=trá»ng sá» cáº§n Äáº·t
luckperms.usage.group-set-display-name.description=Äáº·t tÃªn hiá»n thá» nhÃ³m
luckperms.usage.group-set-display-name.argument.name=tÃªn Äá» Äáº·t
luckperms.usage.group-set-display-name.argument.context=trÆ°á»ng Äá» Äáº·t tÃªn vÃ o
luckperms.usage.group-rename.description=Äá»i tÃªn nhÃ³m
luckperms.usage.group-rename.argument.name=tÃªn má»i
luckperms.usage.group-clone.description=Sao chÃ©p nhÃ³m
luckperms.usage.group-clone.argument.name=tÃªn cá»§a nhÃ³m Äá» sao chÃ©p vÃ o
luckperms.usage.holder-editor.description=Má» trÃ¬nh chá»nh sá»­a quyá»n web
luckperms.usage.holder-showtracks.description=Liá»t kÃª cÃ¡c thang cá»§a Äá»i tÆ°á»£ng
luckperms.usage.holder-clear.description=XÃ³a bá» táº¥t cáº£ cÃ¡c quyá»n, chÃ­nh chá»§ vÃ  dá»¯ liá»u Äá»i tÆ°á»£ng
luckperms.usage.holder-clear.argument.context=trÆ°á»ng Äá» lá»c theo
luckperms.usage.permission.description=Chá»nh sá»­a quyá»n
luckperms.usage.parent.description=Chá»nh sá»­a káº¿ thá»«a
luckperms.usage.meta.description=Sá»­a giÃ¡ trá» dá»¯ liá»u Äá»i tÆ°á»£ng
luckperms.usage.permission-info.description=Liá»t kÃª cÃ¡c máº©u quyá»n mÃ  Äá»i tÆ°á»£ng cÃ³
luckperms.usage.permission-info.argument.page=trang Äá» xem
luckperms.usage.permission-info.argument.sort-mode=cÃ¡ch sáº¯p xáº¿p cÃ¡c má»¥c nháº­p
luckperms.usage.permission-set.description=Äáº·t quyá»n cáº¥p cho Äá»i tÆ°á»£ng
luckperms.usage.permission-set.argument.node=máº©u quyá»n Äá» thiáº¿t láº­p
luckperms.usage.permission-set.argument.value=giÃ¡ trá» cá»§a máº©u
luckperms.usage.permission-set.argument.context=trÆ°á»ng Äá» thÃªm quyá»n vÃ o
luckperms.usage.permission-unset.description=Bá» quyá»n cho Äá»i tÆ°á»£ng
luckperms.usage.permission-unset.argument.node=máº©u quyá»n Äá» gá»¡ bá»
luckperms.usage.permission-unset.argument.context=trÆ°á»ng Äá» xÃ³a quyá»n vÃ o
luckperms.usage.permission-settemp.description=Äáº·t quyá»n cáº¥p cho Äá»i tÆ°á»£ng táº¡m thá»i
luckperms.usage.permission-settemp.argument.node=máº©u quyá»n Äá» thiáº¿t láº­p
luckperms.usage.permission-settemp.argument.value=giÃ¡ trá» cá»§a máº©u
luckperms.usage.permission-settemp.argument.duration=khoáº£ng thá»i gian cho Äáº¿n khi máº©u quyá»n háº¿t háº¡n
luckperms.usage.permission-settemp.argument.temporary-modifier=quyá»n cáº¥p táº¡m thá»i sáº½ ÄÆ°á»£c Ã¡p dá»¥ng nhÆ° tháº¿ nÃ o
luckperms.usage.permission-settemp.argument.context=trÆ°á»ng Äá» thÃªm quyá»n vÃ o
luckperms.usage.permission-unsettemp.description=Gá»¡ quyá»n cáº¥p táº¡m thá»i cho Äá»i tÆ°á»£ng
luckperms.usage.permission-unsettemp.argument.node=máº©u quyá»n Äá» gá»¡ bá»
luckperms.usage.permission-unsettemp.argument.duration=khoáº£ng thá»i gian Äá» trá»« Äi
luckperms.usage.permission-unsettemp.argument.context=trÆ°á»ng Äá» xÃ³a quyá»n
luckperms.usage.permission-check.description=Kiá»m tra xem Äá»i tÆ°á»£ng cÃ³ má»t máº©u quyá»n nháº¥t Äá»nh hay khÃ´ng
luckperms.usage.permission-check.argument.node=máº©u quyá»n cáº¥p Äá» tÃ¬m kiáº¿m
luckperms.usage.permission-clear.description=XÃ³a táº¥t cáº£ quyá»n
luckperms.usage.permission-clear.argument.context=trÆ°á»ng Äá» lá»c theo
luckperms.usage.parent-info.description=Liá»t kÃª cÃ¡c nhÃ³m mÃ  Äá»i tÆ°á»£ng nÃ y káº¿ thá»«a
luckperms.usage.parent-info.argument.page=trang Äá» xem
luckperms.usage.parent-info.argument.sort-mode=cÃ¡ch sáº¯p xáº¿p cÃ¡c má»¥c nháº­p
luckperms.usage.parent-set.description=Loáº¡i bá» táº¥t cáº£ cÃ¡c nhÃ³m khÃ¡c mÃ  Äá»i tÆ°á»£ng ÄÃ£ káº¿ thá»«a vÃ  thÃªm chÃºng vÃ o nhÃ³m ÄÃ£ cho
luckperms.usage.parent-set.argument.group=nhÃ³m Äá» Äáº·t vÃ o
luckperms.usage.parent-set.argument.context=trÆ°á»ng Äá» Äáº·t nhÃ³m vÃ o
luckperms.usage.parent-add.description=Äáº·t má»t nhÃ³m khÃ¡c cho Äá»i tÆ°á»£ng Äá» káº¿ thá»«a quyá»n tá»«
luckperms.usage.parent-add.argument.group=nhÃ³m Äá» thá»«a káº¿
luckperms.usage.parent-add.argument.context=trÆ°á»ng Äá» thá»«a hÆ°á»ng nhÃ³m vÃ o
luckperms.usage.parent-remove.description=Gá»¡ bá» quy táº¯c káº¿ thá»«a ÄÃ£ Äáº·t trÆ°á»c ÄÃ³
luckperms.usage.parent-remove.argument.group=nhÃ³m Äá» gá»¡ bá»
luckperms.usage.parent-remove.argument.context=trÆ°á»ng Äá» xÃ³a nhÃ³m
luckperms.usage.parent-set-track.description=Loáº¡i bá» táº¥t cáº£ cÃ¡c nhÃ³m khÃ¡c mÃ  Äá»i tÆ°á»£ng ÄÃ£ káº¿ thá»«a tá»« thang ÄÃ£ cho vÃ  thÃªm chÃºng vÃ o nhÃ³m ÄÃ£ cho
luckperms.usage.parent-set-track.argument.track=thang Äá» Äáº·t vÃ o
luckperms.usage.parent-set-track.argument.group=nhÃ³m Äá» Äáº·t hoáº·c má»t con sá» liÃªn quan Äáº¿n vá» trÃ­ cá»§a nhÃ³m trÃªn thang ÄÃ£ cho
luckperms.usage.parent-set-track.argument.context=trÆ°á»ng Äá» Äáº·t nhÃ³m vÃ o
luckperms.usage.parent-add-temp.description=Äáº·t má»t nhÃ³m khÃ¡c cho Äá»i tÆ°á»£ng Äá» káº¿ thá»«a quyá»n cáº¥p táº¡m thá»i
luckperms.usage.parent-add-temp.argument.group=nhÃ³m Äá» thá»«a hÆ°á»ng
luckperms.usage.parent-add-temp.argument.duration=thá»i háº¡n cá»§a thÃ nh viÃªn nhÃ³m
luckperms.usage.parent-add-temp.argument.temporary-modifier=quyá»n cáº¥p táº¡m thá»i nÃªn ÄÆ°á»£c Ã¡p dá»¥ng nhÆ° tháº¿ nÃ o
luckperms.usage.parent-add-temp.argument.context=trÆ°á»ng Äá» thá»«a hÆ°á»ng nhÃ³m vÃ o
luckperms.usage.parent-remove-temp.description=Gá»¡ bá» má»t quy táº¯c káº¿ thá»«a táº¡m thá»i ÄÃ£ Äáº·t trÆ°á»c ÄÃ³
luckperms.usage.parent-remove-temp.argument.group=nhÃ³m Äá» gá»¡ bá»
luckperms.usage.parent-remove-temp.argument.duration=khoáº£ng thá»i gian Äá» trá»« Äi
luckperms.usage.parent-remove-temp.argument.context=trÆ°á»ng Äá» xÃ³a nhÃ³m
luckperms.usage.parent-clear.description=XÃ³a táº¥t cáº£ chÃ­nh chá»§
luckperms.usage.parent-clear.argument.context=trÆ°á»ng Äá» lá»c theo
luckperms.usage.parent-clear-track.description=XÃ³a táº¥t cáº£ chá»§ chÃ­nh trÃªn má»t thang ÄÃ£ cho
luckperms.usage.parent-clear-track.argument.track=thang Äá» xÃ³a
luckperms.usage.parent-clear-track.argument.context=trÆ°á»ng Äá» lá»c theo
luckperms.usage.meta-info.description=Hiá»n thá» táº¥t cáº£ dá»¯ liá»u Äá»i tÆ°á»£ng trong tin nháº¯n
luckperms.usage.meta-set.description=Äáº·t giÃ¡ trá» dá»¯ liá»u Äá»i tÆ°á»£ng
luckperms.usage.meta-set.argument.key=khÃ³a chÃ­nh Äá» Äáº·t
luckperms.usage.meta-set.argument.value=giÃ¡ trá» Äá» Äáº·t
luckperms.usage.meta-set.argument.context=trÆ°á»ng Äá» thÃªm cáº·p dá»¯ liá»u Äá»i tÆ°á»£ng vÃ o
luckperms.usage.meta-unset.description=Gá»¡ bá» giÃ¡ trá» dá»¯ liá»u Äá»i tÆ°á»£ng
luckperms.usage.meta-unset.argument.key=khÃ³a chÃ­nh Äá» gá»¡ bá»
luckperms.usage.meta-unset.argument.context=trÆ°á»ng Äá» loáº¡i bá» cáº·p dá»¯ liá»u Äá»i tÆ°á»£ng vÃ o
luckperms.usage.meta-settemp.description=Äáº·t giÃ¡ trá» dá»¯ liá»u Äá»i tÆ°á»£ng táº¡m thá»i
luckperms.usage.meta-settemp.argument.key=khÃ³a chÃ­nh Äá» Äáº·t
luckperms.usage.meta-settemp.argument.value=giÃ¡ trá» Äá» Äáº·t
luckperms.usage.meta-settemp.argument.duration=khoáº£ng thá»i gian cho Äáº¿n khi giÃ¡ trá» dá»¯ liá»u Äá»i tÆ°á»£ng háº¿t háº¡n
luckperms.usage.meta-settemp.argument.context=trÆ°á»ng Äá» thÃªm cáº·p dá»¯ liá»u Äá»i tÆ°á»£ng vÃ o
luckperms.usage.meta-unsettemp.description=Gá»¡ bá» giÃ¡ trá» dá»¯ liá»u Äá»i tÆ°á»£ng táº¡m thá»i
luckperms.usage.meta-unsettemp.argument.key=khÃ³a chÃ­nh Äá» gá»¡ bá»
luckperms.usage.meta-unsettemp.argument.context=trÆ°á»ng Äá» loáº¡i bá» cáº·p dá»¯ liá»u Äá»i tÆ°á»£ng vÃ o
luckperms.usage.meta-addprefix.description=ThÃªm tiá»n tá» vÃ o
luckperms.usage.meta-addprefix.argument.priority=giÃ¡ trá» Æ°u tiÃªn Äá» thÃªm tiá»n tá» táº¡i
luckperms.usage.meta-addprefix.argument.prefix=chuá»i tiá»n tá»
luckperms.usage.meta-addprefix.argument.context=trÆ°á»ng Äá» thÃªm tiá»n tá» vÃ o
luckperms.usage.meta-addsuffix.description=ThÃªm háº­u tá» vÃ o
luckperms.usage.meta-addsuffix.argument.priority=giÃ¡ trá» Æ°u tiÃªn Äá» thÃªm háº­u tá» táº¡i
luckperms.usage.meta-addsuffix.argument.suffix=chuá»i háº­u tá»
luckperms.usage.meta-addsuffix.argument.context=trÆ°á»ng Äá» thÃªm háº­u tá» vÃ o
luckperms.usage.meta-setprefix.description=Äáº·t tiá»n tá» vÃ o
luckperms.usage.meta-setprefix.argument.priority=giÃ¡ trá» Æ°u tiÃªn Äá» Äáº·t tiá»n tá» táº¡i
luckperms.usage.meta-setprefix.argument.prefix=chuá»i tiá»n tá»
luckperms.usage.meta-setprefix.argument.context=trÆ°á»ng Äá» Äáº·t tiá»n tá» vÃ o
luckperms.usage.meta-setsuffix.description=Äáº·t háº­u tá» vÃ o
luckperms.usage.meta-setsuffix.argument.priority=giÃ¡ trá» Æ°u tiÃªn Äá» Äáº·t háº­u tá»
luckperms.usage.meta-setsuffix.argument.suffix=chuá»i háº­u tá»
luckperms.usage.meta-setsuffix.argument.context=trÆ°á»ng Äá» Äáº·t háº­u tá» vÃ o
luckperms.usage.meta-removeprefix.description=Gá»¡ bá» má»t tiá»n tá»
luckperms.usage.meta-removeprefix.argument.priority=giÃ¡ trá» Æ°u tiÃªn Äá» gá»¡ tiá»n tá» táº¡i
luckperms.usage.meta-removeprefix.argument.prefix=chuá»i tiá»n tá»
luckperms.usage.meta-removeprefix.argument.context=trÆ°á»ng Äá» xÃ³a tiá»n tá»
luckperms.usage.meta-removesuffix.description=Gá»¡ bá» má»t háº­u tá»
luckperms.usage.meta-removesuffix.argument.priority=giÃ¡ trá» Æ°u tiÃªn Äá» gá»¡ háº­u tá» táº¡i
luckperms.usage.meta-removesuffix.argument.suffix=chuá»i háº­u tá»
luckperms.usage.meta-removesuffix.argument.context=trÆ°á»ng Äá» xÃ³a háº­u tá»
luckperms.usage.meta-addtemp-prefix.description=ThÃªm má»t tiá»n tá» táº¡m thá»i
luckperms.usage.meta-addtemp-prefix.argument.priority=giÃ¡ trá» Æ°u tiÃªn Äá» thÃªm tiá»n tá» táº¡i
luckperms.usage.meta-addtemp-prefix.argument.prefix=chuá»i tiá»n tá»
luckperms.usage.meta-addtemp-prefix.argument.duration=khoáº£ng thá»i gian cho Äáº¿n khi tiá»n tá» háº¿t háº¡n
luckperms.usage.meta-addtemp-prefix.argument.context=trÆ°á»ng Äá» thÃªm tiá»n tá» vÃ o
luckperms.usage.meta-addtemp-suffix.description=ThÃªm má»t háº­u tá» táº¡m thá»i
luckperms.usage.meta-addtemp-suffix.argument.priority=giÃ¡ trá» Æ°u tiÃªn Äá» thÃªm háº­u tá» táº¡i
luckperms.usage.meta-addtemp-suffix.argument.suffix=chuá»i háº­u tá»
luckperms.usage.meta-addtemp-suffix.argument.duration=khoáº£ng thá»i gian cho Äáº¿n khi háº­u tá» háº¿t háº¡n
luckperms.usage.meta-addtemp-suffix.argument.context=trÆ°á»ng Äá» thÃªm háº­u tá» vÃ o
luckperms.usage.meta-settemp-prefix.description=Äáº·t má»t tiá»n tá» táº¡m thá»i
luckperms.usage.meta-settemp-prefix.argument.priority=giÃ¡ trá» Æ°u tiÃªn Äá» Äáº·t tiá»n tá» táº¡i
luckperms.usage.meta-settemp-prefix.argument.prefix=chuá»i tiá»n tá»
luckperms.usage.meta-settemp-prefix.argument.duration=khoáº£ng thá»i gian cho Äáº¿n khi tiá»n tá» háº¿t háº¡n
luckperms.usage.meta-settemp-prefix.argument.context=trÆ°á»ng Äá» Äáº·t tiá»n tá» vÃ o
luckperms.usage.meta-settemp-suffix.description=Äáº·t má»t háº­u tá» táº¡m thá»i
luckperms.usage.meta-settemp-suffix.argument.priority=giÃ¡ trá» Æ°u tiÃªn Äá» Äáº·t háº­u tá»
luckperms.usage.meta-settemp-suffix.argument.suffix=chuá»i háº­u tá»
luckperms.usage.meta-settemp-suffix.argument.duration=khoáº£ng thá»i gian cho Äáº¿n khi háº­u tá» háº¿t háº¡n
luckperms.usage.meta-settemp-suffix.argument.context=trÆ°á»ng Äá» Äáº·t háº­u tá» vÃ o
luckperms.usage.meta-removetemp-prefix.description=Gá»¡ bá» má»t tiá»n tá» táº¡m thá»i
luckperms.usage.meta-removetemp-prefix.argument.priority=giÃ¡ trá» Æ°u tiÃªn Äá» gá»¡ tiá»n tá»
luckperms.usage.meta-removetemp-prefix.argument.prefix=chuá»i tiá»n tá»
luckperms.usage.meta-removetemp-prefix.argument.context=trÆ°á»ng Äá» xÃ³a tiá»n tá»
luckperms.usage.meta-removetemp-suffix.description=Gá»¡ bá» má»t háº­u tá» táº¡m thá»i
luckperms.usage.meta-removetemp-suffix.argument.priority=giÃ¡ trá» Æ°u tiÃªn Äá» gá»¡ háº­u tá» táº¡i
luckperms.usage.meta-removetemp-suffix.argument.suffix=chuá»i háº­u tá»
luckperms.usage.meta-removetemp-suffix.argument.context=trÆ°á»ng Äá» xÃ³a háº­u tá»
luckperms.usage.meta-clear.description=XÃ³a táº¥t cáº£ dá»¯ liá»u
luckperms.usage.meta-clear.argument.type=loáº¡i dá»¯ liá»u Äá»i tÆ°á»£ng cáº§n loáº¡i bá»
luckperms.usage.meta-clear.argument.context=trÆ°á»ng Äá» lá»c theo
luckperms.usage.track-info.description=Cung cáº¥p thÃ´ng tin vá» thang
luckperms.usage.track-editor.description=Má» trÃ¬nh biÃªn táº­p quyá»n trÃªn web
luckperms.usage.track-append.description=ThÃªm má»t nhÃ³m vÃ o cuá»i thang
luckperms.usage.track-append.argument.group=nhÃ³m Äá» thÃªm vÃ o
luckperms.usage.track-insert.description=ChÃ¨n má»t nhÃ³m vÃ o má»t vá» trÃ­ nháº¥t Äá»nh dá»c theo thang
luckperms.usage.track-insert.argument.group=nhÃ³m Äá» chÃ¨n vÃ o
luckperms.usage.track-insert.argument.position=vá» trÃ­ Äá» chÃ¨n nhÃ³m (vá» trÃ­ Äáº§u tiÃªn trÃªn thang lÃ  1)
luckperms.usage.track-remove.description=XÃ³a má»t nhÃ³m khá»i thang
luckperms.usage.track-remove.argument.group=nhÃ³m Äá» gá»¡ bá»
luckperms.usage.track-clear.description=Gá»¡ bá» cÃ¡c nhÃ³m trÃªn thang
luckperms.usage.track-rename.description=Äá»i tÃªn thang
luckperms.usage.track-rename.argument.name=tÃªn má»i
luckperms.usage.track-clone.description=Sao chÃ©p thang
luckperms.usage.track-clone.argument.name=tÃªn cá»§a thang Äá» sao chÃ©p vÃ o
luckperms.usage.log-recent.description=Xem hÃ nh Äá»ng gáº§n ÄÃ¢y
luckperms.usage.log-recent.argument.user=tÃªn/uuid cá»§a ngÆ°á»i dÃ¹ng Äá» lá»c theo
luckperms.usage.log-recent.argument.page=sá» trang Äá» xem
luckperms.usage.log-search.description=TÃ¬m kiáº¿m nháº­t kÃ½ cho má»t má»¥c nháº­p
luckperms.usage.log-search.argument.query=chuá»i Äá» tÃ¬m kiáº¿m bá»i
luckperms.usage.log-search.argument.page=sá» trang Äá» xem
luckperms.usage.log-notify.description=Báº­t/táº¯t thÃ´ng bÃ¡o nháº­t kÃ½
luckperms.usage.log-notify.argument.toggle=khi cáº§n chuyá»n Äá»i báº­t/táº¯t
luckperms.usage.log-user-history.description=Xem lá»ch sá»­ cá»§a ngÆ°á»i dÃ¹ng
luckperms.usage.log-user-history.argument.user=tÃªn/uuid cá»§a ngÆ°á»i dÃ¹ng
luckperms.usage.log-user-history.argument.page=sá» trang Äá» xem
luckperms.usage.log-group-history.description=Xem lá»ch sá»­ cá»§a nhÃ³m
luckperms.usage.log-group-history.argument.group=tÃªn nhÃ³m
luckperms.usage.log-group-history.argument.page=sá» trang Äá» xem
luckperms.usage.log-track-history.description=Xem lá»ch sá»­ cá»§a thang
luckperms.usage.log-track-history.argument.track=tÃªn thang
luckperms.usage.log-track-history.argument.page=sá» trang Äá» xem
luckperms.usage.sponge.description=Chá»nh sá»­a dá»¯ liá»u bá» sung cá»§a Sponge
luckperms.usage.sponge.argument.collection=bá» sÆ°u táº­p Äá» tra cá»©u
luckperms.usage.sponge.argument.subject=chá»§ thá» Äá» sá»­a Äá»i
luckperms.usage.sponge-permission-info.description=Hiá»n thá» thÃ´ng tin vá» quyá»n cá»§a chá»§ thá»
luckperms.usage.sponge-permission-info.argument.contexts=trÆ°á»ng Äá» lá»c theo
luckperms.usage.sponge-permission-set.description=Äáº·t quyá»n cáº¥p cho chá»§ thá»
luckperms.usage.sponge-permission-set.argument.node=máº©u quyá»n Äá» thiáº¿t láº­p
luckperms.usage.sponge-permission-set.argument.tristate=giÃ¡ trá» Äá» Äáº·t quyá»n thÃ nh
luckperms.usage.sponge-permission-set.argument.contexts=trÆ°á»ng Äá» Äáº·t quyá»n vÃ o
luckperms.usage.sponge-permission-clear.description=XÃ³a quyá»n cho chá»§ thá»
luckperms.usage.sponge-permission-clear.argument.contexts=trÆ°á»ng Äá» xÃ³a cÃ¡c quyá»n
luckperms.usage.sponge-parent-info.description=Hiá»n thá» thÃ´ng tin vá» chá»§ chÃ­nh cá»§a chá»§ thá»
luckperms.usage.sponge-parent-info.argument.contexts=trÆ°á»ng Äá» lá»c theo
luckperms.usage.sponge-parent-add.description=ThÃªm chá»§ chÃ­nh vÃ o chá»§ thá»
luckperms.usage.sponge-parent-add.argument.collection=bá» sÆ°u táº­p chá»§ thá» mÃ  trong ÄÃ³ chá»§ thá» chÃ­nh lÃ 
luckperms.usage.sponge-parent-add.argument.subject=tÃªn cá»§a chá»§ thá» chÃ­nh
luckperms.usage.sponge-parent-add.argument.contexts=trÆ°á»ng Äá» thÃªm chá»§ chÃ­nh vÃ o
luckperms.usage.sponge-parent-remove.description=XÃ³a chá»§ chÃ­nh khá»i chá»§ thá»
luckperms.usage.sponge-parent-remove.argument.collection=bá» sÆ°u táº­p chá»§ thá» mÃ  trong ÄÃ³ chá»§ thá» chÃ­nh lÃ 
luckperms.usage.sponge-parent-remove.argument.subject=tÃªn cá»§a chá»§ thá» chÃ­nh
luckperms.usage.sponge-parent-remove.argument.contexts=cÃ¡c trÆ°á»ng Äá» xÃ³a chá»§ chÃ­nh
luckperms.usage.sponge-parent-clear.description=XÃ³a chá»§ thá» chÃ­nh
luckperms.usage.sponge-parent-clear.argument.contexts=trÆ°á»ng Äá» xÃ³a cÃ¡c chá»§ thá» chÃ­nh
luckperms.usage.sponge-option-info.description=Hiá»n thá» thÃ´ng tin vá» tÃ¹y chá»n cá»§a chá»§ thá»
luckperms.usage.sponge-option-info.argument.contexts=trÆ°á»ng Äá» lá»c theo
luckperms.usage.sponge-option-set.description=Äáº·t tÃ¹y chá»n cho chá»§ thá»
luckperms.usage.sponge-option-set.argument.key=khÃ³a chÃ­nh Äá» Äáº·t
luckperms.usage.sponge-option-set.argument.value=giÃ¡ trá» Äá» Äáº·t khÃ³a chÃ­nh
luckperms.usage.sponge-option-set.argument.contexts=trÆ°á»ng Äá» Äáº·t tÃ¹y chá»n
luckperms.usage.sponge-option-unset.description=Gá»¡ bá» tÃ¹y chá»n cho chá»§ thá»
luckperms.usage.sponge-option-unset.argument.key=khÃ³a chÃ­nh Äá» gá»¡ bá»
luckperms.usage.sponge-option-unset.argument.contexts=trÆ°á»ng Äá» gá»¡ khÃ³a chÃ­nh
luckperms.usage.sponge-option-clear.description=XÃ³a tÃ¹y chá»n cá»§a chá»§ thá»
luckperms.usage.sponge-option-clear.argument.contexts=trÆ°á»ng Äá» xÃ³a cÃ¡c tÃ¹y chá»n
