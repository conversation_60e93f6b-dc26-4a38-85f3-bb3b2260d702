luckperms.logs.actionlog-prefix=è¨é
luckperms.logs.verbose-prefix=è©³ç´°è³è¨
luckperms.logs.export-prefix=å¯åº
luckperms.commandsystem.available-commands=ä½¿ç¨ {0} ä¾æ¥çå¯ç¨çæä»¤
luckperms.commandsystem.command-not-recognised=æªç¥çæä»¤
luckperms.commandsystem.no-permission=ä½ æ²æä½¿ç¨è©²æä»¤çæ¬éï¼
luckperms.commandsystem.no-permission-subcommands=ä½ æ²æä½¿ç¨å­æä»¤çæ¬éï¼
luckperms.commandsystem.already-executing-command=ç®åæå¦ä¸åæä»¤æ­£å¨å·è¡ï¼æ­£å¨ç­å¾è©²æä»¤å·è¡å®æâ¦â¦
luckperms.commandsystem.usage.sub-commands-header=å­æä»¤
luckperms.commandsystem.usage.usage-header=æä»¤ç¨æ³
luckperms.commandsystem.usage.arguments-header=å¼æ¸
luckperms.first-time.no-permissions-setup=ä½ ä¼¼ä¹éæ²è¨­å®ä»»ä½æ¬éï¼
luckperms.first-time.use-console-to-give-access=ä½ å¨éæ²è£¡ä½¿ç¨ä»»ä½ LuckPerms æä»¤åï¼éè¦ä½¿ç¨æ§å¶å°ä¾çµ¦äºä½ å­å LuckPerms çæ¬é
luckperms.first-time.console-command-prompt=éåæ§å¶å°ä¸¦å·è¡ä»¥ä¸æä»¤
luckperms.first-time.next-step=ç¶ä½ å®ææä½å¾ï¼ä¾¿å¯ä»¥å®ç¾©ä½ çæ¬éåéåç¾¤çµ
luckperms.first-time.wiki-prompt=ä¸ç¥éå¾åªè£¡éå§ï¼å°éè£¡ççï¼{0}
luckperms.login.try-again=è«ç¨å¾åè©¦
luckperms.login.loading-database-error=è¼å¥æ¬éè³ææç¼çè³æåº«é¯èª¤
luckperms.login.server-admin-check-console-errors=å¦æä½ æ¯ä¼ºæå¨ç®¡çå¡ï¼è«æª¢æ¥æ§å¶å°æ¯å¦æä»»ä½é¯èª¤
luckperms.login.server-admin-check-console-info=æ´å¤è³è¨è«æ¥çä¼ºæå¨ä¸»æ§å°
luckperms.login.data-not-loaded-at-pre=å¨ç»å¥åéæ®µææªè½è¼å¥ä½¿ç¨èçæ¬éè³æ
luckperms.login.unable-to-continue=ç¡æ³ç¹¼çº
luckperms.login.craftbukkit-offline-mode-error=éå¯è½æ¯å çº CraftBukkit åç·ä¸æ¨¡å¼è¨­å®ä¹éææè¡çª
luckperms.login.unexpected-error=è¨­å®æ¬éè³ææç¼çæªé æçé¯èª¤
luckperms.opsystem.disabled=éåä¼ºæå¨å·²åç¨åç OP ç³»çµ±ã
luckperms.opsystem.sponge-warning=è«æ³¨æï¼å®è£æ¬éæä»¶å¾ï¼ä¼ºæå¨ç®¡çå¡èº«åä¸æå½±é¿ Sponge çæ¬éæª¢æ¥ï¼ä½ å¿é ç´æ¥ç·¨è¼¯ä½¿ç¨èè³æ
luckperms.duration.unit.years.plural={0} å¹´
luckperms.duration.unit.years.singular={0} å¹´
luckperms.duration.unit.years.short={0} å¹´
luckperms.duration.unit.months.plural={0} åæ
luckperms.duration.unit.months.singular={0} åæ
luckperms.duration.unit.months.short={0} åæ
luckperms.duration.unit.weeks.plural={0} é±
luckperms.duration.unit.weeks.singular={0} é±
luckperms.duration.unit.weeks.short={0} é±
luckperms.duration.unit.days.plural={0} å¤©
luckperms.duration.unit.days.singular={0} å¤©
luckperms.duration.unit.days.short={0} å¤©
luckperms.duration.unit.hours.plural={0} å°æ
luckperms.duration.unit.hours.singular={0} å°æ
luckperms.duration.unit.hours.short={0} å°æ
luckperms.duration.unit.minutes.plural={0} åé
luckperms.duration.unit.minutes.singular={0} åé
luckperms.duration.unit.minutes.short={0} å
luckperms.duration.unit.seconds.plural={0} ç§
luckperms.duration.unit.seconds.singular={0} ç§
luckperms.duration.unit.seconds.short={0} ç§
luckperms.duration.since={0} å
luckperms.command.misc.invalid-code=é©è­ç¢¼ç¡æ
luckperms.command.misc.response-code-key=é©è­ç¢¼
luckperms.command.misc.error-message-key=è¨æ¯
luckperms.command.misc.bytebin-unable-to-communicate=ç¡æ³é£ç·è³ bytebin
luckperms.command.misc.webapp-unable-to-communicate=ç¡æ³é£ç·è³ç¶²è·¯æç¨ç¨å¼
luckperms.command.misc.check-console-for-errors=è«å°æ§å¶å°æª¢æ¥é¯èª¤
luckperms.command.misc.file-must-be-in-data=æªæ¡ {0} å¿é ç´æ¥æ¾å¨ data ç®éä¸­
luckperms.command.misc.wait-to-finish=è«ç­å¾å¶å®æå¾åè©¦ä¸æ¬¡
luckperms.command.misc.invalid-priority=ç¡æçåªåæ¬ {0}
luckperms.command.misc.expected-number=æçºæ¸å­
luckperms.command.misc.date-parse-error=ç¡æ³åææ¥æ {0}
luckperms.command.misc.date-in-past-error=ä½ ç¡æ³è¨­å®éå»çæ¥æï¼
luckperms.command.misc.page=ç¬¬ {0} é ï¼å± {1} é 
luckperms.command.misc.page-entries={0} åé ç®
luckperms.command.misc.none=ç¡
luckperms.command.misc.loading.error.unexpected=ç¼çæªé æçé¯èª¤
luckperms.command.misc.loading.error.user=å°æªè¼å¥ä½¿ç¨è
luckperms.command.misc.loading.error.user-specific=ç¡æ³è¼å¥ç®æ¨ä½¿ç¨è {0}
luckperms.command.misc.loading.error.user-not-found=æ¾ä¸å°åçº {0} çä½¿ç¨è
luckperms.command.misc.loading.error.user-save-error=å²å­ä½¿ç¨è {0} çè³ææç¼çé¯èª¤
luckperms.command.misc.loading.error.user-not-online=ç©å®¶ {0} ä¸å¨ç·ä¸
luckperms.command.misc.loading.error.user-invalid={0} ç¡æçä½¿ç¨èåç¨±æ UUID
luckperms.command.misc.loading.error.user-not-uuid=ç®æ¨ä½¿ç¨è {0} ä¸æ¯ææç UUID
luckperms.command.misc.loading.error.group=å°æªè¼å¥ç¾¤çµ
luckperms.command.misc.loading.error.all-groups=ç¡æ³è¼å¥ææç¾¤çµ
luckperms.command.misc.loading.error.group-not-found=æ¾ä¸å°åçº {0} çç¾¤çµ
luckperms.command.misc.loading.error.group-save-error=å²å­ç¾¤çµ {0} çè³ææç¼çé¯èª¤
luckperms.command.misc.loading.error.group-invalid={0} ä¸æ¯ææçç¾¤çµåç¨±
luckperms.command.misc.loading.error.track=å°æªè¼å¥æ¬ééç´
luckperms.command.misc.loading.error.all-tracks=ç¡æ³è¼å¥æææ¬ééç´
luckperms.command.misc.loading.error.track-not-found=æ¾ä¸å°åçº {0} çæ¬ééç´
luckperms.command.misc.loading.error.track-save-error=å²å­æ¬ééç´ {0} çè³ææç¼çé¯èª¤
luckperms.command.misc.loading.error.track-invalid={0} ä¸æ¯ææçæ¬ééç´åç¨±
luckperms.command.editor.no-match=ç¡æ³éåç·¨è¼¯å¨ï¼æ²æèæéé¡åç¬¦åçç©ä»¶
luckperms.command.editor.start=æ­£å¨æºåä¸åæ°çç·¨è¼¯å¨ï¼è«ç¨åâ¦â¦
luckperms.command.editor.url=é»æä¸æ¹é£çµéåç·¨è¼¯å¨
luckperms.command.editor.unable-to-communicate=ç¡æ³é£ç·è³ç·¨è¼¯å¨
luckperms.command.editor.apply-edits.success=ç¶²é ç·¨è¼¯å¨è³æå·²æåå¥ç¨å° {0} {1}
luckperms.command.editor.apply-edits.success-summary={1} {0} é å{3} {2} é 
luckperms.command.editor.apply-edits.success.additions=å¢å 
luckperms.command.editor.apply-edits.success.additions-singular=å¢å 
luckperms.command.editor.apply-edits.success.deletions=åªé¤
luckperms.command.editor.apply-edits.success.deletions-singular=åªé¤
luckperms.command.editor.apply-edits.no-changes=ç¶²é ç·¨è¼¯å¨æ²æå¥ç¨ä»»ä½è®æ´ï¼è¿åçè³æä¸åå«ä»»ä½ç·¨è¼¯
luckperms.command.editor.apply-edits.unknown-type=ç¡æ³å°æå®çç©ä»¶é¡åå¥ç¨ç·¨è¼¯
luckperms.command.editor.apply-edits.unable-to-read=ç¡æ³å¾æå®çä»£ç¢¼è®åè³æ
luckperms.command.search.searching.permission=æ­£å¨æå°ææ {0} æ¬éçä½¿ç¨èåç¾¤çµ
luckperms.command.search.searching.inherit=æ­£å¨æå°ç¹¼æ¿èª {0} çä½¿ç¨èåç¾¤çµ
luckperms.command.search.result=å¾ {1} åä½¿ç¨èå {2} åç¾¤çµä¸­æ¾å° {0} åé ç®
luckperms.command.search.result.default-notice=æ³¨æï¼æå°é è¨­ç¾¤çµæå¡æï¼å°ä¸æé¡¯ç¤ºæ²æå¶ä»æ¬éçé¢ç·ç©å®¶ï¼
luckperms.command.search.showing-users=é¡¯ç¤ºä½¿ç¨èé ç®
luckperms.command.search.showing-groups=é¡¯ç¤ºç¾¤çµé ç®
luckperms.command.tree.start=æ­£å¨ç¢çæ¬éæ¨¹ï¼è«ç¨åâ¦â¦
luckperms.command.tree.empty=æ¾ä¸å°ä»»ä½çµæï¼å æ­¤ç¡æ³ç¢çæ¬éæ¨¹
luckperms.command.tree.url=æ¬éæ¨¹ç¶²å
luckperms.command.verbose.invalid-filter={0} ä¸æ¯ææçè©³ç´°è¨éç¯©é¸å¨
luckperms.command.verbose.enabled=éå°ç¬¦å {1} ç¯©é¸å¨çæª¢æ¥ï¼éå {0} è©³ç´°è¨éåè½
luckperms.command.verbose.command-exec=å¼·å¶ {0} å·è¡æä»¤ {1} ä¸¦åå ±æææª¢æ¥çµæâ¦â¦
luckperms.command.verbose.off=è©³ç´°è¨é {0}
luckperms.command.verbose.command-exec-complete=æä»¤å·è¡å®æ
luckperms.command.verbose.command.no-checks=æä»¤å·è¡å®æï¼ä½æ²æé²è¡æ¬éæª¢æ¥
luckperms.command.verbose.command.possibly-async=éå¯è½æ¯å çºæä»¶å¨èæ¯å·è¡æä»¤ï¼éåæ­¥ï¼
luckperms.command.verbose.command.try-again-manually=ä½ ä»ç¶å¯ä»¥æåéåè©³ç´°è³è¨æ¨¡å¼ä¾åµæ¸¬éé¡æª¢æ¥
luckperms.command.verbose.enabled-recording=éå°ç¬¦å {1} çæª¢æ¥çµæï¼éå {0} è©³ç´°è¨éåè½
luckperms.command.verbose.uploading=æ­£å¨ä¸å³ {0} çè©³ç´°è¨éçµæâ¦â¦
luckperms.command.verbose.url=è©³ç´°çµæç¶²å
luckperms.command.verbose.enabled-term=å·²åç¨
luckperms.command.verbose.disabled-term=å·²åç¨
luckperms.command.verbose.query-any=ä»»æ
luckperms.command.info.running-plugin=å·è¡ä¸­
luckperms.command.info.platform-key=å¹³å°
luckperms.command.info.server-brand-key=ä¼ºæå¨è»é«
luckperms.command.info.server-version-key=ä¼ºæå¨çæ¬
luckperms.command.info.storage-key=å²å­
luckperms.command.info.storage-type-key=é¡å
luckperms.command.info.storage.meta.split-types-key=é¡å
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=å·²é£ç·
luckperms.command.info.storage.meta.file-size-key=æªæ¡å¤§å°
luckperms.command.info.extensions-key=éå åä»¶
luckperms.command.info.messaging-key=è¨æ¯
luckperms.command.info.instance-key=ä¼ºæå¨å¯¦ä¾
luckperms.command.info.static-contexts-key=éæç°å¢
luckperms.command.info.online-players-key=ç·ä¸ç©å®¶
luckperms.command.info.online-players-unique={0} ç¨ç« IP ç©å®¶
luckperms.command.info.uptime-key=å·è¡æé
luckperms.command.info.local-data-key=æ¬æ©è³æ
luckperms.command.info.local-data={0} åä½¿ç¨èã{1} åç¾¤çµã{2} åæ¬ééç´
luckperms.command.generic.create.success=å·²æåå»ºç« {0}
luckperms.command.generic.create.error=å»ºç« {0} æç¼çé¯èª¤
luckperms.command.generic.create.error-already-exists={0} å·²å­å¨ï¼
luckperms.command.generic.delete.success=å·²æååªé¤ {0}
luckperms.command.generic.delete.error=åªé¤ {0} æç¼çé¯èª¤
luckperms.command.generic.delete.error-doesnt-exist={0} ä¸å­å¨ï¼
luckperms.command.generic.rename.success={0} å·²æåéæ°å½åçº {1}
luckperms.command.generic.clone.success={0} å·²æåè¤è£½å° {1}
luckperms.command.generic.info.parent.title=ç¶ç¾¤çµ
luckperms.command.generic.info.parent.temporary-title=è¨æç¶ç¾¤çµ
luckperms.command.generic.info.expires-in=å©é¤æææéï¸°
luckperms.command.generic.info.inherited-from=ç¹¼æ¿èª
luckperms.command.generic.info.inherited-from-self=èªå·±
luckperms.command.generic.show-tracks.title={0} çæ¬ééç´
luckperms.command.generic.show-tracks.empty={0} ä¸å¨ä»»ä½æ¬ééç´è£¡
luckperms.command.generic.clear.node-removed=å·²ç§»é¤ {0} åç¯é»
luckperms.command.generic.clear.node-removed-singular=å·²ç§»é¤ {0} åç¯é»
luckperms.command.generic.clear={0} å¨ç°å¢ {1} ä¸­çç¯é»å·²ç¶æ¸é¤
luckperms.command.generic.permission.info.title={0} çæ¬é
luckperms.command.generic.permission.info.empty={0} æ²æä»»ä½å·²è¨­å®çæ¬é
luckperms.command.generic.permission.info.click-to-remove=é»æä¾å°éåç¯é»å¾ {0} ä¸­ç§»é¤
luckperms.command.generic.permission.check.info.title={0} çæ¬éè³è¨
luckperms.command.generic.permission.check.info.directly=å¨ç°å¢ {3} ä¸­ï¼{0} çæ¬é {1} è¢«è¨­çº {2}
luckperms.command.generic.permission.check.info.inherited=å¨ç°å¢ {4} ä¸­ï¼{0} å¾ {3} ç¹¼æ¿çæ¬é {1} è¢«è¨­çº {2}
luckperms.command.generic.permission.check.info.not-directly={0} æ²æè¨­å®æ¬é {1}
luckperms.command.generic.permission.check.info.not-inherited={0} æ²æç¹¼æ¿ {1}
luckperms.command.generic.permission.check.result.title={0} çæ¬éæª¢æ¥
luckperms.command.generic.permission.check.result.result-key=çµæ
luckperms.command.generic.permission.check.result.processor-key=èçè
luckperms.command.generic.permission.check.result.cause-key=åå 
luckperms.command.generic.permission.check.result.context-key=ç°å¢
luckperms.command.generic.permission.set=å·²è¨­å®å¨ç°å¢ {3} ä¸­ {2} çæ¬é {0} çº {1}
luckperms.command.generic.permission.already-has={0} å·²ç¶å¨ç°å¢ {2} ä¸­è¨­å®äºæ¬é {1}
luckperms.command.generic.permission.set-temp=å·²è¨­å®å¨ç°å¢ {4} ä¸­ {2} çæ¬é {0} çº {1}ï¼æææéï¼{3}
luckperms.command.generic.permission.already-has-temp={0} å·²ç¶å¨ç°å¢ {2} ä¸­è¨­å®äºè¨ææ¬é {1}
luckperms.command.generic.permission.unset=å·²è§£é¤ {1} å¨ç°å¢ {2} ä¸­çæ¬é {0}
luckperms.command.generic.permission.doesnt-have={0} æ²æå¨ç°å¢ {2} ä¸­è¨­å®æ¬é {1}
luckperms.command.generic.permission.unset-temp=å·²è§£é¤ {1} å¨ç°å¢ {2} ä¸­çè¨ææ¬é {0}
luckperms.command.generic.permission.subtract=å·²è¨­å®å¨ç°å¢ {4} ä¸­ {2} çæ¬é {0} çº {1}ï¼æææéï¼{3}ï¼æ¯ä¸æ¬¡è¨­å®å° {5}
luckperms.command.generic.permission.doesnt-have-temp={0} æ²æå¨ç°å¢ {2} ä¸­è¨­å®è¨ææ¬é {1}
luckperms.command.generic.permission.clear={0} å¨ç°å¢ {1} ä¸­ççæ¬éå·²ç¶æ¸é¤
luckperms.command.generic.parent.info.title={0} çç¶ç³»
luckperms.command.generic.parent.info.empty={0} æ²æä»»ä½å·²å®ç¾©çç¶ç³»
luckperms.command.generic.parent.info.click-to-remove=é»æä¾å°éåç¶ç³»å¾ {0} ä¸­ç§»é¤
luckperms.command.generic.parent.add={0} ç¾å¨å¾ç°å¢ {2} ä¸­ç¹¼æ¿ {1} çæ¬é
luckperms.command.generic.parent.add-temp={0} ç¾å¨å¾ç°å¢ {3} ä¸­ç¹¼æ¿ {1} çæ¬éï¼æææéï¼{2}
luckperms.command.generic.parent.set=æ¸é¤äº {0} ç¾æçç¶ç¾¤çµï¼ç¾å¨åªå¨ç°å¢ {2} ä¸­ç¹¼æ¿ {1}
luckperms.command.generic.parent.set-track=æ¸é¤äº {0} å¨æ¬ééç´ {1} ä¸­ç¾æçç¶ç¾¤çµï¼ç¾å¨åªå¨ç°å¢ {3} ä¸­ç¹¼æ¿ {2}
luckperms.command.generic.parent.remove={0} ä¸åå¾ç°å¢ {2} ä¸­ç¹¼æ¿ {1} çæ¬é
luckperms.command.generic.parent.remove-temp={0} ä¸åå¾ç°å¢ {2} ä¸­è¨æç¹¼æ¿ {1} çæ¬é
luckperms.command.generic.parent.subtract={0} ç¾å¨å¾ç°å¢ {3} ä¸­ç¹¼æ¿ {1} çæ¬éï¼æææéï¼{2}ï¼æ¯ä¸æ¬¡è¨­å®å° {4}
luckperms.command.generic.parent.clear={0} å¨ç°å¢ {1} ä¸­çç¶ç³»å·²ç¶æ¸é¤
luckperms.command.generic.parent.clear-track={0} å¨ç°å¢ {2} ä¸­çæ¬ééç´ {1} çç¶ç³»å·²ç¶æ¸é¤
luckperms.command.generic.parent.already-inherits={0} å·²ç¶å¾ç°å¢ {2} ä¸­ç¹¼æ¿äº {1}
luckperms.command.generic.parent.doesnt-inherit={0} æ²æå¾ç°å¢ {2} ä¸­ç¹¼æ¿ {1}
luckperms.command.generic.parent.already-temp-inherits={0} å·²ç¶å¾ç°å¢ {2} ä¸­è¨æç¹¼æ¿ {1}
luckperms.command.generic.parent.doesnt-temp-inherit={0} æ²æå¾ç°å¢ {2} ä¸­è¨æç¹¼æ¿ {1} çæ¬é
luckperms.command.generic.chat-meta.info.title-prefix={0} çåç¶´
luckperms.command.generic.chat-meta.info.title-suffix={0} çå¾ç¶´
luckperms.command.generic.chat-meta.info.none-prefix={0} æ²æåç¶´
luckperms.command.generic.chat-meta.info.none-suffix={0} æ²æå¾ç¶´
luckperms.command.generic.chat-meta.info.click-to-remove=é»æä¾å° {0} å¾ {1} ä¸­ç§»é¤
luckperms.command.generic.chat-meta.already-has={0} å·²ç¶å¨ç°å¢ {4} ä¸è¨­å®äº {1} {2} çåªåæ¬çº {3}
luckperms.command.generic.chat-meta.already-has-temp={0} å·²ç¶å¨ç°å¢ {4} ä¸è¨æè¨­å®äº {1} {2} çåªåæ¬çº {3}
luckperms.command.generic.chat-meta.doesnt-have={0} æ²æå¨ç°å¢ {4} ä¸è¨­å®äº {1} {2} çåªåæ¬çº {3}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} æ²æå¨ç°å¢ {4} ä¸è¨æè¨­å®äº {1} {2} çåªåæ¬çº {3}
luckperms.command.generic.chat-meta.add={0} å¨ç°å¢ {4} ä¸è¨­å®äº {1} {2} çåªåæ¬çº {3}
luckperms.command.generic.chat-meta.add-temp={0} å¨ç°å¢ {5} ä¸è¨­å®äº {1} {2} çåªåæ¬çº {3}ï¼æææéï¼{4}
luckperms.command.generic.chat-meta.remove={0} å¨ç°å¢ {4} ä¸ç§»é¤äº {1} {2} çåªåæ¬ {3}
luckperms.command.generic.chat-meta.remove-bulk={0} å¨ç°å¢ {3} ä¸ç§»é¤äºå¨é¨ {1} çåªåæ¬ {2}
luckperms.command.generic.chat-meta.remove-temp={0} å¨ç°å¢ {4} ä¸è¨æç§»é¤äº {1} {2} çåªåæ¬ {3}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} å¨ç°å¢ {3} ä¸è¨æç§»é¤äºå¨é¨ {1} çåªåæ¬ {2}
luckperms.command.generic.meta.info.title={0} çä¸­ç¹¼è³æ
luckperms.command.generic.meta.info.none={0} æ²æä¸­ç¹¼è³æ
luckperms.command.generic.meta.info.click-to-remove=é»æä¾å°éååç¯é»å¾ {0} ä¸­ç§»é¤
luckperms.command.generic.meta.already-has={0} å·²ç¶å¨ç°å¢ {3} ä¸­å°éé° {1} è¨­çº {2}
luckperms.command.generic.meta.already-has-temp={0} å·²ç¶å¨ç°å¢ {3} ä¸­å°è¨æåéé° {1} è¨­çº {2}
luckperms.command.generic.meta.doesnt-have={0} æ²æå¨ç°å¢ {2} ä¸­è¨­å®åéé° {1}
luckperms.command.generic.meta.doesnt-have-temp={0} æ²æå¨ç°å¢ {2} ä¸­è¨­å®è¨æåéé° {1}
luckperms.command.generic.meta.set=å·²è¨­å®å¨ç°å¢ {3} ä¸­ {2} çåéé° {0} çº {1}
luckperms.command.generic.meta.set-temp=å·²è¨­å®å¨ç°å¢ {4} ä¸­ {2} çåéé° {0} çº {1}ï¼æææéï¼{3}
luckperms.command.generic.meta.unset=å·²è§£é¤ {1} å¨ç°å¢ {2} ä¸­çåéé° {0}
luckperms.command.generic.meta.unset-temp=å·²è§£é¤ {1} å¨ç°å¢ {2} ä¸­çè¨æåéé° {0}
luckperms.command.generic.meta.clear=å¨ {2} ç°å¢ä¸­ï¼å±¬æ¼ {0} çä¸­ç¹¼è³ææ¯å°é¡å {1} å·²è¢«æ¸é¤
luckperms.command.generic.contextual-data.title=ç°å¢è³æ
luckperms.command.generic.contextual-data.mode.key=æ¨¡å¼
luckperms.command.generic.contextual-data.mode.server=ä¼ºæå¨
luckperms.command.generic.contextual-data.mode.active-player=æ´»èºç©å®¶
luckperms.command.generic.contextual-data.contexts-key=ç°å¢
luckperms.command.generic.contextual-data.prefix-key=åç¶´
luckperms.command.generic.contextual-data.suffix-key=å¾ç¶´
luckperms.command.generic.contextual-data.primary-group-key=ä¸»è¦ç¾¤çµ
luckperms.command.generic.contextual-data.meta-key=ä¸­ç¹¼è³æ
luckperms.command.generic.contextual-data.null-result=ç¡
luckperms.command.user.info.title=ä½¿ç¨èè³è¨
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=é¡å
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=é¢ç·
luckperms.command.user.info.status-key=çæ
luckperms.command.user.info.status.online=ç·ä¸
luckperms.command.user.info.status.offline=é¢ç·
luckperms.command.user.removegroup.error-primary=ä½ ä¸è½å°ä½¿ç¨èå¾å¶ä¸»è¦ç¾¤çµä¸­ç§»é¤
luckperms.command.user.primarygroup.not-member={0} å°æªæçº {1} çæå¡ï¼ç¾å¨æ­£å¨æ°å¢
luckperms.command.user.primarygroup.already-has={0} çä¸»è¦ç¾¤çµå·²ç¶æ¯ {1}
luckperms.command.user.primarygroup.warn-option=è­¦åï¼æ­¤ä¼ºæå¨ ({0}) æ­£å¨ä½¿ç¨çä¸»è¦ç¾¤çµè¨ç®æ¹æ³å¯è½ç¡æ³åæ æ¬æ¬¡è®æ´
luckperms.command.user.primarygroup.set={0} çä¸»è¦ç¾¤çµå·²è¨­å®çº {1}
luckperms.command.user.track.error-not-contain-group={0} å°æªå¨ {1} çä»»ä½ç¾¤çµå§
luckperms.command.user.track.unsure-which-track=ç¡æ³ç¢ºå®è¦ä½¿ç¨çæ¬ééç´ï¼è«å°å¶æå®çºå¼æ¸
luckperms.command.user.track.missing-group-advice=è«å»ºç«ç¾¤çµï¼æå¾æ¬ééç´ä¸­åªé¤å®ï¼ç¶å¾åè©¦ä¸æ¬¡
luckperms.command.user.promote.added-to-first={0} ä¸å¨ {1} çä»»ä½ç¾¤çµä¸­ï¼æä»¥ä»åè¢«å å¥å°ç°å¢ {3} çç¬¬ä¸åç¾¤çµ {2}
luckperms.command.user.promote.not-on-track={0} ä¸å¨ {1} çä»»ä½ä¸åç¾¤çµï¼å èæ²æåç´
luckperms.command.user.promote.success=å¨ç°å¢ {4} ä¸­æ²¿æ¬ééç´ {1} åç´ {0} å¾ {2} å° {3}
luckperms.command.user.promote.end-of-track=æ¬ééç´ {0} å·²ç¶å°éçµå°¾ï¼ç¡æ³åç´ {1}
luckperms.command.user.promote.next-group-deleted=æ¬ééç´ä¸çä¸ä¸åç¾¤çµ {0}ï¼å·²ç¶ä¸å­å¨
luckperms.command.user.promote.unable-to-promote=ç¡æ³æåä½¿ç¨èæ¬é
luckperms.command.user.demote.success=å¨ç°å¢ {4} ä¸­æ²¿æ¬ééç´ {1} éç´ {0} å¾ {2} å° {3}
luckperms.command.user.demote.end-of-track=æ¬ééç´ {0} å·²ç¶å°éçµå°¾ï¼å æ­¤ {1} å·²ç¶å¾ {2} ç§»é¤
luckperms.command.user.demote.end-of-track-not-removed=æ¬ééç´ {0} å·²ç¶å°éçµå°¾ï¼ä½æ¯ {1} æ²æå¾ç¬¬ä¸ç¾¤çµä¸­ç§»é¤
luckperms.command.user.demote.previous-group-deleted=æ¬ééç´ä¸çä¸ä¸åç¾¤çµ {0}ï¼å·²ç¶ä¸å­å¨
luckperms.command.user.demote.unable-to-demote=ç¡æ³éä½ä½¿ç¨èæ¬é
luckperms.command.group.list.title=ç¾¤çµ
luckperms.command.group.delete.not-default=ä½ ä¸è½åªé¤é è¨­ç¾¤çµ
luckperms.command.group.info.title=ç¾¤çµè³è¨
luckperms.command.group.info.display-name-key=é¡¯ç¤ºåç¨±
luckperms.command.group.info.weight-key=æ¬é
luckperms.command.group.setweight.set=å·²å°ç¾¤çµ {1} çæ¬éè¨­çº {0}
luckperms.command.group.setdisplayname.doesnt-have={0} å°æªè¨­å®é¡¯ç¤ºåç¨±
luckperms.command.group.setdisplayname.already-has={0} çé¡¯ç¤ºåç¨±å·²æ¯ {1}
luckperms.command.group.setdisplayname.already-in-use=è©²é¡¯ç¤ºåç¨± {0} å·²ç¶è¢« {1} æä½¿ç¨
luckperms.command.group.setdisplayname.set=å·²è¨­å®ç¾¤çµ {1} å¨ç°å¢ {2} ä¸­çé¡¯ç¤ºåç¨±çº {0}
luckperms.command.group.setdisplayname.removed=å·²ç§»é¤ç¾¤çµ {0} å¨ç°å¢ {1} ä¸­çé¡¯ç¤ºåç¨±
luckperms.command.track.list.title=æ¬ééç´
luckperms.command.track.path.empty=ç¡
luckperms.command.track.info.showing-track=é¡¯ç¤ºæ­·å²è¨é
luckperms.command.track.info.path-property=è·¯å¾
luckperms.command.track.clear={0} çç¾¤çµæ¬ééç´å·²è¢«æ¸é¤
luckperms.command.track.append.success=ç¾¤çµ {0} å·²å å¥å°æ¬ééç´ {1}
luckperms.command.track.insert.success=ç¾¤çµ {0} è¢«æå¥å°æ¬ééç´ {1} çä½ç½® {2}
luckperms.command.track.insert.error-number=æçºæ¸å¼ï¼ä½å»æ¶å°ï¼{0}
luckperms.command.track.insert.error-invalid-pos=ç¡æ³æå¥å°ä½ç½® {0}
luckperms.command.track.insert.error-invalid-pos-reason=ç¡æçä½ç½®
luckperms.command.track.remove.success=ç¾¤çµ {0} å·²å¾æ¬ééç´ {1} ä¸­ç§»é¤
luckperms.command.track.error-empty=ç¡æ³ä½¿ç¨ {0}ï¼å çºå®æ¯ç©ºçæååå«ä¸åç¾¤çµ
luckperms.command.track.error-multiple-groups={0} æ¯æ­¤æ¬ééç´ä¸å¤åç¾¤çµçæå¡
luckperms.command.track.error-ambiguous=ç¡æ³ç¢ºå®å¶ä½ç½®
luckperms.command.track.already-contains={0} å·²ç¶åå« {1}
luckperms.command.track.doesnt-contain={0} ä¸¦æªåå« {1}
luckperms.command.log.load-error=ç¡æ³è¼å¥è¨é
luckperms.command.log.invalid-page=ç¡æçé ç¢¼
luckperms.command.log.invalid-page-range=è«è¼¸å¥ä»æ¼ {0} è³ {1} ä¹éçå¼
luckperms.command.log.empty=æ²æå¯é¡¯ç¤ºçè¨éé ç®
luckperms.command.log.notify.error-console=ç¡æ³åææ§å¶å°çéç¥
luckperms.command.log.notify.enabled-term=å·²åç¨
luckperms.command.log.notify.disabled-term=å·²åç¨
luckperms.command.log.notify.changed-state={0} è¨éè¼¸åº
luckperms.command.log.notify.already-on=ä½ å·²æ­£å¨æ¥æ¶éç¥
luckperms.command.log.notify.already-off=ä½ ç®åä¸ææ¥æ¶éç¥
luckperms.command.log.notify.invalid-state=æªç¥ççæãæçº {0} æ {1}
luckperms.command.log.show.search=é¡¯ç¤ºæ¥è©¢ {0} çæè¿åä½
luckperms.command.log.show.recent=é¡¯ç¤ºæè¿çåä½
luckperms.command.log.show.by=é¡¯ç¤º {0} æè¿çåä½
luckperms.command.log.show.history=é¡¯ç¤º {0} {1} çæ­·å²è¨é
luckperms.command.export.error-term=é¯èª¤
luckperms.command.export.already-running=å¦ä¸åå¯åºèçç¨åºæ­£å¨å·è¡ä¸­
luckperms.command.export.file.already-exists=æªæ¡ {0} å·²å­å¨
luckperms.command.export.file.not-writable=æªæ¡ {0} ç¡æ³å¯«å¥
luckperms.command.export.file.success=å·²æåå¯åºè³ {0}
luckperms.command.export.file-unexpected-error-writing=å¨å¯«å¥æªæ¡æç¼çéé æçé¯èª¤
luckperms.command.export.web.export-code=å¯åºä»£ç¢¼
luckperms.command.export.web.import-command-description=ä½¿ç¨ä»¥ä¸æä»¤ä¾å¯å¥
luckperms.command.import.term=å¯å¥
luckperms.command.import.error-term=é¯èª¤
luckperms.command.import.already-running=å·²æä¸é å¯å¥ä½æ¥­æ­£å¨é²è¡
luckperms.command.import.file.doesnt-exist=æªæ¡ {0} ä¸å­å¨
luckperms.command.import.file.not-readable=ç¡æ³è®åæªæ¡ {0}
luckperms.command.import.file.unexpected-error-reading=å¨è®åå¯å¥æªæ¡æç¼çéé æçé¯èª¤
luckperms.command.import.file.correct-format=æ ¼å¼æ¯å¦æ­£ç¢ºï¼
luckperms.command.import.web.unable-to-read=ç¡æ³å¾æå®çä»£ç¢¼è®åè³æ
luckperms.command.import.progress.percent=å·²å®æ {0}%
luckperms.command.import.progress.operations=å·²å®æ {0}/{1} é ä½æ¥­
luckperms.command.import.starting=éå§å¯å¥ä½æ¥­
luckperms.command.import.completed=å·²å®æ
luckperms.command.import.duration=èæ {0} ç§
luckperms.command.bulkupdate.must-use-console=å¤§éæ´æ°æä»¤åªè½å¨æ§å¶å°ä½¿ç¨
luckperms.command.bulkupdate.invalid-data-type=ç¡æçé¡åï¼æçº {0}
luckperms.command.bulkupdate.invalid-constraint=ç¡æçç´æ {0}
luckperms.command.bulkupdate.invalid-constraint-format=ç´ææéµå¾ªæ ¼å¼ {0}
luckperms.command.bulkupdate.invalid-comparison=ç¡æçæ¯è¼éç®å­ {0}
luckperms.command.bulkupdate.invalid-comparison-format=æçºä»¥ä¸ä¹ä¸ï¼{0}
luckperms.command.bulkupdate.queued=å¤§éæ´æ°ä½æ¥­å·²ä½å
luckperms.command.bulkupdate.confirm=å·è¡ {0} ä¾é²è¡æ´æ°
luckperms.command.bulkupdate.unknown-id=ID {0} çä½æ¥­ä¸å­å¨æå·²éæ
luckperms.command.bulkupdate.starting=æ­£å¨å·è¡å¤§éæ´æ°
luckperms.command.bulkupdate.success=å¤§éæ´æ°å·²æåå®æ
luckperms.command.bulkupdate.success.statistics.nodes=åå½±é¿ç¯é»ç¸½æ¸
luckperms.command.bulkupdate.success.statistics.users=åå½±é¿ä½¿ç¨èç¸½æ¸
luckperms.command.bulkupdate.success.statistics.groups=åå½±é¿ç¾¤çµç¸½æ¸
luckperms.command.bulkupdate.failure=å¤§éæ´æ°å¤±æï¼è«æª¢æ¥ä¸»æ§å°æ¯å¦æé¯èª¤è¨æ¯
luckperms.command.update-task.request=å·²æ¶å°æ´æ°ä»»åè«æ±ï¼è«ç¨å
luckperms.command.update-task.complete=æ´æ°ä»»åå®æ
luckperms.command.update-task.push.attempting=ç¾å¨æ­£åè©¦æ¨éå°å¶ä»ä¼ºæå¨
luckperms.command.update-task.push.complete=å·²éé {0} æåéç¥å¶ä»ä¼ºæå¨
luckperms.command.update-task.push.error=å°è®æ´æ¨éå°å¶ä»ä¼ºæå¨æç¼çé¯èª¤
luckperms.command.update-task.push.error-not-setup=ç±æ¼å°æªè¨­å®è¨æ¯æåï¼ç¡æ³å°è®æ´æ¨éå°å¶ä»ä¼ºæå¨
luckperms.command.reload-config.success=å·²éæ°è¼å¥è¨­å®æª
luckperms.command.reload-config.restart-note=é¨åé¸é å°å¨ä¼ºæå¨éæ°ååå¾ææçæ
luckperms.command.translations.searching=æ­£å¨æå°å¯ç¨çç¿»è­¯ï¼è«ç¨åâ¦â¦
luckperms.command.translations.searching-error=ç¡æ³åå¾å¯ç¨çç¿»è­¯æ¸å®
luckperms.command.translations.installed-translations=å·²å®è£çç¿»è­¯
luckperms.command.translations.available-translations=å¯ç¨çç¿»è­¯
luckperms.command.translations.percent-translated=å·²ç¿»è­¯ {0}%
luckperms.command.translations.translations-by=ç¿»è­¯èï¸°
luckperms.command.translations.installing=æ­£å¨å®è£ç¿»è­¯ï¼è«ç¨åâ¦â¦
luckperms.command.translations.download-error=ç¡æ³ä¸è¼èªè¨ {0} çç¿»è­¯
luckperms.command.translations.installing-specific=æ­£å¨å®è£èªè¨ {0}â¦â¦
luckperms.command.translations.install-complete=å®è£å®æ
luckperms.command.translations.download-prompt=ä½¿ç¨ {0} ä¸è¼ä¸¦å®è£ç±ç¤¾ç¾¤æä¾çææ°ç¿»è­¯çæ¬
luckperms.command.translations.download-override-warning=è«æ³¨æï¼éå°ä½ è¦èä½ å°éäºèªè¨æåçä»»ä½è®æ´
luckperms.usage.user.description=å¨ LuckPerms ä¸­ç®¡çä½¿ç¨èçä¸çµæä»¤ãï¼LuckPerms ä¸­çãuserãä»£è¡¨ç©å®¶ï¼å¯ä»¥æ¯å¶ UUID æä½¿ç¨èåç¨±ï¼
luckperms.usage.group.description=å¨ LuckPerms è£¡ç¨ä¾ç®¡çç¾¤çµï¼Groupï¼çä¸çµæä»¤ãç¾¤çµéåäºä¸äºéè¦çµ¦ä½¿ç¨èçæ¬éç¯é»ï¼ä½ å¯ä»¥ä½¿ç¨ãcreategroupãä¾æ°å¢ä¸åç¾¤çµã
luckperms.usage.track.description=å¨ LuckPerms è£¡ç¨ä¾ç®¡çæ¬ééç´ï¼Trackï¼çä¸çµæä»¤ãæ¬ééç´æ¯ç¾¤çµçæä½é åºï¼å¯ç¨æ¼å®ç¾©åç´åéç´ã
luckperms.usage.log.description=ç¨æ¼ç®¡ç LuckPerms ä¸­è¨éåè½çä¸çµæä»¤ã
luckperms.usage.sync.description=å¾æä»¶å²å­ç©ºééæ°è¼å¥ææè³æå°è¨æ¶é«ï¼ä¸¦å¥ç¨æª¢æ¸¬å°çä»»ä½è®æ´ã
luckperms.usage.info.description=ååºæéå·è¡ä¸­çæä»¶å¯¦ä¾çä¸è¬è¨æ¯ã
luckperms.usage.editor.description=å»ºç«ä¸åæ°çç¶²é ç·¨è¼¯å¨å·¥ä½éæ®µ
luckperms.usage.editor.argument.type=è¦è¼å¥è³ç·¨è¼¯å¨ä¸­çé¡åãï¼ãallãããusersãæãgroupsãï¼
luckperms.usage.editor.argument.filter=éæ¿¾ä½¿ç¨èæ¢ç®çæ¬é
luckperms.usage.verbose.description=æ§å¶æä»¶è©³ç´°æ¬éæª¢æ¥ç£æ§ç³»çµ±ã
luckperms.usage.verbose.argument.action=æ¯å¦è¦åç¨æåç¨è¨éåè½ï¼æä¸å³å·²è¨éçè¼¸åºã
luckperms.usage.verbose.argument.filter=ç¨æ¼æ¯å°é ç®çç¯©é¸å¨
luckperms.usage.verbose.argument.commandas=è¦å·è¡çç©å®¶ææä»¤
luckperms.usage.tree.description=ç¢ç LuckPerms å·²ç¥çæææ¬éçæ¨¹çæª¢è¦ï¼å·²æåºçæ¸å®éå±¤ï¼ã
luckperms.usage.tree.argument.scope=æ ¹ç®éæå®ã.ãä»¥åå«æææ¬é
luckperms.usage.tree.argument.player=è¦æª¢æ¥çç·ä¸ç©å®¶åç¨±
luckperms.usage.search.description=æå°ææç¹å®æ¬éçææä½¿ç¨èæç¾¤çµ
luckperms.usage.search.argument.permission=è¦æå°çæ¬é
luckperms.usage.search.argument.page=è¦æ¥ççé é¢
luckperms.usage.network-sync.description=å°ä¿®æ¹åæ­¥å°è³æåº«ä¸­ä¸¦ä¸ä¹å¨é£ç·çå¶ä»ä¼ºæå¨ä¸­åæ­¥
luckperms.usage.import.description=å¾ï¼ååå»ºç«çï¼å¯åºæªæ¡ä¸­å¯å¥è³æ
luckperms.usage.import.argument.file=è¦å¯å¥çæªæ¡
luckperms.usage.import.argument.replace=åä»£ç¾æè³æèä¸ä½åä½µ
luckperms.usage.import.argument.upload=ä¸å³ååå¯åºçè³æ
luckperms.usage.export.description=å°æææ¬éè³æå¯åºå°ãå¯åºãæªæ¡ãå¯å¨ç¨å¾éæ°å¯å¥ã
luckperms.usage.export.argument.file=è¦å¯åºçæªæ¡
luckperms.usage.export.argument.without-users=å¨å¯åºææé¤ä½¿ç¨è
luckperms.usage.export.argument.without-groups=å¨å¯åºææé¤ç¾¤çµ
luckperms.usage.export.argument.upload=å°æææ¬éè³æä¸å³å°ç¶²é ç·¨è¼¯å¨ãå¯å¨ç¨å¾éæ°å¯å¥ã
luckperms.usage.reload-config.description=éæ°è¼å¥ä¸äºè¨­å®çé¸é 
luckperms.usage.bulk-update.description=å°ææè³æå·è¡å¤§éè®æ´æ¥è©¢ã
luckperms.usage.bulk-update.argument.data-type=è¦è®æ´çè³æé¡åãï¼ãallãããusersãæãgroupsãï¼
luckperms.usage.bulk-update.argument.action=è¦å¨è³æä¸å·è¡çåä½ãï¼ãupdateãæãdeleteãï¼
luckperms.usage.bulk-update.argument.action-field=éè¦æ´æ°çååï¼åå¨ãupdateãæéè¦è¼¸å¥ãï¼ãpermissionãããserverãæãworldãï¼
luckperms.usage.bulk-update.argument.action-value=è¦åä»£çå¼ãåå¨ ''update'' æéè¦è¼¸å¥ã
luckperms.usage.bulk-update.argument.constraint=æ´æ°æéè¦çç´æ
luckperms.usage.translations.description=ç®¡çç¿»è­¯
luckperms.usage.translations.argument.install=å®è£ç¿»è­¯çå­æä»¤
luckperms.usage.apply-edits.description=å¥ç¨å¾ç¶²é ç·¨è¼¯å¨é²è¡çæ¬éè®æ´
luckperms.usage.apply-edits.argument.code=è³æçå¯ä¸ä»£ç¢¼
luckperms.usage.apply-edits.argument.target=å°è³æå¥ç¨å°èª°èº«ä¸
luckperms.usage.create-group.description=å»ºç«æ°ç¾¤çµ
luckperms.usage.create-group.argument.name=ç¾¤çµåç¨±
luckperms.usage.create-group.argument.weight=ç¾¤çµçæ¬é
luckperms.usage.create-group.argument.display-name=ç¾¤çµçé¡¯ç¤ºåç¨±
luckperms.usage.delete-group.description=åªé¤ç¾¤çµ
luckperms.usage.delete-group.argument.name=ç¾¤çµåç¨±
luckperms.usage.list-groups.description=ååºå¹³å°ä¸çææç¾¤çµ
luckperms.usage.create-track.description=å»ºç«æ°æ¬ééç´
luckperms.usage.create-track.argument.name=æ¬ééç´åç¨±
luckperms.usage.delete-track.description=åªé¤æ¬ééç´
luckperms.usage.delete-track.argument.name=æ¬ééç´åç¨±
luckperms.usage.list-tracks.description=ååºå¹³å°ä¸ææçæ¬ééç´
luckperms.usage.user-info.description=é¡¯ç¤ºæéä½¿ç¨èçè³è¨
luckperms.usage.user-switchprimarygroup.description=åæä½¿ç¨èçä¸»è¦ç¾¤çµ
luckperms.usage.user-switchprimarygroup.argument.group=è¦åæçç¾¤çµ
luckperms.usage.user-promote.description=æåä½¿ç¨èä¸åæ¬ééç´
luckperms.usage.user-promote.argument.track=è¦åç´ä½¿ç¨èçç®æ¨æ¬ééç´
luckperms.usage.user-promote.argument.context=è¦åç´ä½¿ç¨èçç°å¢
luckperms.usage.user-promote.argument.dont-add-to-first=åªæå·²ç¶å¨æ¬ééç´å§çä½¿ç¨èææåç´
luckperms.usage.user-demote.description=éä½ä½¿ç¨èä¸åæ¬ééç´
luckperms.usage.user-demote.argument.track=è¦éç´ä½¿ç¨èçç®æ¨æ¬ééç´
luckperms.usage.user-demote.argument.context=è¦éç´ä½¿ç¨èçç°å¢
luckperms.usage.user-demote.argument.dont-remove-from-first=é²æ­¢ä½¿ç¨èå¾ç¬¬ä¸ç¾¤çµä¸­ç§»é¤
luckperms.usage.user-clone.description=è¤è£½ä½¿ç¨è
luckperms.usage.user-clone.argument.user=è¦è¤è£½çä½¿ç¨èåç¨±æ UUID
luckperms.usage.group-info.description=é¡¯ç¤ºæéç¾¤çµçè³è¨
luckperms.usage.group-listmembers.description=é¡¯ç¤ºç¹¼æ¿æ­¤ç¾¤çµçä½¿ç¨èæç¾¤çµ
luckperms.usage.group-listmembers.argument.page=è¦æ¥ççé é¢
luckperms.usage.group-setweight.description=è¨­å®ç¾¤çµçæ¬é
luckperms.usage.group-setweight.argument.weight=è¦è¨­å®çæ¬é
luckperms.usage.group-set-display-name.description=è¨­å®ç¾¤çµçé¡¯ç¤ºåç¨±
luckperms.usage.group-set-display-name.argument.name=è¦è¨­å®çåç¨±
luckperms.usage.group-set-display-name.argument.context=è¦è¨­å®åç¨±çç°å¢
luckperms.usage.group-rename.description=éæ°å½åç¾¤çµ
luckperms.usage.group-rename.argument.name=æ°åç¨±
luckperms.usage.group-clone.description=è¤è£½ç¾¤çµ
luckperms.usage.group-clone.argument.name=è¦è¤è£½çç¾¤çµåç¨±
luckperms.usage.holder-editor.description=éåæ¬éç·¨è¼¯å¨ç¶²é 
luckperms.usage.holder-showtracks.description=ååºç©ä»¶æå¨çæ¬ééç´
luckperms.usage.holder-clear.description=ç§»é¤æææ¬éãç¶ç³»åä¸­ç¹¼è³æ
luckperms.usage.holder-clear.argument.context=è¦éæ¿¾çç°å¢
luckperms.usage.permission.description=ç·¨è¼¯æ¬é
luckperms.usage.parent.description=ç·¨è¼¯ç¹¼æ¿é 
luckperms.usage.meta.description=ç·¨è¼¯ä¸­ç¹¼è³æå¼
luckperms.usage.permission-info.description=ååºç©ä»¶ææçæ¬é
luckperms.usage.permission-info.argument.page=è¦æ¥ççé é¢
luckperms.usage.permission-info.argument.sort-mode=å¦ä½æåºæ¢ç®
luckperms.usage.permission-set.description=çºç©ä»¶è¨­å®æ¬é
luckperms.usage.permission-set.argument.node=è¦è¨­å®çæ¬éç¯é»
luckperms.usage.permission-set.argument.value=ç¯é»çå¼
luckperms.usage.permission-set.argument.context=è¦å¢å æ¬éçç°å¢
luckperms.usage.permission-unset.description=çºç©ä»¶è§£é¤è¨­å®æ¬é
luckperms.usage.permission-unset.argument.node=è¦è§£é¤è¨­å®çæ¬éç¯é»
luckperms.usage.permission-unset.argument.context=è¦ç§»é¤æ¬éçç°å¢
luckperms.usage.permission-settemp.description=çºç©ä»¶è¨­å®è¨ææ¬é
luckperms.usage.permission-settemp.argument.node=è¦è¨­å®çæ¬éç¯é»
luckperms.usage.permission-settemp.argument.value=ç¯é»çå¼
luckperms.usage.permission-settemp.argument.duration=æ¬éç¯é»çæææé
luckperms.usage.permission-settemp.argument.temporary-modifier=è¨ææ¬éçå¥ç¨æ¹å¼
luckperms.usage.permission-settemp.argument.context=è¦å¢å æ¬éçç°å¢
luckperms.usage.permission-unsettemp.description=çºç©ä»¶è§£é¤è¨­å®è¨ææ¬é
luckperms.usage.permission-unsettemp.argument.node=è¦è§£é¤è¨­å®çæ¬éç¯é»
luckperms.usage.permission-unsettemp.argument.duration=è¦æ¸å»çæé
luckperms.usage.permission-unsettemp.argument.context=è¦ç§»é¤æ¬éçç°å¢
luckperms.usage.permission-check.description=æª¢æ¥ç©ä»¶æ¯å¦ææç¹å®çæ¬éç¯é»
luckperms.usage.permission-check.argument.node=è¦æª¢æ¥çæ¬éç¯é»
luckperms.usage.permission-clear.description=æ¸é¤æææ¬é
luckperms.usage.permission-clear.argument.context=è¦éæ¿¾çç°å¢
luckperms.usage.parent-info.description=ååºè©²ç©ä»¶ç¹¼æ¿çç¾¤çµ
luckperms.usage.parent-info.argument.page=è¦æ¥ççé é¢
luckperms.usage.parent-info.argument.sort-mode=å¦ä½æåºæ¢ç®
luckperms.usage.parent-set.description=åªé¤ç©ä»¶ç¾æç¹¼æ¿çææç¾¤çµä¸¦å°å¶å¢å å°æå®çç¾¤çµ
luckperms.usage.parent-set.argument.group=è¦è¨­å®çç¾¤çµ
luckperms.usage.parent-set.argument.context=è¦è¨­å®ç¾¤çµçç°å¢
luckperms.usage.parent-add.description=è¨­å®å¦ä¸åç¹¼æ¿ç©ä»¶çæ¬éçç¾¤çµ
luckperms.usage.parent-add.argument.group=è¦ç¹¼æ¿çç¾¤çµ
luckperms.usage.parent-add.argument.context=è¦ç¹¼æ¿ç¾¤çµçç°å¢
luckperms.usage.parent-remove.description=åªé¤ååè¨­å®çç¹¼æ¿è¦å
luckperms.usage.parent-remove.argument.group=è¦ç§»é¤çç¾¤çµ
luckperms.usage.parent-remove.argument.context=è¦ç§»é¤ç¾¤çµçç°å¢
luckperms.usage.parent-set-track.description=åªé¤ç©ä»¶ç¾æå¨æ¬ééç´ç¹¼æ¿çææç¾¤çµä¸¦å°å¶å¢å å°æå®çç¾¤çµ
luckperms.usage.parent-set-track.argument.track=è¦è¨­å®çæ¬ééç´
luckperms.usage.parent-set-track.argument.group=è¦è¨­å®çç¾¤çµï¼ææ¯è©²ç¾¤çµå¨æå®çæ¬ééç´è£¡çä½ç½®
luckperms.usage.parent-set-track.argument.context=è¦è¨­å®ç¾¤çµçç°å¢
luckperms.usage.parent-add-temp.description=è¨­å®å¦ä¸åæ«æç¹¼æ¿ç©ä»¶çæ¬éçç¾¤çµ
luckperms.usage.parent-add-temp.argument.group=è¦ç¹¼æ¿çç¾¤çµ
luckperms.usage.parent-add-temp.argument.duration=ç¾¤çµæå¡çæææé
luckperms.usage.parent-add-temp.argument.temporary-modifier=å¦ä½å¥ç¨è¨ææ¬é
luckperms.usage.parent-add-temp.argument.context=è¦ç¹¼æ¿ç¾¤çµçç°å¢
luckperms.usage.parent-remove-temp.description=åªé¤ååè¨­å®çè¨æç¹¼æ¿è¦å
luckperms.usage.parent-remove-temp.argument.group=è¦ç§»é¤çç¾¤çµ
luckperms.usage.parent-remove-temp.argument.duration=è¦æ¸å»çæé
luckperms.usage.parent-remove-temp.argument.context=è¦ç§»é¤ç¾¤çµçç°å¢
luckperms.usage.parent-clear.description=æ¸é¤ææç¶ç³»
luckperms.usage.parent-clear.argument.context=è¦éæ¿¾çç°å¢
luckperms.usage.parent-clear-track.description=æ¸é¤æå®æ¬ééç´ä¸çææç¶ç³»ç¾¤çµ
luckperms.usage.parent-clear-track.argument.track=è¦ç§»é¤çæ¬ééç´
luckperms.usage.parent-clear-track.argument.context=è¦éæ¿¾çç°å¢
luckperms.usage.meta-info.description=é¡¯ç¤ºææèå¤©ä¸­ç¹¼è³æ
luckperms.usage.meta-set.description=è¨­å®ä¸ååå¼
luckperms.usage.meta-set.argument.key=è¦è¨­å®çéé°
luckperms.usage.meta-set.argument.value=è¦è¨­å®çå¼
luckperms.usage.meta-set.argument.context=è¦å¢å ä¸­ç¹¼è³æçç°å¢
luckperms.usage.meta-unset.description=è§£é¤è¨­å®ä¸ååå¼
luckperms.usage.meta-unset.argument.key=è¦è§£é¤è¨­å®çéé°
luckperms.usage.meta-unset.argument.context=è¦ç§»é¤åè³æçç°å¢
luckperms.usage.meta-settemp.description=è¨­å®ä¸åè¨æåå¼
luckperms.usage.meta-settemp.argument.key=è¦è¨­å®çéé°
luckperms.usage.meta-settemp.argument.value=è¦è¨­å®çå¼
luckperms.usage.meta-settemp.argument.duration=åå¼çæææé
luckperms.usage.meta-settemp.argument.context=è¦å¢å ä¸­ç¹¼è³æçç°å¢
luckperms.usage.meta-unsettemp.description=è§£é¤è¨­å®ä¸åè¨æåå¼
luckperms.usage.meta-unsettemp.argument.key=è¦è§£é¤è¨­å®çéé°
luckperms.usage.meta-unsettemp.argument.context=è¦ç§»é¤ä¸­ç¹¼è³æçç°å¢
luckperms.usage.meta-addprefix.description=å¢å ä¸ååç¶´
luckperms.usage.meta-addprefix.argument.priority=è¦å¢å åç¶´çåªåæ¬
luckperms.usage.meta-addprefix.argument.prefix=åç¶´å­ä¸²
luckperms.usage.meta-addprefix.argument.context=è¦å¢å åç¶´çç°å¢
luckperms.usage.meta-addsuffix.description=å¢å ä¸åå¾ç¶´
luckperms.usage.meta-addsuffix.argument.priority=è¦å¢å å¾ç¶´çåªåæ¬
luckperms.usage.meta-addsuffix.argument.suffix=å¾ç¶´å­ä¸²
luckperms.usage.meta-addsuffix.argument.context=è¦å¢å å¾ç¶´çç°å¢
luckperms.usage.meta-setprefix.description=è¨­å®ä¸ååç¶´
luckperms.usage.meta-setprefix.argument.priority=è¦è¨­å®åç¶´çåªåæ¬
luckperms.usage.meta-setprefix.argument.prefix=åç¶´å­ä¸²
luckperms.usage.meta-setprefix.argument.context=è¦è¨­å®åç¶´çç°å¢
luckperms.usage.meta-setsuffix.description=è¨­å®å¾ç¶´
luckperms.usage.meta-setsuffix.argument.priority=è¦è¨­å®å¾ç¶´çåªåæ¬
luckperms.usage.meta-setsuffix.argument.suffix=å¾ç¶´æå­
luckperms.usage.meta-setsuffix.argument.context=è¦è¨­å®å¾ç¶´çç°å¢
luckperms.usage.meta-removeprefix.description=ç§»é¤ä¸ååç¶´
luckperms.usage.meta-removeprefix.argument.priority=è¦ç§»é¤åç¶´çåªåæ¬
luckperms.usage.meta-removeprefix.argument.prefix=åç¶´å­ä¸²
luckperms.usage.meta-removeprefix.argument.context=è¦ç§»é¤åç¶´çç°å¢
luckperms.usage.meta-removesuffix.description=ç§»é¤ä¸åå¾ç¶´
luckperms.usage.meta-removesuffix.argument.priority=è¦ç§»é¤å¾ç¶´çåªåæ¬
luckperms.usage.meta-removesuffix.argument.suffix=å¾ç¶´å­ä¸²
luckperms.usage.meta-removesuffix.argument.context=è¦ç§»é¤å¾ç¶´çç°å¢
luckperms.usage.meta-addtemp-prefix.description=å¢å ä¸åè¨æåç¶´
luckperms.usage.meta-addtemp-prefix.argument.priority=è¦å¢å åç¶´çåªåæ¬
luckperms.usage.meta-addtemp-prefix.argument.prefix=åç¶´å­ä¸²
luckperms.usage.meta-addtemp-prefix.argument.duration=åç¶´çæææé
luckperms.usage.meta-addtemp-prefix.argument.context=è¦å¢å åç¶´çç°å¢
luckperms.usage.meta-addtemp-suffix.description=å¢å ä¸åè¨æå¾ç¶´
luckperms.usage.meta-addtemp-suffix.argument.priority=è¦å¢å å¾ç¶´çåªåæ¬
luckperms.usage.meta-addtemp-suffix.argument.suffix=å¾ç¶´å­ä¸²
luckperms.usage.meta-addtemp-suffix.argument.duration=å¾ç¶´çæææé
luckperms.usage.meta-addtemp-suffix.argument.context=è¦å¢å å¾ç¶´çç°å¢
luckperms.usage.meta-settemp-prefix.description=è¨­å®ä¸åè¨æåç¶´
luckperms.usage.meta-settemp-prefix.argument.priority=è¦è¨­å®åç¶´çåªåæ¬
luckperms.usage.meta-settemp-prefix.argument.prefix=åç¶´å­ä¸²
luckperms.usage.meta-settemp-prefix.argument.duration=åç¶´çæææé
luckperms.usage.meta-settemp-prefix.argument.context=è¦è¨­å®åç¶´çç°å¢
luckperms.usage.meta-settemp-suffix.description=è¨­å®ä¸åè¨æå¾ç¶´
luckperms.usage.meta-settemp-suffix.argument.priority=è¦è¨­å®å¾ç¶´çåªåæ¬
luckperms.usage.meta-settemp-suffix.argument.suffix=å¾ç¶´å­ä¸²
luckperms.usage.meta-settemp-suffix.argument.duration=å¾ç¶´çæææé
luckperms.usage.meta-settemp-suffix.argument.context=è¦è¨­å®å¾ç¶´çç°å¢
luckperms.usage.meta-removetemp-prefix.description=ç§»é¤ä¸åè¨æåç¶´
luckperms.usage.meta-removetemp-prefix.argument.priority=è¦ç§»é¤åç¶´çåªåæ¬
luckperms.usage.meta-removetemp-prefix.argument.prefix=åç¶´å­ä¸²
luckperms.usage.meta-removetemp-prefix.argument.context=è¦ç§»é¤åç¶´çç°å¢
luckperms.usage.meta-removetemp-suffix.description=ç§»é¤ä¸åè¨æå¾ç¶´
luckperms.usage.meta-removetemp-suffix.argument.priority=è¦ç§»é¤å¾ç¶´çåªåæ¬
luckperms.usage.meta-removetemp-suffix.argument.suffix=å¾ç¶´å­ä¸²
luckperms.usage.meta-removetemp-suffix.argument.context=è¦ç§»é¤å¾ç¶´çç°å¢
luckperms.usage.meta-clear.description=æ¸é¤ææä¸­ç¹¼è³æ
luckperms.usage.meta-clear.argument.type=è¦ç§»é¤çä¸­ç¹¼è³æé¡å
luckperms.usage.meta-clear.argument.context=è¦éæ¿¾çç°å¢
luckperms.usage.track-info.description=é¡¯ç¤ºæéæ¬ééç´çè³è¨
luckperms.usage.track-editor.description=éåæ¬éç·¨è¼¯å¨ç¶²é 
luckperms.usage.track-append.description=å°ä¸åç¾¤çµè¿½å å°æ¬ééç´ççµå°¾
luckperms.usage.track-append.argument.group=è¦éå ççµå¥
luckperms.usage.track-insert.description=å¨æ¬ééç´çæå®ä½ç½®æå¥ç¾¤çµ
luckperms.usage.track-insert.argument.group=è¦æå¥ççµå¥
luckperms.usage.track-insert.argument.position=æå¥ç¾¤çµçä½ç½®ï¼æ¬ééç´ä¸çç¬¬ä¸åä½ç½®æ¯ 1ï¼
luckperms.usage.track-remove.description=å¾æ¬ééç´ä¸­ç§»é¤ç¾¤çµ
luckperms.usage.track-remove.argument.group=è¦ç§»é¤çç¾¤çµ
luckperms.usage.track-clear.description=æ¸é¤æ¬ééç´ä¸çç¾¤çµ
luckperms.usage.track-rename.description=éæ°å½åæ¬ééç´
luckperms.usage.track-rename.argument.name=æ°åç¨±
luckperms.usage.track-clone.description=è¤è£½æ¬ééç´
luckperms.usage.track-clone.argument.name=è¦è¤è£½çæ¬ééç´åç¨±
luckperms.usage.log-recent.description=æ¥çæè¿çæä½
luckperms.usage.log-recent.argument.user=è¦éæ¿¾çä½¿ç¨èåç¨±æ UUID
luckperms.usage.log-recent.argument.page=è¦æ¥ççé ç¢¼
luckperms.usage.log-search.description=å¨æ¥èªä¸­æå°æ¢ç®
luckperms.usage.log-search.argument.query=è¦æå°çæ¥è©¢
luckperms.usage.log-search.argument.page=è¦æ¥ççé ç¢¼
luckperms.usage.log-notify.description=åææ¬ééç¥éåæéé
luckperms.usage.log-notify.argument.toggle=æå®æ¬ééç¥éåæéé
luckperms.usage.log-user-history.description=æ¥çä½¿ç¨èçæ­·ç¨è¨é
luckperms.usage.log-user-history.argument.user=ä½¿ç¨èåç¨±æ UUID
luckperms.usage.log-user-history.argument.page=è¦æ¥ççé ç¢¼
luckperms.usage.log-group-history.description=æ¥çç¾¤çµçæ­·ç¨è¨é
luckperms.usage.log-group-history.argument.group=ç¾¤çµåç¨±
luckperms.usage.log-group-history.argument.page=è¦æ¥ççé ç¢¼
luckperms.usage.log-track-history.description=æ¥çæ­·ç¨è¨é
luckperms.usage.log-track-history.argument.track=æ­·ç¨åç¨±
luckperms.usage.log-track-history.argument.page=è¦æ¥ççé ç¢¼
luckperms.usage.sponge.description=ç·¨è¼¯é¡å¤ç Sponge è³æ
luckperms.usage.sponge.argument.collection=è¦æ¥è©¢çéå
luckperms.usage.sponge.argument.subject=è¦ä¿®æ¹çä¸»é«
luckperms.usage.sponge-permission-info.description=é¡¯ç¤ºä¸»é«çæ¬éè³è¨
luckperms.usage.sponge-permission-info.argument.contexts=è¦éæ¿¾çç°å¢
luckperms.usage.sponge-permission-set.description=çºä¸»é«è¨­å®æ¬é
luckperms.usage.sponge-permission-set.argument.node=è¦è¨­å®çæ¬éç¯é»
luckperms.usage.sponge-permission-set.argument.tristate=è¦è¨­å®æ¬éçå¼
luckperms.usage.sponge-permission-set.argument.contexts=è¦è¨­å®æ¬éçç°å¢
luckperms.usage.sponge-permission-clear.description=æ¸é¤ä¸»é«çæ¬é
luckperms.usage.sponge-permission-clear.argument.contexts=è¦æ¸é¤æ¬éçç°å¢
luckperms.usage.sponge-parent-info.description=é¡¯ç¤ºä¸»é«çç¶ç³»è³è¨
luckperms.usage.sponge-parent-info.argument.contexts=è¦éæ¿¾çç°å¢
luckperms.usage.sponge-parent-add.description=çºä¸»é«å¢å ä¸åç¶ç³»
luckperms.usage.sponge-parent-add.argument.collection=ç®æ¨ç¶ç³»æå¨çä¸»é«éå
luckperms.usage.sponge-parent-add.argument.subject=ç¶ç³»åç¨±
luckperms.usage.sponge-parent-add.argument.contexts=è¦å¢å ç¶ç³»çç°å¢
luckperms.usage.sponge-parent-remove.description=å¾ä¸»é«ç§»é¤ç¶ç³»
luckperms.usage.sponge-parent-remove.argument.collection=ç®æ¨ç¶ç³»æå¨çä¸»é«éå
luckperms.usage.sponge-parent-remove.argument.subject=ç¶ç³»åç¨±
luckperms.usage.sponge-parent-remove.argument.contexts=è¦ç§»é¤ç¶ç³»çç°å¢
luckperms.usage.sponge-parent-clear.description=æ¸é¤ä¸»é«çç¶ç³»
luckperms.usage.sponge-parent-clear.argument.contexts=è¦æ¸é¤ç¶ç³»çç°å¢
luckperms.usage.sponge-option-info.description=é¡¯ç¤ºæéä¸»é«çé¸é çè³è¨
luckperms.usage.sponge-option-info.argument.contexts=è¦ç¯©é¸çæå¢
luckperms.usage.sponge-option-set.description=çºä¸»é«è¨­å®ä¸åé¸é 
luckperms.usage.sponge-option-set.argument.key=è¦è¨­å®çéé°
luckperms.usage.sponge-option-set.argument.value=è¦è¨­å®éé°çå¼
luckperms.usage.sponge-option-set.argument.contexts=è¦è¨­å®é¸é çç°å¢
luckperms.usage.sponge-option-unset.description=çºä¸»é«è§£é¤è¨­å®ä¸åé¸é 
luckperms.usage.sponge-option-unset.argument.key=è¦è§£é¤è¨­å®çéé°
luckperms.usage.sponge-option-unset.argument.contexts=è¦è§£é¤è¨­å®éé°çç°å¢
luckperms.usage.sponge-option-clear.description=æ¸é¤ä¸»é«çé¸é 
luckperms.usage.sponge-option-clear.argument.contexts=è¦æ¸é¤é¸é çç°å¢
