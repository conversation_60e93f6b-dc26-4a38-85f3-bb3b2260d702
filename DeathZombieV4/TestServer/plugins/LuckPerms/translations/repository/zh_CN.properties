luckperms.logs.actionlog-prefix=æ¥å¿
luckperms.logs.verbose-prefix=æéæ£æ¥
luckperms.logs.export-prefix=å¯¼åº
luckperms.commandsystem.available-commands=ä½¿ç¨ {0} æ¥æ¥çå¯ç¨çå½ä»¤
luckperms.commandsystem.command-not-recognised=æªç¥çå½ä»¤
luckperms.commandsystem.no-permission=ä½ æ²¡æä½¿ç¨æ­¤å½ä»¤çæé\!
luckperms.commandsystem.no-permission-subcommands=ä½ æ²¡ææéä½¿ç¨ä»»ä½å­å½ä»¤
luckperms.commandsystem.already-executing-command=æ­£å¨æ§è¡å¦ä¸ä¸ªå½ä»¤ï¼è¯·ç­å¾å®æ...
luckperms.commandsystem.usage.sub-commands-header=å­å½ä»¤
luckperms.commandsystem.usage.usage-header=å½ä»¤ä½¿ç¨æ¹æ³
luckperms.commandsystem.usage.arguments-header=åæ°
luckperms.first-time.no-permissions-setup=ä¼¼ä¹è¿æ²¡æè®¾ç½®æé\!
luckperms.first-time.use-console-to-give-access=å¨æ¸¸æä¸­ä½¿ç¨ LuckPerms å½ä»¤ä¹å, ä½ éè¦å¨æ§å¶å°ä¸­ç»èªå·±ä½¿ç¨ LuckPerms çæé
luckperms.first-time.console-command-prompt=æå¼æ§å¶å°å¹¶æ§è¡ä¸é¢çå½ä»¤
luckperms.first-time.next-step=å®æè¿äºå·¥ä½å, ä½ å°±å¯ä»¥å¼å§å®ä¹æéåéåæéç»å¦
luckperms.first-time.wiki-prompt=ä¸ç¥éä»åªéå¼å§ï¼ç¹å»è¿éæ¥ç\: {0}
luckperms.login.try-again=è¯·ç¨ååè¯
luckperms.login.loading-database-error=å¨å è½½æéæ°æ®æ¶åçäºä¸ä¸ªæ°æ®åºéè¯¯
luckperms.login.server-admin-check-console-errors=å¦æä½ æ¯æå¡å¨ç®¡çå, è¯·æ£æ¥æ§å¶å°æ¯å¦æéè¯¯
luckperms.login.server-admin-check-console-info=è¯·æ£æ¥æå¡å¨æ§å¶å°äºè§£æ´å¤ä¿¡æ¯
luckperms.login.data-not-loaded-at-pre=å¨é¢ç»å½é¶æ®µæªè½å è½½ç¨æ·æéæ°æ®
luckperms.login.unable-to-continue=æ æ³ç»§ç»­
luckperms.login.craftbukkit-offline-mode-error=è¿å¯è½æ¯ç±äº CraftBukkit å online-mode è®¾ç½®ä¹é´çå²çª
luckperms.login.unexpected-error=å¨è®¾ç½®æéæ°æ®æ¶åçäºä¸ä¸ªæå¤çéè¯¯
luckperms.opsystem.disabled=æ­¤æå¡å¨ç¦ç¨äºåçOPç³»ç»
luckperms.opsystem.sponge-warning=è¯·æ³¨æ\: å½å®è£äºæéæä»¶æ¶, æå¡å¨OPèº«ä»½å¯¹Spongeæéæ£æ¥æ²¡æå½±å, ä½ å¿é¡»ç´æ¥ç¼è¾ç¨æ·æ°æ®
luckperms.duration.unit.years.plural={0} å¹´
luckperms.duration.unit.years.singular={0} å¹´
luckperms.duration.unit.years.short={0}å¹´
luckperms.duration.unit.months.plural={0} æ
luckperms.duration.unit.months.singular={0} æ
luckperms.duration.unit.months.short={0}æ
luckperms.duration.unit.weeks.plural={0} å¨
luckperms.duration.unit.weeks.singular={0} å¨
luckperms.duration.unit.weeks.short={0}å¨
luckperms.duration.unit.days.plural={0} å¤©
luckperms.duration.unit.days.singular={0} å¤©
luckperms.duration.unit.days.short={0}å¤©
luckperms.duration.unit.hours.plural={0} å°æ¶
luckperms.duration.unit.hours.singular={0} å°æ¶
luckperms.duration.unit.hours.short={0}æ¶
luckperms.duration.unit.minutes.plural={0} åé
luckperms.duration.unit.minutes.singular={0} åé
luckperms.duration.unit.minutes.short={0}å
luckperms.duration.unit.seconds.plural={0} ç§
luckperms.duration.unit.seconds.singular={0} ç§
luckperms.duration.unit.seconds.short={0}ç§
luckperms.duration.since={0} å
luckperms.command.misc.invalid-code=æ æä»£ç 
luckperms.command.misc.response-code-key=ååºä»£ç 
luckperms.command.misc.error-message-key=æ¶æ¯
luckperms.command.misc.bytebin-unable-to-communicate=æ æ³ä¸ bytebin éä¿¡
luckperms.command.misc.webapp-unable-to-communicate=æ æ³ä¸ç½é¡µç¼è¾å¨éä¿¡
luckperms.command.misc.check-console-for-errors=æ£æ¥æ§å¶å°æ¯å¦æéè¯¯
luckperms.command.misc.file-must-be-in-data=æä»¶ {0} å¿é¡»ç´æ¥æ¾å¨æ°æ®ç®å½ä¸­
luckperms.command.misc.wait-to-finish=è¯·ç­å¾å®å®æååè¯ä¸æ¬¡
luckperms.command.misc.invalid-priority=æ æçä¼åçº§ {0}
luckperms.command.misc.expected-number=éè¦è¾å¥æ°å­
luckperms.command.misc.date-parse-error=æ æ³è§£ææ¥æ {0}
luckperms.command.misc.date-in-past-error=ä¸è½è®¾ç½®å·²ç»è¿å»çæ¥æ\!
luckperms.command.misc.page=ç¬¬ {0} é¡µ, å± {1} é¡µ
luckperms.command.misc.page-entries={0} é¡¹
luckperms.command.misc.none=æ 
luckperms.command.misc.loading.error.unexpected=åçäºä¸ä¸ªæå¤çéè¯¯
luckperms.command.misc.loading.error.user=æªå è½½ç¨æ·
luckperms.command.misc.loading.error.user-specific=æ æ³å è½½ç®æ ç¨æ· {0}
luckperms.command.misc.loading.error.user-not-found=æ æ³æ¾å° {0} è¿ä¸ªç¨æ·
luckperms.command.misc.loading.error.user-save-error=å¨ä¿å­ {0} çç¨æ·æ°æ®æ¶åºç°äºéè¯¯
luckperms.command.misc.loading.error.user-not-online=ç¨æ· {0} æªå¨çº¿
luckperms.command.misc.loading.error.user-invalid={0} ä¸æ¯ä¸ä¸ªææçç¨æ·åç§°æUUID
luckperms.command.misc.loading.error.user-not-uuid=ç®æ ç¨æ· {0} ä¸æ¯ä¸ä¸ªææçUUID
luckperms.command.misc.loading.error.group=æªå è½½æéç»
luckperms.command.misc.loading.error.all-groups=æ æ³å è½½æææéç»
luckperms.command.misc.loading.error.group-not-found=æ æ³æ¾å°ä¸ä¸ªåä¸º {0} çæéç»
luckperms.command.misc.loading.error.group-save-error=å¨ä¿å­æéç» {0} çæ°æ®æ¶åºç°äºéè¯¯
luckperms.command.misc.loading.error.group-invalid={0} ä¸æ¯ä¸ä¸ªææçæéç»åç§°
luckperms.command.misc.loading.error.track=æªå è½½æéç»è·¯çº¿
luckperms.command.misc.loading.error.all-tracks=æ æ³å è½½æææéç»è·¯çº¿
luckperms.command.misc.loading.error.track-not-found=æ æ³æ¾å°ä¸ä¸ªåä¸º {0} çæéç»è·¯çº¿
luckperms.command.misc.loading.error.track-save-error=å¨ä¿å­æéç»è·¯çº¿ {0} çæ°æ®æ¶åºç°äºéè¯¯
luckperms.command.misc.loading.error.track-invalid={0} ä¸æ¯ä¸ä¸ªææçæéç»è·¯çº¿åç§°
luckperms.command.editor.no-match=æ æ³æå¼ç½é¡µç¼è¾å¨, æ²¡æå¹éå°æéç±»åçå¯¹è±¡
luckperms.command.editor.start=æ­£å¨åå¤ä¸ä¸ªæ°çç¼è¾ä¼è¯, è¯·ç¨å...
luckperms.command.editor.url=ç¹å»ä¸é¢çé¾æ¥æå¼ç½é¡µç¼è¾å¨
luckperms.command.editor.unable-to-communicate=æ æ³ä¸ç½é¡µç¼è¾å¨éä¿¡
luckperms.command.editor.apply-edits.success=ç½é¡µç¼è¾å¨çæ°æ®å·²æååºç¨å° {0} {1}
luckperms.command.editor.apply-edits.success-summary={0} {1} å {2} {3}
luckperms.command.editor.apply-edits.success.additions=æ°å¢
luckperms.command.editor.apply-edits.success.additions-singular=æ°å¢
luckperms.command.editor.apply-edits.success.deletions=å å
luckperms.command.editor.apply-edits.success.deletions-singular=å å
luckperms.command.editor.apply-edits.no-changes=æ²¡æä»ç½é¡µç¼è¾å¨ä¸­åºç¨ä»»ä½æ´æ¹ï¼å ä¸ºè¿åçæ°æ®ä¸åå«ä»»ä½ä¿®æ¹
luckperms.command.editor.apply-edits.unknown-type=æ æ³å°ç¼è¾åºç¨äºæå®çå¯¹è±¡ç±»å
luckperms.command.editor.apply-edits.unable-to-read=æ æ³ä½¿ç¨ç»å®çä»£ç è¯»åæ°æ®
luckperms.command.search.searching.permission=æç´¢å¸¦æ {0} çç¨æ·åæéç»
luckperms.command.search.searching.inherit=æç´¢ç»§æ¿èª {0} çç¨æ·åæéç»
luckperms.command.search.result=åç°æ¥èª {1} ä¸ªç¨æ·å {2} ä¸ªæéç»ç {0} ä¸ªæ¡ç®
luckperms.command.search.result.default-notice=æ³¨æ\: å¨æç´¢é»è®¤ç»çæåæ¶, æ²¡æå¶ä»æéçç¦»çº¿ç©å®¶å°ä¸ä¼è¢«æ¾ç¤ºåºæ¥\!
luckperms.command.search.showing-users=æ¾ç¤ºç¨æ·æ¡ç®
luckperms.command.search.showing-groups=æ¾ç¤ºæéç»æ¡ç®
luckperms.command.tree.start=æ­£å¨çææéæ , è¯·ç¨å...
luckperms.command.tree.empty=æ æ³çææéæ , æ²¡ææ¾å°ç»æ
luckperms.command.tree.url=æéæ  URL
luckperms.command.verbose.invalid-filter={0} ä¸æ¯ä¸ä¸ªææçè¯¦ç»æ¥å¿è¿æ»¤å¨
luckperms.command.verbose.enabled=è¯¦ç»æ¥å¿ {0} è¿æ»¤å¨\: {1}
luckperms.command.verbose.command-exec=å¼ºå¶ {0} æ§è¡å½ä»¤ {1} å¹¶æ¥åææçæ£æ¥ç»æ...
luckperms.command.verbose.off=è¯¦ç»æ¥å¿ {0}
luckperms.command.verbose.command-exec-complete=å½ä»¤æ§è¡å®æ¯
luckperms.command.verbose.command.no-checks=å½ä»¤æ§è¡å®æ¯, ä½æ²¡æè¿è¡æéæ£æ¥
luckperms.command.verbose.command.possibly-async=è¿å¯è½æ¯å ä¸ºè¯¥æä»¶å¨åå°è¿è¡å½ä»¤ (å¼æ­¥)
luckperms.command.verbose.command.try-again-manually=ä½ ä»ç¶å¯ä»¥æå¨ä½¿ç¨ verbose æ¥æ£æµ
luckperms.command.verbose.enabled-recording=è¯¦ç»æ¥å¿ {0} è¿æ»¤å¨\: {1}
luckperms.command.verbose.uploading=è¯¦ç»æ¥å¿ {0}, æ­£å¨ä¸ä¼ è®°å½...
luckperms.command.verbose.url=è¯¦ç»çæ¥å¿è®°å½ URL
luckperms.command.verbose.enabled-term=å¯ç¨
luckperms.command.verbose.disabled-term=ç¦ç¨
luckperms.command.verbose.query-any=ä»»æ
luckperms.command.info.running-plugin=æ­£å¨è¿è¡
luckperms.command.info.platform-key=å¹³å°
luckperms.command.info.server-brand-key=æå¡å¨ç±»å
luckperms.command.info.server-version-key=æå¡å¨çæ¬
luckperms.command.info.storage-key=å­å¨
luckperms.command.info.storage-type-key=ç±»å
luckperms.command.info.storage.meta.split-types-key=ç±»å
luckperms.command.info.storage.meta.ping-key=å»¶è¿
luckperms.command.info.storage.meta.connected-key=å·²è¿æ¥
luckperms.command.info.storage.meta.file-size-key=æä»¶å¤§å°
luckperms.command.info.extensions-key=æ©å±
luckperms.command.info.messaging-key=æ¶æ¯æå¡
luckperms.command.info.instance-key=å®ä¾
luckperms.command.info.static-contexts-key=éææå¢
luckperms.command.info.online-players-key=å¨çº¿ç©å®¶
luckperms.command.info.online-players-unique={0} å¯ä¸
luckperms.command.info.uptime-key=è¿è¡æ¶é´
luckperms.command.info.local-data-key=æ¬å°æ°æ®
luckperms.command.info.local-data={0} ä¸ªç¨æ·, {1} ä¸ªæéç», {2} ä¸ªæéç»è·¯çº¿
luckperms.command.generic.create.success={0} å·²æååå»º
luckperms.command.generic.create.error=åå»º {0} æ¶åºç°äºä¸ä¸ªéè¯¯
luckperms.command.generic.create.error-already-exists={0} å·²å­å¨
luckperms.command.generic.delete.success={0} å·²æåå é¤
luckperms.command.generic.delete.error=å é¤ {0} æ¶åºç°äºä¸ä¸ªéè¯¯
luckperms.command.generic.delete.error-doesnt-exist={0} å¹¶ä¸å­å¨
luckperms.command.generic.rename.success={0} æåéåä¸º {1}
luckperms.command.generic.clone.success={0} å·²æååéå° {1}
luckperms.command.generic.info.parent.title=ç¶æéç»
luckperms.command.generic.info.parent.temporary-title=ä¸´æ¶ç¶æéç»
luckperms.command.generic.info.expires-in=è¿ææ¶é´
luckperms.command.generic.info.inherited-from=ç»§æ¿èª
luckperms.command.generic.info.inherited-from-self=èªå·±
luckperms.command.generic.show-tracks.title={0} çæéç»è·¯çº¿
luckperms.command.generic.show-tracks.empty={0} ä¸å¨ä»»ä½æéç»è·¯çº¿ä¸
luckperms.command.generic.clear.node-removed={0} ä¸ªæéèç¹è¢«ç§»é¤
luckperms.command.generic.clear.node-removed-singular=æéèç¹ {0} å·²ç§»é¤
luckperms.command.generic.clear={0} å¨æå¢ {1} ä¸­çèç¹å·²è¢«æ¸é¤
luckperms.command.generic.permission.info.title={0} çæé
luckperms.command.generic.permission.info.empty={0} æ²¡æè®¾ç½®ä»»ä½æé
luckperms.command.generic.permission.info.click-to-remove=ç¹å»ä» {0} ä¸­ç§»é¤æ­¤æéèç¹
luckperms.command.generic.permission.check.info.title={0} çæéä¿¡æ¯
luckperms.command.generic.permission.check.info.directly={0} å·²å¨æå¢ {3} ä¸­å° {1} è®¾ç½®ä¸º {2}
luckperms.command.generic.permission.check.info.inherited={0} å¨æå¢ {4} ä¸­ç»§æ¿ç {3} å° {1} è®¾ç½®ä¸º {2}
luckperms.command.generic.permission.check.info.not-directly={0} æ²¡æè®¾ç½® {1}
luckperms.command.generic.permission.check.info.not-inherited={0} æ²¡æç»§æ¿ {1}
luckperms.command.generic.permission.check.result.title=å¯¹ {0} çæéæ£æ¥
luckperms.command.generic.permission.check.result.result-key=ç»æ
luckperms.command.generic.permission.check.result.processor-key=å¤çå¨
luckperms.command.generic.permission.check.result.cause-key=åå 
luckperms.command.generic.permission.check.result.context-key=æå¢
luckperms.command.generic.permission.set=å¨æå¢ {3} ä¸­å° {2} çæé {0} è®¾ç½®ä¸º {1}
luckperms.command.generic.permission.already-has={0} å¨æå¢ {2} ä¸­å·²ç»è®¾ç½®äº {1}
luckperms.command.generic.permission.set-temp=å¨æå¢ {4} ä¸­å° {2} çæé {0} è®¾ç½®ä¸º {1}, æææ\: {3}
luckperms.command.generic.permission.already-has-temp={0} å¨æå¢ {2} ä¸­å·²ç»ä¸´æ¶è®¾ç½®äº {1}
luckperms.command.generic.permission.unset=å¨æå¢ {2} ä¸­ä¸º {1} åæ¶è®¾ç½® {0}
luckperms.command.generic.permission.doesnt-have={0} æ²¡æå¨æå¢ {2} ä¸­è®¾ç½® {1}
luckperms.command.generic.permission.unset-temp=å¨æå¢ {2} ä¸­ä¸º {1} åæ¶è®¾ç½®ä¸´æ¶æé {0}
luckperms.command.generic.permission.subtract=å¨æå¢ {4} ä¸­å° {2} çæé {0} è®¾ç½®ä¸º {1}, æææ\: {3}, æ¯ä¹åå°äº {5}
luckperms.command.generic.permission.doesnt-have-temp={0} æ²¡æå¨æå¢ {2} ä¸­ä¸´æ¶è®¾ç½® {1}
luckperms.command.generic.permission.clear={0} å¨æå¢ {1} ä¸­çæéå·²è¢«æ¸é¤
luckperms.command.generic.parent.info.title={0} çç¶æéç»
luckperms.command.generic.parent.info.empty={0} æ²¡æå®ä¹ä»»ä½ç¶æéç»
luckperms.command.generic.parent.info.click-to-remove=åå»ä»¥ä» {0} ä¸­ç§»é¤æ­¤ç¶æéç»
luckperms.command.generic.parent.add={0} ç°å¨å¨æå¢ {2} ä¸­ç»§æ¿ {1} çæé
luckperms.command.generic.parent.add-temp={0} ç°å¨å¨æå¢ {3} ä¸­ç»§æ¿ {1} çæé, æææ\: {2}
luckperms.command.generic.parent.set={0} å¨æå¢ {2} ä¸­æ¸é¤äºç°æçç¶æéç», ç°å¨åªç»§æ¿ {1}
luckperms.command.generic.parent.set-track={0} å¨æå¢ {3} ä¸­æ¸é¤äºåå«å¨æéç»è·¯çº¿ {1} ä¸­çç¶æéç», ç°å¨åªç»§æ¿ {2}
luckperms.command.generic.parent.remove={0} å¨æå¢ {2} ä¸­ä¸åç»§æ¿ {1} çæé
luckperms.command.generic.parent.remove-temp={0} å¨æå¢ {2} ä¸­ä¸åä¸´æ¶ç»§æ¿ {1} çæé
luckperms.command.generic.parent.subtract={0} å¨æå¢ {3} ä¸­ç»§æ¿ {1} çæé, æææ\: {2}, æ¯ä¹åå°äº {4}
luckperms.command.generic.parent.clear={0} å¨æå¢ {1} ä¸­çç¶æéç»å·²è¢«æ¸é¤
luckperms.command.generic.parent.clear-track={0} å¨æå¢ {2} ä¸­æ¸é¤äºåå«å¨æéç»è·¯çº¿ {1} ä¸­çç¶æéç»
luckperms.command.generic.parent.already-inherits={0} å¨æå¢ {2} ä¸­å·²ç»ç»§æ¿ {1} çæé
luckperms.command.generic.parent.doesnt-inherit={0} æ²¡æå¨ä¸ä¸æ {2} ä¸­ç»§æ¿ {1} çæé
luckperms.command.generic.parent.already-temp-inherits={0} å¨æå¢ {2} ä¸­å·²ç»ä¸´æ¶ç»§æ¿ {1} çæé
luckperms.command.generic.parent.doesnt-temp-inherit={0} æ²¡æå¨æå¢ {2} ä¸­ä¸´æ¶ç»§æ¿ {1} çæé
luckperms.command.generic.chat-meta.info.title-prefix={0} çåç¼
luckperms.command.generic.chat-meta.info.title-suffix={0} çåç¼
luckperms.command.generic.chat-meta.info.none-prefix={0} æ²¡æåç¼
luckperms.command.generic.chat-meta.info.none-suffix={0} æ²¡æåç¼
luckperms.command.generic.chat-meta.info.click-to-remove=åå»ä»¥ä» {1} ä¸­ç§»é¤ {0}
luckperms.command.generic.chat-meta.already-has={0} å¨æå¢ {4} ä¸­å·²ç»è®¾ç½®äº {1} {2}, ä¼åçº§ä¸º {3}
luckperms.command.generic.chat-meta.already-has-temp={0} å¨æå¢ {4} ä¸­å·²ç»ä¸´æ¶è®¾ç½®äº {1} {2}, ä¼åçº§ä¸º {3}
luckperms.command.generic.chat-meta.doesnt-have={0} æ²¡æå¨æå¢ {4} ä¸­è®¾ç½®ä¼åçº§ä¸º {3} ç {1} {2}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} æ²¡æå¨æå¢ {4} ä¸­ä¸´æ¶è®¾ç½®ä¼åçº§ä¸º {3} ç {1} {2}
luckperms.command.generic.chat-meta.add={0} å¨æå¢ {4} ä¸­è®¾ç½®äº {1} {2}, ä¼åçº§ä¸º {3}
luckperms.command.generic.chat-meta.add-temp={0} å¨æå¢ {5} ä¸­è®¾ç½®äº {1} {2}, ä¼åçº§ä¸º {3}, æææ\: {4}
luckperms.command.generic.chat-meta.remove={0} å¨æå¢ {4} ä¸­ç§»é¤äºä¼åçº§ä¸º {3} ç {1} {2}
luckperms.command.generic.chat-meta.remove-bulk={0} å¨æå¢ {3} ä¸­ç§»é¤äºææä¼åçº§ä¸º {2} ç {1}
luckperms.command.generic.chat-meta.remove-temp={0} å¨æå¢ {4} ä¸­ç§»é¤äºä¼åçº§ä¸º {3} çä¸´æ¶ {1} {2}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} å¨æå¢ {3} ä¸­ç§»é¤äºææä¼åçº§ä¸º {2} çä¸´æ¶ {1}
luckperms.command.generic.meta.info.title={0} çåæ°æ®
luckperms.command.generic.meta.info.none={0} æ²¡æåæ°æ®
luckperms.command.generic.meta.info.click-to-remove=åå»ä»¥ä» {0} ä¸­ç§»é¤æ­¤åæ°æ®èç¹
luckperms.command.generic.meta.already-has={0} å¨æå¢ {3} ä¸­å·²ç»å°åæ°æ®é® {1} è®¾ç½®ä¸º {2}
luckperms.command.generic.meta.already-has-temp={0} å¨æå¢ {3} ä¸­å·²ç»å°åæ°æ®é® {1} ä¸´æ¶è®¾ç½®ä¸º {2}
luckperms.command.generic.meta.doesnt-have={0} æ²¡æå¨æå¢ {2} ä¸­è®¾ç½®åæ°æ®é® {1}
luckperms.command.generic.meta.doesnt-have-temp={0} æ²¡æå¨æå¢ {2} ä¸­ä¸´æ¶è®¾ç½®åæ°æ®é® {1}
luckperms.command.generic.meta.set=å¨æå¢ {3} ä¸­å° {2} çåæ°æ®é® {0} è®¾ç½®ä¸º {1}
luckperms.command.generic.meta.set-temp=å¨æå¢ {4} ä¸­å° {2} çåæ°æ®é® {0} è®¾ç½®ä¸º {1}, æææ\: {3}
luckperms.command.generic.meta.unset=å¨æå¢ {2} ä¸­ä¸º {1} åæ¶è®¾ç½®åæ°æ®é® {0}
luckperms.command.generic.meta.unset-temp=å¨æå¢ {2} ä¸­ä¸º {1} åæ¶è®¾ç½®ä¸´æ¶åæ°æ®é® {0}
luckperms.command.generic.meta.clear={0} å¨æå¢ {2} ä¸­å¹éç±»å {1} çåæ°æ®å·²è¢«æ¸é¤
luckperms.command.generic.contextual-data.title=æå¢æ°æ®
luckperms.command.generic.contextual-data.mode.key=æ¨¡å¼
luckperms.command.generic.contextual-data.mode.server=æå¡å¨
luckperms.command.generic.contextual-data.mode.active-player=æ´»è·ç©å®¶
luckperms.command.generic.contextual-data.contexts-key=æå¢
luckperms.command.generic.contextual-data.prefix-key=åç¼
luckperms.command.generic.contextual-data.suffix-key=åç¼
luckperms.command.generic.contextual-data.primary-group-key=ä¸»ç»
luckperms.command.generic.contextual-data.meta-key=åæ°æ®
luckperms.command.generic.contextual-data.null-result=æ 
luckperms.command.user.info.title=ç¨æ·ä¿¡æ¯
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=ç±»å
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=ç¦»çº¿
luckperms.command.user.info.status-key=ç¶æ
luckperms.command.user.info.status.online=å¨çº¿
luckperms.command.user.info.status.offline=ç¦»çº¿
luckperms.command.user.removegroup.error-primary=ä½ ä¸è½å°ç¨æ·ä»ä»ä»¬çä¸»ç»ä¸­ç§»é¤
luckperms.command.user.primarygroup.not-member={0} è¿ä¸æ¯ {1} çæå, ç°å¨æ­£å¨å å¥
luckperms.command.user.primarygroup.already-has={0} å·²ç»å° {1} è®¾ç½®ä¸ºå¶çä¸»æéç»
luckperms.command.user.primarygroup.warn-option=è­¦å\: æ­¤æå¡å¨ ({0}) ä½¿ç¨çä¸»æéç»è®¡ç®æ¹æ³å¯è½æªåæ æ­¤æ´æ¹
luckperms.command.user.primarygroup.set={0} çä¸»ç»è®¾ç½®ä¸º {1}
luckperms.command.user.track.error-not-contain-group={0} å°æªå¨ {1} ä¸çä»»ä½æéç»ä¸­
luckperms.command.user.track.unsure-which-track=ä¸ç¡®å®ä½¿ç¨åªä¸ªæéç»è·¯çº¿, è¯·å°å¶æå®ä¸ºåæ°
luckperms.command.user.track.missing-group-advice=åå»ºæéç»æå°å¶ä»æéç»è·¯çº¿ä¸­ç§»é¤å¹¶éè¯
luckperms.command.user.promote.added-to-first={0} å¨æå¢ {3} ä¸­ä¸å¨ {1} ä¸çä»»ä½æéç»ä¸­, å æ­¤å¶è¢«æ·»å å°ç¬¬ä¸ä¸ªç» {2}
luckperms.command.user.promote.not-on-track={0} ä¸å¨ {1} ä¸çä»»ä½æéç»ä¸­, å æ­¤æªè½æå
luckperms.command.user.promote.success=å¨æå¢ {4} ä¸­å° {0} æ²¿æéç»è·¯çº¿ {1} ä» {2} æåå° {3}
luckperms.command.user.promote.end-of-track=å·²å°è¾¾æéç»è·¯çº¿ {0} çæ«å°¾, æ æ³ç»§ç»­æå {1}
luckperms.command.user.promote.next-group-deleted=æéç»è·¯çº¿ä¸çä¸ä¸ä¸ªæéç» {0} ä¸åå­å¨
luckperms.command.user.promote.unable-to-promote=æ æ³æåç¨æ·
luckperms.command.user.demote.success=å¨æå¢ {4} ä¸­å° {0} æ²¿æéç»è·¯çº¿ {1} ä» {2} éçº§å° {3}
luckperms.command.user.demote.end-of-track=å·²å°è¾¾æéç»è·¯çº¿ {0} çèµ·ç¹, å æ­¤ {1} å·²ä» {2} ä¸­ç§»é¤
luckperms.command.user.demote.end-of-track-not-removed=å·²å°è¾¾æéç»è·¯çº¿ {0} çèµ·ç¹, ä½æªä»ç¬¬ä¸ä¸ªç»ä¸­ç§»é¤ {1}
luckperms.command.user.demote.previous-group-deleted=æéç»è·¯çº¿ä¸çåä¸ä¸ªæéç» {0} ä¸åå­å¨
luckperms.command.user.demote.unable-to-demote=æ æ³éçº§ç¨æ·
luckperms.command.group.list.title=æéç»
luckperms.command.group.delete.not-default=ä½ ä¸è½å é¤é»è®¤ç»
luckperms.command.group.info.title=æéç»ä¿¡æ¯
luckperms.command.group.info.display-name-key=æ¾ç¤ºåç§°
luckperms.command.group.info.weight-key=æé
luckperms.command.group.setweight.set=å°æéç» {1} çæéè®¾ç½®ä¸º {0}
luckperms.command.group.setdisplayname.doesnt-have={0} æ²¡æè®¾ç½®æ¾ç¤ºåç§°
luckperms.command.group.setdisplayname.already-has={0} å·²ç»è®¾ç½®äºæ¾ç¤ºåç§°ä¸º {1}
luckperms.command.group.setdisplayname.already-in-use=æ¾ç¤ºåç§° {0} å·²è¢« {1} ä½¿ç¨
luckperms.command.group.setdisplayname.set=å¨æå¢ {2} ä¸­å°ç» {1} çæ¾ç¤ºåç§°è®¾ç½®ä¸º {0}
luckperms.command.group.setdisplayname.removed=å¨æå¢ {1} ä¸­ç§»é¤äºç» {0} çæ¾ç¤ºåç§°
luckperms.command.track.list.title=æéç»è·¯çº¿
luckperms.command.track.path.empty=æ 
luckperms.command.track.info.showing-track=æ¾ç¤ºæéç»è·¯çº¿
luckperms.command.track.info.path-property=è·¯çº¿
luckperms.command.track.clear=æéç»è·¯çº¿ {0} ä¸çè·¯çº¿å·²æ¸é¤
luckperms.command.track.append.success=æéç» {0} è¢«éå å°æéç»è·¯çº¿ {1} ä¸­
luckperms.command.track.insert.success=æéç» {0} è¢«æå¥å°æéç»è·¯çº¿ {1} çä½ç½® {2}
luckperms.command.track.insert.error-number=éè¦æ°å­, ä½æ¶å°\: {0}
luckperms.command.track.insert.error-invalid-pos=æ æ³æå¥ä½ç½® {0}
luckperms.command.track.insert.error-invalid-pos-reason=æ æä½ç½®
luckperms.command.track.remove.success=æéç» {0} å·²ä»æéç»è·¯çº¿ {1} ä¸­ç§»é¤
luckperms.command.track.error-empty={0} ä¸è½ä½¿ç¨, å ä¸ºå®æ¯ç©ºçæåªåå«ä¸ä¸ªç»
luckperms.command.track.error-multiple-groups={0} æ¯æ­¤æéç»è·¯çº¿ä¸å¤ä¸ªç»çæå
luckperms.command.track.error-ambiguous=æ æ³ç¡®å®å¶ä½ç½®
luckperms.command.track.already-contains={0} å·²ç»åå« {1}
luckperms.command.track.doesnt-contain={0} æ²¡æåå« {1}
luckperms.command.log.load-error=æ æ³å è½½æ¥å¿
luckperms.command.log.invalid-page=é¡µç æ æ
luckperms.command.log.invalid-page-range=è¯·è¾å¥ä¸ä¸ªä»äº {0} å {1} ä¹é´çå¼
luckperms.command.log.empty=æ²¡æå¯æ¾ç¤ºçæ¥å¿æ¡ç®
luckperms.command.log.notify.error-console=æ æ³åæ¢æ§å¶å°çéç¥
luckperms.command.log.notify.enabled-term=å¯ç¨
luckperms.command.log.notify.disabled-term=ç¦ç¨
luckperms.command.log.notify.changed-state={0} æ¥å¿è¾åº
luckperms.command.log.notify.already-on=ä½ å·²ç»å¨æ¥æ¶éç¥
luckperms.command.log.notify.already-off=ä½ ç®åå¹¶æªæ¥æ¶éç¥
luckperms.command.log.notify.invalid-state=ç¶ææªç¥. éè¦ {0} æ {1}
luckperms.command.log.show.search=æ¾ç¤ºåå«æ¥è¯¢åå®¹ {0} çæè¿çæä½
luckperms.command.log.show.recent=æ¾ç¤ºæè¿çæä½
luckperms.command.log.show.by=æ¾ç¤º {0} æè¿çæä½
luckperms.command.log.show.history=æ¾ç¤º {0} {1} çåå²è®°å½
luckperms.command.export.error-term=éè¯¯
luckperms.command.export.already-running=å¦ä¸ä¸ªå¯¼åºè¿ç¨æ­£å¨è¿è¡
luckperms.command.export.file.already-exists=æä»¶ {0} å·²å­å¨
luckperms.command.export.file.not-writable=æä»¶ {0} æ æ³è¢«åå¥
luckperms.command.export.file.success=å·²æåå¯¼åºå° {0}
luckperms.command.export.file-unexpected-error-writing=å¨åå¥æä»¶æ¶åçäºæå¤çéè¯¯
luckperms.command.export.web.export-code=å¯¼åºä»£ç 
luckperms.command.export.web.import-command-description=ä½¿ç¨ä»¥ä¸å½ä»¤å¯¼å¥
luckperms.command.import.term=å¯¼å¥
luckperms.command.import.error-term=éè¯¯
luckperms.command.import.already-running=å¦ä¸ä¸ªå¯¼å¥è¿ç¨å·²ç»å¨è¿è¡
luckperms.command.import.file.doesnt-exist=æä»¶ {0} ä¸å­å¨
luckperms.command.import.file.not-readable=æä»¶ {0} ä¸å¯è¯»
luckperms.command.import.file.unexpected-error-reading=å¨è¯»åå¯¼å¥æä»¶æ¶åçäºæå¤çéè¯¯
luckperms.command.import.file.correct-format=è¿æ¯æ­£ç¡®çæ ¼å¼åï¼
luckperms.command.import.web.unable-to-read=æ æ³ä½¿ç¨ç»å®çä»£ç è¯»åæ°æ®
luckperms.command.import.progress.percent=å·²å®æ{0}%
luckperms.command.import.progress.operations={0}/{1} æä½å®æ
luckperms.command.import.starting=æ­£å¨å¼å§å¯¼å¥è¿ç¨
luckperms.command.import.completed=å·²å®æ
luckperms.command.import.duration=è±è´¹äº {0} ç§
luckperms.command.bulkupdate.must-use-console=æ¹éæ´æ°å½ä»¤åªè½å¨æ§å¶å°ä½¿ç¨
luckperms.command.bulkupdate.invalid-data-type=æ æçç±»å, éè¦ {0}
luckperms.command.bulkupdate.invalid-constraint=æ æççº¦æ {0}
luckperms.command.bulkupdate.invalid-constraint-format=çº¦æåºéµå¾ªæ ¼å¼ {0}
luckperms.command.bulkupdate.invalid-comparison=æ æçæ¯è¾è¿ç®ç¬¦ {0}
luckperms.command.bulkupdate.invalid-comparison-format=éè¦ä¸ºä»¥ä¸ä¹ä¸\: {0}
luckperms.command.bulkupdate.queued=æ¹éæ´æ°æä½æéä¸­
luckperms.command.bulkupdate.confirm=è¿è¡ {0} æ¥æ§è¡æ´æ°
luckperms.command.bulkupdate.unknown-id=IDä¸º {0} çæä½ä¸å­å¨æå·²è¿æ
luckperms.command.bulkupdate.starting=æ­£å¨è¿è¡æ¹éæ´æ°
luckperms.command.bulkupdate.success=æ¹éæ´æ°å·²æåå®æ
luckperms.command.bulkupdate.success.statistics.nodes=åå½±åçèç¹æ»æ°
luckperms.command.bulkupdate.success.statistics.users=åå½±åçç¨æ·æ»æ°
luckperms.command.bulkupdate.success.statistics.groups=åå½±åçæéç»æ»æ°
luckperms.command.bulkupdate.failure=æ¹éæ´æ°å¤±è´¥, è¯·æ£æ¥æ§å¶å°è·å¾éè¯¯ä¿¡æ¯
luckperms.command.update-task.request=å·²è¯·æ±æ´æ°ä»»å¡, è¯·ç¨å
luckperms.command.update-task.complete=æ´æ°ä»»å¡å·²å®æ
luckperms.command.update-task.push.attempting=æ­£å¨å°è¯æ¨éè³å¶ä»æå¡å¨
luckperms.command.update-task.push.complete=å·²æåéè¿ {0} éç¥å¶å®æå¡å¨
luckperms.command.update-task.push.error=å¨åå¶ä»æå¡å¨æ¨éæ´æ¹æ¶åçäºéè¯¯
luckperms.command.update-task.push.error-not-setup=æ æ³å°æ´æ¹æ¨éå°å¶ä»æå¡å¨, å ä¸ºæ¶æ¯æå¡å°æªéç½®
luckperms.command.reload-config.success=å·²éæ°å è½½éç½®æä»¶
luckperms.command.reload-config.restart-note=æäºéé¡¹ä»å¨æå¡å¨éæ°å¯å¨åæåºç¨
luckperms.command.translations.searching=æ­£å¨æç´¢å¯ç¨çç¿»è¯, è¯·ç¨å...
luckperms.command.translations.searching-error=æ æ³è·å¾å¯ç¨ç¿»è¯çåè¡¨
luckperms.command.translations.installed-translations=å·²å®è£çç¿»è¯
luckperms.command.translations.available-translations=å¯ç¨çç¿»è¯
luckperms.command.translations.percent-translated=å·²ç¿»è¯{0}%
luckperms.command.translations.translations-by=ç±
luckperms.command.translations.installing=æ­£å¨å®è£ç¿»è¯, è¯·ç¨å...
luckperms.command.translations.download-error=æ æ³ä¸è½½ {0} çç¿»è¯
luckperms.command.translations.installing-specific=æ­£å¨å®è£è¯­è¨ {0}...
luckperms.command.translations.install-complete=å®è£å·²å®æ
luckperms.command.translations.download-prompt=ä½¿ç¨ {0} ä¸è½½å¹¶å®è£ç±ç¤¾åºæä¾çç¿»è¯çææ°çæ¬
luckperms.command.translations.download-override-warning=è¯·æ³¨æ, æ­¤æä½å°ä¼è¦çæ¨å¯¹è¿äºè¯­è¨ååºçä»»ä½æ´æ¹
luckperms.usage.user.description=ç¨äºå¨ LuckPerms ä¸­ç®¡çç¨æ·çå½ä»¤. (LuckPerms ä¸­ç "user" ä»£è¡¨ç©å®¶, å¯ä»¥å¼ç¨ UUID æç¨æ·åç§°)
luckperms.usage.group.description=ç¨äºå¨ LuckPerms ä¸­ç®¡çæéç»çå½ä»¤. æéç»åªæ¯å¯ä»¥æäºç¨æ·çæéåéçéå, ä½¿ç¨ ''''creategroup'''' å½ä»¤åå»ºæ°ç»
luckperms.usage.track.description=ç¨äºå¨ LuckPerms ä¸­ç®¡çæéç»è·¯çº¿çå½ä»¤. æéç»è·¯çº¿æ¯ä¸ç»æåºçæéç», å¯ç¨äºå®ä¹æååéçº§
luckperms.usage.log.description=ç¨äºå¨ LuckPerms ä¸­ç®¡çæ¥å¿è®°å½åè½çå½ä»¤.
luckperms.usage.sync.description=å°æä»¶å­å¨ä¸­çæææ°æ®éæ°å è½½å°åå­ä¸­, å¹¶åºç¨æ£æµå°çä»»ä½æ´æ¹.
luckperms.usage.info.description=æå°å³äºå½åæä»¶å®ä¾çä¸è¬ä¿¡æ¯.
luckperms.usage.editor.description=åå»ºä¸ä¸ªæ°çç½é¡µç¼è¾å¨ä¼è¯
luckperms.usage.editor.argument.type=è¦å è½½å°ç¼è¾å¨ä¸­çç±»å. (''''all'''', ''''users'''' æ ''''groups'''')
luckperms.usage.editor.argument.filter=è¿æ»¤ç¨æ·æ¡ç®çæé
luckperms.usage.verbose.description=æ§å¶æä»¶çè¯¦ç»æ¥å¿ç³»ç»
luckperms.usage.verbose.argument.action=å¯ç¨/ç¦ç¨æ¥å¿è®°å½, æä¸ä¼ è®°å½çè¾åº
luckperms.usage.verbose.argument.filter=å¹éæ¡ç®çè¿æ»¤å¨
luckperms.usage.verbose.argument.commandas=è¦è¿è¡å½ä»¤çç©å®¶åè¦è¿è¡çå½ä»¤
luckperms.usage.tree.description=çæ LuckPerms å·²ç¥çæææéçæ è§å¾ (æåºåè¡¨å±æ¬¡ç»æ)
luckperms.usage.tree.argument.scope=æ çæ ¹. æå® "." ä»¥åå«æææé
luckperms.usage.tree.argument.player=è¦æ£æ¥çå¨çº¿ç©å®¶çåç§°
luckperms.usage.search.description=æç´¢å·æç¹å®æéçç¨æ·åæéç»
luckperms.usage.search.argument.permission=è¦æç´¢çæé
luckperms.usage.search.argument.page=è¦æ¥ççé¡µ
luckperms.usage.network-sync.description=ä¸å­å¨åæ­¥æ´æ¹å¹¶è¯·æ±ç½ç»ä¸çææå¶ä»æå¡å¨ä¹è¿æ ·å
luckperms.usage.import.description=ä»ååå¯¼åºçæä»¶å¯¼å¥æ°æ®
luckperms.usage.import.argument.file=è¦å¯¼å¥çæä»¶
luckperms.usage.import.argument.replace=æ¿æ¢ç°ææ°æ®èä¸æ¯åå¹¶
luckperms.usage.import.argument.upload=ä¸ä¼ ååå¯¼åºçæ°æ®
luckperms.usage.export.description=å°æææéæ°æ®å¯¼åºå°æå®æä»¶. ä»¥åå¯ä»¥éæ°å¯¼å¥.
luckperms.usage.export.argument.file=è¦å¯¼åºçæä»¶
luckperms.usage.export.argument.without-users=å°ç¨æ·ä»å¯¼åºä¸­æé¤
luckperms.usage.export.argument.without-groups=å°æéç»ä»å¯¼åºä¸­æé¤
luckperms.usage.export.argument.upload=å°æææéæ°æ®ä¸ä¼ å°ç½ç». ç¨åå¯ä»¥éæ°å¯¼å¥(æå¤ä¿ç14å¤©).
luckperms.usage.reload-config.description=éæ°å è½½ä¸äºéç½®éé¡¹
luckperms.usage.bulk-update.description=å¨æææ°æ®ä¸æ§è¡æ¹éåæ´æ¥è¯¢
luckperms.usage.bulk-update.argument.data-type=è¦æ´æ¹çæ°æ®ç±»å. ("all", "users" æ "groups")
luckperms.usage.bulk-update.argument.action=è¦å¯¹æ°æ®æ§è¡çæä½. ("update" æ "delete")
luckperms.usage.bulk-update.argument.action-field=è¦æ´æ°çå­æ®µ. ä»"update"æä½éè¦. ("permission", "server" æ "world")
luckperms.usage.bulk-update.argument.action-value=è¦æ¿æ¢æçå¼. ä»"update"æä½éè¦.
luckperms.usage.bulk-update.argument.constraint=æ´æ°æéçéå¶æ¡ä»¶
luckperms.usage.translations.description=ç®¡çç¿»è¯
luckperms.usage.translations.argument.install=å®è£ç¿»è¯çå­å½ä»¤
luckperms.usage.apply-edits.description=åºç¨ä»ç½é¡µç¼è¾å¨æåçæéæ´æ¹
luckperms.usage.apply-edits.argument.code=æ°æ®çå¯ä¸ä»£ç 
luckperms.usage.apply-edits.argument.target=å°æ°æ®åºç¨å°è°
luckperms.usage.create-group.description=åå»ºä¸ä¸ªæ°çæéç»
luckperms.usage.create-group.argument.name=æéç»çåç§°
luckperms.usage.create-group.argument.weight=æéç»çæé
luckperms.usage.create-group.argument.display-name=æéç»çæ¾ç¤ºåç§°
luckperms.usage.delete-group.description=å é¤ä¸ä¸ªæéç»
luckperms.usage.delete-group.argument.name=æéç»çåç§°
luckperms.usage.list-groups.description=ååºå¹³å°ä¸çæææéç»
luckperms.usage.create-track.description=åå»ºä¸ä¸ªæ°çæéç»è·¯çº¿
luckperms.usage.create-track.argument.name=æéç»è·¯çº¿çåç§°
luckperms.usage.delete-track.description=å é¤ä¸ä¸ªæéç»è·¯çº¿
luckperms.usage.delete-track.argument.name=æéç»è·¯çº¿çåç§°
luckperms.usage.list-tracks.description=ååºå¹³å°ä¸çæææéç»è·¯çº¿
luckperms.usage.user-info.description=æ¾ç¤ºç¨æ·ä¿¡æ¯
luckperms.usage.user-switchprimarygroup.description=åæ¢ç¨æ·çä¸»ç»
luckperms.usage.user-switchprimarygroup.argument.group=è¦åæ¢å°çæéç»
luckperms.usage.user-promote.description=å¨æå®çæéç»è·¯çº¿ä¸æåç¨æ·
luckperms.usage.user-promote.argument.track=è¦æåç¨æ·çæéç»è·¯çº¿
luckperms.usage.user-promote.argument.context=è¦æåç¨æ·çæå¢
luckperms.usage.user-promote.argument.dont-add-to-first=åªæå½ç¨æ·å·²ç»å¨æéç»è·¯çº¿ä¸æ¶æè¿è¡æå
luckperms.usage.user-demote.description=å¨æå®çæéç»è·¯çº¿ä¸éçº§ç¨æ·
luckperms.usage.user-demote.argument.track=è¦éçº§ç¨æ·çæéç»è·¯çº¿
luckperms.usage.user-demote.argument.context=è¦éçº§ç¨æ·çæå¢
luckperms.usage.user-demote.argument.dont-remove-from-first=é²æ­¢å°ç¨æ·ä»ç¬¬ä¸ä¸ªç»ä¸­ç§»é¤
luckperms.usage.user-clone.description=åéç¨æ·
luckperms.usage.user-clone.argument.user=è¦åéå°çç¨æ·åç§°æUUID
luckperms.usage.group-info.description=æä¾å³äºæéç»çä¿¡æ¯
luckperms.usage.group-listmembers.description=æ¾ç¤ºç»§æ¿æ­¤ç»çç¨æ·åæéç»
luckperms.usage.group-listmembers.argument.page=è¦æ¥ççé¡µ
luckperms.usage.group-setweight.description=è®¾ç½®æéç»çæé
luckperms.usage.group-setweight.argument.weight=è¦è®¾ç½®çæé
luckperms.usage.group-set-display-name.description=è®¾ç½®æéç»çæ¾ç¤ºåç§°
luckperms.usage.group-set-display-name.argument.name=è¦è®¾ç½®çåç§°
luckperms.usage.group-set-display-name.argument.context=è¦è®¾ç½®åç§°çæå¢
luckperms.usage.group-rename.description=éå½åæéç»
luckperms.usage.group-rename.argument.name=æ°çåç§°
luckperms.usage.group-clone.description=åéæéç»
luckperms.usage.group-clone.argument.name=è¦åéå°çæéç»åç§°
luckperms.usage.holder-editor.description=æå¼ç½é¡µæéç¼è¾å¨
luckperms.usage.holder-showtracks.description=ååºå¯¹è±¡æå¨çæéç»è·¯çº¿
luckperms.usage.holder-clear.description=ç§»é¤æææéãç¶æéç»ååæ°æ®
luckperms.usage.holder-clear.argument.context=è¦è¿æ»¤çæå¢ï¼
luckperms.usage.permission.description=ç¼è¾æé
luckperms.usage.parent.description=ç¼è¾ç»§æ¿
luckperms.usage.meta.description=ç¼è¾åæ°æ®å¼
luckperms.usage.permission-info.description=ååºå¯¹è±¡æ¥æçæéèç¹
luckperms.usage.permission-info.argument.page=è¦æ¥ççé¡µ
luckperms.usage.permission-info.argument.sort-mode=å¦ä½æåºæ¡ç®
luckperms.usage.permission-set.description=è®¾ç½®å¯¹è±¡çæé
luckperms.usage.permission-set.argument.node=è¦è®¾ç½®çæéèç¹
luckperms.usage.permission-set.argument.value=èç¹çå¼
luckperms.usage.permission-set.argument.context=è¦æ·»å æéçæå¢
luckperms.usage.permission-unset.description=åæ¶è®¾ç½®å¯¹è±¡çæé
luckperms.usage.permission-unset.argument.node=è¦åæ¶è®¾ç½®çæéèç¹
luckperms.usage.permission-unset.argument.context=è¦åæ¶è®¾ç½®æéçæå¢
luckperms.usage.permission-settemp.description=ä¸´æ¶è®¾ç½®å¯¹è±¡çæé
luckperms.usage.permission-settemp.argument.node=è¦è®¾ç½®çæéèç¹
luckperms.usage.permission-settemp.argument.value=èç¹çå¼
luckperms.usage.permission-settemp.argument.duration=æéèç¹çæææ
luckperms.usage.permission-settemp.argument.temporary-modifier=è¦å¦ä½åºç¨ä¸´æ¶æé
luckperms.usage.permission-settemp.argument.context=è¦æ·»å æéçæå¢
luckperms.usage.permission-unsettemp.description=åæ¶è®¾ç½®å¯¹è±¡çä¸´æ¶æé
luckperms.usage.permission-unsettemp.argument.node=è¦åæ¶è®¾ç½®çæéèç¹
luckperms.usage.permission-unsettemp.argument.duration=è¦åå»çæææ
luckperms.usage.permission-unsettemp.argument.context=è¦åæ¶è®¾ç½®æéçæå¢
luckperms.usage.permission-check.description=æ£æ¥å¯¹è±¡æ¯å¦æ¥æç¹å®çæéèç¹
luckperms.usage.permission-check.argument.node=è¦æ£æ¥çæéèç¹
luckperms.usage.permission-clear.description=æ¸é¤æææé
luckperms.usage.permission-clear.argument.context=è¦è¿æ»¤çæå¢ï¼
luckperms.usage.parent-info.description=ååºè¯¥å¯¹è±¡ç»§æ¿çæéç»
luckperms.usage.parent-info.argument.page=è¦æ¥ççé¡µ
luckperms.usage.parent-info.argument.sort-mode=å¦ä½æåºæ¡ç®
luckperms.usage.parent-set.description=ç§»é¤å¯¹è±¡å·²ç»ç»§æ¿çæææéç»å¹¶å°å¶æ·»å å°ç»å®çæéç»ä¸­
luckperms.usage.parent-set.argument.group=è¦è®¾ç½®çæéç»
luckperms.usage.parent-set.argument.context=è¦è®¾ç½®æéç»çæå¢
luckperms.usage.parent-add.description=ä¸ºå¯¹è±¡æ·»å å¦ä¸ä¸ªè¦ç»§æ¿çæéç»ä»¥ä»å¶ç»§æ¿æé
luckperms.usage.parent-add.argument.group=è¦ç»§æ¿çæéç»
luckperms.usage.parent-add.argument.context=è¦ç»§æ¿æéç»çæå¢
luckperms.usage.parent-remove.description=ç§»é¤ååç»§æ¿çæä¸ªç»
luckperms.usage.parent-remove.argument.group=è¦ç§»é¤çæéç»
luckperms.usage.parent-remove.argument.context=è¦ç§»é¤æéç»çæå¢
luckperms.usage.parent-set-track.description=ç§»é¤å¯¹è±¡å·²ç»ç»§æ¿çææåå«å¨ç»å®æéç»è·¯çº¿ä¸çæéç»å¹¶å°å¶æ·»å å°ç»å®çæéç»ä¸­
luckperms.usage.parent-set-track.argument.track=ç¨äºè®¾ç½®çæéç»è·¯çº¿
luckperms.usage.parent-set-track.argument.group=è¦è®¾ç½®çæéç», æè¯¥æéç»å¨ç»å®æéç»è·¯çº¿ä¸çä½ç½®
luckperms.usage.parent-set-track.argument.context=è¦è®¾ç½®æéç»çæå¢
luckperms.usage.parent-add-temp.description=ä¸ºå¯¹è±¡æ·»å å¦ä¸ä¸ªè¦ä¸´æ¶ç»§æ¿çæéç»ä»¥ä»å¶ç»§æ¿æé
luckperms.usage.parent-add-temp.argument.group=è¦ç»§æ¿çæéç»
luckperms.usage.parent-add-temp.argument.duration=ä¸´æ¶æéç»çæææ
luckperms.usage.parent-add-temp.argument.temporary-modifier=è¦å¦ä½åºç¨ä¸´æ¶æé
luckperms.usage.parent-add-temp.argument.context=è¦ç»§æ¿æéç»çæå¢
luckperms.usage.parent-remove-temp.description=ç§»é¤ååä¸´æ¶ç»§æ¿çæä¸ªç»
luckperms.usage.parent-remove-temp.argument.group=è¦ç§»é¤çæéç»
luckperms.usage.parent-remove-temp.argument.duration=è¦åå»çæææ
luckperms.usage.parent-remove-temp.argument.context=è¦ç§»é¤æéç»çæå¢
luckperms.usage.parent-clear.description=æ¸é¤ææç¶æéç»
luckperms.usage.parent-clear.argument.context=è¦è¿æ»¤çæå¢ï¼
luckperms.usage.parent-clear-track.description=æ¸é¤åå«å¨ç»å®æéç»è·¯çº¿ä¸çææç¶æéç»
luckperms.usage.parent-clear-track.argument.track=ç¨äºæ¸é¤çæéç»è·¯çº¿
luckperms.usage.parent-clear-track.argument.context=è¦è¿æ»¤çæå¢ï¼
luckperms.usage.meta-info.description=æ¾ç¤ºææèå¤©åæ°æ®
luckperms.usage.meta-set.description=è®¾ç½®åæ°æ®å¼
luckperms.usage.meta-set.argument.key=è¦è®¾ç½®çé®
luckperms.usage.meta-set.argument.value=è¦è®¾ç½®çå¼
luckperms.usage.meta-set.argument.context=è¦æ·»å åæ°æ®çæå¢
luckperms.usage.meta-unset.description=åæ¶è®¾ç½®åæ°æ®å¼
luckperms.usage.meta-unset.argument.key=è¦åæ¶è®¾ç½®çé®
luckperms.usage.meta-unset.argument.context=è¦ç§»é¤åæ°æ®çæå¢
luckperms.usage.meta-settemp.description=ä¸´æ¶è®¾ç½®åæ°æ®å¼
luckperms.usage.meta-settemp.argument.key=è¦è®¾ç½®çé®
luckperms.usage.meta-settemp.argument.value=è¦è®¾ç½®çå¼
luckperms.usage.meta-settemp.argument.duration=åæ°æ®å¼çæææ
luckperms.usage.meta-settemp.argument.context=è¦æ·»å åæ°æ®çæå¢
luckperms.usage.meta-unsettemp.description=åæ¶è®¾ç½®ä¸´æ¶åæ°æ®å¼
luckperms.usage.meta-unsettemp.argument.key=è¦åæ¶è®¾ç½®çé®
luckperms.usage.meta-unsettemp.argument.context=è¦ç§»é¤åæ°æ®çæå¢
luckperms.usage.meta-addprefix.description=æ·»å ä¸ä¸ªåç¼
luckperms.usage.meta-addprefix.argument.priority=è¦æ·»å çåç¼çä¼åçº§
luckperms.usage.meta-addprefix.argument.prefix=è¦æ·»å çåç¼
luckperms.usage.meta-addprefix.argument.context=è¦æ·»å åç¼çæå¢
luckperms.usage.meta-addsuffix.description=æ·»å ä¸ä¸ªåç¼
luckperms.usage.meta-addsuffix.argument.priority=è¦æ·»å çåç¼çä¼åçº§
luckperms.usage.meta-addsuffix.argument.suffix=è¦æ·»å çåç¼
luckperms.usage.meta-addsuffix.argument.context=è¦æ·»å åç¼çæå¢
luckperms.usage.meta-setprefix.description=è®¾ç½®ä¸ä¸ªåç¼
luckperms.usage.meta-setprefix.argument.priority=è¦è®¾ç½®çåç¼çä¼åçº§
luckperms.usage.meta-setprefix.argument.prefix=è¦è®¾ç½®çåç¼
luckperms.usage.meta-setprefix.argument.context=è¦è®¾ç½®åç¼çæå¢
luckperms.usage.meta-setsuffix.description=è®¾ç½®ä¸ä¸ªåç¼
luckperms.usage.meta-setsuffix.argument.priority=è¦è®¾ç½®çåç¼çä¼åçº§
luckperms.usage.meta-setsuffix.argument.suffix=è¦è®¾ç½®çåç¼
luckperms.usage.meta-setsuffix.argument.context=è¦è®¾ç½®åç¼çæå¢
luckperms.usage.meta-removeprefix.description=ç§»é¤ä¸ä¸ªåç¼
luckperms.usage.meta-removeprefix.argument.priority=è¦ç§»é¤çåç¼çä¼åçº§
luckperms.usage.meta-removeprefix.argument.prefix=è¦ç§»é¤çåç¼
luckperms.usage.meta-removeprefix.argument.context=è¦ç§»é¤åç¼çæå¢
luckperms.usage.meta-removesuffix.description=ç§»é¤ä¸ä¸ªåç¼
luckperms.usage.meta-removesuffix.argument.priority=è¦ç§»é¤çåç¼çä¼åçº§
luckperms.usage.meta-removesuffix.argument.suffix=è¦ç§»é¤çåç¼
luckperms.usage.meta-removesuffix.argument.context=è¦ç§»é¤åç¼çæå¢
luckperms.usage.meta-addtemp-prefix.description=ä¸´æ¶æ·»å ä¸ä¸ªåç¼
luckperms.usage.meta-addtemp-prefix.argument.priority=è¦æ·»å çåç¼çä¼åçº§
luckperms.usage.meta-addtemp-prefix.argument.prefix=è¦æ·»å çåç¼
luckperms.usage.meta-addtemp-prefix.argument.duration=è¦æ·»å çåç¼çæææ
luckperms.usage.meta-addtemp-prefix.argument.context=è¦æ·»å åç¼çæå¢
luckperms.usage.meta-addtemp-suffix.description=ä¸´æ¶æ·»å ä¸ä¸ªåç¼
luckperms.usage.meta-addtemp-suffix.argument.priority=è¦æ·»å çåç¼çä¼åçº§
luckperms.usage.meta-addtemp-suffix.argument.suffix=è¦æ·»å çåç¼
luckperms.usage.meta-addtemp-suffix.argument.duration=è¦æ·»å çåç¼çæææ
luckperms.usage.meta-addtemp-suffix.argument.context=è¦æ·»å åç¼çæå¢
luckperms.usage.meta-settemp-prefix.description=ä¸´æ¶è®¾ç½®ä¸ä¸ªåç¼
luckperms.usage.meta-settemp-prefix.argument.priority=è¦è®¾ç½®çåç¼çä¼åçº§
luckperms.usage.meta-settemp-prefix.argument.prefix=è¦è®¾ç½®çåç¼
luckperms.usage.meta-settemp-prefix.argument.duration=è¦è®¾ç½®çåç¼çæææ
luckperms.usage.meta-settemp-prefix.argument.context=è¦è®¾ç½®åç¼çæå¢
luckperms.usage.meta-settemp-suffix.description=ä¸´æ¶è®¾ç½®ä¸ä¸ªåç¼
luckperms.usage.meta-settemp-suffix.argument.priority=è¦è®¾ç½®çåç¼çä¼åçº§
luckperms.usage.meta-settemp-suffix.argument.suffix=è¦è®¾ç½®çåç¼
luckperms.usage.meta-settemp-suffix.argument.duration=è¦è®¾ç½®çåç¼çæææ
luckperms.usage.meta-settemp-suffix.argument.context=è¦è®¾ç½®åç¼çæå¢
luckperms.usage.meta-removetemp-prefix.description=ç§»é¤ä¸ä¸ªä¸´æ¶åç¼
luckperms.usage.meta-removetemp-prefix.argument.priority=è¦ç§»é¤çåç¼çä¼åçº§
luckperms.usage.meta-removetemp-prefix.argument.prefix=è¦ç§»é¤çåç¼
luckperms.usage.meta-removetemp-prefix.argument.context=è¦ç§»é¤åç¼çæå¢
luckperms.usage.meta-removetemp-suffix.description=ç§»é¤ä¸ä¸ªä¸´æ¶åç¼
luckperms.usage.meta-removetemp-suffix.argument.priority=è¦ç§»é¤çåç¼çä¼åçº§
luckperms.usage.meta-removetemp-suffix.argument.suffix=è¦ç§»é¤çåç¼
luckperms.usage.meta-removetemp-suffix.argument.context=è¦ç§»é¤åç¼çæå¢
luckperms.usage.meta-clear.description=æ¸é¤ææåæ°æ®
luckperms.usage.meta-clear.argument.type=è¦ç§»é¤çåæ°æ®çç±»å
luckperms.usage.meta-clear.argument.context=è¦è¿æ»¤çæå¢ï¼
luckperms.usage.track-info.description=æä¾å³äºæéç»è·¯çº¿çä¿¡æ¯
luckperms.usage.track-editor.description=æå¼ç½é¡µæéç¼è¾å¨
luckperms.usage.track-append.description=å°ä¸ä¸ªæéç»éå å°æéç»è·¯çº¿çæ«å°¾
luckperms.usage.track-append.argument.group=è¦æ·»å çæéç»
luckperms.usage.track-insert.description=å¨æéç»è·¯çº¿çæå®ä½ç½®æå¥æéç»
luckperms.usage.track-insert.argument.group=è¦æå¥çæéç»
luckperms.usage.track-insert.argument.position=è¦æå¥æéç»çä½ç½® (æéç»è·¯çº¿ä¸çç¬¬ä¸ä¸ªä½ç½®æ¯1)
luckperms.usage.track-remove.description=ä»æéç»è·¯çº¿ä¸­ç§»é¤ä¸ä¸ªæéç»
luckperms.usage.track-remove.argument.group=è¦ç§»é¤çæéç»
luckperms.usage.track-clear.description=æ¸é¤æéç»è·¯çº¿ä¸çæéç»
luckperms.usage.track-rename.description=éå½åæéç»è·¯çº¿
luckperms.usage.track-rename.argument.name=æ°çåç§°
luckperms.usage.track-clone.description=åéæéç»è·¯çº¿
luckperms.usage.track-clone.argument.name=è¦åéå°çæéç»è·¯çº¿åç§°
luckperms.usage.log-recent.description=æ¥çæè¿çæä½
luckperms.usage.log-recent.argument.user=è¦è¿æ»¤çç¨æ·åç§°æUUID
luckperms.usage.log-recent.argument.page=è¦æ¥ççé¡µç 
luckperms.usage.log-search.description=å¨æ¥å¿ä¸­æç´¢æ¡ç®
luckperms.usage.log-search.argument.query=è¦æç´¢çåå®¹
luckperms.usage.log-search.argument.page=è¦æ¥ççé¡µç 
luckperms.usage.log-notify.description=å¼å³æ¥å¿éç¥
luckperms.usage.log-notify.argument.toggle=å¼å¯æå³é­
luckperms.usage.log-user-history.description=æ¥çç¨æ·çåå²è®°å½
luckperms.usage.log-user-history.argument.user=ç¨æ·åç§°æUUID
luckperms.usage.log-user-history.argument.page=è¦æ¥ççé¡µç 
luckperms.usage.log-group-history.description=æ¥çæéç»åå²è®°å½
luckperms.usage.log-group-history.argument.group=æéç»çåç§°
luckperms.usage.log-group-history.argument.page=è¦æ¥ççé¡µç 
luckperms.usage.log-track-history.description=æ¥çæéç»è·¯çº¿åå²è®°å½
luckperms.usage.log-track-history.argument.track=æéç»è·¯çº¿åç§°
luckperms.usage.log-track-history.argument.page=è¦æ¥ççé¡µç 
luckperms.usage.sponge.description=ç¼è¾é¢å¤ç Sponge æ°æ®
luckperms.usage.sponge.argument.collection=è¦æ¥è¯¢çéå
luckperms.usage.sponge.argument.subject=è¦ä¿®æ¹çä¸»ä½
luckperms.usage.sponge-permission-info.description=æ¾ç¤ºä¸»ä½çæéä¿¡æ¯
luckperms.usage.sponge-permission-info.argument.contexts=è¦è¿æ»¤çæå¢ï¼
luckperms.usage.sponge-permission-set.description=ä¸ºä¸»ä½è®¾ç½®æé
luckperms.usage.sponge-permission-set.argument.node=è¦è®¾ç½®çæéèç¹
luckperms.usage.sponge-permission-set.argument.tristate=æéèç¹çå¼
luckperms.usage.sponge-permission-set.argument.contexts=è¦è®¾ç½®æéçæå¢
luckperms.usage.sponge-permission-clear.description=æ¸é¤ä¸»ä½çæé
luckperms.usage.sponge-permission-clear.argument.contexts=è¦æ¸é¤æéçæå¢
luckperms.usage.sponge-parent-info.description=æ¾ç¤ºå¯¹è±¡çç¶æéç»çä¿¡æ¯
luckperms.usage.sponge-parent-info.argument.contexts=è¦è¿æ»¤çæå¢ï¼
luckperms.usage.sponge-parent-add.description=ä¸ºä¸»ä½æ·»å ç¶æéç»
luckperms.usage.sponge-parent-add.argument.collection=ç¶æéç»æå¨çä¸»ä½éå
luckperms.usage.sponge-parent-add.argument.subject=ç¶æéç»çåç§°
luckperms.usage.sponge-parent-add.argument.contexts=è¦æ·»å ç¶æéç»çæå¢
luckperms.usage.sponge-parent-remove.description=ç§»é¤å¯¹è±¡çç¶æéç»
luckperms.usage.sponge-parent-remove.argument.collection=ç¶æéç»æå¨çä¸»ä½éå
luckperms.usage.sponge-parent-remove.argument.subject=ç¶æéç»çåç§°
luckperms.usage.sponge-parent-remove.argument.contexts=è¦ç§»é¤ç¶æéç»çæå¢
luckperms.usage.sponge-parent-clear.description=æ¸é¤ä¸»ä½çç¶æéç»
luckperms.usage.sponge-parent-clear.argument.contexts=è¦æ¸é¤ç¶æéç»çæå¢
luckperms.usage.sponge-option-info.description=æ¾ç¤ºä¸»ä½çéé¡¹çä¿¡æ¯
luckperms.usage.sponge-option-info.argument.contexts=è¦è¿æ»¤çæå¢ï¼
luckperms.usage.sponge-option-set.description=ä¸ºä¸»ä½è®¾ç½®ä¸ä¸ªéé¡¹
luckperms.usage.sponge-option-set.argument.key=è¦è®¾ç½®çé®
luckperms.usage.sponge-option-set.argument.value=è¦è®¾ç½®çå¼
luckperms.usage.sponge-option-set.argument.contexts=è¦è®¾ç½®éé¡¹çæå¢
luckperms.usage.sponge-option-unset.description=åæ¶è®¾ç½®ä¸»ä½çéé¡¹
luckperms.usage.sponge-option-unset.argument.key=è¦åæ¶è®¾ç½®çé®
luckperms.usage.sponge-option-unset.argument.contexts=è¦åæ¶è®¾ç½®é®çæå¢
luckperms.usage.sponge-option-clear.description=æ¸é¤ä¸»ä½çéé¡¹
luckperms.usage.sponge-option-clear.argument.contexts=è¦æ¸é¤éé¡¹çæå¢
