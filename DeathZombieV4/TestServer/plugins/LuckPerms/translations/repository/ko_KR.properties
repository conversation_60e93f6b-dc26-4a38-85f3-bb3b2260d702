luckperms.logs.actionlog-prefix=ê¸°ë¡
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=ë´ë³´ë´ê¸°
luckperms.commandsystem.available-commands=ì¬ì© ê°ë¥í ëªë ¹ì´ë¥¼ íì¸íë ¤ë©´ {0} ì(ë¥¼) ì¬ì©íì­ìì¤
luckperms.commandsystem.command-not-recognised=ì¸ìí  ì ìë ëªë ¹ìëë¤
luckperms.commandsystem.no-permission=ì´ ëªë ¹ì´ë¥¼ ì¬ì©í  ê¶íì´ ììµëë¤\!
luckperms.commandsystem.no-permission-subcommands=íì ëªë ¹ì´ë¥¼ ì¬ì©í  ê¶íì´ ììµëë¤
luckperms.commandsystem.already-executing-command=ë¤ë¥¸ ëªë ¹ì´ê° ì¤í ì¤ìëë¤. ìë£ë  ëê¹ì§ ê¸°ë¤ë¦½ëë¤...
luckperms.commandsystem.usage.sub-commands-header=íì ëªë ¹ì´
luckperms.commandsystem.usage.usage-header=ëªë ¹ì´ ì¬ì©ë²
luckperms.commandsystem.usage.arguments-header=ì¸ì
luckperms.first-time.no-permissions-setup=ì´ë í ê¶íë ì¤ì ëì§ ìì ê²ì¼ë¡ ë³´ìëë¤\!
luckperms.first-time.use-console-to-give-access=ê²ì ë´ìì LuckPerms ëªë ¹ì´ë¥¼ ì¬ì©íê¸° ì ì, ì½ìì ì¬ì©íì¬ ì ê·¼ ê¶íì ë¹ì ìê² ììí´ì¼ í©ëë¤
luckperms.first-time.console-command-prompt=ì½ìì ì´ê³  ì¤ííì­ìì¤
luckperms.first-time.next-step=ì´ ììì ìë£í í, ê¶í í ë¹ ë° ê·¸ë£¹ ì ìë¥¼ ììí  ì ììµëë¤
luckperms.first-time.wiki-prompt=ì´ëìë¶í° ììí´ì¼ í ì§ ëª¨ë¥´ê² ë¤ê³ ì? ì¬ê¸°ë¥¼ íì¸íì¸ì\: {0}
luckperms.login.try-again=ì ì í ë¤ì ìëí´ ì£¼ì¸ì
luckperms.login.loading-database-error=ê¶í ë°ì´í°ë¥¼ ë¡ëíë ëì¤ ë°ì´í°ë² ì´ì¤ ì¤ë¥ê° ë°ìíììµëë¤
luckperms.login.server-admin-check-console-errors=ë§ì½ ë¹ì ì´ ìë² ê´ë¦¬ìì¸ ê²½ì°, ì½ììì ì¤ë¥ê° ìëì§ íì¸í´ ì£¼ì­ìì¤
luckperms.login.server-admin-check-console-info=ìë² ì½ììì ë ìì¸í ì ë³´ë¥¼ íì¸íì¤ ì ììµëë¤
luckperms.login.data-not-loaded-at-pre=ì¬ì  ë¡ê·¸ì¸ ë¨ê³ìì ì¬ì©ìì ê¶í ë°ì´í°ê° ë¡ëëì§ ìììµëë¤
luckperms.login.unable-to-continue=ê³ìí  ì ììµëë¤
luckperms.login.craftbukkit-offline-mode-error=ì´ë ì£¼ë¡ CraftBukkitê³¼ online-mode ì¤ì ì ì¶©ëë¡ ì¸í´ ë°ìí©ëë¤
luckperms.login.unexpected-error=ê·íì ê¶í ë°ì´í°ë¥¼ ì¤ì íë ëì¤ ìê¸°ì¹ ëª»í ì¤ë¥ê° ë°ìíìµëë¤
luckperms.opsystem.disabled=ì´ ìë²ì ê¸°ë³¸ OP ìì¤íì´ ë¹íì±íëì´ ììµëë¤
luckperms.opsystem.sponge-warning=ê¶í íë¬ê·¸ì¸ì´ ì¤ì¹ë ê²½ì° ìë² ê´ë¦¬ì ìíë Sponge ê¶í ê²ì¬ì ìí¥ì ë¯¸ì¹ì§ ìì¼ë¯ë¡ ì¬ì©ì ë°ì´í°ë¥¼ ì§ì  í¸ì§í´ì¼ í©ëë¤
luckperms.duration.unit.years.plural={0}ë
luckperms.duration.unit.years.singular={0}ë
luckperms.duration.unit.years.short={0}ë
luckperms.duration.unit.months.plural={0}ê°ì
luckperms.duration.unit.months.singular={0}ê°ì
luckperms.duration.unit.months.short={0}ë¬
luckperms.duration.unit.weeks.plural={0}ì£¼
luckperms.duration.unit.weeks.singular={0}ì£¼
luckperms.duration.unit.weeks.short={0}ì£¼
luckperms.duration.unit.days.plural={0}ì¼
luckperms.duration.unit.days.singular={0}ì¼
luckperms.duration.unit.days.short={0}ì¼
luckperms.duration.unit.hours.plural={0}ìê°
luckperms.duration.unit.hours.singular={0}ìê°
luckperms.duration.unit.hours.short={0}ìê°
luckperms.duration.unit.minutes.plural={0}ë¶
luckperms.duration.unit.minutes.singular={0}ë¶
luckperms.duration.unit.minutes.short={0}ë¶
luckperms.duration.unit.seconds.plural={0}ì´
luckperms.duration.unit.seconds.singular={0}ì´
luckperms.duration.unit.seconds.short={0}ì´
luckperms.duration.since={0} ì 
luckperms.command.misc.invalid-code=ì¬ë°ë¥´ì§ ìì ì½ë
luckperms.command.misc.response-code-key=ìëµ ì½ë
luckperms.command.misc.error-message-key=ë©ìì§
luckperms.command.misc.bytebin-unable-to-communicate=bytebinê³¼ íµì í  ì ììµëë¤
luckperms.command.misc.webapp-unable-to-communicate=ì¹ ì±ê³¼ íµì í  ì ììµëë¤
luckperms.command.misc.check-console-for-errors=ì½ììì ì¤ë¥ë¥¼ íì¸íì¸ì
luckperms.command.misc.file-must-be-in-data=íì¼ {0}ì(ë) ë°ì´í° ëë í°ë¦¬ ë°ë¡ ìë ìì¹í´ì¼ í©ëë¤
luckperms.command.misc.wait-to-finish=ìë£ë  ëê¹ì§ ê¸°ë¤ë¦° í ë¤ì ìëíì­ìì¤
luckperms.command.misc.invalid-priority=ì í¨íì§ ìì ì°ì  ìì {0}
luckperms.command.misc.expected-number=ì«ìê° íìí©ëë¤
luckperms.command.misc.date-parse-error=ë ì§ {0}ì(ë¥¼) ë¶ìí  ì ììµëë¤
luckperms.command.misc.date-in-past-error=ë ì§ë ê³¼ê±°ë¡ ì¤ì í  ì ììµëë¤\!
luckperms.command.misc.page=ì´ {1}íì´ì§ ì¤ {0}íì´ì§
luckperms.command.misc.page-entries={0}ê°ì í­ëª©
luckperms.command.misc.none=ìì
luckperms.command.misc.loading.error.unexpected=ììì¹ ëª»í ì¤ë¥ê° ë°ìíìµëë¤
luckperms.command.misc.loading.error.user=ì¬ì©ìê° ë¡ëëì§ ìììµëë¤
luckperms.command.misc.loading.error.user-specific=ëì ì¬ì©ì {0}ì(ë¥¼) ë¡ëí  ì ììµëë¤
luckperms.command.misc.loading.error.user-not-found=ì¬ì©ì {0}ì(ë¥¼) ì°¾ì ì ììµëë¤
luckperms.command.misc.loading.error.user-save-error={0}ì ëí ì¬ì©ì ë°ì´í°ë¥¼ ì ì¥íë ëì¤ ì¤ë¥ê° ë°ìíìµëë¤
luckperms.command.misc.loading.error.user-not-online=ì¬ì©ì {0}ì(ë) ì¨ë¼ì¸ì´ ìëëë¤
luckperms.command.misc.loading.error.user-invalid={0}ì(ë) ì í¨íì§ ìì ì¬ì©ì í¹ì UUID ìëë¤
luckperms.command.misc.loading.error.user-not-uuid=ëì ì¬ì©ì {0}ì(ë) ì í¨íì§ ìì UUIDìëë¤
luckperms.command.misc.loading.error.group=ê·¸ë£¹ì´ ë¡ëëì§ ìììµëë¤
luckperms.command.misc.loading.error.all-groups=ëª¨ë  ê·¸ë£¹ì ë¶ë¬ì¬ ì ììµëë¤
luckperms.command.misc.loading.error.group-not-found=ê·¸ë£¹ {0}ì(ë¥¼) ì°¾ì ì ììµëë¤
luckperms.command.misc.loading.error.group-save-error={0}ì ëí ê·¸ë£¹ ë°ì´í°ë¥¼ ì ì¥íë ëì¤ ì¤ë¥ê° ë°ìíìµëë¤
luckperms.command.misc.loading.error.group-invalid={0}ì(ë) ì¬ë°ë¥´ì§ ìì ê·¸ë£¹ ì´ë¦ìëë¤
luckperms.command.misc.loading.error.track=í¸ëì´ ë¡ëëì§ ìììµëë¤
luckperms.command.misc.loading.error.all-tracks=ëª¨ë  í¸ëì ë¡ëí  ì ììµëë¤
luckperms.command.misc.loading.error.track-not-found=í¸ë {0}ì(ë¥¼) ì°¾ì ì ììµëë¤
luckperms.command.misc.loading.error.track-save-error={0}ì ëí í¸ë ë°ì´í°ë¥¼ ì ì¥íë ëì¤ ì¤ë¥ê° ë°ìíìµëë¤
luckperms.command.misc.loading.error.track-invalid={0}ì(ë) ì¬ë°ë¥´ì§ ìì í¸ë ì´ë¦ìëë¤
luckperms.command.editor.no-match=í¸ì§ê¸°ë¥¼ ì´ ì ììµëë¤. ìêµ¬íë ì íê³¼ ì¼ì¹íë ê°ì²´ê° ììµëë¤
luckperms.command.editor.start=ìë¡ì´ í¸ì§ê¸° ì¸ìì ì¤ë¹íë ì¤ìëë¤. ì ìë§ ê¸°ë¤ë ¤ ì£¼ì¸ì...
luckperms.command.editor.url=ìë ë§í¬ë¥¼ í´ë¦­íì¬ ìëí°ë¥¼ ì´ ì ììµëë¤
luckperms.command.editor.unable-to-communicate=í¸ì§ê¸°ì ì°ê²°í  ì ììµëë¤
luckperms.command.editor.apply-edits.success=ì¹ í¸ì§ê¸° ë°ì´í°ê° {0} {1}ì ì±ê³µì ì¼ë¡ ì ì©ëììµëë¤
luckperms.command.editor.apply-edits.success-summary={0} {1} ê³¼(ì) {2} {3}
luckperms.command.editor.apply-edits.success.additions=ì¶ê°
luckperms.command.editor.apply-edits.success.additions-singular=ì¶ê°
luckperms.command.editor.apply-edits.success.deletions=ì­ì 
luckperms.command.editor.apply-edits.success.deletions-singular=ì­ì 
luckperms.command.editor.apply-edits.no-changes=ì¹ í¸ì§ê¸°ìì ë³ê²½ë ì¬í­ì´ ììì¼ë©°, ë°íë ë°ì´í°ì ë³ê²½ë ë´ì©ì´ í¬í¨ëì´ ìì§ ìììµëë¤
luckperms.command.editor.apply-edits.unknown-type=ì§ì ë ê°ì²´ ì íì ë³ê²½ ì¬í­ì ì ì©í  ì ììµëë¤
luckperms.command.editor.apply-edits.unable-to-read=ì£¼ì´ì§ ì½ëë¥¼ ì¬ì©í´ ë°ì´í°ë¥¼ ì½ì ì ììµëë¤
luckperms.command.search.searching.permission={0}ì ëí ì¬ì©ì ë° ê·¸ë£¹ì ê²ìí©ëë¤
luckperms.command.search.searching.inherit={0}ìì ììë ì¬ì©ì ë° ê·¸ë£¹ì ê²ìí©ëë¤
luckperms.command.search.result={1}ëªì ì¬ì©ìì {2}ê°ì ê·¸ë£¹ìì {0}ê°ì í­ëª©ì ì°¾ììµëë¤
luckperms.command.search.result.default-notice=ì°¸ê³ \: ê¸°ë³¸ ê·¸ë£¹ì êµ¬ì±ìì ê²ìí  ë ë¤ë¥¸ ê¶íì´ ìë ì¤íë¼ì¸ íë ì´ì´ë íìëì§ ììµëë¤\!
luckperms.command.search.showing-users=ì¬ì©ì í­ëª© íì
luckperms.command.search.showing-groups=ê·¸ë£¹ í­ëª© íì
luckperms.command.tree.start=ê¶í í¸ë¦¬ë¥¼ ìì±íê³  ììµëë¤. ì ìë§ ê¸°ë¤ë ¤ ì£¼ì¸ì...
luckperms.command.tree.empty=í¸ë¦¬ë¥¼ ìì±í  ì ììµëë¤, ê²°ê³¼ë¥¼ ì°¾ì§ ëª»íìµëë¤
luckperms.command.tree.url=ê¶í í¸ë¦¬ URL
luckperms.command.verbose.invalid-filter={0}ì(ë) ì í¨íì§ ìì ìì¸ íí°ìëë¤
luckperms.command.verbose.enabled={1}ê³¼(ì) ì¼ì¹íë ê²ì ëí ìì¸ ê¸°ë¡ {0}
luckperms.command.verbose.command-exec={0}ì´(ê°) ëªë ¹ {1}ì(ë¥¼) ê°ì ë¡ ì¤ííëë¡ íê³ , ëª¨ë  ê²ì¬ë¥¼ ë³´ê³ í©ëë¤...
luckperms.command.verbose.off=ìì¸í ë¡ê¹ {0}
luckperms.command.verbose.command-exec-complete=ëªë ¹ì´ ì¤í ìë£
luckperms.command.verbose.command.no-checks=ëªë ¹ì´ ì¤íì´ ìë£ëìì§ë§, ê¶í íì¸ì´ ì´ë£¨ì´ì§ì§ ìììµëë¤
luckperms.command.verbose.command.possibly-async=íë¬ê·¸ì¸ì´ ë°±ê·¸ë¼ì´ë(ë¹ëê¸°)ìì ëªë ¹ì ì¤ííê¸° ëë¬¸ì¼ ì ììµëë¤
luckperms.command.verbose.command.try-again-manually=ìì§ ìì¸ ê¸°ë¡ì ì¬ì©íì¬ ì´ì ê°ì íì¸ì ê°ì§í  ì ììµëë¤
luckperms.command.verbose.enabled-recording={1}ê³¼(ì) ì¼ì¹íë ê²ì ëí ìì¸ ê¸°ë¡ {0}
luckperms.command.verbose.uploading=ìì¸í ë¡ê¹ {0}, ê²°ê³¼ ìë¡ë ì¤...
luckperms.command.verbose.url=ìì¸í ê²°ê³¼ URL
luckperms.command.verbose.enabled-term=íì±íë¨
luckperms.command.verbose.disabled-term=ë¹íì±íë¨
luckperms.command.verbose.query-any=ìë¬´ê±°ë
luckperms.command.info.running-plugin=ì¤í ì¤\:
luckperms.command.info.platform-key=íë«í¼
luckperms.command.info.server-brand-key=ìë² ë¸ëë
luckperms.command.info.server-version-key=ìë² ë²ì 
luckperms.command.info.storage-key=ì ì¥ì
luckperms.command.info.storage-type-key=ì í
luckperms.command.info.storage.meta.split-types-key=ì í
luckperms.command.info.storage.meta.ping-key=ì§ì° ìê°
luckperms.command.info.storage.meta.connected-key=ì°ê²°ë¨
luckperms.command.info.storage.meta.file-size-key=íì¼ í¬ê¸°
luckperms.command.info.extensions-key=íì¥ ê¸°ë¥
luckperms.command.info.messaging-key=ë©ìì§
luckperms.command.info.instance-key=ì¸ì¤í´ì¤
luckperms.command.info.static-contexts-key=ì ì  ì»¨íì¤í¸
luckperms.command.info.online-players-key=ì¨ë¼ì¸ íë ì´ì´
luckperms.command.info.online-players-unique={0} ì ê·
luckperms.command.info.uptime-key=ìíì
luckperms.command.info.local-data-key=ë¡ì»¬ ë°ì´í°
luckperms.command.info.local-data={0} ì¬ì©ì, {1} ê·¸ë£¹, {2} í¸ë
luckperms.command.generic.create.success={0}(ì´)ê° ì±ê³µì ì¼ë¡ ìì±ëììµëë¤
luckperms.command.generic.create.error={0}(ì)ë¥¼ ìì±íë ëì¤ ì¤ë¥ê° ë°ìíììµëë¤
luckperms.command.generic.create.error-already-exists={0}(ì´)ê° ì´ë¯¸ ì¡´ì¬í©ëë¤\!
luckperms.command.generic.delete.success={0}(ì´)ê° ì±ê³µì ì¼ë¡ ì­ì ëììµëë¤
luckperms.command.generic.delete.error={0}(ì)ë¥¼ ì­ì íë ëì¤ ì¤ë¥ê° ë°ìíìµëë¤
luckperms.command.generic.delete.error-doesnt-exist={0}ì(ë) ì¡´ì¬íì§ ììµëë¤\!
luckperms.command.generic.rename.success={0}ì ì´ë¦ì´ {1}(ì¼)ë¡ ë³ê²½ëììµëë¤
luckperms.command.generic.clone.success={0}ì(ë¥¼) {1}ì ë³µì íìµëë¤
luckperms.command.generic.info.parent.title=ìì ê·¸ë£¹
luckperms.command.generic.info.parent.temporary-title=ìì ìì ê·¸ë£¹
luckperms.command.generic.info.expires-in=ë§ë£ëê¸°ê¹ì§
luckperms.command.generic.info.inherited-from=ë¤ììì ììë¨\:
luckperms.command.generic.info.inherited-from-self=ìì 
luckperms.command.generic.show-tracks.title={0}ì í¸ë
luckperms.command.generic.show-tracks.empty={0}ì(ë) í¸ëì ê°ì§ê³  ìì§ ììµëë¤
luckperms.command.generic.clear.node-removed=ë¸ë {0}ì´(ê°) ì ê±°ëììµëë¤
luckperms.command.generic.clear.node-removed-singular=ë¸ë {0}ì´(ê°) ì ê±°ëììµëë¤
luckperms.command.generic.clear=ë¸ë {0}ì´(ê°) {1} ì»¨íì¤í¸ìì ì ê±°ëììµëë¤
luckperms.command.generic.permission.info.title={0}ì ê¶í
luckperms.command.generic.permission.info.empty={0}ì(ë) ì´ë í ê¶íë ì¤ì ëì´ ìì§ ììµëë¤
luckperms.command.generic.permission.info.click-to-remove=í´ë¦­íì¬ {0}ìì ì´ ë¸ëë¥¼ ì ê±°í©ëë¤
luckperms.command.generic.permission.check.info.title={0}ì ëí ê¶í ì ë³´
luckperms.command.generic.permission.check.info.directly={0}ì(ë) {3} ì»¨íì¤í¸ìì {2}ì¼(ë¡) ì¤ì ë {1}ì(ë¥¼) ê°ì§ê³  ììµëë¤
luckperms.command.generic.permission.check.info.inherited={0}ì(ë) {4} ì»¨íì¤í¸ìì {2}ì¼(ë¡) ì¤ì ë {1}ì(ë¥¼) {3}ì ìí´ ììë°ê³  ììµëë¤
luckperms.command.generic.permission.check.info.not-directly={0}ìê² {1}ì´(ê°) ì¤ì ëì´ ìì§ ììµëë¤
luckperms.command.generic.permission.check.info.not-inherited={0}ì(ë) {1}ì(ë¥¼) ììíì§ ììµëë¤
luckperms.command.generic.permission.check.result.title={0}ì ëí ê¶í íì¸
luckperms.command.generic.permission.check.result.result-key=ê²°ê³¼
luckperms.command.generic.permission.check.result.processor-key=ì²ë¦¬ì
luckperms.command.generic.permission.check.result.cause-key=ì´ì 
luckperms.command.generic.permission.check.result.context-key=ì»¨íì¤í¸
luckperms.command.generic.permission.set={2}ì {0}ì(ë¥¼) {3} ì»¨íì¤í¸ì ëí´ {1}ì¼(ë¡) ì¤ì íìµëë¤
luckperms.command.generic.permission.already-has={0}ì(ë) {2} ì»¨íì¤í¸ì ëí {1}ì(ë¥¼) ì´ë¯¸ ê°ì§ê³  ììµëë¤
luckperms.command.generic.permission.set-temp={2}ì {0}ì(ë¥¼) {4} ì»¨íì¤í¸ì ëí´ {3} ëì {1}ì¼(ë¡) ì¤ì íìµëë¤
luckperms.command.generic.permission.already-has-temp={0}ì(ë) {2} ì»¨íì¤í¸ì ëí ì¼ìì ì¸ {1}ì(ë¥¼) ì´ë¯¸ ê°ì§ê³  ììµëë¤
luckperms.command.generic.permission.unset={1}ì {2} ì»¨íì¤í¸ì ëí {0}ì(ë¥¼) ì¤ì  í´ì íìµëë¤
luckperms.command.generic.permission.doesnt-have={0}ì {2} ì»¨íì¤í¸ì ëí {1}ì(ë) ì¤ì ëì´ ìì§ ììµëë¤
luckperms.command.generic.permission.unset-temp={1}ì {2} ì»¨íì¤í¸ì ëí ìì {0}ì(ë¥¼) ì¤ì  í´ì íìµëë¤
luckperms.command.generic.permission.subtract={2}ì {0}ì(ë¥¼) {4} ì»¨íì¤í¸ì ëí´ {3} ëì {1}ì¼(ë¡) ì¤ì íìµëë¤ (ì´ì ë³´ë¤ {5} ê°ì)
luckperms.command.generic.permission.doesnt-have-temp={0}ì {2} ì»¨íì¤í¸ì ëí ìì {1}ì(ë) ì¤ì ëì´ ìì§ ììµëë¤
luckperms.command.generic.permission.clear={1} ì»¨íì¤í¸ì ëí {0}ì ëª¨ë  ê¶íì´ ì´ê¸°í ëììµëë¤
luckperms.command.generic.parent.info.title={0}ì ìì í­ëª©
luckperms.command.generic.parent.info.empty={0}ì(ë) ìì í­ëª©ì´ ì§ì ëì§ ìììµëë¤
luckperms.command.generic.parent.info.click-to-remove=í´ë¦­íì¬ {0}ìì ì´ ìì í­ëª©ì ì ê±°í©ëë¤
luckperms.command.generic.parent.add={0}ì(ë) ì´ì  {2} ì»¨íì¤í¸ìì {1}ì ê¶íì ììí©ëë¤
luckperms.command.generic.parent.add-temp={0}ì(ë) ì´ì  {2} ëì {3} ì»¨íì¤í¸ìì {1}ì ê¶íì ììí©ëë¤
luckperms.command.generic.parent.set={0}ì ê¸°ì¡´ ìì ê·¸ë£¹ì´ ì ê±°ëìì¼ë©°, ì´ì  {2} ì»¨íì¤í¸ìì {1}ì ê¶íì ììí©ëë¤
luckperms.command.generic.parent.set-track=í¸ë {1}ì ìë {0}ì ê¸°ì¡´ ìì ê·¸ë£¹ì´ ì ê±°ëìì¼ë©°, ì´ì  {3} ì»¨íì¤í¸ìì {2}ì ê¶íì ììí©ëë¤
luckperms.command.generic.parent.remove={0}ì(ë) ë ì´ì {2} ì»¨íì¤í¸ìì {1}ì ê¶íì ììë°ì§ ììµëë¤
luckperms.command.generic.parent.remove-temp={0}ì(ë) ë ì´ì {2} ì»¨íì¤í¸ìì {1}ì ê¶íì ììë¡ ììë°ì§ ììµëë¤
luckperms.command.generic.parent.subtract={0}ì(ë) {2} ëì {3} ì»¨íì¤í¸ìì {1}ì ê¶íì ììë°ì ê²ìëë¤. (ì´ì ë³´ë¤ {4} ê°ì)
luckperms.command.generic.parent.clear={0}ì ìì í­ëª©ì´ {1} ì»¨íì¤í¸ìì ì­ì ëììµëë¤
luckperms.command.generic.parent.clear-track=í¸ë {1}ì ìë {0}ì ìì í­ëª©ì´ {2} ì»¨íì¤í¸ìì ì ê±°ëììµëë¤
luckperms.command.generic.parent.already-inherits={0}ì(ë) ì´ë¯¸ {2} ì»¨íì¤í¸ìì {1}ìê² ììë°ê³  ììµëë¤
luckperms.command.generic.parent.doesnt-inherit={0}ì(ë) {2} ì»¨íì¤í¸ìì {1}ìê² ììë°ê³  ìì§ ììµëë¤
luckperms.command.generic.parent.already-temp-inherits={0}ì(ë) ì´ë¯¸ {2} ì»¨íì¤í¸ìì {1}ìê² ììë¡ ììë°ê³  ììµëë¤
luckperms.command.generic.parent.doesnt-temp-inherit={0}ì(ë) {2} ì»¨íì¤í¸ìì {1}ìê² ììë¡ ììë°ê³  ìì§ ììµëë¤
luckperms.command.generic.chat-meta.info.title-prefix={0}ì ì ëì¬
luckperms.command.generic.chat-meta.info.title-suffix={0}ì ì ë¯¸ì¬
luckperms.command.generic.chat-meta.info.none-prefix={0}ì(ë) ì ëì¬ë¥¼ ê°ì§ê³  ìì§ ììµëë¤
luckperms.command.generic.chat-meta.info.none-suffix={0}ì(ë) ì ë¯¸ì¬ë¥¼ ê°ì§ê³  ìì§ ììµëë¤
luckperms.command.generic.chat-meta.info.click-to-remove=í´ë¦­íì¬ {1}ìì {0}ì(ë¥¼) ì ê±°í©ëë¤
luckperms.command.generic.chat-meta.already-has={0}ì(ë) ì´ë¯¸ {4} ì»¨íì¤í¸ìì ì°ì  ììê° {3}ì¼(ë¡) ì¤ì ë {1} {2}ì(ë¥¼) ê°ì§ê³  ììµëë¤
luckperms.command.generic.chat-meta.already-has-temp={0}ì(ë) ì´ë¯¸ {4} ì»¨íì¤í¸ìì ì°ì  ììê° {3}ì¼(ë¡) ì¤ì ë ììì ì¸ {1} {2}ì(ë¥¼) ê°ì§ê³  ììµëë¤
luckperms.command.generic.chat-meta.doesnt-have={0}ì(ë) {4} ì»¨íì¤í¸ìì ì°ì  ììê° {3}ì¼(ë¡) ì¤ì ë {1} {2}ì(ë¥¼) ê°ì§ê³  ìì§ ììµëë¤
luckperms.command.generic.chat-meta.doesnt-have-temp={0}ì(ë) {4} ì»¨íì¤í¸ìì ì°ì  ììê° {3}ì¼(ë¡) ì¤ì ë ììì ì¸ {1} {2}ì(ë¥¼) ê°ì§ê³  ìì§ ììµëë¤
luckperms.command.generic.chat-meta.add={0}ì(ë) ì´ì  {4} ì»¨íì¤í¸ìì ì°ì  ììê° {3}ì¼(ë¡) ì¤ì ë {1} {2}ì(ë¥¼) ê°ì§ëë¤
luckperms.command.generic.chat-meta.add-temp={0}ì(ë) ì´ì  {5} ì»¨íì¤í¸ìì ì°ì  ììê° {3}ì¼(ë¡) ì¤ì ë {1} {2}ì(ë¥¼) {4} ëì ê°ì§ëë¤
luckperms.command.generic.chat-meta.remove={0}ì(ë) ì´ì  {4} ì»¨íì¤í¸ìì ì°ì  ììê° {3}ì¼(ë¡) ì¤ì ë {1} {2}ì(ë¥¼) ê°ì§ì§ ììµëë¤
luckperms.command.generic.chat-meta.remove-bulk={0}ì(ë) ì´ì  {3} ì»¨íì¤í¸ìì ì°ì  ììê° {2}ì¸ ëª¨ë  {1}ì(ë¥¼) ê°ì§ì§ ììµëë¤
luckperms.command.generic.chat-meta.remove-temp={0}ì(ë) ì´ì  {4} ì»¨íì¤í¸ìì ì°ì  ììê° {3}ì¸ ì¼ìì ì¸ {1} {2}ì(ë¥¼) ê°ì§ì§ ììµëë¤
luckperms.command.generic.chat-meta.remove-temp-bulk={0}ì(ë) ì´ì  {3} ì»¨íì¤í¸ìì ì°ì  ììê° {2}ì¸ ëª¨ë  ì¼ìì ì¸ {1}ì(ë¥¼) ê°ì§ì§ ììµëë¤
luckperms.command.generic.meta.info.title={0}ì ë©í ë°ì´í°
luckperms.command.generic.meta.info.none={0}ì(ë) ë©í ë°ì´í°ë¥¼ ê°ì§ê³  ìì§ ììµëë¤
luckperms.command.generic.meta.info.click-to-remove=í´ë¦­íì¬ {0}ìì ì´ ë©í ë°ì´í° ë¸ëë¥¼ ì ê±°í©ëë¤
luckperms.command.generic.meta.already-has={0}ì(ë) {3} ì»¨íì¤í¸ìì {2}ì¼(ë¡) ì¤ì ë ë©í ë°ì´í° í¤ {1}ì(ë¥¼) ì´ë¯¸ ê°ì§ê³  ììµëë¤
luckperms.command.generic.meta.already-has-temp={0}ì(ë) {3} ì»¨íì¤í¸ìì {2}ì¼(ë¡) ììë¡ ì¤ì ë ë©í ë°ì´í° í¤ {1}ì(ë¥¼) ì´ë¯¸ ê°ì§ê³  ììµëë¤
luckperms.command.generic.meta.doesnt-have={0}ì {2} ì»¨íì¤í¸ì ëí ë©í ë°ì´í° í¤ {1}ì(ë) ì¤ì ëì´ ìì§ ììµëë¤
luckperms.command.generic.meta.doesnt-have-temp={0}ì {2} ì»¨íì¤í¸ì ëí ìì ë©í ë°ì´í° í¤ {1}ì(ë) ì¤ì ëì´ ìì§ ììµëë¤
luckperms.command.generic.meta.set={2}ì ë©í ë°ì´í° í¤ {0}ì(ë¥¼) ì»¨íì¤í¸ {3}ì ëí´ {1}ì¼(ë¡) ì¤ì íìµëë¤
luckperms.command.generic.meta.set-temp={2}ì ë©í ë°ì´í° í¤ {0}ì(ë¥¼) ì»¨íì¤í¸ {4}ì ëí´ {3} ëì {1}ì¼(ë¡) ì ì§ëëë¡ ì¤ì íìµëë¤
luckperms.command.generic.meta.unset={1}ì ë©í ë°ì´í° í¤ {0}ì(ë¥¼) {2} ì»¨íì¤í¸ì ëí´ ì´ê¸°í íìµëë¤
luckperms.command.generic.meta.unset-temp={1}ì ìì ë©í ë°ì´í° í¤ {0}ì(ë¥¼) {2} ì»¨íì¤í¸ì ëí´ ì´ê¸°í íìµëë¤
luckperms.command.generic.meta.clear={0}ì {1}ê³¼(ì) ì¼ì¹íë ë©í ë°ì´í° ì íì´ {2} ì»¨íì¤í¸ìì ìì ì ê±°ëììµëë¤
luckperms.command.generic.contextual-data.title=ìí©ë³ ë°ì´í°
luckperms.command.generic.contextual-data.mode.key=ëª¨ë
luckperms.command.generic.contextual-data.mode.server=ìë²
luckperms.command.generic.contextual-data.mode.active-player=íëì ì¸ íë ì´ì´
luckperms.command.generic.contextual-data.contexts-key=ì»¨íì¤í¸
luckperms.command.generic.contextual-data.prefix-key=ì ëì¬
luckperms.command.generic.contextual-data.suffix-key=ì ë¯¸ì¬
luckperms.command.generic.contextual-data.primary-group-key=ì£¼ ê·¸ë£¹
luckperms.command.generic.contextual-data.meta-key=ë©í ë°ì´í°
luckperms.command.generic.contextual-data.null-result=ìì
luckperms.command.user.info.title=ì¬ì©ì ì ë³´
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=ì í
luckperms.command.user.info.uuid-type.mojang=ëª¨ì¥
luckperms.command.user.info.uuid-type.not-mojang=ì¤íë¼ì¸
luckperms.command.user.info.status-key=ìí
luckperms.command.user.info.status.online=ì¨ë¼ì¸
luckperms.command.user.info.status.offline=ì¤íë¼ì¸
luckperms.command.user.removegroup.error-primary=ì¬ì©ìë¥¼ ì£¼ ê·¸ë£¹ìì ì ê±°í  ì ììµëë¤
luckperms.command.user.primarygroup.not-member={0}ì(ë) ìì§ {1} ììì´ ìëëë¤. ì§ê¸ ì¶ê°í´ë³´ì¸ì
luckperms.command.user.primarygroup.already-has={0}ì(ë) ì´ë¯¸ {1}ì(ë¥¼) ì£¼ ê·¸ë£¹ì¼ë¡ ì¤ì íìµëë¤
luckperms.command.user.primarygroup.warn-option=ê²½ê³ \: ì´ ìë²({0})ìì ì¬ì© ì¤ì¸ ì£¼ ê·¸ë£¹ ê³ì° ë°©ë²ì ë³ê²½ ì¬í­ì ë°ìíì§ ìì ìë ììµëë¤
luckperms.command.user.primarygroup.set={0}ì ê¸°ë³¸ ê·¸ë£¹ì´ {1}ì¼(ë¡) ì¤ì ëììµëë¤
luckperms.command.user.track.error-not-contain-group={0}ì(ë) {1}ì ì´ë¤ ê·¸ë£¹ìë ìí´ ìì§ ììµëë¤
luckperms.command.user.track.unsure-which-track=ì¬ì©í  í¸ëì´ íì¤íì§ ììµëë¤. ë§¤ê° ë³ìì ì§ì í´ ì£¼ì¸ì
luckperms.command.user.track.missing-group-advice=ê·¸ë£¹ì ë§ë¤ê±°ë í¸ëìì ì ê±°í í ë¤ì ìëíì­ìì¤
luckperms.command.user.promote.added-to-first={0}ì(ë) {1}ì ì´ë¤ ê·¸ë£¹ìë ìí´ ìì§ ìì¼ë¯ë¡ {3} ì»¨íì¤í¸ìì {2} ì²«ë²ì§¸ ê·¸ë£¹ì ì¶ê°ëììµëë¤
luckperms.command.user.promote.not-on-track={0}ì(ë) {1}ì ì´ë¤ ê·¸ë£¹ìë ìí´ ìì§ ìì¼ë¯ë¡ ì¹ê²©ëì§ ìììµëë¤
luckperms.command.user.promote.success={4} ì»¨íì¤í¸ìì í¸ë {1}ì(ë¥¼) ë°ë¼ {0}ì(ë¥¼) {2}ìì {3}ì¼(ë¡) ì¹ê²©í©ëë¤
luckperms.command.user.promote.end-of-track=í¸ë {0}ì ë§ì§ë§ì ëë¬íì¬ {1}ì(ë¥¼) ë ì´ì ì¹ê²©í  ì ììµëë¤
luckperms.command.user.promote.next-group-deleted=í¸ëì ë¤ì ê·¸ë£¹ {0}ì´(ê°) ë ì´ì ì¡´ì¬íì§ ììµëë¤
luckperms.command.user.promote.unable-to-promote=ì¬ì©ìë¥¼ ì¹ê²©ìí¬ ì ììµëë¤
luckperms.command.user.demote.success={4} ì»¨íì¤í¸ìì í¸ë {1}ì(ë¥¼) ë°ë¼ {0}ì(ë¥¼) {2}ìì {3}ì¼(ë¡) ê°ë±í©ëë¤
luckperms.command.user.demote.end-of-track=í¸ë {0}ì ë§ì§ë§ì ëë¬íì¬ {1}ì´(ê°) {2}ìì ì ê±°ëììµëë¤
luckperms.command.user.demote.end-of-track-not-removed=í¸ë {0}ì ë§ì§ë§ì ëë¬íìì§ë§ {1}ì(ë) ì²« ë²ì§¸ ê·¸ë£¹ìì ì ê±°ëì§ ìììµëë¤
luckperms.command.user.demote.previous-group-deleted=í¸ëì ì´ì  ê·¸ë£¹ {0}ì´(ê°) ë ì´ì ì¡´ì¬íì§ ììµëë¤
luckperms.command.user.demote.unable-to-demote=ì¬ì©ìë¥¼ ê°ë±ìí¬ ì ììµëë¤
luckperms.command.group.list.title=ê·¸ë£¹
luckperms.command.group.delete.not-default=ê¸°ë³¸ ê·¸ë£¹ì ì­ì í  ì ììµëë¤
luckperms.command.group.info.title=ê·¸ë£¹ ì ë³´
luckperms.command.group.info.display-name-key=íì ì´ë¦
luckperms.command.group.info.weight-key=ê°ì¤ì¹
luckperms.command.group.setweight.set={1}ì ê°ì¤ì¹ë¥¼ {0}ì¼(ë¡) ì¤ì íìµëë¤
luckperms.command.group.setdisplayname.doesnt-have={0}ìë íì ì´ë¦ì´ ì¤ì ëì´ ìì§ ììµëë¤
luckperms.command.group.setdisplayname.already-has={0}ì(ë) ì´ë¯¸ íì ì´ë¦ {1}ì(ë¥¼) ê°ì§ê³  ììµëë¤
luckperms.command.group.setdisplayname.already-in-use=íì ì´ë¦ {0}ì(ë) ì´ë¯¸ {1}ìì ì¬ì©ëììµëë¤
luckperms.command.group.setdisplayname.set=ê·¸ë£¹ {1}ì {2} ì»¨íì¤í¸ íì ì´ë¦ì {0}ì¼(ë¡) ì¤ì íìµëë¤
luckperms.command.group.setdisplayname.removed=ê·¸ë£¹ {0}ì {1} ì»¨íì¤í¸ íì ì´ë¦ì ì ê±°íìµëë¤
luckperms.command.track.list.title=í¸ë
luckperms.command.track.path.empty=ìì
luckperms.command.track.info.showing-track=í¸ë íì
luckperms.command.track.info.path-property=ê²½ë¡
luckperms.command.track.clear={0}ì ê·¸ë£¹ í¸ëì´ ì ê±°ëììµëë¤
luckperms.command.track.append.success=ê·¸ë£¹ {0}ì´(ê°) í¸ë {1}ì ì¶ê°ëììµëë¤
luckperms.command.track.insert.success=ê·¸ë£¹ {0}ì´(ê°) í¸ë {1}ì {2} ìì¹ì ì½ìëììµëë¤
luckperms.command.track.insert.error-number=ììë ì«ìì´ì§ë§ ëì  ìì ëììµëë¤\: {0}
luckperms.command.track.insert.error-invalid-pos={0} ìì¹ì ì½ìí  ì ììµëë¤
luckperms.command.track.insert.error-invalid-pos-reason=ì í¨íì§ ìì ìì¹ìëë¤
luckperms.command.track.remove.success=ê·¸ë£¹ {0}ì´(ê°) í¸ë {1}ìì ì ê±°ëììµëë¤
luckperms.command.track.error-empty={0}ì(ë) ë¹ì´ ìê±°ë íëì ê·¸ë£¹ë§ í¬í¨íë¯ë¡ ì¬ì©í  ì ììµëë¤
luckperms.command.track.error-multiple-groups={0}ì(ë) ì´ í¸ëì ìë ì¬ë¬ ê·¸ë£¹ì êµ¬ì±ììëë¤
luckperms.command.track.error-ambiguous=ìì¹ë¥¼ íì¸í  ì ììµëë¤
luckperms.command.track.already-contains={0}ì(ë) ì´ë¯¸ {1}ì(ë¥¼) í¬í¨íê³  ììµëë¤
luckperms.command.track.doesnt-contain={0}ì(ë) {1}ì(ë¥¼) í¬í¨íê³  ìì§ ììµëë¤
luckperms.command.log.load-error=ë¡ê·¸ê° ë¡ëë  ì ìììµëë¤
luckperms.command.log.invalid-page=ì í¨íì§ ìì íì´ì§ ë²í¸
luckperms.command.log.invalid-page-range={0}ê³¼(ì) {1} ì¬ì´ì ê°ì ìë ¥í´ ì£¼ì¸ì
luckperms.command.log.empty=íìí  ë¡ê·¸ í­ëª©ì´ ììµëë¤
luckperms.command.log.notify.error-console=ì½ì ìë¦¼ì ì íí  ì ììµëë¤
luckperms.command.log.notify.enabled-term=íì±íë¨
luckperms.command.log.notify.disabled-term=ë¹íì±íë¨
luckperms.command.log.notify.changed-state=ë¡ê¹ ì¶ë ¥ {0}
luckperms.command.log.notify.already-on=ë¹ì ì ì´ë¯¸ ìë¦¼ì ë°ê³  ììµëë¤
luckperms.command.log.notify.already-off=ë¹ì ì íì¬ ìë¦¼ì ë°ê³  ìì§ ììµëë¤
luckperms.command.log.notify.invalid-state=ì ì ìë ìíìëë¤. {0} ëë {1} ì(ë¥¼) ììí©ëë¤
luckperms.command.log.show.search=ê²ìì´ {0}ì ëí ìµê·¼ ìì íì
luckperms.command.log.show.recent=ìµê·¼ ìì íì
luckperms.command.log.show.by={0}ì ìµê·¼ ìì íì
luckperms.command.log.show.history={0} {1}ì ëí ê¸°ë¡ íì
luckperms.command.export.error-term=ì¤ë¥
luckperms.command.export.already-running=ë¤ë¥¸ ë´ë³´ë´ê¸° íë¡ì¸ì¤ê° ì´ë¯¸ ì¤í ì¤ìëë¤
luckperms.command.export.file.already-exists=íì¼ {0}ì´(ê°) ì´ë¯¸ ì¡´ì¬í©ëë¤
luckperms.command.export.file.not-writable=íì¼ {0}ì´(ê°) ì½ê¸° ì ì©ìëë¤
luckperms.command.export.file.success={0}ì ì±ê³µì ì¼ë¡ ë´ë³´ëìµëë¤
luckperms.command.export.file-unexpected-error-writing=íì¼ì ì°ë ëì ìê¸°ì¹ ìì ì¤ë¥ê° ë°ìíìµëë¤
luckperms.command.export.web.export-code=ë´ë³´ë´ê¸° ì½ë
luckperms.command.export.web.import-command-description=ë¤ì ëªë ¹ì ì¬ì©íì¬ ê°ì ¸ìµëë¤
luckperms.command.import.term=ë¶ë¬ì¤ê¸°
luckperms.command.import.error-term=ì¤ë¥
luckperms.command.import.already-running=ë¤ë¥¸ ë¶ë¬ì¤ê¸° íë¡ì¸ì¤ê° ì´ë¯¸ ì¤í ì¤ìëë¤
luckperms.command.import.file.doesnt-exist=íì¼ {0}ì´(ê°) ì¡´ì¬íì§ ììµëë¤
luckperms.command.import.file.not-readable=íì¼ {0}ì(ë) ì½ì ì ììµëë¤
luckperms.command.import.file.unexpected-error-reading=ê°ì ¸ì¤ê¸° íì¼ì ì½ë ëì¤ ìê¸°ì¹ ìì ì¤ë¥ê° ë°ìíìµëë¤
luckperms.command.import.file.correct-format=ì¬ë°ë¥¸ íììëê¹?
luckperms.command.import.web.unable-to-read=ì£¼ì´ì§ ì½ëë¥¼ ì¬ì©í´ ë°ì´í°ë¥¼ ì½ì ì ììµëë¤
luckperms.command.import.progress.percent={0}% ìë£
luckperms.command.import.progress.operations={0}/{1} ìì ìë£ë¨
luckperms.command.import.starting=ë¶ë¬ì¤ê¸° ììì ììí©ëë¤
luckperms.command.import.completed=ìë£ë¨
luckperms.command.import.duration={0}ì´ê° ììëììµëë¤
luckperms.command.bulkupdate.must-use-console=ëë ìë°ì´í¸ ëªë ¹ì ì½ìììë§ ì§íí  ì ììµëë¤
luckperms.command.bulkupdate.invalid-data-type=ì í¨íì§ ìì ì íìëë¤. {0}ì´(ê°) íìí©ëë¤
luckperms.command.bulkupdate.invalid-constraint={0}ì(ë) ì¬ë°ë¥´ì§ ìì ì ì½ ì¡°ê±´ìëë¤
luckperms.command.bulkupdate.invalid-constraint-format=ì ì½ ì¡°ê±´ì {0} íìì´ì¬ì¼ í©ëë¤
luckperms.command.bulkupdate.invalid-comparison={0}ì(ë) ì¬ë°ë¥´ì§ ìì ë¹êµ ì°ì°ììëë¤
luckperms.command.bulkupdate.invalid-comparison-format=ë¤ì ì¤ íëê° íìí©ëë¤\: {0}
luckperms.command.bulkupdate.queued=ëë ìë°ì´í¸ ììì´ ëê¸°ì´ì ì¶ê°ëììµëë¤
luckperms.command.bulkupdate.confirm={0}ì(ë¥¼) ì¬ì©í´ ìë°ì´í¸ë¥¼ ì¤íí©ëë¤
luckperms.command.bulkupdate.unknown-id=ëì ID {0}ì´(ê°) ì¡´ì¬íì§ ìê±°ë ë§ë£ëììµëë¤
luckperms.command.bulkupdate.starting=ëë ìë°ì´í¸ ì¤í
luckperms.command.bulkupdate.success=ëë ìë°ì´í¸ê° ì±ê³µì ì¼ë¡ ìë£ëììµëë¤
luckperms.command.bulkupdate.success.statistics.nodes=ìí¥ì ë°ì ì´ ë¸ë
luckperms.command.bulkupdate.success.statistics.users=ìí¥ì ë°ì ì´ ì¬ì©ì
luckperms.command.bulkupdate.success.statistics.groups=ìí¥ì ë°ì ì´ ê·¸ë£¹
luckperms.command.bulkupdate.failure=ëë ìë°ì´í¸ì ì¤í¨íìµëë¤. ì½ììì ì¤ë¥ë¥¼ íì¸íì­ìì¤
luckperms.command.update-task.request=ìë°ì´í¸ ììì´ ìì³¥ëììµëë¤. ì ìë§ ê¸°ë¤ë ¤ ì£¼ì¸ì
luckperms.command.update-task.complete=ìë°ì´í¸ ììì´ ìë£ëììµëë¤
luckperms.command.update-task.push.attempting=ì´ì  ë¤ë¥¸ ìë²ë¡ ì ì¡ì ìëí©ëë¤
luckperms.command.update-task.push.complete=ë¤ë¥¸ ìë²ë {0}ì(ë¥¼) íµí´ ìë¦¼ì ì±ê³µì ì¼ë¡ ë°ììµëë¤
luckperms.command.update-task.push.error=ë³ê²½ ì¬í­ì ë¤ë¥¸ ìë²ë¡ ì ì¡íë ëì¤ ì¤ë¥ê° ë°ìíìµëë¤
luckperms.command.update-task.push.error-not-setup=ë©ìì§ ìë¹ì¤ê° êµ¬ì±ëì§ ììì¼ë¯ë¡ ë³ê²½ ì¬í­ì ë¤ë¥¸ ìë²ì ì ì¡í  ì ììµëë¤
luckperms.command.reload-config.success=ì¤ì  íì¼ì´ ë¤ì ë¡ëëììµëë¤
luckperms.command.reload-config.restart-note=ì¼ë¶ ì¤ì ì ìë²ê° ë¤ì ììë íì ì ì©ë©ëë¤
luckperms.command.translations.searching=ì¬ì© ê°ë¥í ë²ì­ì ê²ìíë ì¤ìëë¤. ì ìë§ ê¸°ë¤ë ¤ ì£¼ì¸ì...
luckperms.command.translations.searching-error=ì¬ì© ê°ë¥í ë²ì­ ëª©ë¡ì ê°ì ¸ì¬ ì ììµëë¤
luckperms.command.translations.installed-translations=ì¤ì¹ë ë²ì­
luckperms.command.translations.available-translations=ì¬ì© ê°ë¥í ë²ì­
luckperms.command.translations.percent-translated={0}% ë²ì­ë¨
luckperms.command.translations.translations-by=ê¸°ì¬ì\:
luckperms.command.translations.installing=ë²ì­ì ì¤ì¹íë ì¤ìëë¤. ì ìë§ ê¸°ë¤ë ¤ ì£¼ì¸ì...
luckperms.command.translations.download-error={0}ì ëí ë²ì­ì ë¤ì´ë¡ëí  ì ììµëë¤
luckperms.command.translations.installing-specific=ì¸ì´ {0} ì¤ì¹ ì¤...
luckperms.command.translations.install-complete=ì¤ì¹ê° ìë£ëììµëë¤
luckperms.command.translations.download-prompt={0} ì(ë¥¼) ì¬ì©íì¬ ì»¤ë®¤ëí°ìì ì ê³µíë ë²ì­ì ìµì  ë²ì ì ë¤ì´ë¡ëíê³  ì¤ì¹íì¤ ì ììµëë¤
luckperms.command.translations.download-override-warning=ì°¸ê³ íì¸ì, ì´ë í´ë¹ ì¸ì´ì ëí ëª¨ë  ë³ê²½ ë´ì©ì ë®ì´ìëë¤
luckperms.usage.user.description=LuckPerms ë´ìì ì¬ì©ìë¥¼ ê´ë¦¬íê¸° ìí ëªë ¹ì´ ì§í©ìëë¤. (LuckPermsì ''ì¬ì©ì''ë íë ì´ì´ì¼ ë¿ì´ë©°, UUID ëë ì¬ì©ì ì´ë¦ì ì°¸ì¡°í  ì ììµëë¤)
luckperms.usage.group.description=LuckPerms ë´ìì ê·¸ë£¹ì ê´ë¦¬íê¸° ìí ëªë ¹ì´ ì§í©ìëë¤. ê·¸ë£¹ì ì¬ì©ììê² ë¶ì¬í  ì ìë ê¶í í ë¹ ëª¨ìì¼ ë¿ìëë¤. ì ê·¸ë£¹ì ''create group'' ëªë ¹ì ì¬ì©íì¬ ë§ë¤ ì ììµëë¤.
luckperms.usage.track.description=LuckPerms ë´ìì í¸ëì ê´ë¦¬íê¸° ìí ëªë ¹ì´ ì§í©ìëë¤. í¸ëì ì¹ê¸ ë° ê°ë±ì ì ìíëë° ì¬ì©í  ì ìë ì ë ¬ë ê·¸ë£¹ ëª¨ììëë¤.
luckperms.usage.log.description=LuckPerms ë´ìì ë¡ê¹ ê¸°ë¥ì ê´ë¦¬íê¸° ìí ëªë ¹ ì§í©ìëë¤.
luckperms.usage.sync.description=íë¬ê·¸ì¸ì ëª¨ë  ë°ì´í°ë¥¼ ë©ëª¨ë¦¬ë¡ ë¤ì ë¡ëíê³ , ë³ê²½ë ëª¨ë  ì¬í­ì ì ì©í©ëë¤
luckperms.usage.info.description=íì± íë¬ê·¸ì¸ í¸ì¶ì ëí ì¼ë°ì ì¸ ì ë³´ë¥¼ ì¸ìí©ëë¤
luckperms.usage.editor.description=ì ì¹ í¸ì§ê¸° ì¸ìì ë§ë­ëë¤
luckperms.usage.editor.argument.type=í¸ì§ê¸°ì ë¡ëí  ì íë¤ìëë¤. (''all'', ''users'' ëë ''groups'')
luckperms.usage.editor.argument.filter=ì¬ì©ì í­ëª©ì íí°ë§í  ì ìë ê¶í
luckperms.usage.verbose.description=íë¬ê·¸ì¸ ìì¸ ê¶í ê²ì¬ ëª¨ëí°ë§ ìì¤íì ì ì´í©ëë¤
luckperms.usage.verbose.argument.action=ë¡ê¹ì íì±í/ë¹íì±í í ì§ ëë ë¡ê¹ë ì¶ë ¥ì ìë¡ëí  ì§ì ëí ì¬ë¶
luckperms.usage.verbose.argument.filter=í­ëª©ì ì¼ì¹ìí¬ íí°
luckperms.usage.verbose.argument.commandas=ì¤íí  íë ì´ì´ ë° ëªë ¹ì´
luckperms.usage.tree.description=LuckPermsì ìë ¤ì§ ëª¨ë  ê¶íì í¸ë¦¬(ìì ëª©ë¡ ê³ì¸µ êµ¬ì¡°)ë¥¼ ìì±í©ëë¤.
luckperms.usage.tree.argument.scope=í¸ë¦¬ì ìµìì ê¶í. "."ì(ë¥¼) ì§ì íì¬ ëª¨ë  ê¶íì í¬í¨í¨
luckperms.usage.tree.argument.player=íì¸í  ì¨ë¼ì¸ íë ì´ì´ì ì´ë¦
luckperms.usage.search.description=í¹ì  ê¶íì´ ìë ëª¨ë  ì¬ì©ì ë° ê·¸ë£¹ì ê²ìí©ëë¤
luckperms.usage.search.argument.permission=ê²ìí  ê¶í
luckperms.usage.search.argument.page=íì¸í  íì´ì§
luckperms.usage.network-sync.description=ë³ê²½ ì¬í­ì ì ì¥ìí ëê¸°ííê³  ë¤í¸ìí¬ì ë¤ë¥¸ ëª¨ë  ìë²ê° ëì¼í ììì ìííëë¡ ìì²­í©ëë¤
luckperms.usage.import.description=(ì´ì ì ìì±ë) ë´ë³´ë¸ íì¼ìì ë°ì´í°ë¥¼ ê°ì ¸ìµëë¤
luckperms.usage.import.argument.file=ë¶ë¬ì¬ íì¼
luckperms.usage.import.argument.replace=ë³í©íë ëì  ê¸°ì¡´ ë°ì´í° êµì²´
luckperms.usage.import.argument.upload=ì´ì  ë´ë³´ë´ê¸°ìì ë°ì´í° ìë¡ë
luckperms.usage.export.description=ëª¨ë  ê¶í ë°ì´í°ë¥¼ ''export'' íì¼ì ë´ë³´ëëë¤. ì¶í ë¤ì ë¶ë¬ì¬ ì ììµëë¤.
luckperms.usage.export.argument.file=ë´ë³´ë¼ íì¼
luckperms.usage.export.argument.without-users=ë´ë³´ë´ê¸°ìì ì¬ì©ì ì ì¸
luckperms.usage.export.argument.without-groups=ë´ë³´ë´ê¸°ìì ê·¸ë£¹ ì ì¸
luckperms.usage.export.argument.upload=ëª¨ë  ê¶í ë°ì´í°ë¥¼ ì¹ ìëí°ì ìë¡ëí©ëë¤. ì¶í ë¤ì ë¶ë¬ì¬ ì ììµëë¤.
luckperms.usage.reload-config.description=ì¼ë¶ ì¤ì ì ë¤ì ë¡ëí©ëë¤
luckperms.usage.bulk-update.description=ëª¨ë  ëìí°ì ëí´ ëë ë³ê²½ ì¿¼ë¦¬ ì¤í
luckperms.usage.bulk-update.argument.data-type=ë³ê²½ëë ë°ì´í°ì ì í. (''all'', ''users'' ëë ''groups'')
luckperms.usage.bulk-update.argument.action=ë°ì´í°ì ëí´ ìíí  ìì. (''update'' ëë ''delete'')
luckperms.usage.bulk-update.argument.action-field=ëì ì í. ''update''ìë§ íìí©ëë¤. (''permission'', ''server'' ëë ''world'')
luckperms.usage.bulk-update.argument.action-value=ëì²´í  ê°. ''update''ìë§ íìí©ëë¤
luckperms.usage.bulk-update.argument.constraint=ìë°ì´í¸ì íìí ì ì½
luckperms.usage.translations.description=ë²ì­ ê´ë¦¬
luckperms.usage.translations.argument.install=ë²ì­ì ì¤ì¹íë íì ëªë ¹
luckperms.usage.apply-edits.description=ì¹ í¸ì§ê¸°ìì ë³ê²½í ê¶íì ì ì©í©ëë¤
luckperms.usage.apply-edits.argument.code=ë°ì´í°ì ê³ ì  ì½ë
luckperms.usage.apply-edits.argument.target=ë°ì´í°ë¥¼ ì ì©í  ëì
luckperms.usage.create-group.description=ìë¡ì´ ê·¸ë£¹ì ìì±í©ëë¤
luckperms.usage.create-group.argument.name=ê·¸ë£¹ ì´ë¦
luckperms.usage.create-group.argument.weight=ê·¸ë£¹ì ê°ì¤ì¹
luckperms.usage.create-group.argument.display-name=ê·¸ë£¹ íì ì´ë¦
luckperms.usage.delete-group.description=ê·¸ë£¹ì ì­ì í©ëë¤
luckperms.usage.delete-group.argument.name=ê·¸ë£¹ ì´ë¦
luckperms.usage.list-groups.description=íë«í¼ì ëª¨ë  ê·¸ë£¹ì ëì´í©ëë¤
luckperms.usage.create-track.description=ìë¡ì´ í¸ëì ë§ë­ëë¤
luckperms.usage.create-track.argument.name=í¸ë ì´ë¦
luckperms.usage.delete-track.description=í¸ëì ì ê±°í©ëë¤
luckperms.usage.delete-track.argument.name=í¸ë ì´ë¦
luckperms.usage.list-tracks.description=íë«í¼ì ëª¨ë  í¸ëì ëì´í©ëë¤
luckperms.usage.user-info.description=ì¬ì©ìì ëí ì ë³´ë¥¼ íìí©ëë¤
luckperms.usage.user-switchprimarygroup.description=ì¬ì©ìì ê¸°ë³¸ ê·¸ë£¹ì ë³ê²½í©ëë¤
luckperms.usage.user-switchprimarygroup.argument.group=ë³ê²½í  ê·¸ë£¹
luckperms.usage.user-promote.description=ì¬ì©ìë¥¼ í¸ëìì ì¹ê¸ìíµëë¤
luckperms.usage.user-promote.argument.track=ì¬ì©ìë¥¼ ì¹ê¸ìí¬ í¸ë
luckperms.usage.user-promote.argument.context=ì¹ê¸ì´ ì ì©ë  ì»¨íì¤í¸
luckperms.usage.user-promote.argument.dont-add-to-first=ì¬ì©ìê° ì´ë¯¸ í¸ëì ìë ê²½ì°ìë§ ì¹ê¸
luckperms.usage.user-demote.description=ì¬ì©ìë¥¼ í¸ëìì ê°ë±ìíµëë¤
luckperms.usage.user-demote.argument.track=ì¬ì©ìë¥¼ ê°ë±ìí¬ í¸ë
luckperms.usage.user-demote.argument.context=ê°ë±ì´ ì ì©ë  ì»¨íì¤í¸
luckperms.usage.user-demote.argument.dont-remove-from-first=ì¬ì©ìê° ì²« ë²ì§¸ ê·¸ë£¹ìì ì ê±°ëì§ ìëë¡ íê¸°
luckperms.usage.user-clone.description=ì¬ì©ìë¥¼ ë³µì í©ëë¤
luckperms.usage.user-clone.argument.user=ë³µì  ëì ì¬ì©ì ì´ë¦ í¹ì UUID
luckperms.usage.group-info.description=ê·¸ë£¹ì ëí ì ë³´ë¥¼ ì ê³µí©ëë¤
luckperms.usage.group-listmembers.description=ì´ ê·¸ë£¹ìì ììë ì¬ì©ì ë° ê·¸ë£¹ íì
luckperms.usage.group-listmembers.argument.page=íì¸í  íì´ì§
luckperms.usage.group-setweight.description=ê·¸ë£¹ì ê°ì¤ì¹ë¥¼ ì¤ì í©ëë¤
luckperms.usage.group-setweight.argument.weight=ì¤ì í  ê°ì¤ì¹
luckperms.usage.group-set-display-name.description=ê·¸ë£¹ì íì ì´ë¦ì ì¤ì í©ëë¤
luckperms.usage.group-set-display-name.argument.name=ì¤ì í  ì´ë¦
luckperms.usage.group-set-display-name.argument.context=íì ì´ë¦ì´ ì ì©ë  ì»¨íì¤í¸
luckperms.usage.group-rename.description=ê·¸ë£¹ì ì´ë¦ì ë³ê²½í©ëë¤
luckperms.usage.group-rename.argument.name=ìë¡ì´ ì´ë¦
luckperms.usage.group-clone.description=ê·¸ë£¹ì ë³µì í©ëë¤
luckperms.usage.group-clone.argument.name=ë³µì  ëì ê·¸ë£¹ ì´ë¦
luckperms.usage.holder-editor.description=ì¹ ê¶í ìëí°ë¥¼ ì½ëë¤
luckperms.usage.holder-showtracks.description=ê°ì²´ê° ìë í¸ëì ëì´í©ëë¤
luckperms.usage.holder-clear.description=ëª¨ë  ê¶í, ìì, ë©í ë°ì´í°ë¥¼ ì ê±°í©ëë¤
luckperms.usage.holder-clear.argument.context=íí°ë§í  ì»¨íì¤í¸
luckperms.usage.permission.description=ê¶í í¸ì§
luckperms.usage.parent.description=ììì í¸ì§í©ëë¤
luckperms.usage.meta.description=ë©í ë°ì´í° ê°ì í¸ì§í©ëë¤
luckperms.usage.permission-info.description=ê°ì²´ì ìë ê¶í ë¸ëë¥¼ ëì´í©ëë¤
luckperms.usage.permission-info.argument.page=íì¸í  íì´ì§
luckperms.usage.permission-info.argument.sort-mode=í­ëª©ì ì ë ¬í  ë°©ë²
luckperms.usage.permission-set.description=ê°ì²´ì ê¶íì ì¤ì í©ëë¤
luckperms.usage.permission-set.argument.node=ì¤ì í  ê¶í ë¸ë
luckperms.usage.permission-set.argument.value=ì¤ì í  ê°
luckperms.usage.permission-set.argument.context=ê¶íì ì¶ê°í  ì»¨íì¤í¸
luckperms.usage.permission-unset.description=ê°ì²´ì ê¶íì ì¤ì  í´ì í©ëë¤
luckperms.usage.permission-unset.argument.node=ì´ê¸°íí  ê¶í ë¸ë
luckperms.usage.permission-unset.argument.context=ê¶íì ì ê±°í  ì»¨íì¤í¸
luckperms.usage.permission-settemp.description=ê°ì²´ì ëí ê¶íì ì¼ìì ì¼ë¡ ì¤ì í©ëë¤
luckperms.usage.permission-settemp.argument.node=ì¤ì í  ê¶í ë¸ë
luckperms.usage.permission-settemp.argument.value=ì¤ì í  ê°
luckperms.usage.permission-settemp.argument.duration=ê¶í ë¸ëê° ë§ë£ëê¸°ê¹ì§ì ìê°
luckperms.usage.permission-settemp.argument.temporary-modifier=ì¼ìì ì¸ ê¶íì ì ì©íë ë°©ë²
luckperms.usage.permission-settemp.argument.context=ê¶íì ì¶ê°í  ì»¨íì¤í¸
luckperms.usage.permission-unsettemp.description=ê°ì²´ì ì¼ìì ì¸ ê¶íì ì¤ì  í´ì í©ëë¤
luckperms.usage.permission-unsettemp.argument.node=ì¤ì  í´ì í  ê¶í ë¸ë
luckperms.usage.permission-unsettemp.argument.duration=ì°¨ê°í  ìê°
luckperms.usage.permission-unsettemp.argument.context=ê¶íì ì ê±°í  ì»¨íì¤í¸
luckperms.usage.permission-check.description=ê°ì²´ê° í¹ì  ê¶í ë¸ëë¥¼ ê°ì§ê³  ìëì§ íì¸í©ëë¤
luckperms.usage.permission-check.argument.node=íì¸í  ê¶í
luckperms.usage.permission-clear.description=ëª¨ë  ê¶í ì ê±°
luckperms.usage.permission-clear.argument.context=íí°ë§í  ì»¨íì¤í¸
luckperms.usage.parent-info.description=ì´ ê°ì²´ê° ììíë ê·¸ë£¹ì ëì´í©ëë¤
luckperms.usage.parent-info.argument.page=íì¸í  íì´ì§
luckperms.usage.parent-info.argument.sort-mode=í­ëª©ì ì ë ¬í  ë°©ë²
luckperms.usage.parent-set.description=ê°ì²´ê° ì´ë¯¸ ììí ëª¨ë  ê·¸ë£¹ì ì ê±°íê³  ì£¼ì´ì§ ê·¸ë£¹ì ì¶ê°í©ëë¤
luckperms.usage.parent-set.argument.group=ì¤ì í  ê·¸ë£¹
luckperms.usage.parent-set.argument.context=ê·¸ë£¹ì´ ì¤ì ë  ì»¨íì¤í¸
luckperms.usage.parent-add.description=ê°ì²´ê° ê¶íì ììí  ë¤ë¥¸ ê·¸ë£¹ì ì¤ì í©ëë¤
luckperms.usage.parent-add.argument.group=ììë°ì ê·¸ë£¹
luckperms.usage.parent-add.argument.context=ììë°ì ê·¸ë£¹ì´ ì¤ì ë  ì»¨íì¤í¸
luckperms.usage.parent-remove.description=ì´ì ì ì¤ì í ìì ê·ì¹ì ì ê±°í©ëë¤
luckperms.usage.parent-remove.argument.group=ì ê±°í  ê·¸ë£¹
luckperms.usage.parent-remove.argument.context=ê·¸ë£¹ì ì ê±°ë  ì»¨íì¤í¸
luckperms.usage.parent-set-track.description=ì£¼ì´ì§ í¸ëìì ì´ë¯¸ ê°ì²´ê° ììë°ì ë¤ë¥¸ ëª¨ë  ê·¸ë£¹ì ì ê±°íê³  ì£¼ì´ì§ ê·¸ë£¹ì ì¶ê°í©ëë¤
luckperms.usage.parent-set-track.argument.track=ì¤ì í  í¸ë
luckperms.usage.parent-set-track.argument.group=ì¤ì í  ê·¸ë£¹. ëë ì£¼ì´ì§ í¸ëìì ê·¸ë£¹ì ìì¹ì ê´ë ¨ë ì«ì
luckperms.usage.parent-set-track.argument.context=ê·¸ë£¹ì´ ì¤ì ë  ì»¨íì¤í¸
luckperms.usage.parent-add-temp.description=ê°ì²´ê° ì¼ìì ì¼ë¡ ê¶íì ììíëë¡ ë¤ë¥¸ ê·¸ë£¹ì ì¤ì í©ëë¤
luckperms.usage.parent-add-temp.argument.group=ììë°ì ê·¸ë£¹
luckperms.usage.parent-add-temp.argument.duration=ê·¸ë£¹ ë©¤ë²ì­ ê¸°ê°
luckperms.usage.parent-add-temp.argument.temporary-modifier=ì¼ìì ì¸ ê¶íì ì ì©íë ë°©ë²
luckperms.usage.parent-add-temp.argument.context=ììë°ì ê·¸ë£¹ì´ ì¤ì ë  ì»¨íì¤í¸
luckperms.usage.parent-remove-temp.description=ì´ì ì ì¤ì í ì¼ìì ì¸ ìì ê·ì¹ì ì ê±°í©ëë¤
luckperms.usage.parent-remove-temp.argument.group=ì ê±°í  ê·¸ë£¹
luckperms.usage.parent-remove-temp.argument.duration=ì°¨ê°í  ìê°
luckperms.usage.parent-remove-temp.argument.context=ê·¸ë£¹ì ì ê±°ë  ì»¨íì¤í¸
luckperms.usage.parent-clear.description=ëª¨ë  ìì ê·¸ë£¹ì ì§ìëë¤
luckperms.usage.parent-clear.argument.context=íí°ë§í  ì»¨íì¤í¸
luckperms.usage.parent-clear-track.description=ì£¼ì´ì§ í¸ëìì ëª¨ë  ìì í­ëª©ì ì§ìëë¤
luckperms.usage.parent-clear-track.argument.track=ì§ì¸ í¸ë
luckperms.usage.parent-clear-track.argument.context=íí°ë§í  ì»¨íì¤í¸
luckperms.usage.meta-info.description=ëª¨ë  ëí ë©í ë°ì´í°ë¥¼ íìí©ëë¤
luckperms.usage.meta-set.description=ë©í ë°ì´í° ê°ì ì¤ì í©ëë¤
luckperms.usage.meta-set.argument.key=ì¤ì í  í¤
luckperms.usage.meta-set.argument.value=ì¤ì í  ê°
luckperms.usage.meta-set.argument.context=ë©í ë°ì´í° ìì ì¶ê°í  ì»¨íì¤í¸
luckperms.usage.meta-unset.description=ë©í ë°ì´í° ê°ì ì¤ì  í´ì í©ëë¤
luckperms.usage.meta-unset.argument.key=ì¤ì  í´ì í  í¤
luckperms.usage.meta-unset.argument.context=ë©í ë°ì´í° ìì ì ê±°í  ì»¨íì¤í¸
luckperms.usage.meta-settemp.description=ë©í ë°ì´í° ê°ì ì¼ìì ì¼ë¡ ì¤ì í©ëë¤
luckperms.usage.meta-settemp.argument.key=ì¤ì í  í¤
luckperms.usage.meta-settemp.argument.value=ì¤ì í  ê°
luckperms.usage.meta-settemp.argument.duration=ë©í ë°ì´í° ê°ì´ ë§ë£ëê¸°ê¹ì§ì ìê°
luckperms.usage.meta-settemp.argument.context=ë©í ë°ì´í° ìì ì¶ê°í  ì»¨íì¤í¸
luckperms.usage.meta-unsettemp.description=ì¼ìì ì¸ ë©í ë°ì´í° ê°ì ì¤ì  í´ì í©ëë¤
luckperms.usage.meta-unsettemp.argument.key=ì¤ì  í´ì í  í¤
luckperms.usage.meta-unsettemp.argument.context=ë©í ë°ì´í° ìì ì ê±°í  ì»¨íì¤í¸
luckperms.usage.meta-addprefix.description=ì ëì¬ ì¶ê°
luckperms.usage.meta-addprefix.argument.priority=ì ëì´ê° ì¶ê°ë  ì°ì  ìì
luckperms.usage.meta-addprefix.argument.prefix=ì ëì´ ë¬¸ìì´
luckperms.usage.meta-addprefix.argument.context=ì ëì´ë¥¼ ì¶ê°í  ì»¨íì¤í¸
luckperms.usage.meta-addsuffix.description=ì ë¯¸ì¬ ì¶ê°
luckperms.usage.meta-addsuffix.argument.priority=ì¶ê°í  ì ë¯¸ì´ì ì°ì  ìì
luckperms.usage.meta-addsuffix.argument.suffix=ì ë¯¸ì´ ë¬¸ìì´
luckperms.usage.meta-addsuffix.argument.context=ì ë¯¸ì´ë¥¼ ì¶ê°í  ì»¨íì¤í¸
luckperms.usage.meta-setprefix.description=ì ëì¬ ì¤ì 
luckperms.usage.meta-setprefix.argument.priority=ì ëì´ê° ì¤ì ë  ì°ì  ìì
luckperms.usage.meta-setprefix.argument.prefix=ì ëì´ ë¬¸ìì´
luckperms.usage.meta-setprefix.argument.context=ì ëì´ê° ì¤ì ë  ì»¨íì¤í¸
luckperms.usage.meta-setsuffix.description=ì ë¯¸ì¬ ì¤ì 
luckperms.usage.meta-setsuffix.argument.priority=ì ë¯¸ì´ë¥¼ ì¤ì í  ì°ì  ìì
luckperms.usage.meta-setsuffix.argument.suffix=ì ë¯¸ì´ ë¬¸ìì´
luckperms.usage.meta-setsuffix.argument.context=ì ë¯¸ì´ê° ì¤ì ë  ì»¨íì¤í¸
luckperms.usage.meta-removeprefix.description=ì ëì¬ ì ê±°
luckperms.usage.meta-removeprefix.argument.priority=ì ëì´ê° ì ê±°ë  ì°ì  ìì
luckperms.usage.meta-removeprefix.argument.prefix=ì ëì´ ë¬¸ìì´
luckperms.usage.meta-removeprefix.argument.context=ì ëì´ê° ì ê±°ë  ì»¨íì¤í¸
luckperms.usage.meta-removesuffix.description=ì ë¯¸ì¬ ì ê±°
luckperms.usage.meta-removesuffix.argument.priority=ì ë¯¸ì´ê° ì ê±°ë  ì°ì  ìì
luckperms.usage.meta-removesuffix.argument.suffix=ì ë¯¸ì´ ë¬¸ìì´
luckperms.usage.meta-removesuffix.argument.context=ì ë¯¸ì´ê° ì ê±°ë  ì»¨íì¤í¸
luckperms.usage.meta-addtemp-prefix.description=ì¼ìì ì¼ë¡ ì ì©ëë ì ëì´ë¥¼ ì¶ê°í©ëë¤
luckperms.usage.meta-addtemp-prefix.argument.priority=ì ëì´ê° ì¶ê°ë  ì°ì  ìì
luckperms.usage.meta-addtemp-prefix.argument.prefix=ì ëì´ ë¬¸ìì´
luckperms.usage.meta-addtemp-prefix.argument.duration=ì ëì´ê° ë§ë£ëê¸°ê¹ì§ì ìê°
luckperms.usage.meta-addtemp-prefix.argument.context=ì ëì´ë¥¼ ì¶ê°í  ì»¨íì¤í¸
luckperms.usage.meta-addtemp-suffix.description=ì¼ìì ì¼ë¡ ì ì©ëë ì ë¯¸ì´ë¥¼ ì¶ê°í©ëë¤
luckperms.usage.meta-addtemp-suffix.argument.priority=ì¶ê°í  ì ë¯¸ì´ì ì°ì  ìì
luckperms.usage.meta-addtemp-suffix.argument.suffix=ì ë¯¸ì´ ë¬¸ìì´
luckperms.usage.meta-addtemp-suffix.argument.duration=ì ë¯¸ì´ê° ë§ë£ëê¸°ê¹ì§ì ìê°
luckperms.usage.meta-addtemp-suffix.argument.context=ì ë¯¸ì´ë¥¼ ì¶ê°í  ì»¨íì¤í¸
luckperms.usage.meta-settemp-prefix.description=ì¼ìì ì¼ë¡ ì ì©ëë ì ëì´ë¥¼ ì¤ì í©ëë¤
luckperms.usage.meta-settemp-prefix.argument.priority=ì ëì´ê° ì¤ì ë  ì°ì  ìì
luckperms.usage.meta-settemp-prefix.argument.prefix=ì ëì´ ë¬¸ìì´
luckperms.usage.meta-settemp-prefix.argument.duration=ì ëì´ê° ë§ë£ëê¸°ê¹ì§ì ìê°
luckperms.usage.meta-settemp-prefix.argument.context=ì ëì´ê° ì¤ì ë  ì»¨íì¤í¸
luckperms.usage.meta-settemp-suffix.description=ì¼ìì ì¼ë¡ ì ì©ëë ì ë¯¸ì´ë¥¼ ì¤ì í©ëë¤
luckperms.usage.meta-settemp-suffix.argument.priority=ì ë¯¸ì´ë¥¼ ì¤ì í  ì°ì  ìì
luckperms.usage.meta-settemp-suffix.argument.suffix=ì ë¯¸ì´ ë¬¸ìì´
luckperms.usage.meta-settemp-suffix.argument.duration=ì ë¯¸ì´ê° ë§ë£ëê¸°ê¹ì§ì ìê°
luckperms.usage.meta-settemp-suffix.argument.context=ì ë¯¸ì´ê° ì¤ì ë  ì»¨íì¤í¸
luckperms.usage.meta-removetemp-prefix.description=ì¼ìì ì¼ë¡ ì ì©ëë ì ëì´ë¥¼ ì ê±°í©ëë¤
luckperms.usage.meta-removetemp-prefix.argument.priority=ì ëì´ê° ì ê±°ë  ì°ì  ìì
luckperms.usage.meta-removetemp-prefix.argument.prefix=ì ëì´ ë¬¸ìì´
luckperms.usage.meta-removetemp-prefix.argument.context=ì ëì´ê° ì ê±°ë  ì»¨íì¤í¸
luckperms.usage.meta-removetemp-suffix.description=ì¼ìì ì¼ë¡ ì ì©ëë ì ë¯¸ì´ë¥¼ ì ê±°í©ëë¤
luckperms.usage.meta-removetemp-suffix.argument.priority=ì ë¯¸ì´ê° ì ê±°ë  ì°ì  ìì
luckperms.usage.meta-removetemp-suffix.argument.suffix=ì ë¯¸ì´ ë¬¸ìì´
luckperms.usage.meta-removetemp-suffix.argument.context=ì ë¯¸ì´ê° ì ê±°ë  ì»¨íì¤í¸
luckperms.usage.meta-clear.description=ëª¨ë  ë©í ë°ì´í°ë¥¼ ì§ìëë¤
luckperms.usage.meta-clear.argument.type=ì ê±°í  ë©í ë°ì´í° ì í
luckperms.usage.meta-clear.argument.context=íí°ë§í  ì»¨íì¤í¸
luckperms.usage.track-info.description=í¸ëì ëí ì ë³´ë¥¼ ì ê³µí©ëë¤
luckperms.usage.track-editor.description=ì¹ ê¶í ìëí°ë¥¼ ì½ëë¤
luckperms.usage.track-append.description=í¸ë ë§ì§ë§ì ê·¸ë£¹ì ì¶ê°í©ëë¤
luckperms.usage.track-append.argument.group=ì¶ê°í  ê·¸ë£¹
luckperms.usage.track-insert.description=í¸ëì ì£¼ì´ì§ ìì¹ì ê·¸ë£¹ì ì½ìí©ëë¤
luckperms.usage.track-insert.argument.group=ì½ìí  ê·¸ë£¹
luckperms.usage.track-insert.argument.position=ê·¸ë£¹ì ì½ìí  ìì¹ (í¸ëì ì²« ë²ì§¸ ìì¹ë 1ì)
luckperms.usage.track-remove.description=í¸ëìì ê·¸ë£¹ì ì ê±°í©ëë¤
luckperms.usage.track-remove.argument.group=ì ê±°í  ê·¸ë£¹
luckperms.usage.track-clear.description=í¸ëìì ëª¨ë  ê·¸ë£¹ì ì§ìëë¤
luckperms.usage.track-rename.description=í¸ëì ì´ë¦ì ë³ê²½í©ëë¤
luckperms.usage.track-rename.argument.name=ìë¡ì´ ì´ë¦
luckperms.usage.track-clone.description=í¸ëì ë³µì í©ëë¤
luckperms.usage.track-clone.argument.name=ë³µì  ëì í¸ë ì´ë¦
luckperms.usage.log-recent.description=ìµê·¼ ììì ë´ëë¤
luckperms.usage.log-recent.argument.user=íí°ë§í  ì¬ì©ì ì´ë¦ í¹ì UUID
luckperms.usage.log-recent.argument.page=íì¸í  íì´ì§ ë²í¸
luckperms.usage.log-search.description=ë¡ê·¸ìì í­ëª©ì ê²ìí©ëë¤
luckperms.usage.log-search.argument.query=ê²ìì´
luckperms.usage.log-search.argument.page=íì¸í  íì´ì§ ë²í¸
luckperms.usage.log-notify.description=ë¡ê·¸ ìë¦¼ ìíë¥¼ ì íí©ëë¤
luckperms.usage.log-notify.argument.toggle=on í¹ì off
luckperms.usage.log-user-history.description=ì¬ì©ìì ê¸°ë¡ì íì¸í©ëë¤
luckperms.usage.log-user-history.argument.user=ì¬ì©ì ì´ë¦ í¹ì UUID
luckperms.usage.log-user-history.argument.page=íì¸í  íì´ì§ ë²í¸
luckperms.usage.log-group-history.description=ê·¸ë£¹ì ê¸°ë¡ì íì¸í©ëë¤
luckperms.usage.log-group-history.argument.group=ê·¸ë£¹ ì´ë¦
luckperms.usage.log-group-history.argument.page=íì¸í  íì´ì§ ë²í¸
luckperms.usage.log-track-history.description=í¸ëì ê¸°ë¡ì íì¸í©ëë¤
luckperms.usage.log-track-history.argument.track=í¸ë ì´ë¦
luckperms.usage.log-track-history.argument.page=íì¸í  íì´ì§ ë²í¸
luckperms.usage.sponge.description=ì¶ê°ì ì¸ Sponge ë°ì´í°ë¥¼ í¸ì§í©ëë¤
luckperms.usage.sponge.argument.collection=ì¿¼ë¦¬í  ì»¬ë ì
luckperms.usage.sponge.argument.subject=ìì í  ì£¼ì 
luckperms.usage.sponge-permission-info.description=ì£¼ì ì ê¶íì ëí ì ë³´ë¥¼ íìí©ëë¤
luckperms.usage.sponge-permission-info.argument.contexts=íí°ë§í  ì»¨íì¤í¸
luckperms.usage.sponge-permission-set.description=ì£¼ì ì ê¶íì ì¤ì í©ëë¤
luckperms.usage.sponge-permission-set.argument.node=ì¤ì í  ê¶í ë¸ë
luckperms.usage.sponge-permission-set.argument.tristate=ê¶íì ì¤ì í  ê°
luckperms.usage.sponge-permission-set.argument.contexts=ê¶íì ì¤ì í  ì»¨íì¤í¸
luckperms.usage.sponge-permission-clear.description=ì£¼ì ì ëª¨ë  ê¶íì ì§ìëë¤
luckperms.usage.sponge-permission-clear.argument.contexts=ê¶íì ì§ì¸ ì»¨íì¤í¸
luckperms.usage.sponge-parent-info.description=ì£¼ì ì ìì í­ëª©ì ëí ì ë³´ë¥¼ íìí©ëë¤
luckperms.usage.sponge-parent-info.argument.contexts=íí°ë§í  ì»¨íì¤í¸
luckperms.usage.sponge-parent-add.description=ì£¼ì ì ìì í­ëª©ì ì¶ê°í©ëë¤
luckperms.usage.sponge-parent-add.argument.collection=ìì ì£¼ì ê° ìë ì£¼ì  ì»¬ë ì
luckperms.usage.sponge-parent-add.argument.subject=ìì ì£¼ì ì ì´ë¦
luckperms.usage.sponge-parent-add.argument.contexts=ìì í­ëª©ì´ ì¶ê°ë  ì»¨íì¤í¸
luckperms.usage.sponge-parent-remove.description=ì£¼ì ìì ìì í­ëª©ì ì ê±°í©ëë¤
luckperms.usage.sponge-parent-remove.argument.collection=ìì ì£¼ì ê° ìë ì£¼ì  ì»¬ë ì
luckperms.usage.sponge-parent-remove.argument.subject=ìì ì£¼ì ì ì´ë¦
luckperms.usage.sponge-parent-remove.argument.contexts=ìì í­ëª©ì´ ì ê±°ë  ì»¨íì¤í¸
luckperms.usage.sponge-parent-clear.description=ì£¼ì ì ëª¨ë  ìì í­ëª©ì ì§ìëë¤
luckperms.usage.sponge-parent-clear.argument.contexts=ìì í­ëª©ì ì§ì¸ ì»¨íì¤í¸
luckperms.usage.sponge-option-info.description=ì£¼ì ì ìµìì ëí ì ë³´ë¥¼ íìí©ëë¤
luckperms.usage.sponge-option-info.argument.contexts=íí°ë§í  ì»¨íì¤í¸
luckperms.usage.sponge-option-set.description=ì£¼ì ì ìµìì ì¤ì í©ëë¤
luckperms.usage.sponge-option-set.argument.key=ì¤ì í  í¤
luckperms.usage.sponge-option-set.argument.value=ì¤ì í  ê°
luckperms.usage.sponge-option-set.argument.contexts=ì¤ì ë  ì»¨íì¤í¸
luckperms.usage.sponge-option-unset.description=ì£¼ì ì ìµìì ì¤ì  í´ì í©ëë¤
luckperms.usage.sponge-option-unset.argument.key=ì¤ì  í´ì í  í¤
luckperms.usage.sponge-option-unset.argument.contexts=í¤ê° ì¤ì  í´ì ë  ì»¨íì¤í¸
luckperms.usage.sponge-option-clear.description=ì£¼ì ì ëª¨ë  ìµìì ì§ìëë¤
luckperms.usage.sponge-option-clear.argument.contexts=ìµìì ì§ì¸ ì»¨íì¤í¸
