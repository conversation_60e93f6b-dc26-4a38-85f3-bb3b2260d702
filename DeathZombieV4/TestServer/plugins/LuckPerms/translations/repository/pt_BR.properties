luckperms.logs.actionlog-prefix=REGISTRO
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=EXPORTAR
luckperms.commandsystem.available-commands=Use {0} para ver os comandos disponÃ­veis
luckperms.commandsystem.command-not-recognised=Comando nÃ£o reconhecido
luckperms.commandsystem.no-permission=VocÃª nÃ£o tem permissÃ£o para usar este comando\!
luckperms.commandsystem.no-permission-subcommands=VocÃª nÃ£o tem permissÃ£o para usar quaisquer subcomandos
luckperms.commandsystem.already-executing-command=Outro comando estÃ¡ sendo executado, aguarde terminar...
luckperms.commandsystem.usage.sub-commands-header=Sub Comandos
luckperms.commandsystem.usage.usage-header=UtilizaÃ§Ã£o do Comando
luckperms.commandsystem.usage.arguments-header=Argumentos
luckperms.first-time.no-permissions-setup=Parece que nenhuma permissÃ£o foi configurada\!
luckperms.first-time.use-console-to-give-access=Antes de vocÃª poder usar qualquer um dos comandos do LuckPerms no jogo, vocÃª precisa usar o console para dar a si mesmo acesso
luckperms.first-time.console-command-prompt=Abra seu console e execute
luckperms.first-time.next-step=Depois de ter feito isso, vocÃª pode comeÃ§ar a definir suas atribuiÃ§Ãµes de grupos e permissÃµes
luckperms.first-time.wiki-prompt=NÃ£o sabe por onde comeÃ§ar? Confira aqui\: {0}
luckperms.login.try-again=Por favor, tente novamente mais tarde
luckperms.login.loading-database-error=Ocorreu um erro durante o carregamento dos dados de permissÃµes no banco de dados
luckperms.login.server-admin-check-console-errors=Se vocÃª Ã© um administrador do servidor, por favor verifique no console se hÃ¡ algum erro
luckperms.login.server-admin-check-console-info=Por favor, verifique o console do servidor para mais informaÃ§Ãµes
luckperms.login.data-not-loaded-at-pre=Dados de permissÃµes do usuÃ¡rio nÃ£o foram carregados durante a fase de prÃ©-login
luckperms.login.unable-to-continue=Incapaz de continuar
luckperms.login.craftbukkit-offline-mode-error=provavelmente isto aconteceu devido a um conflito entre o CraftBukkit e a configuraÃ§Ã£o do online-mode
luckperms.login.unexpected-error=Ocorreu um erro inesperado ao configurar seus dados de permissÃµes
luckperms.opsystem.disabled=O sistema OP estÃ¡ desativado neste servidor
luckperms.opsystem.sponge-warning=Note que o status de Operador de Servidor nÃ£o tem efeito nas verificaÃ§Ãµes de permissÃ£o do Sponge quando um plugin de permissÃ£o estÃ¡ instalado, vocÃª deve editar os dados do usuÃ¡rio diretamente
luckperms.duration.unit.years.plural={0} anos
luckperms.duration.unit.years.singular={0} ano
luckperms.duration.unit.years.short={0}ano
luckperms.duration.unit.months.plural={0} meses
luckperms.duration.unit.months.singular={0} mÃªs
luckperms.duration.unit.months.short={0}mÃªs
luckperms.duration.unit.weeks.plural={0} semanas
luckperms.duration.unit.weeks.singular={0} semana
luckperms.duration.unit.weeks.short={0}sem
luckperms.duration.unit.days.plural={0} dias
luckperms.duration.unit.days.singular={0} dia
luckperms.duration.unit.days.short={0}d
luckperms.duration.unit.hours.plural={0} horas
luckperms.duration.unit.hours.singular={0} hora
luckperms.duration.unit.hours.short={0}h
luckperms.duration.unit.minutes.plural={0} minutos
luckperms.duration.unit.minutes.singular={0} minuto
luckperms.duration.unit.minutes.short={0}min
luckperms.duration.unit.seconds.plural={0} segundos
luckperms.duration.unit.seconds.singular={0} segundo
luckperms.duration.unit.seconds.short={0}seg
luckperms.duration.since={0} atrÃ¡s
luckperms.command.misc.invalid-code=CÃ³digo invÃ¡lido
luckperms.command.misc.response-code-key=cÃ³digo de resposta
luckperms.command.misc.error-message-key=mensagem
luckperms.command.misc.bytebin-unable-to-communicate=NÃ£o foi possÃ­vel comunicar-se com bytebin
luckperms.command.misc.webapp-unable-to-communicate=NÃ£o foi possÃ­vel comunicar-se com o aplicativo web
luckperms.command.misc.check-console-for-errors=Verifique no console se hÃ¡ erros
luckperms.command.misc.file-must-be-in-data=Arquivo {0} deve ser um filho direto do diretÃ³rio de dados
luckperms.command.misc.wait-to-finish=Por favor, aguarde atÃ© que a sua solicitaÃ§Ã£o seja concluÃ­da e tente novamente
luckperms.command.misc.invalid-priority=Prioridade invÃ¡lida {0}
luckperms.command.misc.expected-number=Esperado um nÃºmero
luckperms.command.misc.date-parse-error=NÃ£o foi possÃ­vel analisar data {0}
luckperms.command.misc.date-in-past-error=VocÃª nÃ£o pode definir uma data no passado\!
luckperms.command.misc.page=pÃ¡gina {0} de {1}
luckperms.command.misc.page-entries={0} entradas
luckperms.command.misc.none=Nenhum
luckperms.command.misc.loading.error.unexpected=Um erro inesperado ocorreu
luckperms.command.misc.loading.error.user=UsuÃ¡rio nÃ£o carregado
luckperms.command.misc.loading.error.user-specific=NÃ£o foi possÃ­vel carregar usuÃ¡rio alvo {0}
luckperms.command.misc.loading.error.user-not-found=NÃ£o foi possÃ­vel encontrar um usuÃ¡rio para {0}
luckperms.command.misc.loading.error.user-save-error=Houve um erro ao salvar os dados de usuÃ¡rio para {0}
luckperms.command.misc.loading.error.user-not-online=UsuÃ¡rio {0} nÃ£o estÃ¡ online
luckperms.command.misc.loading.error.user-invalid=''{0}'' nÃ£o Ã© um nome/uuid de usuÃ¡rio vÃ¡lido
luckperms.command.misc.loading.error.user-not-uuid=O usuÃ¡rio do destino {0} nÃ£o Ã© um uuid vÃ¡lido
luckperms.command.misc.loading.error.group=Grupo nÃ£o carregado
luckperms.command.misc.loading.error.all-groups=NÃ£o foi possÃ­vel carregar todos os grupos
luckperms.command.misc.loading.error.group-not-found=NÃ£o foi possÃ­vel encontrar o grupo chamado {0}
luckperms.command.misc.loading.error.group-save-error=Ocorreu um erro ao salvar os dados do grupo para {0}
luckperms.command.misc.loading.error.group-invalid={0} nÃ£o Ã© um nome de grupo vÃ¡lido
luckperms.command.misc.loading.error.track=Track nÃ£o carregada
luckperms.command.misc.loading.error.all-tracks=ImpossÃ­vel carregar todos as tracks
luckperms.command.misc.loading.error.track-not-found=O caminho de promoÃ§Ã£o {0} nÃ£o foi encontrada
luckperms.command.misc.loading.error.track-save-error=Ocorreu um erro ao salvar os dados do caminho de promoÃ§Ã£o para {0}
luckperms.command.misc.loading.error.track-invalid={0} nÃ£o Ã© um nome de track vÃ¡lido
luckperms.command.editor.no-match=Incapaz de abrir o editor, nenhum objeto corresponde ao tipo desejado
luckperms.command.editor.start=Preparando uma nova sessÃ£o do web editor, por favor aguarde...
luckperms.command.editor.url=Clique no link abaixo para abrir o editor
luckperms.command.editor.unable-to-communicate=ImpossÃ­vel se comunicar com o editor
luckperms.command.editor.apply-edits.success=Dados do web editor aplicados a {0} {1} com sucesso
luckperms.command.editor.apply-edits.success-summary={0} {1} e {2} {3}
luckperms.command.editor.apply-edits.success.additions=adiÃ§Ãµes
luckperms.command.editor.apply-edits.success.additions-singular=adiÃ§Ã£o
luckperms.command.editor.apply-edits.success.deletions=eliminaÃ§Ãµes
luckperms.command.editor.apply-edits.success.deletions-singular=eliminaÃ§Ã£o
luckperms.command.editor.apply-edits.no-changes=Nenhuma alteraÃ§Ã£o foi aplicada pelo web editor, os dados enviados nÃ£o contÃªm nenhuma ediÃ§Ã£o
luckperms.command.editor.apply-edits.unknown-type=ImpossÃ­vel aplicar a ediÃ§Ã£o ao tipo de objeto especificado
luckperms.command.editor.apply-edits.unable-to-read=ImpossÃ­vel ler os dados usando o cÃ³digo fornecido
luckperms.command.search.searching.permission=Procurando usuÃ¡rios e grupos com {0}
luckperms.command.search.searching.inherit=Procurando por usuÃ¡rios e grupos que herdam de {0}
luckperms.command.search.result=Encontrados {0} entradas de {1} usuÃ¡rios e {2} grupos
luckperms.command.search.result.default-notice=Nota\: ao procurar membros do grupo padrÃ£o, jogadores offline sem outras permissÃµes nÃ£o serÃ£o exibidos\!
luckperms.command.search.showing-users=Exibindo usuÃ¡rio entradas
luckperms.command.search.showing-groups=Exibindo entradas de grupo
luckperms.command.tree.start=Gerando lista de permissÃµes, por favor aguarde...
luckperms.command.tree.empty=ImpossÃ­vel gerar a lista, nenhum resultado foi encontrado
luckperms.command.tree.url=URL da lista de permissÃµes
luckperms.command.verbose.invalid-filter={0} nÃ£o Ã© um filtro verboso vÃ¡lido
luckperms.command.verbose.enabled=Registro detalhado {0} para verificaÃ§Ãµes correspondentes a {1}
luckperms.command.verbose.command-exec=ForÃ§ando {0} a executar o comando {1} e reportando todas as verificaÃ§Ãµes feitas...
luckperms.command.verbose.off=Registro verboso {0}
luckperms.command.verbose.command-exec-complete=ExecuÃ§Ã£o do comando concluÃ­da
luckperms.command.verbose.command.no-checks=A execuÃ§Ã£o do comando foi concluÃ­da, mas nenhuma verificaÃ§Ã£o de permissÃ£o foi feita
luckperms.command.verbose.command.possibly-async=Isso pode ocorrer porque o plug-in executa comandos em segundo plano (async)
luckperms.command.verbose.command.try-again-manually=VocÃª ainda pode usar verbose manualmente para detectar verificaÃ§Ãµes feitas como esta
luckperms.command.verbose.enabled-recording=GravaÃ§Ã£o detalhada de {0} para verificaÃ§Ãµes correspondentes a {1}
luckperms.command.verbose.uploading=Registro verboso {0}, envio de resultados...
luckperms.command.verbose.url=URL de resultados verboso
luckperms.command.verbose.enabled-term=ativado
luckperms.command.verbose.disabled-term=desativado
luckperms.command.verbose.query-any=QUALQUER
luckperms.command.info.running-plugin=Executando
luckperms.command.info.platform-key=Plataforma
luckperms.command.info.server-brand-key=Marca do Servidor
luckperms.command.info.server-version-key=VersÃ£o do Servidor
luckperms.command.info.storage-key=Armazenamento
luckperms.command.info.storage-type-key=Tipo
luckperms.command.info.storage.meta.split-types-key=Tipos
luckperms.command.info.storage.meta.ping-key=LatÃªncia
luckperms.command.info.storage.meta.connected-key=Conectado
luckperms.command.info.storage.meta.file-size-key=Tamanho do Arquivo
luckperms.command.info.extensions-key=ExtensÃµes
luckperms.command.info.messaging-key=Sistema de Mensagens
luckperms.command.info.instance-key=InstÃ¢ncia
luckperms.command.info.static-contexts-key=Contextos estÃ¡ticos
luckperms.command.info.online-players-key=Jogadores Online
luckperms.command.info.online-players-unique={0} Ãºnico
luckperms.command.info.uptime-key=Tempo de atividade
luckperms.command.info.local-data-key=Dados Locais
luckperms.command.info.local-data={0} usuÃ¡rios, {1} grupos, {2} tracks
luckperms.command.generic.create.success={0} foi criado com sucesso
luckperms.command.generic.create.error=Houve um erro ao criar {0}
luckperms.command.generic.create.error-already-exists={0} jÃ¡ existe\!
luckperms.command.generic.delete.success={0} foi excluÃ­do com sucesso
luckperms.command.generic.delete.error=Houve um erro ao excluir {0}
luckperms.command.generic.delete.error-doesnt-exist={0} nÃ£o existe\!
luckperms.command.generic.rename.success={0} foi renomeado com sucesso para {1}
luckperms.command.generic.clone.success={0} foi clonado com sucesso em {1}
luckperms.command.generic.info.parent.title=Grupos de Parents
luckperms.command.generic.info.parent.temporary-title=Grupos TemporÃ¡rios de Pais
luckperms.command.generic.info.expires-in=expira em
luckperms.command.generic.info.inherited-from=herdado de
luckperms.command.generic.info.inherited-from-self=si mesmo
luckperms.command.generic.show-tracks.title=Tracks de {0}
luckperms.command.generic.show-tracks.empty={0} nÃ£o estÃ¡ em nenhum track
luckperms.command.generic.clear.node-removed={0} nÃ³dulos foram removidos
luckperms.command.generic.clear.node-removed-singular={0} nÃ³dulo foi removido
luckperms.command.generic.clear=NÃ³dulos de {0} limpos no contexto {1}
luckperms.command.generic.permission.info.title=PermissÃµes de {0}
luckperms.command.generic.permission.info.empty={0} nÃ£o possui quaisquer permissÃµes definidas
luckperms.command.generic.permission.info.click-to-remove=Clique para remover este nÃ³dulo de {0}
luckperms.command.generic.permission.check.info.title=InformaÃ§Ãµes da permissÃ£o para {0}
luckperms.command.generic.permission.check.info.directly={0} foi {1} definido como {2} no contexto {3}
luckperms.command.generic.permission.check.info.inherited={0} herda {1} definido como {2} de {3} no contexto {4}
luckperms.command.generic.permission.check.info.not-directly={0} nÃ£o tem {1} definido
luckperms.command.generic.permission.check.info.not-inherited={0} nÃ£o herda {1}
luckperms.command.generic.permission.check.result.title=VerificaÃ§Ã£o de permissÃ£o para {0}
luckperms.command.generic.permission.check.result.result-key=Resultado
luckperms.command.generic.permission.check.result.processor-key=Processador
luckperms.command.generic.permission.check.result.cause-key=Causa
luckperms.command.generic.permission.check.result.context-key=Contexto
luckperms.command.generic.permission.set=Definir {0} a {1} para {2} no contexto {3}
luckperms.command.generic.permission.already-has={0} jÃ¡ tem {1} definidos no contexto {2}
luckperms.command.generic.permission.set-temp=Definir {0} a {1} para {2} por uma duraÃ§Ã£o de {3} no contexto {4}
luckperms.command.generic.permission.already-has-temp={0} jÃ¡ possui {1} definido temporariamente no contexto {2}
luckperms.command.generic.permission.unset=Removido {0} para {1} no contexto {2}
luckperms.command.generic.permission.doesnt-have={0} nÃ£o tem {1} definido no contexto {2}
luckperms.command.generic.permission.unset-temp=Desconfigurar a permissÃ£o temporÃ¡ria {0} para {1} no contexto {2}
luckperms.command.generic.permission.subtract=Definir {0} como {1} para {2} por uma duraÃ§Ã£o de {3} no contexto {4}, {5} a menos do que antes
luckperms.command.generic.permission.doesnt-have-temp={0} nÃ£o possui {1} definido temporariamente no contexto {2}
luckperms.command.generic.permission.clear=As permissÃµes de {0} foram apagadas no contexto {1}
luckperms.command.generic.parent.info.title=Grupos de {0}
luckperms.command.generic.parent.info.empty={0} nÃ£o tem quaisquer pais definidos
luckperms.command.generic.parent.info.click-to-remove=Clique para remover esse grupo de {0}
luckperms.command.generic.parent.add={0} agora herda as permissÃµes de {1} no contexto {2}
luckperms.command.generic.parent.add-temp={0} agora herda as permissÃµes de {1} por uma duraÃ§Ã£o de {2} no contexto {3}
luckperms.command.generic.parent.set={0} teve seus grupos-pai existentes apagados, e agora herda apenas {1} em contexto {2}
luckperms.command.generic.parent.set-track={0} teve seus grupos-pai existentes na escada {1}, e agora sÃ³ herda {2} no contexto {3}
luckperms.command.generic.parent.remove={0} nÃ£o herda mais as permissÃµes de {1} no contexto {2}
luckperms.command.generic.parent.remove-temp={0} nÃ£o herda mais temporariamente as permissÃµes de {1} no contexto {2}
luckperms.command.generic.parent.subtract={0} herdarÃ¡ permissÃµes de {1} por uma duraÃ§Ã£o de {2} no contexto {3}, {4} menos que antes
luckperms.command.generic.parent.clear=Os pais de {0} foram apagadas no contexto {1}
luckperms.command.generic.parent.clear-track=Os pais de {0} na escada {1} foram apagadas no contexto {2}
luckperms.command.generic.parent.already-inherits={0} jÃ¡ herda de {1} no contexto {2}
luckperms.command.generic.parent.doesnt-inherit={0} nÃ£o herda de {1} no contexto {2}
luckperms.command.generic.parent.already-temp-inherits={0} jÃ¡ herda temporariamente de {1} no contexto {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} nÃ£o herda temporariamente de {1} no contexto {2}
luckperms.command.generic.chat-meta.info.title-prefix=Prefixos de {0}
luckperms.command.generic.chat-meta.info.title-suffix=Sufixos de {0}
luckperms.command.generic.chat-meta.info.none-prefix={0} nÃ£o tem prefixos
luckperms.command.generic.chat-meta.info.none-suffix={0} nÃ£o tem sufixos
luckperms.command.generic.chat-meta.info.click-to-remove=Clique para remover este {0} de {1}
luckperms.command.generic.chat-meta.already-has={0} jÃ¡ tem {1} {2} definido a uma prioridade de {3} no contexto {4}
luckperms.command.generic.chat-meta.already-has-temp={0} jÃ¡ tem {1} {2} definido temporariamente a uma prioridade de {3} no contexto {4}
luckperms.command.generic.chat-meta.doesnt-have={0} nÃ£o tem {1} {2} definido a uma prioridade de {3} no contexto {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} nÃ£o tem {1} {2} definido temporariamente a uma prioridade de {3} no contexto {4}
luckperms.command.generic.chat-meta.add={0} teve {1} {2} definido como uma prioridade de {3} no contexto {4}
luckperms.command.generic.chat-meta.add-temp={0} teve {1} {2} definido a uma prioridade de {3} por uma duraÃ§Ã£o de {4} no contexto {5}
luckperms.command.generic.chat-meta.remove={0} teve {1} {2} na prioridade {3} removido no contexto {4}
luckperms.command.generic.chat-meta.remove-bulk={0} teve todos {1} na prioridade {2} removidos no contexto {3}
luckperms.command.generic.chat-meta.remove-temp={0} teve temporÃ¡rio {1} {2} na prioridade {3} removido no contexto {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} teve todos temporÃ¡rios os {1} na prioridade {2} removido no contexto {3}
luckperms.command.generic.meta.info.title=Metadados de {0}
luckperms.command.generic.meta.info.none={0} nÃ£o tem meta
luckperms.command.generic.meta.info.click-to-remove=Clique para remover este meta node de {0}
luckperms.command.generic.meta.already-has={0} jÃ¡ tem meta-chave {1} definida como {2} no contexto {3}
luckperms.command.generic.meta.already-has-temp={0} jÃ¡ tem meta-chave {1} temporariamente definida como {2} no contexto {3}
luckperms.command.generic.meta.doesnt-have={0} nÃ£o tem meta-chave {1} definida no contexto {2}
luckperms.command.generic.meta.doesnt-have-temp={0} nÃ£o tem meta-chave {1} definida temporariamente no contexto {2}
luckperms.command.generic.meta.set=Defina a meta-chave {0} como {1} para {2} no contexto {3}
luckperms.command.generic.meta.set-temp=Defina a meta-chave {0} como {1} para {2} por uma duraÃ§Ã£o de {3} no contexto {4}
luckperms.command.generic.meta.unset=Remova a meta-chave {0} para {1} no contexto {2}
luckperms.command.generic.meta.unset-temp=Remova a meta-chave temporÃ¡ria {0} para {1} no contexto {2}
luckperms.command.generic.meta.clear=O metatipo de correspondÃªncia {1} de {0} foi apagado no contexto {2}
luckperms.command.generic.contextual-data.title=Dados Contextuais
luckperms.command.generic.contextual-data.mode.key=modo
luckperms.command.generic.contextual-data.mode.server=servidor
luckperms.command.generic.contextual-data.mode.active-player=jogador ativo
luckperms.command.generic.contextual-data.contexts-key=Contextos
luckperms.command.generic.contextual-data.prefix-key=Prefixo
luckperms.command.generic.contextual-data.suffix-key=Sufixo
luckperms.command.generic.contextual-data.primary-group-key=Grupo PrimÃ¡rio
luckperms.command.generic.contextual-data.meta-key=Meta
luckperms.command.generic.contextual-data.null-result=Nenhum
luckperms.command.user.info.title=InformaÃ§Ã£o do UsuÃ¡rio
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=tipo
luckperms.command.user.info.uuid-type.mojang=Mojang
luckperms.command.user.info.uuid-type.not-mojang=offline
luckperms.command.user.info.status-key=Estado
luckperms.command.user.info.status.online=Online
luckperms.command.user.info.status.offline=Offline
luckperms.command.user.removegroup.error-primary=VocÃª nÃ£o pode remover um usuÃ¡rio do grupo primÃ¡rio
luckperms.command.user.primarygroup.not-member={0} nÃ£o era um membro de {1}, adicionando-o agora
luckperms.command.user.primarygroup.already-has={0} jÃ¡ tem {1} definido como seu grupo principal
luckperms.command.user.primarygroup.warn-option=Aviso\: O mÃ©todo de cÃ¡lculo do grupo primÃ¡rio sendo usado por este servidor ({0}) pode nÃ£o refletir esta alteraÃ§Ã£o
luckperms.command.user.primarygroup.set=O grupo primÃ¡rio {0} foi definido como {1}
luckperms.command.user.track.error-not-contain-group={0} ainda nÃ£o estÃ¡ em nenhum grupo em {1}
luckperms.command.user.track.unsure-which-track=NÃ£o sabe qual caminho usar, por favor especificar um argumento
luckperms.command.user.track.missing-group-advice=Crie o grupo ou o remova da faixa e tente novamente
luckperms.command.user.promote.added-to-first={0} nÃ£o estÃ¡ em nenhum grupo em {1}, entÃ£o foram adicionados ao primeiro grupo, {2} no contexto {3}
luckperms.command.user.promote.not-on-track={0} nÃ£o estÃ¡ em nenhum grupo em {1}, entÃ£o nÃ£o foi promovido
luckperms.command.user.promote.success=Promovendo {0} ao longo da faixa {1} de {2} para {3} no contexto {4}
luckperms.command.user.promote.end-of-track=O fim da faixa {0} foi alcanÃ§ado, incapaz de promover {1}
luckperms.command.user.promote.next-group-deleted=O prÃ³ximo grupo na faixa, {0}, nÃ£o existe mais
luckperms.command.user.promote.unable-to-promote=NÃ£o foi possÃ­vel promover o usuÃ¡rio
luckperms.command.user.demote.success=Promovendo {0} ao longo da faixa {1} de {2} para {3} no contexto {4}
luckperms.command.user.demote.end-of-track=O fim da faixa {0} foi alcanÃ§ado, entÃ£o {1} foi removido de {2}
luckperms.command.user.demote.end-of-track-not-removed=O fim da faixa {0} foi alcanÃ§ado, mas {1} nÃ£o foi removido do primeiro grupo
luckperms.command.user.demote.previous-group-deleted=O grupo anterior na faixa, {0}, nÃ£o existe mais
luckperms.command.user.demote.unable-to-demote=NÃ£o foi possÃ­vel despromover o usuÃ¡rio
luckperms.command.group.list.title=Grupos
luckperms.command.group.delete.not-default=VocÃª nÃ£o pode excluir o grupo padrÃ£o
luckperms.command.group.info.title=InformaÃ§Ãµes do Grupo
luckperms.command.group.info.display-name-key=Nome de ExibiÃ§Ã£o
luckperms.command.group.info.weight-key=Peso
luckperms.command.group.setweight.set=Definir o peso {0} para o grupo {1}
luckperms.command.group.setdisplayname.doesnt-have={0} nÃ£o possui um nome de exibiÃ§Ã£o definido
luckperms.command.group.setdisplayname.already-has={0} jÃ¡ tem um nome de exibiÃ§Ã£o de {1}
luckperms.command.group.setdisplayname.already-in-use=O nome de exibiÃ§Ã£o {0} jÃ¡ estÃ¡ sendo usado por {1}
luckperms.command.group.setdisplayname.set=Definir nome de exibiÃ§Ã£o para {0} para o grupo {1} no contexto {2}
luckperms.command.group.setdisplayname.removed=Remover nome de exibiÃ§Ã£o para o grupo {0} no contexto {1}
luckperms.command.track.list.title=Lista de promoÃ§Ã£o
luckperms.command.track.path.empty=Nenhum
luckperms.command.track.info.showing-track=Mostrar lista de promoÃ§Ã£o
luckperms.command.track.info.path-property=Caminho
luckperms.command.track.clear={0} grupos de faixas foram limpos
luckperms.command.track.append.success=Grupo {0} foi anexado Ã  faixa {1}
luckperms.command.track.insert.success=O grupo {0} foi inserido na faixa {1} na posiÃ§Ã£o {2}
luckperms.command.track.insert.error-number=NÃºmero esperado, mas em vez disso recebido\: {0}
luckperms.command.track.insert.error-invalid-pos=NÃ£o Ã© possÃ­vel inserir na posiÃ§Ã£o {0}
luckperms.command.track.insert.error-invalid-pos-reason=posiÃ§Ã£o InvÃ¡lida
luckperms.command.track.remove.success=O grupo {0} foi removido da faixa {1}
luckperms.command.track.error-empty={0} nÃ£o pode ser usado, pois estÃ¡ vazio ou contÃ©m apenas um grupo
luckperms.command.track.error-multiple-groups={0} Ã© um membro de mÃºltiplos grupos deste caminho
luckperms.command.track.error-ambiguous=NÃ£o foi possÃ­vel determinar sua localizaÃ§Ã£o
luckperms.command.track.already-contains={0} jÃ¡ contÃ©m {1}
luckperms.command.track.doesnt-contain={0} nÃ£o deve conter {1}
luckperms.command.log.load-error=NÃ£o foi possÃ­vel carregar o log
luckperms.command.log.invalid-page=NÃºmero de pÃ¡gina invÃ¡lido
luckperms.command.log.invalid-page-range=Por favor, insira um valor entre {0} e {1}
luckperms.command.log.empty=NÃ£o hÃ¡ nenhum registro na log
luckperms.command.log.notify.error-console=NÃ£o Ã© possÃ­vel alterar as notificaÃ§Ãµes para o console
luckperms.command.log.notify.enabled-term=Ativado
luckperms.command.log.notify.disabled-term=Desativado
luckperms.command.log.notify.changed-state={0} saÃ­das no registro
luckperms.command.log.notify.already-on=VocÃª jÃ¡ estÃ¡ recebendo notificaÃ§Ãµes
luckperms.command.log.notify.already-off=VocÃª nÃ£o estÃ¡ recebendo notificaÃ§Ãµes no momento
luckperms.command.log.notify.invalid-state=Estado desconhecido. Esperando {0} ou {1}
luckperms.command.log.show.search=Mostrando aÃ§Ãµes recentes para consulta {0}
luckperms.command.log.show.recent=Mostrando aÃ§Ãµes recentes
luckperms.command.log.show.by=Mostrando aÃ§Ãµes recentes de {0}
luckperms.command.log.show.history=Mostrando histÃ³rico de {0} {1}
luckperms.command.export.error-term=Erro
luckperms.command.export.already-running=Outro processo de exportaÃ§Ã£o jÃ¡ estÃ¡ sendo executado
luckperms.command.export.file.already-exists=Arquivo {0} jÃ¡ existe
luckperms.command.export.file.not-writable=O arquivo {0} nÃ£o pode ser lido
luckperms.command.export.file.success=Exportado com sucesso para {0}
luckperms.command.export.file-unexpected-error-writing=Ocorreu um erro inesperado ao escrever no arquivo
luckperms.command.export.web.export-code=Exportar CÃ³digo
luckperms.command.export.web.import-command-description=Use o seguinte comando para importar
luckperms.command.import.term=Importar
luckperms.command.import.error-term=Erro
luckperms.command.import.already-running=Outro processo de importaÃ§Ã£o jÃ¡ estÃ¡ em execuÃ§Ã£o
luckperms.command.import.file.doesnt-exist=O Arquivo {0} nÃ£o existe
luckperms.command.import.file.not-readable=O arquivo {0} nÃ£o pode ser lido
luckperms.command.import.file.unexpected-error-reading=Ocorreu um erro inesperado ao ler o arquivo de importaÃ§Ã£o
luckperms.command.import.file.correct-format=este Ã© o formato correto?
luckperms.command.import.web.unable-to-read=NÃ£o foi possÃ­vel ler os dados usando o cÃ³digo fornecido
luckperms.command.import.progress.percent={0}% concluÃ­do
luckperms.command.import.progress.operations={0}/{1} operaÃ§Ãµes concluÃ­das
luckperms.command.import.starting=Iniciando processo de importaÃ§Ã£o
luckperms.command.import.completed=CONCLUÃDO
luckperms.command.import.duration=demorou {0} segundos
luckperms.command.bulkupdate.must-use-console=O comando de atualizaÃ§Ã£o em massa sÃ³ pode ser usado pelo console
luckperms.command.bulkupdate.invalid-data-type=Tipo invÃ¡lido, estava esperando {0}
luckperms.command.bulkupdate.invalid-constraint=RestriÃ§Ã£o invÃ¡lida {0}
luckperms.command.bulkupdate.invalid-constraint-format=As restriÃ§Ãµes devem estar no formato {0}
luckperms.command.bulkupdate.invalid-comparison=Operador de comparaÃ§Ã£o invÃ¡lido {0}
luckperms.command.bulkupdate.invalid-comparison-format=Era esperado um dos seguintes\: {0}
luckperms.command.bulkupdate.queued=A operaÃ§Ã£o de atualizaÃ§Ã£o em massa foi colocada na fila
luckperms.command.bulkupdate.confirm=Execute {0} para executar a atualizaÃ§Ã£o
luckperms.command.bulkupdate.unknown-id=A operaÃ§Ã£o com id {0} nÃ£o existe ou expirou
luckperms.command.bulkupdate.starting=A atualizaÃ§Ã£o em massa estÃ¡ agora em execuÃ§Ã£o
luckperms.command.bulkupdate.success=A atualizaÃ§Ã£o em massa foi concluÃ­da com sucesso
luckperms.command.bulkupdate.success.statistics.nodes=Total de nÃ³s afetados
luckperms.command.bulkupdate.success.statistics.users=Total de usuÃ¡rios afetados
luckperms.command.bulkupdate.success.statistics.groups=Total de grupos afetados
luckperms.command.bulkupdate.failure=Falha na atualizaÃ§Ã£o em massa, verifique os erros no console
luckperms.command.update-task.request=Uma tarefa de atualizaÃ§Ã£o foi solicitada, por favor aguarde
luckperms.command.update-task.complete=AtualizaÃ§Ã£o completa
luckperms.command.update-task.push.attempting=Agora tentando enviar para outros servidores
luckperms.command.update-task.push.complete=Outros servidores foram notificados via {0} com Ãªxito
luckperms.command.update-task.push.error=Erro ao enviar as mudanÃ§as para outros servidores
luckperms.command.update-task.push.error-not-setup=NÃ£o Ã© possÃ­vel enviar as alteraÃ§Ãµes para outros servidores porque o serviÃ§o de mensagens nÃ£o foi configurado
luckperms.command.reload-config.success=O arquivo de configuraÃ§Ã£o foi recarregado
luckperms.command.reload-config.restart-note=algumas opÃ§Ãµes sÃ³ serÃ£o aplicadas depois que o servidor tiver sido reiniciado
luckperms.command.translations.searching=Procurando por traduÃ§Ãµes disponÃ­veis, por favor aguarde...
luckperms.command.translations.searching-error=NÃ£o foi possÃ­vel obter a lista de traduÃ§Ãµes disponÃ­veis
luckperms.command.translations.installed-translations=TraduÃ§Ãµes instaladas
luckperms.command.translations.available-translations=TraduÃ§Ãµes disponÃ­veis
luckperms.command.translations.percent-translated={0}% traduzido
luckperms.command.translations.translations-by=por
luckperms.command.translations.installing=Instalando traduÃ§Ãµes, por favor aguarde...
luckperms.command.translations.download-error=NÃ£o foi possÃ­vel baixar a traduÃ§Ã£o para {0}
luckperms.command.translations.installing-specific=Instalando idioma {0}...
luckperms.command.translations.install-complete=InstalaÃ§Ã£o completa
luckperms.command.translations.download-prompt=Use {0} para baixar e instalar as versÃµes mais atualizadas dessas traduÃ§Ãµes fornecidas pela comunidade
luckperms.command.translations.download-override-warning=Isto irÃ¡ substituir todas as alteraÃ§Ãµes feitas por vocÃª nestes idiomas
luckperms.usage.user.description=Comandos para gerenciar os usuÃ¡rios dentro do LuckPerms. (Um ''usuÃ¡rio'' no LuckPerms Ã© apenas um jogador, e pode-se referir ao seu UUID ou nick)
luckperms.usage.group.description=Comandos para o gerenciamento de grupos dentro do LuckPerms. Grupos sÃ£o apenas coleÃ§Ãµes de permissÃ£o que podem ser dadas para mÃºltiplos usuÃ¡rios. Para criar novos grupos utilize o comando ''creategroup''.
luckperms.usage.track.description=Comandos para gerenciar faixas de caminhos dentro de LuckPerms. Uma faixa de caminho Ã© uma coleÃ§Ã£o ordenada de grupos que podem ser usados para definir promoÃ§Ãµes e rebaixamentos.
luckperms.usage.log.description=Um conjunto de comandos para gerenciar as configuraÃ§Ãµes da log no LuckPerms.
luckperms.usage.sync.description=Recarrega todos os dados dentro do armazenamento do plugin na memÃ³ria e aplica quaisquer alteraÃ§Ãµes detectadas.
luckperms.usage.info.description=Mostra informaÃ§Ãµes gerais sobre a instÃ¢ncia ativa do plugin.
luckperms.usage.editor.description=Cria uma nova sessÃ£o de editor web
luckperms.usage.editor.argument.type=os tipos para carregar no editor. (''todos'', ''usuÃ¡rios'' ou ''grupos'')
luckperms.usage.editor.argument.filter=permissÃ£o para filtrar entradas do usuÃ¡rio por
luckperms.usage.verbose.description=Controla o sistema de verificaÃ§Ã£o de permissÃ£o detalhada do plugin.
luckperms.usage.verbose.argument.action=se habilitar ou desabilitar o log ou fazer upload da saÃ­da registrada
luckperms.usage.verbose.argument.filter=o filtro para combinar as entradas com
luckperms.usage.verbose.argument.commandas=o jogador/comando para executar
luckperms.usage.tree.description=Gera uma lista de exibiÃ§Ã£o (hierarquia ordenada) de todas as permissÃµes conhecidas pelo LuckPerms.
luckperms.usage.tree.argument.scope=a raiz da Ã¡rvore. Especifique "." para incluir todas as permissÃµes
luckperms.usage.tree.argument.player=o nome do usuario para checar contra
luckperms.usage.search.description=Procura por todos os usuÃ¡rios/grupos com uma permissÃ£o especÃ­fica
luckperms.usage.search.argument.permission=a permissÃ£o para pesquisar
luckperms.usage.search.argument.page=a pÃ¡gina para ver
luckperms.usage.network-sync.description=Sincronize as alteraÃ§Ãµes com o armazenamento e solicite que todos os outros servidores da rede faÃ§am o mesmo
luckperms.usage.import.description=Importa dados de um arquivo de exportaÃ§Ã£o (previamente criado)
luckperms.usage.import.argument.file=selecione o arquivo Ã  importar
luckperms.usage.import.argument.replace=substituir os dados existentes em vez de se fundir
luckperms.usage.import.argument.upload=carregar os dados de uma exportaÃ§Ã£o anterior
luckperms.usage.export.description=Exporta todos os dados de permissÃµes para um arquivo de "exportaÃ§Ã£o". PoderÃ¡ ser importado novamente mais tarde.
luckperms.usage.export.argument.file=exportar o arquivo para
luckperms.usage.export.argument.without-users=excluir os usuÃ¡rios da exportaÃ§Ã£o
luckperms.usage.export.argument.without-groups=excluir grupos da exportaÃ§Ã£o
luckperms.usage.export.argument.upload=FaÃ§a o upload de todos os dados de permissÃ£o para o editor web. PoderÃ¡ ser importado novamente mais tarde.
luckperms.usage.reload-config.description=Recarregue algumas das opÃ§Ãµes da config
luckperms.usage.bulk-update.description=Executa alteraÃ§Ãµes em massa em todos os dados
luckperms.usage.bulk-update.argument.data-type=o tipo de dados sendo alterados. (''todos'', ''usuÃ¡rios'' ou ''grupos'')
luckperms.usage.bulk-update.argument.action=a aÃ§Ã£o a realizar-se sobre os dados. (''atualizaÃ§Ã£o'' ou ''apagar'')
luckperms.usage.bulk-update.argument.action-field=o campo para agir. Somente necessÃ¡rio para ''atualizar''. (''permissÃ£o'', ''servidor'' ou ''mundo'')
luckperms.usage.bulk-update.argument.action-value=o valor para substituir. Apenas necessÃ¡rio para ''atualizar''.
luckperms.usage.bulk-update.argument.constraint=as restriÃ§Ãµes necessÃ¡rias para a atualizaÃ§Ã£o
luckperms.usage.translations.description=Gerenciar traduÃ§Ãµes
luckperms.usage.translations.argument.install=subcomando para instalar traduÃ§Ãµes
luckperms.usage.apply-edits.description=Aplica alteraÃ§Ãµes de permissÃ£o feitas a partir do editor web
luckperms.usage.apply-edits.argument.code=o cÃ³digo Ãºnico para os dados
luckperms.usage.apply-edits.argument.target=a quem aplicar os dados para
luckperms.usage.create-group.description=Criar um novo grupo
luckperms.usage.create-group.argument.name=o nome do grupo
luckperms.usage.create-group.argument.weight=a posiÃ§Ã£o do grupo
luckperms.usage.create-group.argument.display-name=o nome de exibiÃ§Ã£o do grupo
luckperms.usage.delete-group.description=Excluir um grupo
luckperms.usage.delete-group.argument.name=o nome do grupo
luckperms.usage.list-groups.description=Listar todos os grupos na plataforma
luckperms.usage.create-track.description=Criar uma nova track
luckperms.usage.create-track.argument.name=o nome da track
luckperms.usage.delete-track.description=Deleta uma track
luckperms.usage.delete-track.argument.name=o nome da track
luckperms.usage.list-tracks.description=Listar todas as tracks na plataforma
luckperms.usage.user-info.description=Mostra informaÃ§Ãµes sobre o jogador
luckperms.usage.user-switchprimarygroup.description=Muda o grupo primÃ¡rio do jogador
luckperms.usage.user-switchprimarygroup.argument.group=mudar o grupo para
luckperms.usage.user-promote.description=Promove o jogador a uma track
luckperms.usage.user-promote.argument.track=a track para promover um usuÃ¡rio
luckperms.usage.user-promote.argument.context=os contextos para promover o usuÃ¡rio em
luckperms.usage.user-promote.argument.dont-add-to-first=sÃ³ promova o jogador se ele jÃ¡ estiver na track
luckperms.usage.user-demote.description=Rebaixa o jogador a uma track
luckperms.usage.user-demote.argument.track=a track para rebaixar um usuÃ¡rio
luckperms.usage.user-demote.argument.context=os contextos para promover o usuÃ¡rio em
luckperms.usage.user-demote.argument.dont-remove-from-first=evitar que o jogador seja removido do grupo primÃ¡rio
luckperms.usage.user-clone.description=Clonar usuÃ¡rio
luckperms.usage.user-clone.argument.user=o nome/UUID do jogador para clonar sobre
luckperms.usage.group-info.description=DÃ¡ informaÃ§Ãµes sobre o grupo
luckperms.usage.group-listmembers.description=Mostrar os usuÃ¡rios/grupos que herdam deste grupo
luckperms.usage.group-listmembers.argument.page=a pÃ¡gina para ver
luckperms.usage.group-setweight.description=Definir o peso dos grupos
luckperms.usage.group-setweight.argument.weight=a posiÃ§Ã£o a setar
luckperms.usage.group-set-display-name.description=Definir o nome de exibiÃ§Ã£o dos grupos
luckperms.usage.group-set-display-name.argument.name=definir o nome para
luckperms.usage.group-set-display-name.argument.context=os contextos para definir o nome em
luckperms.usage.group-rename.description=Renomear o grupo
luckperms.usage.group-rename.argument.name=o novo nome
luckperms.usage.group-clone.description=Clonar o grupo
luckperms.usage.group-clone.argument.name=o nome do grupo para clonar sobre
luckperms.usage.holder-editor.description=Abrir o editor de permissÃ£o web
luckperms.usage.holder-showtracks.description=Lista as tracks em que o objeto estÃ¡
luckperms.usage.holder-clear.description=Remova todas as permissÃµes, grupos e meta
luckperms.usage.holder-clear.argument.context=os contextos a serem filtrados por
luckperms.usage.permission.description=Editar permissÃµes
luckperms.usage.parent.description=Editar heranÃ§as
luckperms.usage.meta.description=Editar valores de metadata
luckperms.usage.permission-info.description=Lista os nodes de permissÃ£o que o objeto tem
luckperms.usage.permission-info.argument.page=a pÃ¡gina para ver
luckperms.usage.permission-info.argument.sort-mode=como classificar as entradas
luckperms.usage.permission-set.description=Setar uma permissÃ£o para o jogador
luckperms.usage.permission-set.argument.node=o node da permissÃ£o a definir
luckperms.usage.permission-set.argument.value=o valor do node
luckperms.usage.permission-set.argument.context=os contextos para definir a opÃ§Ã£o em
luckperms.usage.permission-unset.description=Remove uma permissÃ£o do jogador
luckperms.usage.permission-unset.argument.node=o node da permissÃ£o a definir
luckperms.usage.permission-unset.argument.context=os contextos para remover a permissÃ£o
luckperms.usage.permission-settemp.description=Define uma permissÃ£o para o objeto temporariamente
luckperms.usage.permission-settemp.argument.node=o node da permissÃ£o a definir
luckperms.usage.permission-settemp.argument.value=o valor do node
luckperms.usage.permission-settemp.argument.duration=a duraÃ§Ã£o da permissÃ£o atÃ© que ela se expire
luckperms.usage.permission-settemp.argument.temporary-modifier=o tempo que a permissÃ£o ficarÃ¡ aplicada
luckperms.usage.permission-settemp.argument.context=os contextos para adicionar a permissÃ£o em
luckperms.usage.permission-unsettemp.description=Remove uma permissÃ£o temporÃ¡ria para o objeto
luckperms.usage.permission-unsettemp.argument.node=o node da permissÃ£o a remover
luckperms.usage.permission-unsettemp.argument.duration=a duraÃ§Ã£o para subtrair
luckperms.usage.permission-unsettemp.argument.context=os contextos para remover a permissÃ£o
luckperms.usage.permission-check.description=Verifica se um jogador possui uma certa permissÃ£o
luckperms.usage.permission-check.argument.node=a permissÃ£o para ser verificada
luckperms.usage.permission-clear.description=Limpar todas as permissÃµes
luckperms.usage.permission-clear.argument.context=os contextos a serem filtrados por
luckperms.usage.parent-info.description=Lista os grupos que o objeto herda
luckperms.usage.parent-info.argument.page=a pÃ¡gina para ver
luckperms.usage.parent-info.argument.sort-mode=como classificar as entradas
luckperms.usage.parent-set.description=Remove todos os outros grupos que o jogador herda e adiciona o escolhido
luckperms.usage.parent-set.argument.group=definir o grupo para
luckperms.usage.parent-set.argument.context=os contextos para definir o grupo em
luckperms.usage.parent-add.description=Define outro grupo para o jogador herdar permissÃµes
luckperms.usage.parent-add.argument.group=herdar o grupo de
luckperms.usage.parent-add.argument.context=os contextos para herdar o grupo em
luckperms.usage.parent-remove.description=Remove um grupo previamente definido
luckperms.usage.parent-remove.argument.group=o grupo a ser removido
luckperms.usage.parent-remove.argument.context=os contextos para remover o grupo em
luckperms.usage.parent-set-track.description=Remove todos os outros grupos que o objeto herda jÃ¡ na track dada e adiciona-os ao dado
luckperms.usage.parent-set-track.argument.track=a track para definir
luckperms.usage.parent-set-track.argument.group=o grupo a definir ou um nÃºmero relativo Ã  posiÃ§Ã£o do grupo na track
luckperms.usage.parent-set-track.argument.context=os contextos para definir o grupo em
luckperms.usage.parent-add-temp.description=Define outro grupo para o objeto herdar permissÃµes temporariamente
luckperms.usage.parent-add-temp.argument.group=o grupo irÃ¡ herdar de
luckperms.usage.parent-add-temp.argument.duration=a duraÃ§Ã£o da associaÃ§Ã£o ao grupo
luckperms.usage.parent-add-temp.argument.temporary-modifier=como a permissÃ£o temporÃ¡ria deve ser aplicada
luckperms.usage.parent-add-temp.argument.context=os contextos para definir o grupo em
luckperms.usage.parent-remove-temp.description=Remove uma regra de heranÃ§a temporÃ¡ria definida anteriormente
luckperms.usage.parent-remove-temp.argument.group=o grupo a ser removido
luckperms.usage.parent-remove-temp.argument.duration=o tempo para subtrair
luckperms.usage.parent-remove-temp.argument.context=os contextos para remover o grupo em
luckperms.usage.parent-clear.description=Limpa todos os "parents"
luckperms.usage.parent-clear.argument.context=os contextos a serem filtrados por
luckperms.usage.parent-clear-track.description=Limpa todos os grupos em uma determinada track
luckperms.usage.parent-clear-track.argument.track=remover a track em
luckperms.usage.parent-clear-track.argument.context=os contextos a serem filtrados por
luckperms.usage.meta-info.description=Mostrar todos os prefixos/sufixos e meta no chat
luckperms.usage.meta-set.description=Define um valor meta
luckperms.usage.meta-set.argument.key=a chave a ser definida
luckperms.usage.meta-set.argument.value=definir o valor para
luckperms.usage.meta-set.argument.context=os contextos para adicionar o par meta em
luckperms.usage.meta-unset.description=Remove um valor meta
luckperms.usage.meta-unset.argument.key=a chave a ser removida
luckperms.usage.meta-unset.argument.context=os contextos para remover o meta
luckperms.usage.meta-settemp.description=Setar um valor meta temporariamente
luckperms.usage.meta-settemp.argument.key=a chave a ser definida
luckperms.usage.meta-settemp.argument.value=definir o valor para
luckperms.usage.meta-settemp.argument.duration=a duraÃ§Ã£o atÃ© o valor meta expirar
luckperms.usage.meta-settemp.argument.context=os contextos para adicionar o par meta em
luckperms.usage.meta-unsettemp.description=Remove um valor meta temporÃ¡rio
luckperms.usage.meta-unsettemp.argument.key=a chave a ser removida
luckperms.usage.meta-unsettemp.argument.context=os contextos para adicionar o par meta em
luckperms.usage.meta-addprefix.description=Adicionar um prefixo
luckperms.usage.meta-addprefix.argument.priority=a prioridade do prefixo
luckperms.usage.meta-addprefix.argument.prefix=a string prefixo
luckperms.usage.meta-addprefix.argument.context=os contextos para adicionar o prefixo em
luckperms.usage.meta-addsuffix.description=Adiciona um sufixo
luckperms.usage.meta-addsuffix.argument.priority=a prioridade para adicionar o sufixo
luckperms.usage.meta-addsuffix.argument.suffix=a string sufixo
luckperms.usage.meta-addsuffix.argument.context=os contextos para adicionar o sufixo em
luckperms.usage.meta-setprefix.description=Definir um prefixo
luckperms.usage.meta-setprefix.argument.priority=a prioridade para definir o prefixo em
luckperms.usage.meta-setprefix.argument.prefix=a string prefixo
luckperms.usage.meta-setprefix.argument.context=os contextos para definir o prefixo em
luckperms.usage.meta-setsuffix.description=Definir um sufixo
luckperms.usage.meta-setsuffix.argument.priority=a prioridade para definir o sufixo no
luckperms.usage.meta-setsuffix.argument.suffix=a string sufixo
luckperms.usage.meta-setsuffix.argument.context=os contextos para definir o sufixo
luckperms.usage.meta-removeprefix.description=Remover um prefixo
luckperms.usage.meta-removeprefix.argument.priority=a prioridade do prefixo a ser removido
luckperms.usage.meta-removeprefix.argument.prefix=a string prefixo
luckperms.usage.meta-removeprefix.argument.context=os contextos para remover o prefixo
luckperms.usage.meta-removesuffix.description=Remover um sufixo
luckperms.usage.meta-removesuffix.argument.priority=a prioridade do sufixo a ser removido
luckperms.usage.meta-removesuffix.argument.suffix=a string sufixo
luckperms.usage.meta-removesuffix.argument.context=os contextos para remover o sufixo
luckperms.usage.meta-addtemp-prefix.description=Adicionar um prefixo temporÃ¡rio
luckperms.usage.meta-addtemp-prefix.argument.priority=a prioridade do prefixo a ser definido
luckperms.usage.meta-addtemp-prefix.argument.prefix=a string sufixo
luckperms.usage.meta-addtemp-prefix.argument.duration=a duraÃ§Ã£o que o prefixo irÃ¡ expirar
luckperms.usage.meta-addtemp-prefix.argument.context=os contextos para adicionar o prefixo
luckperms.usage.meta-addtemp-suffix.description=Adicionar um suffix temporÃ¡rio
luckperms.usage.meta-addtemp-suffix.argument.priority=a prioridade do sufixo a ser adicionado
luckperms.usage.meta-addtemp-suffix.argument.suffix=a string sufixo
luckperms.usage.meta-addtemp-suffix.argument.duration=a duraÃ§Ã£o que o sufixo irÃ¡ expirar
luckperms.usage.meta-addtemp-suffix.argument.context=os contextos para adicionar a suffix em
luckperms.usage.meta-settemp-prefix.description=Setar um prefixo temporÃ¡rio
luckperms.usage.meta-settemp-prefix.argument.priority=a prioridade do prefixo a ser adicionado
luckperms.usage.meta-settemp-prefix.argument.prefix=a string prefixo
luckperms.usage.meta-settemp-prefix.argument.duration=a duraÃ§Ã£o que o prefixo irÃ¡ expirar
luckperms.usage.meta-settemp-prefix.argument.context=os contextos para definir o prefixo
luckperms.usage.meta-settemp-suffix.description=Setar um suffix temporÃ¡rio
luckperms.usage.meta-settemp-suffix.argument.priority=a prioridade para definir o sufixo
luckperms.usage.meta-settemp-suffix.argument.suffix=a string sufixo
luckperms.usage.meta-settemp-suffix.argument.duration=a duraÃ§Ã£o que o sufixo irÃ¡ expirar
luckperms.usage.meta-settemp-suffix.argument.context=os contextos para definir o sufixo em
luckperms.usage.meta-removetemp-prefix.description=Remover um prefixo temporÃ¡rio
luckperms.usage.meta-removetemp-prefix.argument.priority=a prioridade do prefixo a ser removido
luckperms.usage.meta-removetemp-prefix.argument.prefix=a string prefixo
luckperms.usage.meta-removetemp-prefix.argument.context=os contextos para remover o prefixo em
luckperms.usage.meta-removetemp-suffix.description=Remover um sufixo temporÃ¡rio
luckperms.usage.meta-removetemp-suffix.argument.priority=a prioridade do sufixo a ser removido
luckperms.usage.meta-removetemp-suffix.argument.suffix=a string sufixo
luckperms.usage.meta-removetemp-suffix.argument.context=os contextos para remover o sufixo em
luckperms.usage.meta-clear.description=Limpa todos os metadados
luckperms.usage.meta-clear.argument.type=o tipo de meta que serÃ¡ removido
luckperms.usage.meta-clear.argument.context=os contextos a serem filtrados por
luckperms.usage.track-info.description=Exibe informaÃ§Ãµes sobre uma track
luckperms.usage.track-editor.description=Abre o editor de permissÃ£o da web
luckperms.usage.track-append.description=Acrescenta um grupo ao final da track
luckperms.usage.track-append.argument.group=o grupo a ser adicionado
luckperms.usage.track-insert.description=Insere um grupo em uma determinada posiÃ§Ã£o na track
luckperms.usage.track-insert.argument.group=o grupo a ser inserido
luckperms.usage.track-insert.argument.position=a posiÃ§Ã£o de inserir o grupo em (a primeira posiÃ§Ã£o na track Ã© 1)
luckperms.usage.track-remove.description=Remover um grupo de uma track
luckperms.usage.track-remove.argument.group=o grupo a ser removido
luckperms.usage.track-clear.description=Limpa os grupos na track
luckperms.usage.track-rename.description=Renomear a faixa
luckperms.usage.track-rename.argument.name=o novo nome
luckperms.usage.track-clone.description=Clonar a track
luckperms.usage.track-clone.argument.name=o nome da track para clonar
luckperms.usage.log-recent.description=Ver aÃ§Ãµes recentes
luckperms.usage.log-recent.argument.user=o nome/uuid do usuÃ¡rio para filtrar por
luckperms.usage.log-recent.argument.page=o nÃºmero da pÃ¡gina para visualizar
luckperms.usage.log-search.description=Procurar no registro por uma entrada
luckperms.usage.log-search.argument.query=a consulta para pesquisa por
luckperms.usage.log-search.argument.page=o nÃºmero da pÃ¡gina para visualizar
luckperms.usage.log-notify.description=Ativar/desativar notificaÃ§Ãµes de registro
luckperms.usage.log-notify.argument.toggle=se deve ligar ou desligar
luckperms.usage.log-user-history.description=Visualizar histÃ³rico de um usuÃ¡rio
luckperms.usage.log-user-history.argument.user=o nome/UUID do jogador
luckperms.usage.log-user-history.argument.page=o nÃºmero da pÃ¡gina para visualizar
luckperms.usage.log-group-history.description=Ver o histÃ³rico de um grupo
luckperms.usage.log-group-history.argument.group=o nome do grupo
luckperms.usage.log-group-history.argument.page=o nÃºmero da pÃ¡gina para visualizar
luckperms.usage.log-track-history.description=Ver o histÃ³rico de uma track
luckperms.usage.log-track-history.argument.track=o nome da track
luckperms.usage.log-track-history.argument.page=o nÃºmero da pÃ¡gina para visualizar
luckperms.usage.sponge.description=Editar dados extras do Sponge
luckperms.usage.sponge.argument.collection=a coleÃ§Ã£o a realizar consultar
luckperms.usage.sponge.argument.subject=o assunto a ser modificado
luckperms.usage.sponge-permission-info.description=Exibe informaÃ§Ãµes sobre as permissÃµes do assunto
luckperms.usage.sponge-permission-info.argument.contexts=os contextos a serem filtrados por
luckperms.usage.sponge-permission-set.description=Define uma permissÃ£o para o Assunto
luckperms.usage.sponge-permission-set.argument.node=o node da permissÃ£o a definir
luckperms.usage.sponge-permission-set.argument.tristate=o valor para definir a permissÃ£o para
luckperms.usage.sponge-permission-set.argument.contexts=os contextos para definir a permissÃ£o em
luckperms.usage.sponge-permission-clear.description=Limpa as permissÃµes dos assuntos
luckperms.usage.sponge-permission-clear.argument.contexts=os contextos para limpar as permissÃµes em
luckperms.usage.sponge-parent-info.description=Mostra informaÃ§Ãµes sobre os parentes do sujeito
luckperms.usage.sponge-parent-info.argument.contexts=os contextos a serem filtrados por
luckperms.usage.sponge-parent-add.description=Adiciona um parente ao sujeito
luckperms.usage.sponge-parent-add.argument.collection=a coleÃ§Ã£o de assunto onde o assunto pai Ã©
luckperms.usage.sponge-parent-add.argument.subject=o nome do parente do assunto
luckperms.usage.sponge-parent-add.argument.contexts=os contextos para adicionar o grupo
luckperms.usage.sponge-parent-remove.description=Remove um parente do assunto
luckperms.usage.sponge-parent-remove.argument.collection=a coleÃ§Ã£o de assunto onde o assunto pai Ã©
luckperms.usage.sponge-parent-remove.argument.subject=o nome do parente do assunto
luckperms.usage.sponge-parent-remove.argument.contexts=os contextos para remover o parente em
luckperms.usage.sponge-parent-clear.description=Limpa os parentes dos assuntos
luckperms.usage.sponge-parent-clear.argument.contexts=os contextos para limpar o parente em
luckperms.usage.sponge-option-info.description=Exibe informaÃ§Ãµes sobre as opÃ§Ãµes do assunto
luckperms.usage.sponge-option-info.argument.contexts=os contextos a serem filtradados por
luckperms.usage.sponge-option-set.description=Define uma opÃ§Ã£o para o assunto
luckperms.usage.sponge-option-set.argument.key=a chave a ser definida
luckperms.usage.sponge-option-set.argument.value=o valor para definir a chave para
luckperms.usage.sponge-option-set.argument.contexts=os contextos para definir a opÃ§Ã£o em
luckperms.usage.sponge-option-unset.description=Desdefine uma opÃ§Ã£o para o assunto
luckperms.usage.sponge-option-unset.argument.key=a chave a ser removida
luckperms.usage.sponge-option-unset.argument.contexts=os contextos para desativar a chave em
luckperms.usage.sponge-option-clear.description=Limpa as opÃ§Ãµes de assuntos
luckperms.usage.sponge-option-clear.argument.contexts=os contextos para limpar opÃ§Ãµes em
