luckperms.logs.actionlog-prefix=ÐÑÑÐ´
luckperms.logs.verbose-prefix=ÐÐ
luckperms.logs.export-prefix=ÐÐÐ¡ÐÐÐ Ð¢
luckperms.commandsystem.available-commands=ÐÐ¸ÐºÐ¾ÑÐ¸ÑÑÐ¾Ð²ÑÐ¹ÑÐµ {0}, ÑÐ¾Ð± Ð¿ÐµÑÐµÐ³Ð»ÑÐ½ÑÑÐ¸ Ð´Ð¾ÑÑÑÐ¿Ð½Ñ ÐºÐ¾Ð¼Ð°Ð½Ð´Ð¸
luckperms.commandsystem.command-not-recognised=ÐÐ¾Ð¼Ð°Ð½Ð´Ð° Ð½ÐµÑÐ¾Ð·Ð¿ÑÐ·Ð½Ð°Ð½Ð°
luckperms.commandsystem.no-permission=Ð£ Ð²Ð°Ñ Ð½ÐµÐ¼Ð°Ñ Ð´Ð¾Ð·Ð²Ð¾Ð»Ñ Ð½Ð° Ð²Ð¸ÐºÐ¾ÑÐ¸ÑÑÐ°Ð½Ð½Ñ ÑÑÑÑ ÐºÐ¾Ð¼Ð°Ð½Ð´Ð¸\!
luckperms.commandsystem.no-permission-subcommands=Ð£ Ð²Ð°Ñ Ð½ÐµÐ¼Ð°Ñ Ð´Ð¾Ð·Ð²Ð¾Ð»Ñ Ð½Ð° Ð²Ð¸ÐºÐ¾ÑÐ¸ÑÑÐ°Ð½Ð½Ñ Ð±ÑÐ´Ñ-ÑÐºÐ¸Ñ Ð¿ÑÐ´ÐºÐ¾Ð¼Ð°Ð½Ð´
luckperms.commandsystem.already-executing-command=ÐÐ¸ÐºÐ¾Ð½ÑÑÑÑÑÑ ÑÐ½ÑÐ° ÐºÐ¾Ð¼Ð°Ð½Ð´Ð°, Ð·Ð°ÑÐµÐºÐ°Ð¹ÑÐµ Ð½Ð° ÑÑ Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð½Ñ...
luckperms.commandsystem.usage.sub-commands-header=ÐÑÐ´ÐºÐ¾Ð¼Ð°Ð½Ð´Ð¸
luckperms.commandsystem.usage.usage-header=ÐÐ¸ÐºÐ¾ÑÐ¸ÑÑÐ°Ð½Ð½Ñ ÐºÐ¾Ð¼Ð°Ð½Ð´
luckperms.commandsystem.usage.arguments-header=ÐÑÐ³ÑÐ¼ÐµÐ½ÑÐ¸
luckperms.first-time.no-permissions-setup=ÐÐ´Ð°ÑÑÑÑÑ, ÑÐ¾ Ð½ÑÑÐºÑ Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸ ÑÐµ Ð½Ðµ Ð½Ð°Ð»Ð°ÑÑÐ¾Ð²Ð°Ð½Ñ\!
luckperms.first-time.use-console-to-give-access=ÐÐµÑÐµÐ´ ÑÐ¸Ð¼ ÑÐº Ð²Ð¸ Ð·Ð¼Ð¾Ð¶ÐµÑÐµ Ð²Ð¸ÐºÐ¾ÑÐ¸ÑÑÐ¾Ð²ÑÐ²Ð°ÑÐ¸ Ð±ÑÐ´Ñ-ÑÐºÑ ÐºÐ¾Ð¼Ð°Ð½Ð´Ð¸ LuckPerms Ñ Ð³ÑÑ, Ð²Ð°Ð¼ Ð²Ð°ÑÑÐ¾ Ð²Ð¸ÐºÐ¾ÑÐ¸ÑÑÐ¾Ð²ÑÐ²Ð°ÑÐ¸ ÐºÐ¾Ð½ÑÐ¾Ð»Ñ, ÑÐ¾Ð± Ð½Ð°Ð´Ð°ÑÐ¸ ÑÐ¾Ð±Ñ Ð´Ð¾ÑÑÑÐ¿
luckperms.first-time.console-command-prompt=Ð Ð¾Ð·Ð³Ð¾ÑÐ½ÑÑÑ ÑÐ²Ð¾Ñ ÐºÐ¾Ð½ÑÐ¾Ð»Ñ Ñ Ð·Ð°Ð¿ÑÑÑÑÑÑ ÑÑ
luckperms.first-time.next-step=ÐÑÑÐ»Ñ ÑÐ¾Ð³Ð¾ ÑÐº Ð²Ð¸ ÑÐµ Ð²Ð¸ÐºÐ¾Ð½Ð°Ð»Ð¸, Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿Ð¾ÑÐ¸Ð½Ð°ÑÐ¸ Ð²Ð¸Ð·Ð½Ð°ÑÐ°ÑÐ¸ ÑÐ²Ð¾Ñ Ð¿ÑÐ°Ð²Ð° Ð´Ð¾ÑÑÑÐ¿Ñ ÑÐ° Ð³ÑÑÐ¿Ð¸
luckperms.first-time.wiki-prompt=ÐÐµ Ð·Ð½Ð°ÑÑÐµ Ð´Ðµ Ð¿Ð¾ÑÐ°ÑÐ¸? ÐÐµÑÐµÐ²ÑÑÑÐµ ÑÑÑ {0}
luckperms.login.try-again=ÐÑÐ´Ñ Ð»Ð°ÑÐºÐ°, ÑÐ¿ÑÐ¾Ð±ÑÐ¹ÑÐµ Ð¿ÑÐ·Ð½ÑÑÐµ
luckperms.login.loading-database-error=ÐÐ¾Ð¼Ð¸Ð»ÐºÐ° Ð±Ð°Ð·Ð¸ Ð´Ð°Ð½Ð¸Ñ ÑÑÐ°Ð»Ð°ÑÑ Ð¿ÑÐ´ ÑÐ°Ñ Ð·Ð°Ð²Ð°Ð½ÑÐ°Ð¶ÐµÐ½Ð½Ñ Ð´Ð°Ð½Ð¸Ñ Ð´Ð»Ñ Ð´Ð¾ÑÑÑÐ¿Ñ
luckperms.login.server-admin-check-console-errors=Ð¯ÐºÑÐ¾ Ð²Ð¸ Ð°Ð´Ð¼ÑÐ½ÑÑÑÑÐ°ÑÐ¾Ñ ÑÐµÑÐ²ÐµÑÑ, Ð±ÑÐ´Ñ Ð»Ð°ÑÐºÐ°, Ð¿ÐµÑÐµÐ²ÑÑÑÐµ ÐºÐ¾Ð½ÑÐ¾Ð»Ñ Ð½Ð° Ð½Ð°ÑÐ²Ð½ÑÑÑÑ Ð±ÑÐ´Ñ-ÑÐºÐ¸Ñ Ð¿Ð¾Ð¼Ð¸Ð»Ð¾Ðº
luckperms.login.server-admin-check-console-info=ÐÑÐ´Ñ Ð»Ð°ÑÐºÐ°, Ð¿ÐµÑÐµÐ²ÑÑÑÐµ ÑÐµÑÐ²ÐµÑÐ½Ñ ÐºÐ¾Ð½ÑÐ¾Ð»Ñ Ð´Ð»Ñ Ð´Ð¾Ð´Ð°ÑÐºÐ¾Ð²Ð¾Ñ ÑÐ½ÑÐ¾ÑÐ¼Ð°ÑÑÑ
luckperms.login.data-not-loaded-at-pre=ÐÐ°Ð½Ñ Ð´Ð¾Ð·Ð²Ð¾Ð»ÑÐ² Ð´Ð»Ñ Ð²Ð°ÑÐ¾Ð³Ð¾ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ° Ð½Ðµ Ð±ÑÐ»Ð¸ Ð·Ð°Ð²Ð°Ð½ÑÐ°Ð¶ÐµÐ½Ñ Ð¿ÑÐ´ ÑÐ°Ñ ÐµÑÐ°Ð¿Ñ Ð¿Ð¾Ð¿ÐµÑÐµÐ´Ð½ÑÐ¾Ð³Ð¾ Ð²ÑÐ¾Ð´Ñ
luckperms.login.unable-to-continue=Ð¿ÑÐ¾Ð´Ð¾Ð²Ð¶Ð¸ÑÐ¸ Ð½ÐµÐ¼Ð¾Ð¶Ð»Ð¸Ð²Ð¾
luckperms.login.craftbukkit-offline-mode-error=ÑÐµ, Ð¹Ð¼Ð¾Ð²ÑÑÐ½Ð¾, ÑÐµÑÐµÐ· ÐºÐ¾Ð½ÑÐ»ÑÐºÑ Ð¼ÑÐ¶ CraftBukkit Ð¹ Ð¿Ð°ÑÐ°Ð¼ÐµÑÑÐ¾Ð¼ Ð¾Ð½Ð»Ð°Ð¹Ð½ ÑÐµÐ¶Ð¸Ð¼Ñ
luckperms.login.unexpected-error=Ð¡ÑÐ°Ð»Ð°ÑÑ Ð½ÐµÐ¾ÑÑÐºÑÐ²Ð°Ð½Ð° Ð¿Ð¾Ð¼Ð¸Ð»ÐºÐ° Ð¿ÑÐ´ ÑÐ°Ñ Ð½Ð°Ð»Ð°ÑÑÑÐ²Ð°Ð½Ð½Ñ Ð±Ð°Ð·Ð¸ Ð´Ð°Ð½Ð¸Ñ Ð´Ð¾Ð·Ð²Ð¾Ð»ÑÐ²
luckperms.opsystem.disabled=Ð¡Ð¸ÑÑÐµÐ¼Ð° Ð²Ð°Ð½ÑÐ»ÑÐ½Ð¸Ñ Ð¾Ð¿ÐµÑÐ°ÑÐ¾ÑÑÐ² Ð²Ð¸Ð¼ÐºÐ½ÐµÐ½Ð° Ð½Ð° ÑÑÐ¾Ð¼Ñ ÑÐµÑÐ²ÐµÑÑ
luckperms.opsystem.sponge-warning=ÐÑÐ´Ñ Ð»Ð°ÑÐºÐ°, Ð·Ð²ÐµÑÐ½ÑÑÑ ÑÐ²Ð°Ð³Ñ, ÑÐ¾ ÑÑÐ°ÑÑÑ Ð¾Ð¿ÐµÑÐ°ÑÐ¾ÑÐ° ÑÐµÑÐ²ÐµÑÐ° Ð½Ðµ Ð¼Ð°Ñ Ð²Ð¿Ð»Ð¸Ð²Ñ Ð½Ð° Ð¿ÐµÑÐµÐ²ÑÑÐºÐ¸ ÑÐ¾Ð´Ð¾ Ð´Ð¾Ð·Ð²Ð¾Ð»ÑÐ² Sponge, ÐºÐ¾Ð»Ð¸ Ð¿Ð»Ð°Ð³ÑÐ½ Ð´Ð¾Ð·Ð²Ð¾Ð»Ñ ÑÐ½ÑÑÐ°Ð»ÑÐ¾Ð²Ð°Ð½Ð¸Ð¹, Ð²Ð¸ Ð¿Ð¾Ð²Ð¸Ð½Ð½Ñ Ð²ÑÐ´ÑÐµÐ´Ð°Ð³ÑÐ²Ð°ÑÐ¸ Ð´Ð°Ð½Ñ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ° Ð±ÐµÐ·Ð¿Ð¾ÑÐµÑÐµÐ´Ð½ÑÐ¾
luckperms.duration.unit.years.plural={0} ÑÐ¾ÐºÑÐ²
luckperms.duration.unit.years.singular={0} ÑÑÐº
luckperms.duration.unit.years.short={0}Ñ.
luckperms.duration.unit.months.plural={0} Ð¼ÑÑÑÑÑÐ²
luckperms.duration.unit.months.singular={0} Ð¼ÑÑÑÑÑ
luckperms.duration.unit.months.short={0}Ð¼ÑÑ.
luckperms.duration.unit.weeks.plural={0} ÑÐ¸Ð¶Ð½ÑÐ²
luckperms.duration.unit.weeks.singular={0} ÑÐ¸Ð¶Ð´ÐµÐ½Ñ
luckperms.duration.unit.weeks.short={0}ÑÐ¸Ð¶Ð´
luckperms.duration.unit.days.plural={0} Ð´Ð½ÑÐ²
luckperms.duration.unit.days.singular={0} Ð´ÐµÐ½Ñ
luckperms.duration.unit.days.short={0}Ð´Ð½
luckperms.duration.unit.hours.plural={0} Ð³Ð¾Ð´Ð¸Ð½
luckperms.duration.unit.hours.singular={0} Ð³Ð¾Ð´Ð¸Ð½Ð°
luckperms.duration.unit.hours.short={0}Ð³
luckperms.duration.unit.minutes.plural={0} ÑÐ²Ð¸Ð»Ð¸Ð½
luckperms.duration.unit.minutes.singular={0} ÑÐ²Ð¸Ð»Ð¸Ð½Ð°
luckperms.duration.unit.minutes.short={0}ÑÐ²
luckperms.duration.unit.seconds.plural={0} ÑÐµÐºÑÐ½Ð´
luckperms.duration.unit.seconds.singular={0} ÑÐµÐºÑÐ½Ð´Ð°
luckperms.duration.unit.seconds.short={0}ÑÐµÐº
luckperms.duration.since={0} ÑÐ¾Ð¼Ñ
luckperms.command.misc.invalid-code=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð¸Ð¹ ÐºÐ¾Ð´
luckperms.command.misc.response-code-key=ÐºÐ¾Ð´ Ð²ÑÐ´Ð¿Ð¾Ð²ÑÐ´Ñ
luckperms.command.misc.error-message-key=Ð¿Ð¾Ð²ÑÐ´Ð¾Ð¼Ð»ÐµÐ½Ð½Ñ
luckperms.command.misc.bytebin-unable-to-communicate=ÐÐµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð·Ð²âÑÐ·Ð°ÑÐ¸ÑÑ Ð· bytebin
luckperms.command.misc.webapp-unable-to-communicate=ÐÐµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð·Ð´ÑÐ¹ÑÐ½Ð¸ÑÐ¸ Ð¾Ð±Ð¼ÑÐ½ ÑÐ½ÑÐ¾ÑÐ¼Ð°ÑÑÑÑ Ð· Ð²ÐµÐ±-Ð´Ð¾Ð´Ð°ÑÐºÐ¾Ð¼
luckperms.command.misc.check-console-for-errors=ÐÐµÑÐµÐ²ÑÑÑÐµ ÐºÐ¾Ð½ÑÐ¾Ð»Ñ Ð½Ð° Ð¿Ð¾Ð¼Ð¸Ð»ÐºÐ¸
luckperms.command.misc.file-must-be-in-data=Ð¤Ð°Ð¹Ð» {0} Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð² Ð¿Ð°Ð¿ÑÑ Ð±Ð°Ð·Ð¸ Ð´Ð°Ð½Ð¸Ñ
luckperms.command.misc.wait-to-finish=ÐÐ°ÑÐµÐºÐ°Ð¹ÑÐµ, Ð±ÑÐ´Ñ Ð»Ð°ÑÐºÐ°, Ð¿Ð¾ÐºÐ¸ Ð²Ð¾Ð½Ð¾ Ð·Ð°ÐºÑÐ½ÑÐ¸ÑÑ Ñ ÑÐ¿ÑÐ¾Ð±ÑÐ¹ÑÐµ Ð·Ð½Ð¾Ð²Ñ
luckperms.command.misc.invalid-priority=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð¸Ð¹ Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑ {0}
luckperms.command.misc.expected-number=ÐÑÑÐºÑÐ²Ð°Ð»Ð¾ÑÑ ÑÑÐ»Ðµ ÑÐ¸ÑÐ»Ð¾\!
luckperms.command.misc.date-parse-error=ÐÐµ Ð²Ð´Ð°Ð»Ð¾ÑÑ ÑÐ¾Ð·Ð¿ÑÐ·Ð½Ð°ÑÐ¸ Ð´Ð°ÑÑ {0}
luckperms.command.misc.date-in-past-error=ÐÐ¸ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÐ¸ Ð¼Ð¸Ð½ÑÐ»Ñ Ð´Ð°ÑÑ\!
luckperms.command.misc.page=ÑÑÐ¾ÑÑÐ½ÐºÐ° {0} Ð· {1}
luckperms.command.misc.page-entries={0} Ð·Ð°Ð¿Ð¸ÑÑÐ²
luckperms.command.misc.none=ÐÑÐ´ÑÑÑÐ½ÑÐ¾
luckperms.command.misc.loading.error.unexpected=Ð¡ÑÐ°Ð»Ð°ÑÑ Ð½ÐµÐ¿ÐµÑÐµÐ´Ð±Ð°ÑÐµÐ½Ð° Ð¿Ð¾Ð¼Ð¸Ð»ÐºÐ°
luckperms.command.misc.loading.error.user=ÐÐ¾ÑÐ¸ÑÑÑÐ²Ð°Ñ Ð½ÐµÐ·Ð°Ð²Ð°Ð½ÑÐ°Ð¶ÐµÐ½Ð¸Ð¹
luckperms.command.misc.loading.error.user-specific=ÐÐµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð·Ð°Ð²Ð°Ð½ÑÐ°Ð¶Ð¸ÑÐ¸ ÑÑÐ»ÑÐ¾Ð²Ð¾Ð³Ð¾ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ° {0}
luckperms.command.misc.loading.error.user-not-found=ÐÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ° Ð´Ð»Ñ {0} Ð½Ðµ Ð·Ð½Ð°Ð¹Ð´ÐµÐ½Ð¾
luckperms.command.misc.loading.error.user-save-error=ÐÑÐ´ ÑÐ°Ñ Ð·Ð±ÐµÑÐµÐ¶ÐµÐ½Ð½Ñ Ð´Ð°Ð½Ð¸Ñ Ð´Ð»Ñ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ° {0} ÑÑÐ°Ð»Ð°ÑÑ Ð¿Ð¾Ð¼Ð¸Ð»ÐºÐ°
luckperms.command.misc.loading.error.user-not-online=ÐÐ¾ÑÐ¸ÑÑÑÐ²Ð°Ñ {0} Ð½Ðµ Ð² Ð¼ÐµÑÐµÐ¶Ñ
luckperms.command.misc.loading.error.user-invalid={0} Ð½ÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ðµ ÑÐ¼''Ñ/uid
luckperms.command.misc.loading.error.user-not-uuid=ÐÐµÐ´ÑÐ¹ÑÐ½Ðµ uid {0} ÑÑÐ»ÑÐ¾Ð²Ð¾Ð³Ð¾ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°
luckperms.command.misc.loading.error.group=ÐÑÑÐ¿Ñ Ð½Ðµ Ð·Ð°Ð²Ð°Ð½ÑÐ°Ð¶ÐµÐ½Ð¾
luckperms.command.misc.loading.error.all-groups=ÐÐµ Ð²Ð´Ð°ÑÑÑÑÑ Ð·Ð°Ð²Ð°Ð½ÑÐ°Ð¶Ð¸ÑÐ¸ Ð²ÑÑ Ð³ÑÑÐ¿Ð¸
luckperms.command.misc.loading.error.group-not-found=ÐÑÑÐ¿Ñ Ð½Ð° ÑÐ¼''Ñ {0} Ð½Ðµ Ð·Ð½Ð°Ð¹Ð´ÐµÐ½Ð¾
luckperms.command.misc.loading.error.group-save-error=Ð¡ÑÐ°Ð»Ð°ÑÑ Ð¿Ð¾Ð¼Ð¸Ð»ÐºÐ° Ð¿ÑÐ´ ÑÐ°Ñ Ð·Ð±ÐµÑÐµÐ¶ÐµÐ½Ð½Ñ Ð´Ð°Ð½Ð¸Ñ Ð³ÑÑÐ¿Ð¸ Ð´Ð»Ñ {0}
luckperms.command.misc.loading.error.group-invalid={0} - Ð½ÐµÐºÐ¾ÑÐµÐºÑÐ½Ð° Ð½Ð°Ð·Ð²Ð° Ð³ÑÑÐ¿Ð¸
luckperms.command.misc.loading.error.track=Ð¢ÑÐµÐº Ð½Ðµ Ð·Ð°Ð²Ð°Ð½ÑÐ°Ð¶ÐµÐ½Ð¸Ð¹
luckperms.command.misc.loading.error.all-tracks=ÐÐµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð·Ð°Ð²Ð°Ð½ÑÐ°Ð¶Ð¸ÑÐ¸ Ð²ÑÑ Ð¼Ð°ÑÑÑÑÑÐ¸
luckperms.command.misc.loading.error.track-not-found=Ð¢ÑÐµÐº Ð· Ð½Ð°Ð·Ð²Ð¾Ñ {0} Ð½Ðµ Ð·Ð½Ð°Ð¹Ð´ÐµÐ½Ð¾
luckperms.command.misc.loading.error.track-save-error=Ð¡ÑÐ°Ð»Ð°ÑÑ Ð¿Ð¾Ð¼Ð¸Ð»ÐºÐ° Ð¿ÑÐ´ ÑÐ°Ñ Ð·Ð±ÐµÑÐµÐ¶ÐµÐ½Ð½Ñ Ð´Ð°Ð½Ð¸Ñ Ð¼Ð°ÑÑÑÑÑÑ Ð´Ð»Ñ {0}
luckperms.command.misc.loading.error.track-invalid={0} Ð½ÐµÐºÐ¾ÑÐµÐºÑÐ½Ð° Ð½Ð°Ð·Ð²Ð° Ð¼Ð°ÑÑÑÑÑÑ
luckperms.command.editor.no-match=ÐÐµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð²ÑÐ´ÐºÑÐ¸ÑÐ¸ ÑÐµÐ´Ð°ÐºÑÐ¾Ñ, Ð¶Ð¾Ð´ÐµÐ½ Ð¾Ð±''ÑÐºÑ Ð½Ðµ Ð²ÑÐ´Ð¿Ð¾Ð²ÑÐ´Ð°Ñ Ð²Ð¸Ð±ÑÐ°Ð½Ð¾Ð¼Ñ ÑÐ¸Ð¿Ñ
luckperms.command.editor.start=ÐÑÐ´Ð³Ð¾ÑÐ¾Ð²ÐºÐ° Ð½Ð¾Ð²Ð¾Ñ ÑÐµÑÑÑ ÑÐµÐ´Ð°ÐºÑÐ¾ÑÐ°, Ð±ÑÐ´Ñ Ð»Ð°ÑÐºÐ°, Ð·Ð°ÑÐµÐºÐ°Ð¹ÑÐµ...
luckperms.command.editor.url=ÐÐ°ÑÐ¸ÑÐ½ÑÑÑ Ð½Ð° Ð¿Ð¾ÑÐ¸Ð»Ð°Ð½Ð½Ñ Ð½Ð¸Ð¶ÑÐµ, ÑÐ¾Ð± Ð²ÑÐ´ÐºÑÐ¸ÑÐ¸ ÑÐµÐ´Ð°ÐºÑÐ¾Ñ
luckperms.command.editor.unable-to-communicate=ÐÐµÐ¼Ð¾Ð¶Ð»Ð¸Ð²Ð¾ Ð·''ÑÐ´Ð½Ð°ÑÐ¸ÑÑ Ð· ÑÐµÐ´Ð°ÐºÑÐ¾ÑÐ¾Ð¼
luckperms.command.editor.apply-edits.success=ÐÐ°Ð½Ñ Ð²ÐµÐ±-ÑÐµÐ´Ð°ÐºÑÐ¾ÑÐ° Ð·Ð°ÑÑÐ¾ÑÐ¾Ð²Ð°Ð½Ñ Ð´Ð¾ {0} {1} ÑÑÐ¿ÑÑÐ½Ð¾
luckperms.command.editor.apply-edits.success-summary={0} {1} Ñ {2} {3}
luckperms.command.editor.apply-edits.success.additions=Ð´Ð¾Ð¿Ð¾Ð²Ð½ÐµÐ½Ñ
luckperms.command.editor.apply-edits.success.additions-singular=Ð´Ð¾Ð¿Ð¾Ð²Ð½ÐµÐ½Ð½Ñ
luckperms.command.editor.apply-edits.success.deletions=Ð²Ð¸Ð»ÑÑÐµÐ½Ñ
luckperms.command.editor.apply-edits.success.deletions-singular=Ð²Ð¸Ð»ÑÑÐµÐ½Ð½Ñ
luckperms.command.editor.apply-edits.no-changes=ÐÐµ Ð·Ð°ÑÑÐ¾ÑÐ¾Ð²Ð°Ð½Ð¾ Ð¶Ð¾Ð´Ð½Ð¸Ñ Ð·Ð¼ÑÐ½ Ñ Ð²ÐµÐ±-ÑÐµÐ´Ð°ÐºÑÐ¾ÑÑ, Ð¾ÑÐºÑÐ»ÑÐºÐ¸ Ð¾ÑÑÐ¸Ð¼Ð°Ð½Ñ Ð´Ð°Ð½Ñ Ð½Ðµ Ð¼ÑÑÑÐ¸Ð»Ð¸ ÑÐµÐ´Ð°Ð³ÑÐ²Ð°Ð½Ñ
luckperms.command.editor.apply-edits.unknown-type=ÐÐµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð·Ð°ÑÑÐ¾ÑÑÐ²Ð°ÑÐ¸ ÑÐµÐ´Ð°Ð³ÑÐ²Ð°Ð½Ð½Ñ Ð´Ð¾ Ð²ÐºÐ°Ð·Ð°Ð½Ð¾Ð³Ð¾ ÑÐ¸Ð¿Ñ Ð¾Ð±''ÑÐºÑÐ°
luckperms.command.editor.apply-edits.unable-to-read=ÐÐ¸ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿ÑÐ¾ÑÐ¸ÑÐ°ÑÐ¸ Ð±Ð°Ð·Ñ Ð´Ð°Ð½Ð¸Ñ, Ð²Ð¸ÐºÐ¾ÑÐ¸ÑÑÐ¾Ð²ÑÑÑÐ¸ Ð´Ð°Ð½Ð¸Ð¹ ÐºÐ¾Ð´
luckperms.command.search.searching.permission=ÐÐ¾ÑÑÐº ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÑÐ² Ñ Ð³ÑÑÐ¿ Ð· {0}
luckperms.command.search.searching.inherit=ÐÐ¾ÑÑÐº ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÑÐ² Ñ Ð³ÑÑÐ¿ Ð· {0}
luckperms.command.search.result=ÐÐ½Ð°Ð¹Ð´ÐµÐ½Ð¾ {0} Ð·Ð°Ð¿Ð¸ÑÑÐ² ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÑÐ² {1} Ñ Ð³ÑÑÐ¿ {2}
luckperms.command.search.result.default-notice=ÐÑÐ¸Ð¼ÑÑÐºÐ°\: Ð¿ÑÐ¸ Ð¿Ð¾ÑÑÐºÑ ÑÑÐ°ÑÐ½Ð¸ÐºÑÐ² Ð³ÑÑÐ¿Ð¸ Ð·Ð° Ð·Ð°Ð¼Ð¾Ð²ÑÑÐ²Ð°Ð½Ð½ÑÐ¼ Ð² Ð°Ð²ÑÐ¾Ð½Ð¾Ð¼Ð½Ð¾Ð¼Ñ ÑÐµÐ¶Ð¸Ð¼Ñ Ð³ÑÐ°Ð²ÑÑÐ² Ð±ÐµÐ· Ð±ÑÐ´Ñ-ÑÐºÐ¸Ñ Ð¿ÑÐ°Ð² Ð½Ðµ Ð²ÑÐ´Ð¾Ð±ÑÐ°Ð¶Ð°ÑÐ¸Ð¼ÐµÑÑÑÑ\!
luckperms.command.search.showing-users=ÐÐ¾ÐºÐ°Ð·Ð°Ð½Ð¾ Ð¼Ð°ÑÐµÑÑÐ°Ð»Ð¸ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°
luckperms.command.search.showing-groups=ÐÐ¾ÐºÐ°Ð·Ð°Ð½Ð¾ Ð¼Ð°ÑÐµÑÑÐ°Ð»Ð¸ Ð³ÑÑÐ¿Ð¸
luckperms.command.tree.start=Ð¡ÑÐ²Ð¾ÑÐµÐ½Ð½Ñ Ð´ÐµÑÐµÐ²Ð° Ð´Ð¾Ð·Ð²Ð¾Ð»ÑÐ², Ð·Ð°ÑÐµÐºÐ°Ð¹ÑÐµ, Ð±ÑÐ´Ñ Ð»Ð°ÑÐºÐ°...
luckperms.command.tree.empty=ÐÐµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð·Ð³ÐµÐ½ÐµÑÑÐ²Ð°ÑÐ¸ Ð´ÐµÑÐµÐ²Ð¾, ÑÐµÐ·ÑÐ»ÑÑÐ°ÑÑÐ² Ð½Ðµ Ð·Ð½Ð°Ð¹Ð´ÐµÐ½Ð¾
luckperms.command.tree.url=ÐÐ¾ÑÐ¸Ð»Ð°Ð½Ð½Ñ Ð½Ð° Ð´ÐµÑÐµÐ²Ð¾ Ð´Ð¾Ð·Ð²Ð¾Ð»ÑÐ²
luckperms.command.verbose.invalid-filter={0} - Ð½ÐµÐºÐ¾ÑÐµÐºÑÐ½Ð¸Ð¹ ÑÑÐ»ÑÑÑ
luckperms.command.verbose.enabled=Ð Ð¾Ð·ÑÐ¸ÑÐµÐ½Ð¸Ð¹ Ð¶ÑÑÐ½Ð°Ð» {0} Ð´Ð»Ñ Ð¿ÐµÑÐµÐ²ÑÑÐºÐ¸ {1}
luckperms.command.verbose.command-exec=ÐÑÐ¸Ð¼ÑÑÐ¾Ð²Ð¾ {0} Ð²Ð¸ÐºÐ¾Ð½ÑÐ²Ð°ÑÐ¸ ÐºÐ¾Ð¼Ð°Ð½Ð´Ñ {1} Ñ Ð¿Ð¾Ð²ÑÐ´Ð¾Ð¼Ð»ÑÑÐ¸ Ð¿ÑÐ¾ Ð²ÑÑ Ð¿ÐµÑÐµÐ²ÑÑÐºÐ¸...
luckperms.command.verbose.off=ÐÐ¾ÐºÐ»Ð°Ð´Ð½Ðµ Ð²ÐµÐ´ÐµÐ½Ð½Ñ Ð¶ÑÑÐ½Ð°Ð»Ñ{0}
luckperms.command.verbose.command-exec-complete=ÐÐ¸ÐºÐ¾Ð½Ð°Ð½Ð½Ñ ÐºÐ¾Ð¼Ð°Ð½Ð´Ð¸ Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð¾
luckperms.command.verbose.command.no-checks=ÐÐ¾Ð¼Ð°Ð½Ð´Ð° Ð²Ð¸ÐºÐ¾Ð½Ð°Ð½Ð°, Ð°Ð»Ðµ Ð½ÑÑÐºÐ¸Ñ Ð¿ÐµÑÐµÐ²ÑÑÐ¾Ðº Ð´Ð¾Ð·Ð²Ð¾Ð»ÑÐ² Ð½Ðµ Ð·ÑÐ¾Ð±Ð»ÐµÐ½Ñ
luckperms.command.verbose.command.possibly-async=Ð¦Ðµ Ð¼Ð¾Ð¶Ðµ Ð±ÑÑÐ¸ ÑÐµÑÐµÐ· ÑÐµ ÑÐ¾ Ð¿Ð»Ð°Ð³ÑÐ½ Ð·Ð°Ð¿ÑÑÐºÐ°Ñ ÐºÐ¾Ð¼Ð°Ð½Ð´Ð¸ Ñ ÑÐ¾Ð½Ð¾Ð²Ð¾Ð¼Ñ ÑÐµÐ¶Ð¸Ð¼Ñ (async)
luckperms.command.verbose.command.try-again-manually=ÐÐ¸ Ð´Ð¾ÑÑ Ð¼Ð¾Ð¶ÐµÑÐµ Ð²Ð¸ÐºÐ¾ÑÐ¸ÑÑÐ¾Ð²ÑÐ²Ð°ÑÐ¸ Ð´ÐµÑÐ°Ð»ÑÐ½Ñ ÑÐ½ÑÐ¾ÑÐ¼Ð°ÑÑÑ Ð²ÑÑÑÐ½Ñ Ð´Ð»Ñ Ð²Ð¸Ð·Ð½Ð°ÑÐµÐ½Ð½Ñ Ð¿ÐµÑÐµÐ²ÑÑÐ¾Ðº, Ð·ÑÐ¾Ð±Ð»ÐµÐ½Ð¸Ñ ÑÐº ÑÑ
luckperms.command.verbose.enabled-recording=Ð Ð¾Ð·ÑÐ¸ÑÐµÐ½Ð¸Ð¹ Ð¶ÑÑÐ½Ð°Ð» {0} Ð´Ð»Ñ Ð¿ÐµÑÐµÐ²ÑÑÐºÐ¸ {1}
luckperms.command.verbose.uploading=Ð¢ÑÐ¸Ð²Ð°Ð»Ðµ Ð¶ÑÑÐ½Ð°Ð»ÑÐ²Ð°Ð½Ð½Ñ {0}, Ð·Ð°Ð²Ð°Ð½ÑÐ°Ð¶ÐµÐ½Ð½Ñ ÑÐµÐ·ÑÐ»ÑÑÐ°ÑÑÐ²...
luckperms.command.verbose.url=ÐÐ¾ÐºÐ»Ð°Ð´Ð½Ð° ÑÐ½ÑÐ¾ÑÐ¼Ð°ÑÑÑ Ð¿ÑÐ¾ ÑÐµÐ·ÑÐ»ÑÑÐ°ÑÐ¸ Ð·Ð°Ð²Ð°Ð½ÑÐ°Ð¶ÐµÐ½Ð½Ñ URL
luckperms.command.verbose.enabled-term=ÑÐ²ÑÐ¼ÐºÐ½ÐµÐ½Ð¾
luckperms.command.verbose.disabled-term=Ð²Ð¸Ð¼ÐºÐ½ÐµÐ½Ð¾
luckperms.command.verbose.query-any=ÐÐ£ÐÐ¬-Ð¯ÐÐÐ
luckperms.command.info.running-plugin=ÐÐ°Ð¿ÑÑÐµÐ½Ð¾
luckperms.command.info.platform-key=ÐÐ»Ð°ÑÑÐ¾ÑÐ¼Ð°
luckperms.command.info.server-brand-key=Ð¢Ð¸Ð¿ ÑÐµÑÐ²ÐµÑÐ°
luckperms.command.info.server-version-key=ÐÐµÑÑÑÑ ÑÐµÑÐ²ÐµÑÐ°
luckperms.command.info.storage-key=Ð¡ÑÐ¾Ð²Ð¸ÑÐµ
luckperms.command.info.storage-type-key=Ð¢Ð¸Ð¿
luckperms.command.info.storage.meta.split-types-key=Ð¢Ð¸Ð¿Ð¸
luckperms.command.info.storage.meta.ping-key=ÐÑÐ½Ð³
luckperms.command.info.storage.meta.connected-key=Ð''ÑÐ´Ð½Ð°Ð½Ð¾
luckperms.command.info.storage.meta.file-size-key=Ð Ð¾Ð·Ð¼ÑÑ ÑÐ°Ð¹Ð»Ñ
luckperms.command.info.extensions-key=Ð Ð¾Ð·ÑÐ¸ÑÐµÐ½Ð½Ñ
luckperms.command.info.messaging-key=ÐÐ±Ð¼ÑÐ½ Ð¿Ð¾Ð²ÑÐ´Ð¾Ð¼Ð»ÐµÐ½Ð½ÑÐ¼Ð¸
luckperms.command.info.instance-key=ÐÑÐ°Ð·Ð¾Ðº
luckperms.command.info.static-contexts-key=Ð¡ÑÐ°ÑÐ¸ÑÐ½Ð¸Ð¹ ÐºÐ¾Ð½ÑÐµÐºÑÑ
luckperms.command.info.online-players-key=ÐÑÐ°Ð²ÑÑ Ð¾Ð½Ð»Ð°Ð¹Ð½
luckperms.command.info.online-players-unique={0} ÑÐ½ÑÐºÐ°Ð»ÑÐ½Ð¸Ð¹
luckperms.command.info.uptime-key=Ð§Ð°Ñ ÑÐ¾Ð±Ð¾ÑÐ¸
luckperms.command.info.local-data-key=ÐÐ¾ÐºÐ°Ð»ÑÐ½Ñ Ð´Ð°Ð½Ñ
luckperms.command.info.local-data={0} ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÑÐ², {1} Ð³ÑÑÐ¿ â {2} Ð´Ð¾ÑÑÐ¶Ð¾Ðº
luckperms.command.generic.create.success={0} - ÑÑÐ¿ÑÑÐ½Ð¾ ÑÑÐ²Ð¾ÑÐµÐ½Ð¾
luckperms.command.generic.create.error=Ð¡ÑÐ°Ð»Ð°ÑÑ Ð¿Ð¾Ð¼Ð¸Ð»ÐºÐ° Ð¿ÑÐ¸ ÑÑÐ²Ð¾ÑÐµÐ½Ð½Ñ {0}
luckperms.command.generic.create.error-already-exists=''{0}'' ÑÐ¶Ðµ ÑÑÐ½ÑÑ\!
luckperms.command.generic.delete.success={0} ÑÑÐ¿ÑÑÐ½Ð¾ Ð²Ð¸Ð´Ð°Ð»ÐµÐ½Ð¾
luckperms.command.generic.delete.error=Ð¡ÑÐ°Ð»Ð°ÑÑ Ð¿Ð¾Ð¼Ð¸Ð»ÐºÐ° Ð¿ÑÐ¸ Ð²Ð¸Ð´Ð°Ð»ÐµÐ½Ð½Ñ {0}
luckperms.command.generic.delete.error-doesnt-exist={0} Ð½Ðµ ÑÑÐ½ÑÑ\!
luckperms.command.generic.rename.success={0} ÑÑÐ¿ÑÑÐ½Ð¾ Ð¿ÐµÑÐµÐ¹Ð¼ÐµÐ½Ð¾Ð²Ð°Ð½Ð¾ Ð½Ð° {1}
luckperms.command.generic.clone.success={0} ÑÑÐ¿ÑÑÐ½Ð¾ ÐºÐ»Ð¾Ð½Ð¾Ð²Ð°Ð½Ð¾ Ð² {1}
luckperms.command.generic.info.parent.title=ÐÐ°ÑÑÐºÑÐ²ÑÑÐºÑ Ð³ÑÑÐ¿Ð¸
luckperms.command.generic.info.parent.temporary-title=Ð¢Ð¸Ð¼ÑÐ°ÑÐ¾Ð²Ñ Ð±Ð°ÑÑÐºÑÐ²ÑÑÐºÑ Ð³ÑÑÐ¿Ð¸
luckperms.command.generic.info.expires-in=Ð·Ð°Ð²ÐµÑÑÑÑÑÑÑÑ
luckperms.command.generic.info.inherited-from=ÑÑÐ¿Ð°Ð´ÐºÐ¾Ð²Ð°Ð½Ð¸Ð¹ Ð²ÑÐ´
luckperms.command.generic.info.inherited-from-self=ÑÐ°Ð¼Ð¾Ð³Ð¾ ÑÐµÐ±Ðµ
luckperms.command.generic.show-tracks.title={0} ÑÑÐµÐºÐ¸
luckperms.command.generic.show-tracks.empty={0} Ð½Ðµ Ð¼ÑÑÑÐ¸ÑÑ Ð¶Ð¾Ð´Ð½Ð¸Ñ Ð¼Ð°ÑÑÑÑÑÑÐ²
luckperms.command.generic.clear.node-removed={0} Ð·Ð°Ð¿Ð¸ÑÐ¸ Ð²Ð¸Ð´Ð°Ð»ÐµÐ½Ñ
luckperms.command.generic.clear.node-removed-singular={0} Ð·Ð°Ð¿Ð¸Ñ Ð²Ð¸Ð´Ð°Ð»ÐµÐ½Ð¸Ð¹
luckperms.command.generic.clear={0} ÐÐ°Ð¿Ð¸ÑÐ¸ Ð² ÐºÐ¾Ð½ÑÐµÐºÑÑÑ{1} Ð¾ÑÐ¸ÑÐµÐ½Ñ
luckperms.command.generic.permission.info.title={0} Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸
luckperms.command.generic.permission.info.empty={0} Ð½Ðµ Ð¼Ð°Ñ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ Ð´Ð¾Ð·Ð²Ð¾Ð»ÑÐ²
luckperms.command.generic.permission.info.click-to-remove=ÐÐ°ÑÐ¸ÑÐ½ÑÑÑ, ÑÐ¾Ð± Ð²Ð¸Ð»ÑÑÐ¸ÑÐ¸ ÑÐµÐ¹ Ð´Ð¾Ð·Ð²ÑÐ» ÑÐ· {0}
luckperms.command.generic.permission.check.info.title=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÑÑ Ð¿ÑÐ¾ Ð´Ð¾Ð·Ð²ÑÐ» Ð´Ð»Ñ {0}
luckperms.command.generic.permission.check.info.directly={0} Ð¼Ð°Ñ {1} Ð· ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¼ Ð·Ð½Ð°ÑÐµÐ½Ð½ÑÐ¼ {2} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {3}
luckperms.command.generic.permission.check.info.inherited={0} Ð½Ð°ÑÐ»ÑÐ´ÑÑ {1} Ð· ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¼ Ð·Ð½Ð°ÑÐµÐ½Ð½ÑÐ¼ {2} Ð²ÑÐ´ {3} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {4}
luckperms.command.generic.permission.check.info.not-directly={0} Ð½Ðµ Ð¼Ð°Ñ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¾Ð³Ð¾ {1}
luckperms.command.generic.permission.check.info.not-inherited={0} Ð½Ðµ Ð½Ð°ÑÐ»ÑÐ´ÑÑ Ð²ÑÐ´ {1}
luckperms.command.generic.permission.check.result.title=ÐÐµÑÐµÐ²ÑÑÐºÐ° Ð´Ð¾Ð·Ð²Ð¾Ð»Ñ {0}
luckperms.command.generic.permission.check.result.result-key=Ð ÐµÐ·ÑÐ»ÑÑÐ°Ñ
luckperms.command.generic.permission.check.result.processor-key=ÐÑÐ¾ÑÐµÑÐ¾Ñ
luckperms.command.generic.permission.check.result.cause-key=ÐÑÐ¸ÑÐ¸Ð½Ð°
luckperms.command.generic.permission.check.result.context-key=ÐÐ¼ÑÑÑ
luckperms.command.generic.permission.set=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÐ¸ {0} Ð² {1} Ð´Ð»Ñ {2} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {3}
luckperms.command.generic.permission.already-has={0} ÑÐ¶Ðµ Ð¼Ð°Ñ Ð´Ð¾Ð·Ð²ÑÐ» {1}, Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¹ Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.generic.permission.set-temp=ÐÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¾ Ð¿ÑÐ°Ð²Ð¾ {0} Ð·Ñ Ð·Ð½Ð°ÑÐµÐ½Ð½ÑÐ¼ {1} Ð´Ð»Ñ {2} Ð· ÑÑÐ¸Ð²Ð°Ð»ÑÑÑÑ {3} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {4}
luckperms.command.generic.permission.already-has-temp={0} ÑÐ¶Ðµ Ð¼Ð°Ñ ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¾ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¹ Ð´Ð¾Ð·Ð²ÑÐ» {1} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.generic.permission.unset=Ð¡ÐºÐ°ÑÑÐ²Ð°ÑÐ¸ {0} Ð´Ð»Ñ {1} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.generic.permission.doesnt-have={0} Ð½Ðµ Ð¼Ð°Ñ {1}, ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¾Ð³Ð¾ Ð² ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.generic.permission.unset-temp=Ð¡ÐºÐ°ÑÑÐ²Ð°ÑÐ¸ ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¸Ð¹ Ð´Ð¾Ð·Ð²ÑÐ» {0} Ð´Ð»Ñ {1} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.generic.permission.subtract=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð»ÑÑ {0} Ð² {1} Ð´Ð»Ñ {2} ÑÑÐ¸Ð²Ð°Ð»ÑÑÑÑ {3} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {4}, Ð½Ð° {5} Ð¼ÐµÐ½ÑÐµ, Ð½ÑÐ¶ ÑÐ°Ð½ÑÑÐµ
luckperms.command.generic.permission.doesnt-have-temp={0} Ð½Ðµ Ð¼Ð°Ñ {1}, ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¾ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ðµ Ð² ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.generic.permission.clear=ÐÐ¾Ð·Ð²Ð¾Ð»Ð¸ {0} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {1} Ð¾ÑÐ¸ÑÐµÐ½Ð¾
luckperms.command.generic.parent.info.title=ÐÐ°ÑÑÐºÐ¸ {0}
luckperms.command.generic.parent.info.empty={0} Ð½Ðµ Ð¼Ð°Ñ Ð²Ð¸Ð·Ð½Ð°ÑÐµÐ½Ð¸Ñ Ð±Ð°ÑÑÐºÑÐ²
luckperms.command.generic.parent.info.click-to-remove=ÐÐ°ÑÐ¸ÑÐ½ÑÑÑ, ÑÐ¾Ð± Ð²Ð¸Ð»ÑÑÐ¸ÑÐ¸ ÑÐµÐ¹ Ð±Ð°ÑÑÐºÑÐ²ÑÑÐºÐ¸Ð¹ ÐµÐ»ÐµÐ¼ÐµÐ½Ñ Ð· {0}
luckperms.command.generic.parent.add={0} ÑÐµÐ¿ÐµÑ Ð½Ð°ÑÐ»ÑÐ´ÑÑ Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸ Ð²ÑÐ´ {1} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.generic.parent.add-temp={0} ÑÐµÐ¿ÐµÑ ÑÑÐ¿Ð°Ð´ÐºÐ¾Ð²ÑÑ Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸ Ð²ÑÐ´ {1} Ð½Ð° ÑÑÐ¸Ð²Ð°Ð»ÑÑÑÑ {2} Ð² ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {3}
luckperms.command.generic.parent.set=Ð Ð°Ð½ÑÑÐµ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ñ Ð±Ð°ÑÑÐºÑÐ²ÑÑÐºÑ Ð³ÑÑÐ¿Ð¸ {0} Ð¾ÑÐ¸ÑÐµÐ½Ñ Ð¹ ÑÐµÐ¿ÐµÑ ÑÐµÐ¹ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°Ñ Ð½Ð°ÑÐ»ÑÐ´ÑÑ Ð»Ð¸ÑÐµ {1} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.generic.parent.set-track=Ð Ð°Ð½ÑÑÐµ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ñ Ð±Ð°ÑÑÐºÑÐ²ÑÑÐºÑ Ð³ÑÑÐ¿Ð¸ {0} Ñ ÑÑÐµÐºÑ {1} Ð¾ÑÐ¸ÑÐµÐ½Ñ Ð¹ ÑÐµÐ¿ÐµÑ ÑÐµÐ¹ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°Ñ Ð½Ð°ÑÐ»ÑÐ´ÑÑ Ð»Ð¸ÑÐµ {2} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {3}
luckperms.command.generic.parent.remove={0} Ð±ÑÐ»ÑÑÐµ Ð½Ðµ Ð½Ð°ÑÐ»ÑÐ´ÑÑ Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸ Ð²ÑÐ´ {1} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.generic.parent.remove-temp={0} ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¾ Ð½Ðµ Ð½Ð°ÑÐ»ÑÐ´ÑÑ Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸ Ð²ÑÐ´ {1} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.generic.parent.subtract={0} Ð½Ð°ÑÐ»ÑÐ´ÑÐ²Ð°ÑÐ¸Ð¼Ðµ Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸ Ð²ÑÐ´ {1} Ð½Ð° ÑÑÐ¸Ð²Ð°Ð»ÑÑÑÑ {2} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {3} ÑÐµÑÐ¼ÑÐ½Ð¾Ð¼ Ð½Ð° {4} Ð¼ÐµÐ½ÑÐµ, Ð½ÑÐ¶ ÑÐ°Ð½ÑÑÐµ
luckperms.command.generic.parent.clear=ÐÐ°ÑÑÐºÑÐ²ÑÑÐºÑ Ð³ÑÑÐ¿Ð¸ {0} Ð² ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {1} Ð¾ÑÐ¸ÑÐµÐ½Ð¾
luckperms.command.generic.parent.clear-track=ÐÐ°ÑÑÐºÑÐ²ÑÑÐºÑ Ð³ÑÑÐ¿Ð¸ {0} Ñ ÑÑÐµÐºÑ {1} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2} Ð¾ÑÐ¸ÑÐµÐ½Ð¾
luckperms.command.generic.parent.already-inherits={0} ÑÐ¶Ðµ Ð½Ð°ÑÐ»ÑÐ´ÑÑ Ð²ÑÐ´ {1} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.generic.parent.doesnt-inherit={0} Ð½Ðµ Ð½Ð°ÑÐ»ÑÐ´ÑÑ Ð²ÑÐ´ {1} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.generic.parent.already-temp-inherits={0} ÑÐ¶Ðµ ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¾ Ð½Ð°ÑÐ»ÑÐ´ÑÑ Ð²ÑÐ´ {1} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¾ Ð½Ðµ Ð½Ð°ÑÐ»ÑÐ´ÑÑ Ð²ÑÐ´ {1} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.generic.chat-meta.info.title-prefix=ÐÑÐµÑÑÐºÑÐ¸ {0}
luckperms.command.generic.chat-meta.info.title-suffix=Ð¡ÑÑÑÐºÑÐ¸ {0}
luckperms.command.generic.chat-meta.info.none-prefix={0} Ð½Ðµ Ð¼Ð°Ñ Ð¿ÑÐµÑÑÐºÑÑÐ²
luckperms.command.generic.chat-meta.info.none-suffix={0} Ð½Ðµ Ð¼Ð°Ñ ÑÑÑÑÐºÑÑÐ²
luckperms.command.generic.chat-meta.info.click-to-remove=ÐÐ°ÑÐ¸ÑÐ½ÑÑÑ, ÑÐ¾Ð± Ð²Ð¸Ð»ÑÑÐ¸ÑÐ¸ {0} Ð· {1}
luckperms.command.generic.chat-meta.already-has={0} ÑÐ¶Ðµ Ð¼Ð°Ñ {1} {2} Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ñ Ð· Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑÐ¾Ð¼ {3} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {4}
luckperms.command.generic.chat-meta.already-has-temp={0} ÑÐ¶Ðµ Ð¼Ð°Ñ {1} {2} ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¾ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ñ Ð· Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑÐ¾Ð¼ {3} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {4}
luckperms.command.generic.chat-meta.doesnt-have={0} Ð½Ðµ Ð¼Ð°Ñ {1} {2} Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ñ Ð· Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑÐ¾Ð¼ {3} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} ÑÐ¶Ðµ Ð¼Ð°Ñ {1} {2} ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¾ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ñ Ð· Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑÐ¾Ð¼ {3} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {4}
luckperms.command.generic.chat-meta.add={0} ÑÐµÐ¿ÐµÑ Ð¼Ð°Ñ {1} {2} Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¹ Ð· Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑÐ¾Ð¼ {3} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {4}
luckperms.command.generic.chat-meta.add-temp={0} ÑÐµÐ¿ÐµÑ Ð¼Ð°Ñ {1} {2}, ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¾ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¹ Ð· Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑÐ¾Ð¼ {3} Ð½Ð° ÑÑÐ¸Ð²Ð°Ð»ÑÑÑÑ {4} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {5}
luckperms.command.generic.chat-meta.remove={0} ÑÐµÐ¿ÐµÑ Ð½Ðµ Ð¼Ð°Ñ {1} {2}, ÑÐºÐ¸Ð¹ Ð±ÑÐ² Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¹ Ð· Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑÐ¾Ð¼ {3} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {4}
luckperms.command.generic.chat-meta.remove-bulk={0} ÑÐµÐ¿ÐµÑ Ð½Ðµ Ð¼Ð°Ñ Ð²ÑÐµ {1}, ÑÐºÑ Ð±ÑÐ»Ð¸ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ñ Ð· Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑÐ¾Ð¼ {2} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {3}
luckperms.command.generic.chat-meta.remove-temp={0} ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¾ Ð¼Ð°Ð² {1} {2} Ñ Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑÑ {3}, Ð²Ð¸Ð»ÑÑÐµÐ½Ñ Ð² ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} Ð¼Ð°Ð² ÑÑÐµ ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¾ {1}, ÑÐ¾ Ð±ÑÐ»Ð¾ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¾ Ð· Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑÐ¾Ð¼ {2} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {3}
luckperms.command.generic.meta.info.title=ÐÐµÑÐ°Ð´Ð°Ð½Ñ {0}
luckperms.command.generic.meta.info.none={0} Ð½Ðµ Ð¼Ð°Ñ Ð¼ÐµÑÐ°Ð´Ð°Ð½Ð¸Ñ
luckperms.command.generic.meta.info.click-to-remove=ÐÐ°ÑÐ¸ÑÐ½ÑÑÑ, ÑÐ¾Ð± Ð²Ð¸Ð´Ð°Ð»Ð¸ÑÐ¸ ÑÑ Ð¼ÐµÑÐ°Ð´Ð°Ð½Ñ Ð· Ð³ÑÑÐ¿Ð¸ {0}
luckperms.command.generic.meta.already-has=ÐÑÑÐ¿Ð° {0} ÑÐ¶Ðµ Ð¼Ð°Ñ Ð¼ÐµÑÐ°ÐºÐ»ÑÑ {1} Ð· ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¼ Ð·Ð½Ð°ÑÐµÐ½Ð½ÑÐ¼ {2} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {3}
luckperms.command.generic.meta.already-has-temp={0} ÑÐ¶Ðµ Ð¼Ð°Ñ Ð¼ÐµÑÐ°ÐºÐ»ÑÑ {1} Ð· ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¾ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¼ Ð·Ð½Ð°ÑÐµÐ½Ð½ÑÐ¼ {2} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {3}
luckperms.command.generic.meta.doesnt-have={0} Ð½Ðµ Ð¼Ð°Ñ Ð¼ÐµÑÐ°ÐºÐ»ÑÑÐ°{1}, ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¾Ð³Ð¾ Ð² ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.generic.meta.doesnt-have-temp={0} Ð½Ðµ Ð¼Ð°Ñ Ð¼ÐµÑÐ°ÐºÐ»ÑÑÐ° {1} ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¾ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¾Ð³Ð¾ Ð² ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.generic.meta.set=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÐ¸ Ð¼ÐµÑÐ°ÐºÐ»ÑÑ {0} Ð·Ñ Ð·Ð½Ð°ÑÐµÐ½Ð½ÑÐ¼ {1} Ð´Ð»Ñ {2} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {3}
luckperms.command.generic.meta.set-temp=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÐ¸ Ð¼ÐµÑÐ°ÐºÐ»ÑÑ {0} Ð·Ñ Ð·Ð½Ð°ÑÐµÐ½Ð½ÑÐ¼ {1} Ð´Ð»Ñ {2} ÑÑÐ¸Ð²Ð°Ð»ÑÑÑÑ Ð½Ð° {3} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {4}
luckperms.command.generic.meta.unset=ÐÐ½ÑÑÐ¸ Ð¼ÐµÑÐ°ÐºÐ»ÑÑ {0} Ð·Ñ Ð·Ð½Ð°ÑÐµÐ½Ð½ÑÐ¼ {1} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.generic.meta.unset-temp=ÐÐ½ÑÑÐ¸ ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¸Ð¹ Ð¼ÐµÑÐ°ÐºÐ»ÑÑ {0} Ð·Ñ Ð·Ð½Ð°ÑÐµÐ½Ð½ÑÐ¼ {1} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.generic.meta.clear=ÐÐµÑÐ°Ð´Ð°ÑÐ° Ð´Ð»Ñ {0} Ð²ÑÐ´Ð¿Ð¾Ð²ÑÐ´Ð½Ð¾Ð³Ð¾ ÑÐ¸Ð¿Ñ {1} Ð¾ÑÐ¸ÑÐµÐ½Ð° Ð² ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.generic.contextual-data.title=ÐÐ¾Ð½ÑÐµÐºÑÑÑÐ°Ð»ÑÐ½Ñ Ð´Ð°Ð½Ñ
luckperms.command.generic.contextual-data.mode.key=ÑÐµÐ¶Ð¸Ð¼
luckperms.command.generic.contextual-data.mode.server=ÑÐµÑÐ²ÐµÑ
luckperms.command.generic.contextual-data.mode.active-player=Ð°ÐºÑÐ¸Ð²Ð½Ð¸Ð¹ Ð³ÑÐ°Ð²ÐµÑÑ
luckperms.command.generic.contextual-data.contexts-key=ÐÐ¾Ð½ÑÐµÐºÑÑÐ¸
luckperms.command.generic.contextual-data.prefix-key=ÐÑÐµÑÑÐºÑ
luckperms.command.generic.contextual-data.suffix-key=Ð¡ÑÑÑÐºÑ
luckperms.command.generic.contextual-data.primary-group-key=ÐÐµÑÐ²Ð¸Ð½Ð½Ð° Ð³ÑÑÐ¿Ð°
luckperms.command.generic.contextual-data.meta-key=ÐÐµÑÐ°
luckperms.command.generic.contextual-data.null-result=ÐÐ¾Ð´Ð½Ð¾Ð³Ð¾
luckperms.command.user.info.title=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÑÑ Ð¿ÑÐ¾ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=ÑÐ¸Ð¿
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=Ð¾ÑÐ»Ð°Ð¹Ð½
luckperms.command.user.info.status-key=Ð¡ÑÐ°ÑÑÑ
luckperms.command.user.info.status.online=ÐÐ½Ð»Ð°Ð¹Ð½
luckperms.command.user.info.status.offline=ÐÑÐ»Ð°Ð¹Ð½
luckperms.command.user.removegroup.error-primary=ÐÐ¸ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð²Ð¸Ð»ÑÑÐ¸ÑÐ¸ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ° Ð· Ð¿ÐµÑÐ²Ð¸Ð½Ð½Ð¾Ñ Ð³ÑÑÐ¿Ð¸
luckperms.command.user.primarygroup.not-member={0} Ð´Ð¾Ð´Ð°Ð½Ð¸Ð¹ Ñ ÑÑÐµÐº {1}, Ð¾ÑÐºÑÐ»ÑÐºÐ¸ ÑÐ°Ð½ÑÑÐµ ÑÐµÐ¹ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°Ñ Ð½Ðµ Ð±ÑÐ² Ð¿Ð¾Ð²''ÑÐ·Ð°Ð½Ð¸Ð¹ Ð· Ð½Ð¸Ð¼
luckperms.command.user.primarygroup.already-has={0} ÑÐ¶Ðµ Ð¼Ð°Ñ Ð³ÑÑÐ¿Ñ {1}, ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ñ Ð² ÑÐ¾Ð»Ñ Ð³Ð¾Ð»Ð¾Ð²Ð½Ð¾Ñ
luckperms.command.user.primarygroup.warn-option=ÐÐ¾Ð¿ÐµÑÐµÐ´Ð¶ÐµÐ½Ð½Ñ\: ÐÐµÑÐ¾Ð´ Ð¿ÑÐ´ÑÐ°ÑÑÐ½ÐºÑ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÑÐ² Ð· Ð¾ÑÐ½Ð¾Ð²Ð½Ð¾Ñ Ð³ÑÑÐ¿Ð¸, Ð²Ð¸ÐºÐ¾ÑÐ¸ÑÑÐ°Ð½Ð¸Ð¹ Ð½Ð° ÑÐµÑÐ²ÐµÑÑ ({0}), Ð¼Ð¾Ð¶Ðµ Ð½Ðµ Ð¿Ð¾ÐºÐ°Ð·ÑÐ²Ð°ÑÐ¸ Ð½Ð°Ð²ÐµÐ´ÐµÐ½Ñ Ð·Ð¼ÑÐ½Ð¸
luckperms.command.user.primarygroup.set=ÐÑÐ½Ð¾Ð²Ð½Ð° Ð³ÑÑÐ¿Ð° {0} ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð° Ð½Ð° Ð³ÑÑÐ¿Ñ {1}
luckperms.command.user.track.error-not-contain-group={0} ÑÐµ Ð½Ðµ Ð¼Ð°Ñ Ð¶Ð¾Ð´Ð½Ð¾Ñ Ð³ÑÑÐ¿Ð¸, ÑÐºÐ° ÑÐ¾Ð·ÑÐ°ÑÐ¾Ð²Ð°Ð½Ð° Ð² ÑÑÐµÑÑ {1}
luckperms.command.user.track.unsure-which-track=ÐÐ° Ð¶Ð°Ð»Ñ, Ð½Ðµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð²Ð¸Ð±ÑÐ°ÑÐ¸ Ð½ÐµÐ¾Ð±ÑÑÐ´Ð½Ð¸Ð¹ ÑÑÐµÐº Ð´Ð»Ñ Ð²Ð¸ÐºÐ¾ÑÐ¸ÑÑÐ°Ð½Ð½Ñ, Ð±ÑÐ´Ñ Ð»Ð°ÑÐºÐ°, ÑÐºÐ°Ð¶ÑÑÑ Ð¹Ð¾Ð³Ð¾ Ð½Ð°Ð·Ð²Ñ Ð² ÑÐ¾Ð»Ñ Ð°ÑÐ³ÑÐ¼ÐµÐ½ÑÑ
luckperms.command.user.track.missing-group-advice=ÐÐ±Ð¾ ÑÑÐ²Ð¾ÑÑÑÑ Ð³ÑÑÐ¿Ñ, Ð°Ð±Ð¾ Ð²Ð¸Ð´Ð°Ð»ÑÐ¹ÑÐµ ÑÑ Ð· ÑÑÐµÐºÑ Ð¹ ÑÐ¿ÑÐ¾Ð±ÑÐ¹ÑÐµ Ð·Ð½Ð¾Ð²Ñ
luckperms.command.user.promote.added-to-first={0} Ð´Ð¾Ð´Ð°Ð½Ð¸Ð¹ Ð² Ð³ÑÑÐ¿Ñ {2} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {3}, Ð¾ÑÐºÑÐ»ÑÐºÐ¸ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°Ñ Ð½Ðµ Ð¼Ð°Ð² Ð³ÑÑÐ¿, ÑÐºÑ ÑÐ¾Ð·ÑÐ°ÑÐ¾Ð²Ð°Ð½Ñ Ð² ÑÑÐµÐºÑ {1}
luckperms.command.user.promote.not-on-track=ÐÐµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð¿ÑÐ´Ð²Ð¸ÑÐ¸ÑÐ¸ Ð³ÑÑÐ¿Ñ {0}, Ð¾ÑÐºÑÐ»ÑÐºÐ¸ ÑÐµÐ¹ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°Ñ Ð½Ðµ Ð¼Ð°Ñ Ð³ÑÑÐ¿, ÑÐºÑ ÑÐ¾Ð·ÑÐ°ÑÐ¾Ð²Ð°Ð½Ñ Ð² ÑÑÐµÐºÑ {1}
luckperms.command.user.promote.success=ÐÑÐ´Ð²Ð¸ÑÐµÐ½Ð½Ñ Ð³ÑÑÐ¿Ð¸ {0} Ð² ÑÑÐµÑÑ {1} Ð· {2} Ð´Ð¾ {3} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {4}
luckperms.command.user.promote.end-of-track=ÐÐ¾ÑÑÐ³Ð½ÑÑÐ¾ ÐºÑÐ½ÑÑ ÑÑÐµÐºÑ {0}, ÑÐ¾Ð¼Ñ Ð½Ðµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð¿ÑÐ´Ð²Ð¸ÑÐ¸ÑÐ¸ Ð³ÑÑÐ¿Ñ {1}
luckperms.command.user.promote.next-group-deleted=ÐÐ°ÑÑÑÐ¿Ð½Ð° Ð³ÑÑÐ¿Ð° Ð² ÑÑÐµÑÑ, {0}, Ð±ÑÐ»ÑÑÐµ Ð½Ðµ ÑÑÐ½ÑÑ
luckperms.command.user.promote.unable-to-promote=ÐÐµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð¿ÑÐ´Ð²Ð¸ÑÐ¸ÑÐ¸ Ð³ÑÑÐ¿Ñ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°
luckperms.command.user.demote.success=ÐÐ½Ð¸Ð¶ÐµÐ½Ð½Ñ Ð³ÑÑÐ¿Ð¸ {0} Ñ ÑÑÐµÑÑ {1} Ð· {2} Ð´Ð¾ {3} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {4}
luckperms.command.user.demote.end-of-track=ÐÑÐ½ÐµÑÑ Ð¼Ð°ÑÑÑÑÑÑ Ð´Ð¾ÑÑÐ³Ð½ÐµÐ½Ð¾ {0}, ÑÐ¾Ð¼Ñ {1} Ð²Ð¸Ð»ÑÑÐµÐ½Ð¾ Ð· {2}
luckperms.command.user.demote.end-of-track-not-removed=ÐÐ¾ÑÑÐ³Ð½ÑÑÐ¾ ÐºÑÐ½ÑÑ Ð¼Ð°ÑÑÑÑÑÑ {0}, Ð°Ð»Ðµ {1} Ð½Ðµ Ð²Ð¸Ð»ÑÑÐµÐ½Ð¾ Ð· Ð¿ÐµÑÑÐ¾Ñ Ð³ÑÑÐ¿Ð¸
luckperms.command.user.demote.previous-group-deleted=ÐÐ¾Ð¿ÐµÑÐµÐ´Ð½ÑÐ¾Ñ Ð³ÑÑÐ¿Ð¸ Ð½Ð° Ð¼Ð°ÑÑÑÑÑÑ {0} Ð±ÑÐ»ÑÑÐµ Ð½Ðµ ÑÑÐ½ÑÑ
luckperms.command.user.demote.unable-to-demote=ÐÐµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð·Ð½Ð¸Ð·Ð¸ÑÐ¸ Ð³ÑÑÐ¿Ñ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°
luckperms.command.group.list.title=ÐÑÑÐ¿Ð¸
luckperms.command.group.delete.not-default=ÐÐµ Ð¼Ð¾Ð¶Ð½Ð° Ð²Ð¸Ð´Ð°Ð»Ð¸ÑÐ¸ Ð³ÑÑÐ¿Ñ Ð·Ð° Ð·Ð°Ð¼Ð¾Ð²ÑÑÐ²Ð°Ð½Ð½ÑÐ¼
luckperms.command.group.info.title=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÑÑ Ð¿ÑÐ¾ Ð³ÑÑÐ¿Ñ
luckperms.command.group.info.display-name-key=ÐÐ¼''Ñ
luckperms.command.group.info.weight-key=ÐÐ°Ð³Ð°
luckperms.command.group.setweight.set=ÐÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÐ¸ Ð²Ð°Ð³Ñ {0} Ð´Ð»Ñ Ð³ÑÑÐ¿Ð¸ {1}
luckperms.command.group.setdisplayname.doesnt-have={0} Ð½Ðµ Ð¼Ð°Ñ Ð½Ð°Ð±Ð¾ÑÑ ÑÐ¼ÐµÐ½
luckperms.command.group.setdisplayname.already-has={0} ÑÐ¶Ðµ Ð¼Ð°Ñ ÑÐ¼''Ñ {1}
luckperms.command.group.setdisplayname.already-in-use=ÐÐ¼''Ñ {0} ÑÐ¶Ðµ Ð²Ð¸ÐºÐ¾ÑÐ¸ÑÑÐ¾Ð²ÑÑÑÑÑÑ Ð³ÑÑÐ¿Ð¾Ñ {1}
luckperms.command.group.setdisplayname.set=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¾ ÑÐ¼''Ñ {0} Ð´Ð»Ñ Ð³ÑÑÐ¿Ð¸ {1} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {2}
luckperms.command.group.setdisplayname.removed=ÐÐ¼''Ñ Ð´Ð»Ñ Ð³ÑÑÐ¿Ð¸ {0} Ñ ÐºÐ¾Ð½ÑÐµÐºÑÑÑ {1} Ð²Ð¸Ð»ÑÑÐµÐ½Ð¾
luckperms.command.track.list.title=Ð¢ÑÐµÐºÐ¸
luckperms.command.track.path.empty=ÐÐ¾Ð´Ð½Ð¾Ð³Ð¾
luckperms.command.track.info.showing-track=ÐÑÐ´Ð¾Ð±ÑÐ°Ð¶Ð°Ð½Ð½Ñ ÑÑÐµÐºÑ
luckperms.command.track.info.path-property=Ð¨Ð»ÑÑ
luckperms.command.track.clear=ÐÑÑ Ð³ÑÑÐ¿Ð¸ ÑÑÐµÐºÑ {0} Ð¾ÑÐ¸ÑÐµÐ½Ð¾
luckperms.command.track.append.success=ÐÑÑÐ¿Ñ {0} Ð´Ð¾Ð´Ð°Ð½Ð¾ Ð´Ð»Ñ Ð²ÑÐ´ÑÑÐµÐ¶ÐµÐ½Ð½Ñ {1}
luckperms.command.track.insert.success=ÐÑÑÐ¿Ñ {0} Ð´Ð¾Ð´Ð°Ð½Ð¾ Ð² ÑÑÐµÐº {1} Ð· Ð¿Ð¾Ð·Ð¸ÑÑÑÑ {2}
luckperms.command.track.insert.error-number=ÐÐ»Ñ Ð·Ð°Ð´Ð°Ð½Ð½Ñ Ð¿Ð¾Ð·Ð¸ÑÑÑ Ð¿Ð¾ÑÑÑÐ±Ð½Ð¾ Ð²ÐºÐ°Ð·Ð°ÑÐ¸ ÑÐ¸ÑÐ»Ð¾, Ð°Ð»Ðµ Ð½Ð°ÑÐ¾Ð¼ÑÑÑÑ ÑÐ²ÐµÐ´ÐµÐ½Ð¾\: {0}
luckperms.command.track.insert.error-invalid-pos=ÐÐµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð´Ð¾Ð´Ð°ÑÐ¸ Ð² Ð¿Ð¾Ð·Ð¸ÑÑÑ {0}
luckperms.command.track.insert.error-invalid-pos-reason=Ð½ÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð¸Ð¹ Ð½Ð¾Ð¼ÐµÑ Ð¿Ð¾Ð·Ð¸ÑÑÑ
luckperms.command.track.remove.success=ÐÑÑÐ¿Ñ {0} Ð²Ð¸Ð»ÑÑÐµÐ½Ð¾ Ð· ÑÑÐµÐºÑ {1}
luckperms.command.track.error-empty=Ð¢ÑÐµÐº {0} Ð½Ðµ Ð¼Ð¾Ð¶Ðµ Ð²Ð¸ÐºÐ¾ÑÐ¸ÑÑÐ¾Ð²ÑÐ²Ð°ÑÐ¸ÑÑ, Ð¾ÑÐºÑÐ»ÑÐºÐ¸ Ð²ÑÐ½ Ð¿Ð¾ÑÐ¾Ð¶Ð½ÑÐ¹ Ð°Ð±Ð¾ ÑÐºÐ»Ð°Ð´Ð°Ñ Ð»Ð¸ÑÐµ Ð¾Ð´Ð½Ñ Ð³ÑÑÐ¿Ñ
luckperms.command.track.error-multiple-groups={0} Ñ ÑÑÐ°ÑÐ½Ð¸ÐºÐ¾Ð¼ ÐºÑÐ»ÑÐºÐ¾Ñ Ð³ÑÑÐ¿ Ð½Ð° ÑÑÐ¾Ð¼Ñ ÑÑÐµÐºÑ
luckperms.command.track.error-ambiguous=ÐÐµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð²Ð¸Ð·Ð½Ð°ÑÐ¸ÑÐ¸ Ð¼ÑÑÑÐµ ÑÐ¾Ð·ÑÐ°ÑÑÐ²Ð°Ð½Ð½Ñ
luckperms.command.track.already-contains=ÐÑÑÐ¿Ð° {1} ÑÐ¶Ðµ Ñ Ð² ÑÑÐµÐºÑ {0}
luckperms.command.track.doesnt-contain={0} Ð½Ðµ Ð²ÐºÐ»ÑÑÐ°Ñ {1}
luckperms.command.log.load-error=ÐÐµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð·Ð°Ð²Ð°Ð½ÑÐ°Ð¶Ð¸ÑÐ¸ Ð»Ð¾Ð³
luckperms.command.log.invalid-page=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð¸Ð¹ Ð½Ð¾Ð¼ÐµÑ ÑÑÐ¾ÑÑÐ½ÐºÐ¸
luckperms.command.log.invalid-page-range=ÐÑÐ´Ñ Ð»Ð°ÑÐºÐ°, ÑÐºÐ°Ð¶ÑÑÑ Ð·Ð½Ð°ÑÐµÐ½Ð½Ñ Ð²ÑÐ´ {0} Ð´Ð¾ {1}
luckperms.command.log.empty=ÐÐµÐ¼Ð° Ð·Ð°Ð¿Ð¸ÑÑÐ² Ð»Ð¾Ð³Ñ Ð´Ð»Ñ Ð¿Ð¾ÐºÐ°Ð·Ñ
luckperms.command.log.notify.error-console=ÐÐµ Ð¼Ð¾Ð¶Ð½Ð° Ð·Ð¼ÑÐ½Ð¸ÑÐ¸ Ð½Ð°Ð»Ð°ÑÑÑÐ²Ð°Ð½Ð½Ñ ÑÐ¿Ð¾Ð²ÑÑÐµÐ½Ñ Ð´Ð»Ñ ÐºÐ¾Ð½ÑÐ¾Ð»Ñ
luckperms.command.log.notify.enabled-term=Ð£Ð²ÑÐ¼ÐºÐ½ÐµÐ½Ñ
luckperms.command.log.notify.disabled-term=ÐÐ¸Ð¼ÐºÐ½ÐµÐ½Ñ
luckperms.command.log.notify.changed-state=ÐÐ¾Ð³-ÑÐ¿Ð¾Ð²ÑÑÐµÐ½Ð½Ñ {0}
luckperms.command.log.notify.already-on=ÐÑÑÐ¸Ð¼Ð°Ð½Ð½Ñ Ð»Ð¾Ð³-ÑÐ¿Ð¾Ð²ÑÑÐµÐ½Ñ ÑÐ¶Ðµ Ð²Ð²ÑÐ¼ÐºÐ½ÐµÐ½Ð¾
luckperms.command.log.notify.already-off=ÐÑÑÐ¸Ð¼Ð°Ð½Ð½Ñ ÑÐ¿Ð¾Ð²ÑÑÐµÐ½Ñ ÑÐ¶Ðµ Ð²Ð¸Ð¼ÐºÐ½ÐµÐ½Ð¾
luckperms.command.log.notify.invalid-state=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð½Ñ, ÑÐ¾Ð¼Ñ Ð²Ð¸Ð±ÐµÑÑÑÑ {0} Ð°Ð±Ð¾ {1}
luckperms.command.log.show.search=ÐÐ¾ÐºÐ°Ð· Ð¾ÑÑÐ°Ð½Ð½ÑÑ Ð´ÑÐ¹ Ð·Ð° Ð·Ð°Ð¿Ð¸ÑÐ¾Ð¼ {0}
luckperms.command.log.show.recent=ÐÐ¾ÐºÐ°Ð· Ð¾ÑÑÐ°Ð½Ð½ÑÑ Ð´ÑÐ¹
luckperms.command.log.show.by=ÐÐ¾ÐºÐ°Ð· Ð¾ÑÑÐ°Ð½Ð½ÑÑ Ð´ÑÐ¹ {0}
luckperms.command.log.show.history=ÐÐ¾ÐºÐ°Ð· ÑÑÑÐ¾ÑÑÑ Ð´ÑÐ¹ Ð´Ð»Ñ {0} {1}
luckperms.command.export.error-term=ÐÐ¾Ð¼Ð¸Ð»ÐºÐ°
luckperms.command.export.already-running=ÐÐ° ÑÐµÐ¹ Ð¼Ð¾Ð¼ÐµÐ½Ñ Ð²Ð¸ÐºÐ¾Ð½ÑÑÑÑÑÑ ÑÐ½ÑÐ¸Ð¹ Ð¿ÑÐ¾ÑÐµÑ ÐµÐºÑÐ¿Ð¾ÑÑÑÐ²Ð°Ð½Ð½Ñ
luckperms.command.export.file.already-exists=Ð¤Ð°Ð¹Ð» Ð· ÑÐ¼''ÑÐ¼ {0} ÑÐ¶Ðµ ÑÑÐ½ÑÑ
luckperms.command.export.file.not-writable=Ð¤Ð°Ð¹Ð» {0} Ð½ÐµÐ´Ð¾ÑÑÑÐ¿Ð½Ð¸Ð¹ Ð´Ð»Ñ Ð·Ð°Ð¿Ð¸ÑÑ
luckperms.command.export.file.success=Ð£ÑÐ¿ÑÑÐ½Ð¾ ÐµÐºÑÐ¿Ð¾ÑÑÐ¾Ð²Ð°Ð½Ð¾ Ñ {0}
luckperms.command.export.file-unexpected-error-writing=Ð¢ÑÐ°Ð¿Ð¸Ð»Ð°ÑÑ Ð½ÐµÐ¾ÑÑÐºÑÐ²Ð°Ð½Ð° Ð¿Ð¾Ð¼Ð¸Ð»ÐºÐ° Ð¿ÑÐ¸ Ð·Ð°Ð¿Ð¸ÑÑ ÑÐ°Ð¹Ð»Ñ
luckperms.command.export.web.export-code=ÐÐ¾Ð´ Ð´Ð»Ñ ÐµÐºÑÐ¿Ð¾ÑÑÑ
luckperms.command.export.web.import-command-description=ÐÐ¸ÐºÐ¾ÑÐ¸ÑÑÐ°Ð¹ÑÐµ Ð½Ð°ÑÑÑÐ¿Ð½Ñ ÐºÐ¾Ð¼Ð°Ð½Ð´Ñ Ð´Ð»Ñ ÑÐ¼Ð¿Ð¾ÑÑÑ
luckperms.command.import.term=ÐÐ¼Ð¿Ð¾ÑÑÑÐ²Ð°ÑÐ¸
luckperms.command.import.error-term=ÐÐ¾Ð¼Ð¸Ð»ÐºÐ°
luckperms.command.import.already-running=ÐÐ° ÑÐµÐ¹ ÑÐ°Ñ Ð²Ð¸ÐºÐ¾Ð½ÑÑÑÑÑÑ ÑÐ½ÑÐ¸Ð¹ Ð¿ÑÐ¾ÑÐµÑ ÑÐ¼Ð¿Ð¾ÑÑÑÐ²Ð°Ð½Ð½Ñ
luckperms.command.import.file.doesnt-exist=Ð¤Ð°Ð¹Ð»Ñ {0} Ð½Ðµ ÑÑÐ½ÑÑ
luckperms.command.import.file.not-readable=Ð¤Ð°Ð¹Ð» {0} Ð½ÐµÐ´Ð¾ÑÑÑÐ¿Ð½Ð¸Ð¹ Ð´Ð»Ñ ÑÐ¸ÑÐ°Ð½Ð½Ñ
luckperms.command.import.file.unexpected-error-reading=Ð¢ÑÐ°Ð¿Ð¸Ð»Ð°ÑÑ Ð½ÐµÐ¾ÑÑÐºÑÐ²Ð°Ð½Ð° Ð¿Ð¾Ð¼Ð¸Ð»ÐºÐ° Ð¿ÑÐ´ ÑÐ°Ñ ÑÐ¸ÑÐ°Ð½Ð½Ñ Ð´Ð°Ð½Ð¸Ñ ÑÐ· ÑÐ°Ð¹Ð»Ñ ÑÐ¼Ð¿Ð¾ÑÑÑ
luckperms.command.import.file.correct-format=ÑÐ¸ Ð¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð¸Ð¹ ÑÐ¾ÑÐ¼Ð°Ñ?
luckperms.command.import.web.unable-to-read=ÐÐµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð¾ÑÑÐ¸Ð¼Ð°ÑÐ¸ ÑÐ½ÑÐ¾ÑÐ¼Ð°ÑÑÑ Ð· Ð²Ð¸ÐºÐ¾ÑÐ¸ÑÑÐ°Ð½Ð½ÑÐ¼ ÑÑÐ¾Ð³Ð¾ ÐºÐ¾Ð´Ñ
luckperms.command.import.progress.percent={0}% Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð¾
luckperms.command.import.progress.operations={0}/{1} Ð¾Ð¿ÐµÑÐ°ÑÑÐ¹ Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð¾
luckperms.command.import.starting=ÐÐ°Ð¿ÑÑÐº Ð¿ÑÐ¾ÑÐµÑÑ ÑÐ¼Ð¿Ð¾ÑÑÑ
luckperms.command.import.completed=ÐÐÐÐÐ Ð¨ÐÐÐ
luckperms.command.import.duration=Ð·Ð°Ð¹Ð½ÑÐ»Ð¾ {0} ÑÐµÐºÑÐ½Ð´
luckperms.command.bulkupdate.must-use-console=ÐÐ¸ÐºÐ¾Ð½Ð°Ð½Ð½Ñ ÐºÐ¾Ð¼Ð°Ð½Ð´Ð¸ Ð¼Ð°ÑÐ¾Ð²Ð¾Ð³Ð¾ Ð¾Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ Ð¼Ð¾Ð¶Ð»Ð¸Ð²Ðµ Ð»Ð¸ÑÐµ Ð· ÐºÐ¾Ð½ÑÐ¾Ð»Ñ
luckperms.command.bulkupdate.invalid-data-type=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð¸Ð¹ ÑÐ¸Ð¿, Ð¾ÑÐºÑÐ»ÑÐºÐ¸ Ð¾ÑÑÐºÑÐ²Ð°Ð»Ð¾ÑÑ {0}
luckperms.command.bulkupdate.invalid-constraint=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ðµ Ð¾Ð±Ð¼ÐµÐ¶ÐµÐ½Ð½Ñ {0}
luckperms.command.bulkupdate.invalid-constraint-format=ÐÐ±Ð¼ÐµÐ¶ÐµÐ½Ð½Ñ Ð¿Ð¾Ð²Ð¸Ð½Ð½Ñ Ð±ÑÑÐ¸ Ñ ÑÐ¾ÑÐ¼Ð°ÑÑ {0}
luckperms.command.bulkupdate.invalid-comparison=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð¸Ð¹ Ð¾Ð¿ÐµÑÐ°ÑÐ¾Ñ Ð¿Ð¾ÑÑÐ²Ð½ÑÐ½Ð½Ñ {0}
luckperms.command.bulkupdate.invalid-comparison-format=ÐÑÑÐºÑÐ²Ð°Ð»Ð¾ÑÑ Ð¾Ð´Ð½Ðµ Ð· Ð½Ð°Ð²ÐµÐ´ÐµÐ½Ð¸Ñ\: {0}
luckperms.command.bulkupdate.queued=ÐÐ¿ÐµÑÐ°ÑÑÑ Ð¼Ð°ÑÐ¾Ð²Ð¾Ð³Ð¾ Ð¾Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ Ð´Ð¾Ð´Ð°Ð½Ð° Ð² ÑÐµÑÐ³Ñ
luckperms.command.bulkupdate.confirm=ÐÐ²ÐµÐ´ÑÑÑ {0}, ÑÐ¾Ð± Ð²Ð¸ÐºÐ¾Ð½Ð°ÑÐ¸ Ð¾Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ
luckperms.command.bulkupdate.unknown-id=ÐÐ¿ÐµÑÐ°ÑÑÑ Ð· ID {0} Ð½Ðµ ÑÑÐ½ÑÑ Ð°Ð±Ð¾ ÑÑ Ð²ÑÐ´Ð»ÑÐº Ð¾ÑÑÐºÑÐ²Ð°Ð½Ð½Ñ Ð²Ð¸ÑÐµÑÐ¿Ð°Ð²ÑÑ
luckperms.command.bulkupdate.starting=ÐÐ¸ÐºÐ¾Ð½Ð°Ð½Ð½Ñ Ð¼Ð°ÑÐ¾Ð²Ð¾Ð³Ð¾ Ð¾Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ
luckperms.command.bulkupdate.success=ÐÑÐ¾ÑÐµÑ Ð¼Ð°ÑÐ¾Ð²Ð¾Ð³Ð¾ Ð¾Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ ÑÑÐ¿ÑÑÐ½Ð¾ Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð¸Ð¹
luckperms.command.bulkupdate.success.statistics.nodes=ÐÐ°ÑÐµÐ¿Ð»ÐµÐ½Ð¸Ñ Ð´Ð¾Ð·Ð²Ð¾Ð»ÑÐ² ÑÑÑÐ¾Ð³Ð¾
luckperms.command.bulkupdate.success.statistics.users=Ð£ÑÑÐ¾Ð³Ð¾ Ð·Ð°ÑÐµÐ¿Ð»ÐµÐ½Ð¸Ñ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÑÐ²
luckperms.command.bulkupdate.success.statistics.groups=Ð£ÑÑÐ¾Ð³Ð¾ Ð·Ð°ÑÐµÐ¿Ð»ÐµÐ½Ð¸Ñ Ð³ÑÑÐ¿
luckperms.command.bulkupdate.failure=ÐÐ° Ð¶Ð°Ð»Ñ, Ð½Ðµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð²Ð¸ÐºÐ¾Ð½Ð°ÑÐ¸ Ð¼Ð°ÑÐ¾Ð²Ðµ Ð¾Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ, Ð¿ÐµÑÐµÐ²ÑÑÑÐµ ÐºÐ¾Ð½ÑÐ¾Ð»Ñ Ð½Ð° Ð½Ð°ÑÐ²Ð½ÑÑÑÑ Ð¿Ð¾Ð¼Ð¸Ð»Ð¾Ðº
luckperms.command.update-task.request=ÐÐ°Ð¿Ð¸Ñ Ð½Ð° Ð¾Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ Ð¿ÑÐ¸Ð¹Ð½ÑÑÐ¾, Ð±ÑÐ´Ñ Ð»Ð°ÑÐºÐ°, Ð·Ð°ÑÐµÐºÐ°Ð¹ÑÐµ
luckperms.command.update-task.complete=ÐÐ½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ Ð´Ð°Ð½Ð¸Ñ Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð¾
luckperms.command.update-task.push.attempting=ÐÐ¸ÐºÐ¾Ð½ÑÑÐ¼Ð¾ ÑÐ¿ÑÐ¾Ð±Ñ Ð½Ð°Ð´ÑÐ¸Ð»Ð°Ð½Ð½Ñ Ð·Ð¼ÑÐ½ Ð½Ð° ÑÐ½ÑÑ ÑÐµÑÐ²ÐµÑÐ¸
luckperms.command.update-task.push.complete=ÐÐ½ÑÑ ÑÐµÑÐ²ÐµÑÐ¸ ÑÑÐ¿ÑÑÐ½Ð¾ Ð¾ÑÑÐ¸Ð¼Ð°Ð»Ð¸ ÑÐ¿Ð¾Ð²ÑÑÐµÐ½Ð½Ñ ÑÐµÑÐµÐ· {0}
luckperms.command.update-task.push.error=Ð¡ÑÐ°Ð»Ð°ÑÑ Ð¿Ð¾Ð¼Ð¸Ð»ÐºÐ° Ð¿ÑÐ´ ÑÐ°Ñ Ð½Ð°Ð´ÑÐ¸Ð»Ð°Ð½Ð½Ñ Ð·Ð¼ÑÐ½ Ð½Ð° ÑÐ½ÑÑ ÑÐµÑÐ²ÐµÑÐ¸
luckperms.command.update-task.push.error-not-setup=ÐÐµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð½Ð°Ð´ÑÑÐ»Ð°ÑÐ¸ Ð·Ð¼ÑÐ½Ð¸ Ð½Ð° ÑÐ½ÑÑ ÑÐµÑÐ²ÐµÑÐ¸, Ð¾ÑÐºÑÐ»ÑÐºÐ¸ ÑÐ¾Ð·Ð´ÑÐ» "messaging service" Ð½Ðµ Ð½Ð°Ð»Ð°ÑÑÐ¾Ð²Ð°Ð½Ð¸Ð¹
luckperms.command.reload-config.success=Ð¤Ð°Ð¹Ð» ÐºÐ¾Ð½ÑÑÐ³ÑÑÐ°ÑÑÑ Ð¿ÐµÑÐµÐ·Ð°Ð²Ð°Ð½ÑÐ°Ð¶ÐµÐ½Ð¾
luckperms.command.reload-config.restart-note=Ð´ÐµÑÐºÑ Ð½Ð°Ð»Ð°ÑÑÑÐ²Ð°Ð½Ð½Ñ Ð±ÑÐ´ÑÑÑ Ð·Ð°ÑÑÐ¾ÑÐ¾Ð²Ð°Ð½Ñ Ð»Ð¸ÑÐµ Ð¿ÑÑÐ»Ñ Ð¿ÐµÑÐµÐ·Ð°Ð²Ð°Ð½ÑÐ°Ð¶ÐµÐ½Ð½Ñ ÑÐµÑÐ²ÐµÑÑ
luckperms.command.translations.searching=ÐÐ¾ÑÑÐº Ð´Ð¾ÑÑÑÐ¿Ð½Ð¸Ñ Ð¿ÐµÑÐµÐºÐ»Ð°Ð´ÑÐ², Ð±ÑÐ´Ñ Ð»Ð°ÑÐºÐ°, Ð·Ð°ÑÐµÐºÐ°Ð¹ÑÐµ...
luckperms.command.translations.searching-error=ÐÐ° Ð¶Ð°Ð»Ñ, Ð½Ðµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð¾ÑÑÐ¸Ð¼Ð°ÑÐ¸ ÑÐ¿Ð¸ÑÐ¾Ðº Ð´Ð¾ÑÑÑÐ¿Ð½Ð¸Ñ Ð¿ÐµÑÐµÐºÐ»Ð°Ð´ÑÐ²
luckperms.command.translations.installed-translations=ÐÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ñ Ð¿ÐµÑÐµÐºÐ»Ð°Ð´Ð¸
luckperms.command.translations.available-translations=ÐÐ¾ÑÑÑÐ¿Ð½Ñ Ð¿ÐµÑÐµÐºÐ»Ð°Ð´Ð¸
luckperms.command.translations.percent-translated={0}% Ð¿ÐµÑÐµÐºÐ»Ð°Ð´ÐµÐ½Ð¾
luckperms.command.translations.translations-by=â 
luckperms.command.translations.installing=ÐÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ Ð¿ÐµÑÐµÐºÐ»Ð°Ð´ÑÐ², Ð±ÑÐ´Ñ Ð»Ð°ÑÐºÐ°, Ð·Ð°ÑÐµÐºÐ°Ð¹ÑÐµ...
luckperms.command.translations.download-error=ÐÐµ Ð²Ð´Ð°Ð»Ð¾ÑÑ Ð·Ð°Ð²Ð°Ð½ÑÐ°Ð¶Ð¸ÑÐ¸ Ð¿ÐµÑÐµÐºÐ»Ð°Ð´ Ð´Ð»Ñ {0}
luckperms.command.translations.installing-specific=ÐÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ Ð¼Ð¾Ð²Ð¸ {0}...
luckperms.command.translations.install-complete=Ð£ÑÑÐ°Ð½Ð¾Ð²ÐºÑ Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð¾
luckperms.command.translations.download-prompt=ÐÐ¸ÐºÐ¾ÑÐ¸ÑÑÐ°Ð¹ÑÐµ {0}, ÑÐ¾Ð± Ð·Ð°Ð²Ð°Ð½ÑÐ°Ð¶Ð¸ÑÐ¸ ÑÐ° ÑÐ½ÑÑÐ°Ð»ÑÐ²Ð°ÑÐ¸ Ð°ÐºÑÑÐ°Ð»ÑÐ½Ñ Ð²ÐµÑÑÑÑ Ð¿ÐµÑÐµÐºÐ»Ð°Ð´ÑÐ², Ð½Ð°Ð´Ð°Ð½Ð¸Ñ ÑÐ¿ÑÐ»ÑÐ½Ð¾ÑÐ¾Ñ
luckperms.command.translations.download-override-warning=ÐÑÐ´Ñ Ð»Ð°ÑÐºÐ°, Ð·Ð²ÐµÑÐ½ÑÑÑ ÑÐ²Ð°Ð³Ñ, ÑÐ¾ Ð²ÑÑ Ð·Ð¼ÑÐ½Ð¸, Ð²Ð½ÐµÑÐµÐ½Ñ Ð²Ð°Ð¼Ð¸ Ð´Ð»Ñ ÑÐ¸Ñ Ð¼Ð¾Ð², Ð±ÑÐ´ÑÑÑ Ð¿ÐµÑÐµÐ²Ð¸Ð·Ð½Ð°ÑÐµÐ½Ñ
luckperms.usage.user.description=ÐÐ°Ð±ÑÑ ÐºÐ¾Ð¼Ð°Ð½Ð´ Ð´Ð»Ñ ÐºÐµÑÑÐ²Ð°Ð½Ð½Ñ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°Ð¼Ð¸ Ð² LuckPerms (ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°Ñ Ð² LuckPerms - ÑÐµ Ð³ÑÐ°Ð²ÐµÑÑ, ÑÐ¾ Ð¼Ð¾Ð¶Ðµ Ð¿Ð¾ÑÐ¸Ð»Ð°ÑÐ¸ÑÑ Ð½Ð° UUID Ð°Ð±Ð¾ Ð½Ð° ÑÐ¼''Ñ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°)
luckperms.usage.group.description=ÐÐ°Ð±ÑÑ ÐºÐ¾Ð¼Ð°Ð½Ð´ Ð´Ð»Ñ ÐºÐµÑÑÐ²Ð°Ð½Ð½Ñ Ð³ÑÑÐ¿Ð°Ð¼Ð¸ Ð² LuckPerms. ÐÑÑÐ¿Ð¸ - ÑÐµ Ð·Ð³ÑÑÐ¿Ð¾Ð²Ð°Ð½Ñ Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸, ÑÐºÑ Ð¼Ð¾Ð¶ÑÑÑ Ð±ÑÑÐ¸ Ð²Ð¸Ð´Ð°Ð½Ñ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°Ð¼. ÐÐ¾Ð²Ñ Ð³ÑÑÐ¿Ð¸ Ð¼Ð¾Ð¶ÑÑÑ Ð±ÑÑÐ¸ ÑÑÐ²Ð¾ÑÐµÐ½Ñ Ð·Ð° Ð´Ð¾Ð¿Ð¾Ð¼Ð¾Ð³Ð¾Ñ ÐºÐ¾Ð¼Ð°Ð½Ð´Ð¸ "creategroup".
luckperms.usage.track.description=ÐÐ°Ð±ÑÑ ÐºÐ¾Ð¼Ð°Ð½Ð´ Ð´Ð»Ñ ÐºÐµÑÑÐ²Ð°Ð½Ð½Ñ ÑÑÐµÐºÐ°Ð¼Ð¸ Ð² Ð¼ÐµÐ¶Ð°Ñ LuckPerms. Ð¢ÑÐµÐºÐ¸ - ÑÐµ ÑÐ¿Ð¾ÑÑÐ´ÐºÐ¾Ð²Ð°Ð½Ð° Ð¿Ð¾ÑÐ»ÑÐ´Ð¾Ð²Ð½ÑÑÑÑ Ð³ÑÑÐ¿, ÑÐºÑ Ð²Ð¸ÐºÐ¾ÑÐ¸ÑÑÐ¾Ð²ÑÑÑÑÑÑ Ð´Ð»Ñ Ð¿ÑÐ´Ð²Ð¸ÑÐµÐ½Ð½Ñ ÑÐ¸ Ð·Ð½Ð¸Ð¶ÐµÐ½Ð½Ñ Ð³ÑÑÐ¿Ð¸.
luckperms.usage.log.description=ÐÐ°Ð±ÑÑ ÐºÐ¾Ð¼Ð°Ð½Ð´ Ð´Ð»Ñ ÐºÐµÑÑÐ²Ð°Ð½Ð½Ñ ÑÑÐ½ÐºÑÑÑÐ¼Ð¸ Ð»Ð¾Ð³ÑÐ²Ð°Ð½Ð½Ñ Ð² LuckPerms.
luckperms.usage.sync.description=ÐÐµÑÐµÐ·Ð°Ð²Ð°Ð½ÑÐ°Ð¶ÑÑ Ð²ÑÑ Ð´Ð°Ð½Ñ ÑÐ· Ð¿Ð»Ð°Ð³ÑÐ½ÑÐ² Ñ Ð¿Ð°Ð¼''ÑÑÑ Ñ Ð·Ð°ÑÑÐ¾ÑÐ¾Ð²ÑÑ Ð±ÑÐ´Ñ-ÑÐºÑ Ð·Ð¼ÑÐ½Ð¸, ÑÐºÑ Ð²Ð¸ÑÐ²Ð»ÐµÐ½Ð¾.
luckperms.usage.info.description=ÐÑÑÐºÑÑ Ð¾ÑÐ½Ð¾Ð²Ð½Ñ ÑÐ½ÑÐ¾ÑÐ¼Ð°ÑÑÑ Ð¿ÑÐ¾ Ð°ÐºÑÐ¸Ð²Ð½Ð¸Ð¹ ÐµÐºÐ·ÐµÐ¼Ð¿Ð»ÑÑ Ð¿Ð»Ð°Ð³ÑÐ½Ð°.
luckperms.usage.editor.description=Ð¡ÑÐ²Ð¾ÑÑÑ Ð½Ð¾Ð²Ñ ÑÐµÑÑÑ Ð²ÐµÐ±-ÑÐµÐ´Ð°ÐºÑÐ¾ÑÐ°
luckperms.usage.editor.argument.type=ÑÐ¸Ð¿Ð¸ Ð´Ð»Ñ Ð·Ð°Ð²Ð°Ð½ÑÐ°Ð¶ÐµÐ½Ð½Ñ Ð² ÑÐµÐ´Ð°ÐºÑÐ¾Ñ. (''all'', ''users'' Ð°Ð±Ð¾ ''groups'')
luckperms.usage.editor.argument.filter=Ð´Ð¾Ð·Ð²ÑÐ» Ð½Ð° ÑÑÐ»ÑÑÑÑÐ²Ð°Ð½Ð½Ñ Ð·Ð°Ð¿Ð¸ÑÑÐ² ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°
luckperms.usage.verbose.description=ÐÐµÑÑÐ²Ð°Ð½Ð½Ñ ÑÐ¸ÑÑÐµÐ¼Ð¾Ñ Ð¼Ð¾Ð½ÑÑÐ¾ÑÐ¸Ð½Ð³Ñ Ð´Ð¾Ð·Ð²Ð¾Ð»ÑÐ² Ð¿Ð»Ð°Ð³ÑÐ½ÑÐ².
luckperms.usage.verbose.argument.action=ÑÐ¾Ð± ÑÐ²ÑÐ¼ÐºÐ½ÑÑÐ¸/Ð²Ð¸Ð¼ÐºÐ½ÑÑÐ¸ Ð»Ð¾Ð³ÑÐ²Ð°Ð½Ð½Ñ ÑÐ¸ Ð·Ð°Ð²Ð°Ð½ÑÐ°Ð¶Ð¸ÑÐ¸
luckperms.usage.verbose.argument.filter=ÑÑÐ»ÑÑÑ, ÑÐ¾ Ð²ÑÐ´Ð¿Ð¾Ð²ÑÐ´Ð°Ñ Ð·Ð°Ð¿Ð¸ÑÐ°Ð¼
luckperms.usage.verbose.argument.commandas=Ð³ÑÐ°Ð²ÐµÑÑ/ÐºÐ¾Ð¼Ð°Ð½Ð´Ð° Ð´Ð»Ñ Ð²Ð¸ÐºÐ¾Ð½Ð°Ð½Ð½Ñ
luckperms.usage.tree.description=ÐÐµÐ½ÐµÑÑÑ Ð²Ð¸Ð´ Ð´ÐµÑÐµÐ²Ð° (ÑÐ¿Ð¾ÑÑÐ´ÐºÐ¾Ð²Ð°Ð½Ð° ÑÑÑÐ°ÑÑÑÑ ÑÐ¿Ð¸ÑÐºÑÐ²) ÑÑÑÑ Ð´Ð¾Ð·Ð²Ð¾Ð»ÑÐ² Ñ LuckPerms.
luckperms.usage.tree.argument.scope=ÐºÐ¾ÑÐµÐ½ÐµÐ²Ð¸Ð¹ ÐºÐ°ÑÐ°Ð»Ð¾Ð³ Ð´ÐµÑÐµÐ²Ð°. Ð£ÐºÐ°Ð¶ÑÑÑ "." ÑÐ¾Ð± Ð²Ð¸Ð±ÑÐ°ÑÐ¸ Ð²ÑÑ Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸
luckperms.usage.tree.argument.player=ÑÐ¼''Ñ Ð³ÑÐ°Ð²ÑÑ Ð´Ð»Ñ Ð¿ÐµÑÐµÐ²ÑÑÐºÐ¸
luckperms.usage.search.description=ÐÐ¾ÑÑÐº ÑÑÑÑ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÑÐ²/Ð³ÑÑÐ¿ Ð· Ð¿ÐµÐ²Ð½Ð¸Ð¼ Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¾Ð¼
luckperms.usage.search.argument.permission=Ð´Ð¾Ð·Ð²ÑÐ» Ð½Ð° Ð¿Ð¾ÑÑÐº
luckperms.usage.search.argument.page=ÑÑÐ¾ÑÑÐ½ÐºÐ° Ð´Ð»Ñ Ð¿ÐµÑÐµÐ³Ð»ÑÐ´Ñ
luckperms.usage.network-sync.description=Ð¡Ð¸Ð½ÑÑÐ¾Ð½ÑÐ·ÑÐ²Ð°ÑÐ¸ Ð·Ð¼ÑÐ½Ð¸ Ð·Ñ ÑÑÐ¾Ð²Ð¸ÑÐµÐ¼ Ñ Ð½Ð°Ð´ÑÑÐ»Ð°ÑÐ¸ Ð·Ð°Ð¿Ð¸Ñ ÑÐ½ÑÐ¸Ð¼ ÑÐµÑÐ²ÐµÑÐ°Ð¼ Ñ Ð¼ÐµÑÐµÐ¶Ñ ÑÐ¾Ð±Ð¸ÑÐ¸ ÑÐµ ÑÐ°Ð¼Ðµ
luckperms.usage.import.description=ÐÐ¼Ð¿Ð¾ÑÑÑÑ Ð´Ð°Ð½Ñ Ð· (Ð¿Ð¾Ð¿ÐµÑÐµÐ´Ð½ÑÐ¾ ÑÑÐ²Ð¾ÑÐµÐ½Ð¾Ð³Ð¾) ÑÐ°Ð¹Ð»Ñ ÐµÐºÑÐ¿Ð¾ÑÑÑ
luckperms.usage.import.argument.file=ÑÐ°Ð¹Ð», Ð· ÑÐºÐ¾Ð³Ð¾ Ð½ÐµÐ¾Ð±ÑÑÐ´Ð½Ð¾ Ð²Ð¸ÐºÐ¾Ð½Ð°ÑÐ¸ ÑÐ¼Ð¿Ð¾ÑÑ
luckperms.usage.import.argument.replace=Ð·Ð°Ð¼ÑÐ½Ð¸ÑÐ¸ Ð½Ð°ÑÐ²Ð½Ñ Ð´Ð°Ð½Ñ Ð·Ð°Ð¼ÑÑÑÑ ÑÑÐ½ÑÐ¾Ð³Ð¾ Ð¾Ð±''ÑÐ´Ð½Ð°Ð½Ð½Ñ
luckperms.usage.import.argument.upload=Ð²Ð¸Ð²Ð°Ð½ÑÐ°Ð¶Ð¸ÑÐ¸ Ð´Ð°Ð½Ñ Ð· Ð½Ð°ÑÐ²Ð½Ð¾Ð³Ð¾ ÑÐ°Ð¹Ð»Ñ ÐµÐºÑÐ¿Ð¾ÑÑÑ
luckperms.usage.export.description=ÐÐºÑÐ¿Ð¾ÑÑÑÐ²Ð°ÑÐ¸ Ð´Ð°Ð½Ñ Ð´Ð¾Ð·Ð²Ð¾Ð»ÑÐ² Ñ ÑÐ¿ÐµÑÑÐ°Ð»ÑÐ½Ð¸Ð¹ ÑÐ°Ð¹Ð» Ð´Ð»Ñ ÐµÐºÑÐ¿Ð¾ÑÑÑ. ÐÑÐ·Ð½ÑÑÐµ Ð·Ð°Ð²Ð´ÑÐºÐ¸ Ð½ÑÐ¾Ð¼Ñ Ð²Ð¸ Ð·Ð¼Ð¾Ð¶ÐµÑÐµ Ð²ÑÐ´Ð½Ð¾Ð²Ð¸ÑÐ¸ Ð´Ð°Ð½Ñ, ÑÐ¼Ð¿Ð¾ÑÑÑÐ²Ð°Ð²ÑÐ¸ Ð¹Ð¾Ð³Ð¾.
luckperms.usage.export.argument.file=ÑÐ°Ð¹Ð», Ñ ÑÐºÐ¸Ð¹ Ð½ÐµÐ¾Ð±ÑÑÐ´Ð½Ð¾ Ð²Ð¸ÐºÐ¾Ð½Ð°ÑÐ¸ ÐµÐºÑÐ¿Ð¾ÑÑ
luckperms.usage.export.argument.without-users=Ð½Ðµ ÐµÐºÑÐ¿Ð¾ÑÑÑÐ²Ð°ÑÐ¸ Ð´Ð°Ð½Ð¸Ñ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÑÐ²
luckperms.usage.export.argument.without-groups=Ð½Ðµ ÐµÐºÑÐ¿Ð¾ÑÑÑÐ²Ð°ÑÐ¸ Ð´Ð°Ð½Ñ Ð³ÑÑÐ¿Ð¸
luckperms.usage.export.argument.upload=ÐÐ¸Ð²Ð°Ð½ÑÐ°Ð¶Ð¸ÑÐ¸ Ð²ÑÑ Ð´Ð°Ð½Ñ Ð¿ÑÐ¾ Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸ Ð½Ð° Ð²ÐµÐ±ÑÐµÐ´Ð°ÐºÑÐ¾Ñ. ÐÑÐ·Ð½ÑÑÐµ Ð´Ð°Ð½Ñ Ð¼Ð¾Ð¶Ð½Ð° Ð±ÑÐ´Ðµ ÑÐ¼Ð¿Ð¾ÑÑÑÐ²Ð°ÑÐ¸.
luckperms.usage.reload-config.description=ÐÐµÑÐµÐ·Ð°Ð²Ð°Ð½ÑÐ°Ð¶Ð¸ÑÐ¸ Ð´ÐµÑÐºÑ Ð¿Ð°ÑÐ°Ð¼ÐµÑÑÐ¸ ÐºÐ¾Ð½ÑÑÐ³ÑÑÐ°ÑÑÑ
luckperms.usage.bulk-update.description=ÐÐ¸ÐºÐ¾Ð½Ð°ÑÐ¸ Ð·Ð°Ð¿Ð¸ÑÐ¸ Ð¼Ð°ÑÐ¾Ð²Ð¾Ð³Ð¾ Ð¾Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ Ð´Ð»Ñ Ð²ÑÑÑ Ð´Ð°Ð½Ð¸Ñ
luckperms.usage.bulk-update.argument.data-type=ÑÐ¸Ð¿ Ð´Ð°Ð½Ð¸Ñ, ÑÐ¾ Ð·Ð¼ÑÐ½ÑÑÑÑÑÑ (''all'', ''users'' Ð°Ð±Ð¾ ''groups'')
luckperms.usage.bulk-update.argument.action=Ð´ÑÑ, ÑÐºÑ Ð¼Ð¾Ð¶Ð½Ð° Ð²Ð¸ÐºÐ¾Ð½Ð°ÑÐ¸ Ð½Ð°Ð´ Ð´Ð°Ð½Ð¸Ð¼Ð¸ (''update'' ÑÐ¸ ''delete'')
luckperms.usage.bulk-update.argument.action-field=Ð¿Ð¾Ð»Ðµ Ð²Ð¸Ð±Ð¾ÑÑ Ð´ÑÑ. ÐÐ¾ÑÑÑÐ±Ð½Ðµ Ð»Ð¸ÑÐµ Ð´Ð»Ñ Ð¾Ð¿ÑÑÑ ''update'' (''permission'', ''server'' ÑÐ¸ ''world'')
luckperms.usage.bulk-update.argument.action-value=Ð·Ð½Ð°ÑÐµÐ½Ð½Ñ Ð´Ð»Ñ Ð·Ð°Ð¼ÑÐ½Ð¸. ÐÐ¸Ð¼Ð°Ð³Ð°ÑÑÑÑÑ Ð»Ð¸ÑÐµ Ð´Ð»Ñ Ð¾Ð¿ÑÑÑ ''update''.
luckperms.usage.bulk-update.argument.constraint=Ð¾Ð±Ð¼ÐµÐ¶ÐµÐ½Ð½Ñ, Ð½ÐµÐ¾Ð±ÑÑÐ´Ð½Ñ Ð´Ð»Ñ Ð¾Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ
luckperms.usage.translations.description=ÐÐµÑÑÐ²Ð°Ð½Ð½Ñ Ð¿ÐµÑÐµÐºÐ»Ð°Ð´Ð°Ð¼Ð¸
luckperms.usage.translations.argument.install=ÑÑÐ±ÐºÐ¾Ð¼Ð°Ð½Ð´Ð° Ð´Ð»Ñ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ Ð¿ÐµÑÐµÐºÐ»Ð°Ð´ÑÐ²
luckperms.usage.apply-edits.description=ÐÐ°ÑÑÐ¾ÑÐ¾Ð²ÑÑ Ð·Ð¼ÑÐ½Ð¸ Ð² Ð´Ð¾Ð·Ð²Ð¾Ð»Ð°Ñ, Ð²Ð¸ÐºÐ¾Ð½Ð°Ð½Ñ Ñ Ð²ÐµÐ±ÑÐµÐ´Ð°ÐºÑÐ¾ÑÑ
luckperms.usage.apply-edits.argument.code=ÑÐ½ÑÐºÐ°Ð»ÑÐ½Ð¸Ð¹ ÐºÐ¾Ð´ Ð´Ð»Ñ Ð·Ð°ÑÑÐ¾ÑÑÐ²Ð°Ð½Ð½Ñ Ð·Ð¼ÑÐ½
luckperms.usage.apply-edits.argument.target=ÑÑÐ¾ Ð·Ð°ÑÑÐ¾ÑÐ¾Ð²ÑÑ Ð·Ð¼ÑÐ½Ð¸ Ð´Ð°Ð½Ð¸Ñ
luckperms.usage.create-group.description=Ð¡ÑÐ²Ð¾ÑÐ¸ÑÐ¸ Ð½Ð¾Ð²Ñ Ð³ÑÑÐ¿Ñ
luckperms.usage.create-group.argument.name=ÑÐ¼''Ñ Ð³ÑÑÐ¿Ð¸
luckperms.usage.create-group.argument.weight=Ð²Ð°Ð³Ð° Ð³ÑÑÐ¿Ð¸
luckperms.usage.create-group.argument.display-name=Ð²ÑÐ´Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð° Ð½Ð°Ð·Ð²Ð° Ð³ÑÑÐ¿Ð¸
luckperms.usage.delete-group.description=ÐÐ¸Ð´Ð°Ð»Ð¸ÑÐ¸ Ð³ÑÑÐ¿Ñ
luckperms.usage.delete-group.argument.name=Ð½Ð°Ð·Ð²Ð° Ð³ÑÑÐ¿Ð¸
luckperms.usage.list-groups.description=Ð¡Ð¿Ð¸ÑÐ¾Ðº ÑÑÑÑ Ð³ÑÑÐ¿ Ð½Ð° Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ñ
luckperms.usage.create-track.description=Ð¡ÑÐ²Ð¾ÑÐ¸ÑÐ¸ Ð½Ð¾Ð²Ð¸Ð¹ ÑÑÐµÐº
luckperms.usage.create-track.argument.name=ÑÐ¼''Ñ ÑÑÐµÐºÑ
luckperms.usage.delete-track.description=ÐÐ¸Ð»ÑÑÐ¸ÑÐ¸ ÑÑÐµÐº
luckperms.usage.delete-track.argument.name=ÑÐ¼''Ñ ÑÑÐµÐºÑ
luckperms.usage.list-tracks.description=Ð¡Ð¿Ð¸ÑÐ¾Ðº ÑÑÑÑ ÑÑÐµÐºÑÐ² Ð½Ð° Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ñ
luckperms.usage.user-info.description=ÐÐ¾ÐºÐ°Ð·ÑÑ ÑÐ½ÑÐ¾ÑÐ¼Ð°ÑÑÑ Ð¿ÑÐ¾ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°
luckperms.usage.user-switchprimarygroup.description=ÐÐ¼ÑÐ½ÑÑ Ð¾ÑÐ½Ð¾Ð²Ð½Ñ Ð³ÑÑÐ¿Ñ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°
luckperms.usage.user-switchprimarygroup.argument.group=Ð³ÑÑÐ¿Ð°, ÑÐºÑ Ð¿Ð¾ÑÑÑÐ±Ð½Ð¾ Ð·Ð°Ð¼ÑÐ½Ð¸ÑÐ¸ Ð½Ð° Ð½Ð°ÑÐ²Ð½Ñ
luckperms.usage.user-promote.description=ÐÑÐ¾ÑÑÐ²Ð°Ñ Ð³ÑÑÐ¿Ñ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ° Ð´Ð°Ð»Ñ Ð¿Ð¾ ÑÑÐµÐºÑ
luckperms.usage.user-promote.argument.track=ÑÑÐµÐº Ð´Ð»Ñ Ð¿ÑÐ¾ÑÑÐ²Ð°Ð½Ð½Ñ Ð³ÑÑÐ¿Ð¸ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°
luckperms.usage.user-promote.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½Ð½Ð¾ Ð±ÑÑÐ¸ Ð²Ð¸ÐºÐ¾Ð½Ð°Ð½Ð¾ Ð¿ÑÐ¾ÑÑÐ²Ð°Ð½Ð½Ñ Ð³ÑÑÐ¿Ð¸ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°
luckperms.usage.user-promote.argument.dont-add-to-first=Ð¿ÑÐ¾ÑÑÐ²Ð°Ñ Ð³ÑÑÐ¿Ñ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°, ÑÐºÑÐ¾ Ð²ÑÐ½ ÑÐ¶Ðµ Ð¿Ð¾Ð²''ÑÐ·Ð°Ð½Ð¸Ð¹ Ð· ÑÑÐµÐºÐ¾Ð¼
luckperms.usage.user-demote.description=ÐÐ½Ð¸Ð¶ÑÑ Ð³ÑÑÐ¿Ñ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ° Ð½Ð° Ð¿Ð¾Ð¿ÐµÑÐµÐ´Ð½Ñ Ð² ÑÑÐµÑÑ
luckperms.usage.user-demote.argument.track=ÑÑÐµÐº Ð´Ð»Ñ Ð·Ð½Ð¸Ð¶ÐµÐ½Ð½Ñ Ð³ÑÑÐ¿Ð¸ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°
luckperms.usage.user-demote.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½Ð½Ð¾ Ð±ÑÑÐ¸ Ð²Ð¸ÐºÐ¾Ð½Ð°Ð½Ð¾ Ð·Ð½Ð¸Ð¶ÐµÐ½Ð½Ñ Ð³ÑÑÐ¿Ð¸ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°
luckperms.usage.user-demote.argument.dont-remove-from-first=Ð·Ð°Ð¿Ð¾Ð±ÑÐ³ÑÐ¸ Ð²Ð¸Ð»ÑÑÐµÐ½Ð½Ñ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ° Ð· Ð¿ÐµÑÑÐ¾Ñ Ð³ÑÑÐ¿Ð¸ ÑÑÐµÐºÑ Ð¿ÑÐ¸ ÑÑ Ð·Ð½Ð¸Ð¶ÐµÐ½Ð½Ñ
luckperms.usage.user-clone.description=Ð¡ÐºÐ¾Ð¿ÑÑÐ²Ð°ÑÐ¸ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°
luckperms.usage.user-clone.argument.user=ÑÐ¼''Ñ/uuid ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°, ÑÐ¾Ð± ÑÐºÐ¾Ð¿ÑÑÐ²Ð°ÑÐ¸ Ð¹Ð¾Ð³Ð¾
luckperms.usage.group-info.description=ÐÐ°Ð´Ð°Ñ ÑÐ½ÑÐ¾ÑÐ¼Ð°ÑÑÑ Ð¿ÑÐ¾ Ð³ÑÑÐ¿Ñ
luckperms.usage.group-listmembers.description=ÐÐ¾ÐºÐ°Ð·ÑÐ²Ð°ÑÐ¸ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÑÐ²/Ð³ÑÑÐ¿Ð¸, ÑÐ¾ Ð½Ð°ÑÐ»ÑÐ´ÑÑÑÑ Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸ Ð²ÑÐ´ ÑÑÑÑ Ð³ÑÑÐ¿Ð¸
luckperms.usage.group-listmembers.argument.page=ÑÑÐ¾ÑÑÐ½ÐºÐ° Ð´Ð»Ñ Ð¿ÐµÑÐµÐ³Ð»ÑÐ´Ñ
luckperms.usage.group-setweight.description=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÐ¸ Ð²Ð°Ð³Ñ Ð³ÑÑÐ¿Ð¸
luckperms.usage.group-setweight.argument.weight=Ð²Ð°Ð³Ñ, ÑÐºÑ Ð½ÐµÐ¾Ð±ÑÑÐ´Ð½Ð¾ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÐ¸ 
luckperms.usage.group-set-display-name.description=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÐ¸ Ð²ÑÐ´Ð¾Ð±ÑÐ°Ð¶ÑÐ²Ð°Ð½Ðµ ÑÐ¼''Ñ Ð³ÑÑÐ¿Ð¸
luckperms.usage.group-set-display-name.argument.name=ÑÐ¼''Ñ Ð´Ð»Ñ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ
luckperms.usage.group-set-display-name.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ð² ÑÐºÐ¸Ñ Ð¿Ð¾ÑÑÑÐ±Ð½Ð¾ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÐ¸ ÑÐ¼''Ñ
luckperms.usage.group-rename.description=ÐÐµÑÐµÐ¹Ð¼ÐµÐ½ÑÐ²Ð°ÑÐ¸ Ð³ÑÑÐ¿Ñ
luckperms.usage.group-rename.argument.name=Ð½Ð¾Ð²Ðµ ÑÐ¼''Ñ
luckperms.usage.group-clone.description=ÐÐ»Ð¾Ð½ÑÑ Ð³ÑÑÐ¿Ñ
luckperms.usage.group-clone.argument.name=ÑÐ¼''Ñ ÐºÐ»Ð¾Ð½Ð¾Ð²Ð°Ð½Ð¾Ñ Ð³ÑÑÐ¿Ð¸ Ð½Ð°
luckperms.usage.holder-editor.description=ÐÑÐ´ÐºÑÐ¸Ð²Ð°Ñ Ð²ÐµÐ±ÑÐµÐ´Ð°ÐºÑÐ¾Ñ Ð´Ð¾Ð·Ð²Ð¾Ð»ÑÐ²
luckperms.usage.holder-showtracks.description=ÐÐ¾ÐºÐ°Ð·ÑÑ ÑÐ¿Ð¸ÑÐ¾Ðº ÑÑÐµÐºÑÐ², Ñ ÑÐºÐ¸Ñ Ð·Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑÑ Ð¾Ð±''ÑÐºÑ
luckperms.usage.holder-clear.description=ÐÐ¸Ð»ÑÑÐ°Ñ Ð²ÑÑ Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸, Ð±Ð°ÑÑÐºÑÐ²ÑÑÐºÑ Ð³ÑÑÐ¿Ð¸ Ð¹ Ð¼ÐµÑÐ°
luckperms.usage.holder-clear.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸ Ð´Ð»Ñ ÑÑÐ»ÑÑÑÐ°ÑÑÑ
luckperms.usage.permission.description=Ð ÐµÐ´Ð°Ð³ÑÐ²Ð°ÑÐ¸ Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸
luckperms.usage.parent.description=Ð ÐµÐ´Ð°Ð³ÑÐ²Ð°ÑÐ¸ ÑÑÐ¿Ð°Ð´ÐºÑÐ²Ð°Ð½Ð½Ñ
luckperms.usage.meta.description=Ð ÐµÐ´Ð°Ð³ÑÐ²Ð°ÑÐ¸ Ð·Ð½Ð°ÑÐµÐ½Ð½Ñ Ð¼ÐµÑÐ°Ð´Ð°Ð½Ð¸Ñ
luckperms.usage.permission-info.description=ÐÐ¾ÐºÐ°Ð·ÑÑ ÑÐ¿Ð¸ÑÐ¾Ðº Ð´Ð¾Ð·Ð²Ð¾Ð»ÑÐ², ÑÐºÑ Ð¼Ð°Ñ Ð¾Ð±''ÑÐºÑ
luckperms.usage.permission-info.argument.page=ÑÑÐ¾ÑÑÐ½ÐºÐ° Ð´Ð»Ñ Ð¿ÐµÑÐµÐ³Ð»ÑÐ´Ñ
luckperms.usage.permission-info.argument.sort-mode=ÑÐº ÑÐ¾ÑÑÑÐ²Ð°ÑÐ¸ Ð·Ð°Ð¿Ð¸ÑÐ¸
luckperms.usage.permission-set.description=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð»ÑÑ Ð´Ð¾Ð·Ð²ÑÐ» Ð´Ð»Ñ Ð¾Ð±''ÑÐºÑÐ°
luckperms.usage.permission-set.argument.node=Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸ Ð´Ð»Ñ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ
luckperms.usage.permission-set.argument.value=Ð·Ð½Ð°ÑÐµÐ½Ð½Ñ Ð´Ð¾Ð·Ð²Ð¾Ð»Ñ
luckperms.usage.permission-set.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÑ Ð¿Ð¾ÑÑÑÐ±Ð½Ð¾ Ð´Ð¾Ð´Ð°ÑÐ¸ Ð´Ð¾Ð·Ð²ÑÐ»
luckperms.usage.permission-unset.description=Ð¡ÐºÐ°ÑÐ¾Ð²ÑÑ Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸ Ð² ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°
luckperms.usage.permission-unset.argument.node=Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸, ÑÐºÑ Ð½ÐµÐ¾Ð±ÑÑÐ´Ð½Ð¾ ÑÐºÐ°ÑÑÐ²Ð°ÑÐ¸
luckperms.usage.permission-unset.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð½ÐµÐ¾Ð±ÑÑÐ´Ð½Ð¾ Ð²Ð¸Ð»ÑÑÐ¸ÑÐ¸ Ð´Ð¾Ð·Ð²ÑÐ»
luckperms.usage.permission-settemp.description=Ð¢Ð¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¾ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÑÑ Ð´Ð¾Ð·Ð²ÑÐ» Ð´Ð»Ñ Ð¾Ð±''ÑÐºÑÐ°
luckperms.usage.permission-settemp.argument.node=Ð´Ð¾Ð·Ð²ÑÐ» Ð½Ð° Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ
luckperms.usage.permission-settemp.argument.value=Ð·Ð½Ð°ÑÐµÐ½Ð½Ñ Ð´Ð¾Ð·Ð²Ð¾Ð»Ñ
luckperms.usage.permission-settemp.argument.duration=ÑÐ°Ñ, Ð¿ÑÐ¾ÑÑÐ³Ð¾Ð¼ ÑÐºÐ¾Ð³Ð¾ Ð´ÑÑÑÐ¸Ð¼Ðµ Ð´Ð¾Ð·Ð²ÑÐ»
luckperms.usage.permission-settemp.argument.temporary-modifier=ÑÐº ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¸Ð¹ Ð´Ð¾Ð·Ð²ÑÐ» Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð·Ð°ÑÑÐ¾ÑÐ¾Ð²Ð°Ð½Ð¸Ð¼
luckperms.usage.permission-settemp.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÑ Ð¿Ð¾ÑÑÑÐ±Ð½Ð¾ Ð´Ð¾Ð´Ð°ÑÐ¸ Ð´Ð¾Ð·Ð²ÑÐ»
luckperms.usage.permission-unsettemp.description=Ð¡ÐºÐ°ÑÑÐ²Ð°ÑÐ¸ ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¸Ð¹ Ð´Ð¾Ð·Ð²ÑÐ» Ð´Ð»Ñ Ð¾Ð±''ÑÐºÑÐ°
luckperms.usage.permission-unsettemp.argument.node=Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸, ÑÐºÑ Ð½ÐµÐ¾Ð±ÑÑÐ´Ð½Ð¾ ÑÐºÐ°ÑÑÐ²Ð°ÑÐ¸
luckperms.usage.permission-unsettemp.argument.duration=ÑÑÐ¸Ð²Ð°Ð»ÑÑÑÑ Ð²ÑÐ´Ð½ÑÐ¼Ð°Ð½Ð½Ñ
luckperms.usage.permission-unsettemp.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð½ÐµÐ¾Ð±ÑÑÐ´Ð½Ð¾ Ð²Ð¸Ð»ÑÑÐ¸ÑÐ¸ Ð´Ð¾Ð·Ð²ÑÐ»
luckperms.usage.permission-check.description=ÐÐµÑÐµÐ²ÑÑÑÑ, ÑÐ¸ Ð² Ð¾Ð±''ÑÐºÑÐ° Ñ Ð¿ÐµÐ²Ð½Ð¸Ð¹ Ð´Ð¾Ð·Ð²ÑÐ»
luckperms.usage.permission-check.argument.node=Ð´Ð¾Ð·Ð²ÑÐ» Ð´Ð»Ñ Ð¿ÐµÑÐµÐ²ÑÑÐºÐ¸
luckperms.usage.permission-clear.description=ÐÐ¸Ð»ÑÑÐ°Ñ Ð²ÑÑ Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸
luckperms.usage.permission-clear.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸ Ð´Ð»Ñ ÑÑÐ»ÑÑÑÐ°ÑÑÑ
luckperms.usage.parent-info.description=ÐÐ¾ÐºÐ°Ð·ÑÑ ÑÐ¿Ð¸ÑÐ¾Ðº Ð³ÑÑÐ¿, ÑÐºÑ Ð½Ð°ÑÐ»ÑÐ´ÑÑÑÑ ÑÐµÐ¹ Ð¾Ð±''ÑÐºÑ
luckperms.usage.parent-info.argument.page=ÑÑÐ¾ÑÑÐ½ÐºÐ° Ð´Ð»Ñ Ð¿ÐµÑÐµÐ³Ð»ÑÐ´Ñ
luckperms.usage.parent-info.argument.sort-mode=ÑÐº ÑÐ¾ÑÑÑÐ²Ð°ÑÐ¸ Ð·Ð°Ð¿Ð¸ÑÐ¸
luckperms.usage.parent-set.description=ÐÐ¸Ð»ÑÑÐ°Ñ Ð²ÑÑ Ð±Ð°ÑÑÐºÑÐ²ÑÑÐºÑ Ð³ÑÑÐ¿Ð¸, ÑÐºÑ Ð²Ð¶Ðµ Ð½Ð°ÑÐ»ÑÐ´ÑÑ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°Ñ, Ñ Ð´Ð¾Ð´Ð°Ñ Ð²ÐºÐ°Ð·Ð°Ð½Ñ
luckperms.usage.parent-set.argument.group=Ð½Ð°Ð·Ð²Ð° Ð³ÑÑÐ¿Ð¸ Ð´Ð»Ñ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ
luckperms.usage.parent-set.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½Ð½Ð° Ð±ÑÑÐ¸ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð° Ð³ÑÑÐ¿Ð°
luckperms.usage.parent-add.description=ÐÐ¾Ð´Ð°Ñ Ð³ÑÑÐ¿Ñ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÑ Ð´Ð»Ñ Ð½Ð°ÑÐ»ÑÐ´ÑÐ²Ð°Ð½Ð½Ñ Ð´Ð¾Ð·Ð²Ð¾Ð»ÑÐ² Ð²ÑÐ´ Ð½ÐµÑ
luckperms.usage.parent-add.argument.group=Ð³ÑÑÐ¿Ð° Ð´Ð»Ñ Ð½Ð°ÑÐ»ÑÐ´ÑÐ²Ð°Ð½Ð½Ñ
luckperms.usage.parent-add.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½Ð½Ð° Ð½Ð°ÑÐ»ÑÐ´ÑÐ²Ð°ÑÐ¸ÑÑ Ð³ÑÑÐ¿Ð°
luckperms.usage.parent-remove.description=ÐÐ¸Ð»ÑÑÐ°Ñ ÑÐ°Ð½ÑÑÐµ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ñ Ð½Ð°ÑÐ»ÑÐ´ÑÐ²Ð°Ð½Ð½Ñ Ð³ÑÑÐ¿Ð¸
luckperms.usage.parent-remove.argument.group=Ð³ÑÑÐ¿Ð° Ð´Ð»Ñ Ð²Ð¸Ð»ÑÑÐµÐ½Ð½Ñ
luckperms.usage.parent-remove.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð³ÑÑÐ¿Ð° Ð¿Ð¾Ð²Ð¸Ð½Ð½Ð° Ð±ÑÑÐ¸ Ð²Ð¸Ð»ÑÑÐµÐ½Ð¾Ñ
luckperms.usage.parent-set-track.description=ÐÐ¸Ð»ÑÑÐ°Ñ Ð²ÑÑ Ð³ÑÑÐ¿Ð¸, ÑÐºÑ Ð²Ð¶Ðµ Ð½Ð°ÑÐ»ÑÐ´ÑÑ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°Ñ Ñ Ð²ÐºÐ°Ð·Ð°Ð½Ð¾Ð¼Ñ ÑÑÐµÑÑ, Ñ Ð´Ð¾Ð´Ð°Ñ Ð²Ð¸Ð±ÑÐ°Ð½Ñ
luckperms.usage.parent-set-track.argument.track=ÑÑÐµÐº Ð´Ð»Ñ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ
luckperms.usage.parent-set-track.argument.group=Ð½Ð°Ð·Ð²Ð° Ð³ÑÑÐ¿Ð¸ ÑÐ¸ Ð½Ð¾Ð¼ÐµÑ ÑÐ¾Ð·ÑÐ°ÑÑÐ²Ð°Ð½Ð½Ñ Ð³ÑÑÐ¿Ð¸ Ð² Ð´Ð°Ð½Ð¾Ð¼Ñ ÑÑÐµÑÑ Ð´Ð»Ñ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ
luckperms.usage.parent-set-track.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½Ð½Ð° Ð±ÑÑÐ¸ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð° Ð³ÑÑÐ¿Ð°
luckperms.usage.parent-add-temp.description=Ð¢Ð¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¾ Ð´Ð¾Ð´Ð°Ñ Ð³ÑÑÐ¿Ñ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÑ Ð´Ð»Ñ Ð½Ð°ÑÐ»ÑÐ´ÑÐ²Ð°Ð½Ð½Ñ Ð´Ð¾Ð·Ð²Ð¾Ð»ÑÐ² Ð²ÑÐ´ Ð½ÐµÑ
luckperms.usage.parent-add-temp.argument.group=Ð³ÑÑÐ¿Ð° Ð´Ð»Ñ Ð½Ð°ÑÐ»ÑÐ´ÑÐ²Ð°Ð½Ð½Ñ
luckperms.usage.parent-add-temp.argument.duration=ÑÑÐ¸Ð²Ð°Ð»ÑÑÑÑ ÑÐ»ÐµÐ½ÑÑÐ²Ð° Ð² Ð³ÑÑÐ¿Ñ
luckperms.usage.parent-add-temp.argument.temporary-modifier=ÑÐº Ð²Ð°ÑÑÐ¾ Ð·Ð°ÑÑÐ¾ÑÐ¾Ð²ÑÐ²Ð°ÑÐ¸ ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¸Ð¹ Ð´Ð¾Ð·Ð²ÑÐ»
luckperms.usage.parent-add-temp.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½Ð½Ð° Ð½Ð°ÑÐ»ÑÐ´ÑÐ²Ð°ÑÐ¸ÑÑ Ð³ÑÑÐ¿Ð°
luckperms.usage.parent-remove-temp.description=ÐÐ¸Ð»ÑÑÐ°Ñ ÑÐ°Ð½ÑÑÐµ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ñ Ð½Ð°ÑÐ»ÑÐ´ÑÐ²Ð°Ð½Ð½Ñ Ð³ÑÑÐ¿Ð¸
luckperms.usage.parent-remove-temp.argument.group=Ð³ÑÑÐ¿Ð° Ð´Ð»Ñ Ð²Ð¸Ð»ÑÑÐµÐ½Ð½Ñ
luckperms.usage.parent-remove-temp.argument.duration=ÑÑÐ¸Ð²Ð°Ð»ÑÑÑÑ Ð²ÑÐ´Ð½ÑÐ¼Ð°Ð½Ð½Ñ
luckperms.usage.parent-remove-temp.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð³ÑÑÐ¿Ð° Ð¿Ð¾Ð²Ð¸Ð½Ð½Ð° Ð±ÑÑÐ¸ Ð²Ð¸Ð»ÑÑÐµÐ½Ð¾Ñ
luckperms.usage.parent-clear.description=ÐÑÐ¸ÑÐ°Ñ Ð²ÑÑ Ð±Ð°ÑÑÐºÑÐ²ÑÑÐºÑ Ð³ÑÑÐ¿Ð¸
luckperms.usage.parent-clear.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸ Ð´Ð»Ñ ÑÑÐ»ÑÑÑÐ°ÑÑÑ
luckperms.usage.parent-clear-track.description=ÐÑÐ¸ÑÐ°Ñ Ð²ÑÑ Ð±Ð°ÑÑÐºÑÐ²ÑÑÐºÑ Ð³ÑÑÐ¿Ð¸, ÑÐºÑ Ð·Ð½Ð°ÑÐ¾Ð´ÑÑÑÑÑ Ð² ÑÑÐ¾Ð¼Ñ ÑÑÐµÑÑ
luckperms.usage.parent-clear-track.argument.track=ÑÑÐµÐº Ð´Ð»Ñ Ð²Ð¸Ð»ÑÑÐµÐ½Ð½Ñ
luckperms.usage.parent-clear-track.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸ Ð´Ð»Ñ ÑÑÐ»ÑÑÑÐ°ÑÑÑ
luckperms.usage.meta-info.description=ÐÐ¾ÐºÐ°Ð·ÑÑ Ð²ÑÑ Ð¼ÐµÑÐ°Ð´Ð°Ð½Ñ Ð´Ð»Ñ ÑÐ°ÑÑ
luckperms.usage.meta-set.description=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð»ÑÑ Ð·Ð½Ð°ÑÐµÐ½Ð½Ñ Ð´Ð»Ñ Ð¼ÐµÑÐ°
luckperms.usage.meta-set.argument.key=ÐºÐ»ÑÑ Ð´Ð»Ñ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ
luckperms.usage.meta-set.argument.value=Ð·Ð½Ð°ÑÐµÐ½Ð½Ñ Ð´Ð»Ñ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ
luckperms.usage.meta-set.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½Ð½Ð° Ð±ÑÑÐ¸ Ð´Ð¾Ð´Ð°Ð½Ð° Ð¼ÐµÑÐ°
luckperms.usage.meta-unset.description=Ð¡ÐºÐ°ÑÐ¾Ð²ÑÑ Ð·Ð½Ð°ÑÐµÐ½Ð½Ñ Ð¼ÐµÑÐ°
luckperms.usage.meta-unset.argument.key=ÐºÐ»ÑÑ Ð´Ð»Ñ Ð·Ð½ÑÑÑÑ
luckperms.usage.meta-unset.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½Ð½Ð° Ð±ÑÑÐ¸ Ð²Ð¸Ð´Ð°Ð»ÐµÐ½Ð° Ð¼ÐµÑÐ°
luckperms.usage.meta-settemp.description=Ð¢Ð¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¾ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÑÑ Ð·Ð½Ð°ÑÐµÐ½Ð½Ñ Ð¼ÐµÑÐ°
luckperms.usage.meta-settemp.argument.key=ÐºÐ»ÑÑ Ð´Ð»Ñ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ
luckperms.usage.meta-settemp.argument.value=Ð·Ð½Ð°ÑÐµÐ½Ð½Ñ Ð´Ð»Ñ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ
luckperms.usage.meta-settemp.argument.duration=Ð¿ÑÐ¾Ð¼ÑÐ¶Ð¾Ðº ÑÐ°ÑÑ Ð¿ÑÑÐ»Ñ ÑÐºÐ¾Ð³Ð¾ ÑÑÐ¸Ð²Ð°Ð»ÑÑÑÑ Ð¼ÐµÑÐ°Ð´Ð°Ð½Ð¸Ñ Ð·Ð°Ð²ÐµÑÑÐ¸ÑÑÑÑ
luckperms.usage.meta-settemp.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½Ð½Ð° Ð±ÑÑÐ¸ Ð´Ð¾Ð´Ð°Ð½Ð° Ð¼ÐµÑÐ°
luckperms.usage.meta-unsettemp.description=Ð¡ÐºÐ¸Ð´Ð°Ñ ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð½Ñ Ð¼ÐµÑÐ°
luckperms.usage.meta-unsettemp.argument.key=ÐºÐ»ÑÑ Ð´Ð»Ñ ÑÐºÐ¸Ð´Ð°Ð½Ð½Ñ
luckperms.usage.meta-unsettemp.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½Ð½Ð° Ð±ÑÑÐ¸ Ð²Ð¸Ð»ÑÑÐµÐ½Ð° Ð¼ÐµÑÐ°
luckperms.usage.meta-addprefix.description=ÐÐ¾Ð´Ð°Ñ Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-addprefix.argument.priority=Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑ, Ñ ÑÐºÐ¾Ð¼Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð´Ð¾Ð´Ð°Ð½Ð¸Ð¹ Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-addprefix.argument.prefix=Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-addprefix.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð´Ð¾Ð´Ð°Ð½Ð¸Ð¹ Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-addsuffix.description=ÐÐ¾Ð´Ð°Ñ ÑÑÑÑÐºÑ
luckperms.usage.meta-addsuffix.argument.priority=Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑ, Ñ ÑÐºÐ¾Ð¼Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð´Ð¾Ð´Ð°Ð½Ð¸Ð¹ ÑÑÑÑÐºÑ
luckperms.usage.meta-addsuffix.argument.suffix=ÑÑÑÑÐºÑ
luckperms.usage.meta-addsuffix.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð´Ð¾Ð´Ð°Ð½Ð¸Ð¹ ÑÑÑÑÐºÑ
luckperms.usage.meta-setprefix.description=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð»ÑÑ Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-setprefix.argument.priority=Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑ, Ñ ÑÐºÐ¾Ð¼Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¼ Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-setprefix.argument.prefix=Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-setprefix.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¹ Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-setsuffix.description=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð»ÑÑ ÑÑÑÑÐºÑ
luckperms.usage.meta-setsuffix.argument.priority=Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑ, Ñ ÑÐºÐ¾Ð¼Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¹ ÑÑÑÑÐºÑ
luckperms.usage.meta-setsuffix.argument.suffix=ÑÑÑÑÐºÑ
luckperms.usage.meta-setsuffix.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¹ ÑÑÑÑÐºÑ
luckperms.usage.meta-removeprefix.description=ÐÐ¸Ð»ÑÑÐ°Ñ Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-removeprefix.argument.priority=Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑ Ð²Ð¸Ð´Ð°Ð»ÐµÐ½Ð½Ñ Ð¿ÑÐµÑÑÐºÑÑ
luckperms.usage.meta-removeprefix.argument.prefix=Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-removeprefix.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð²Ð¸Ð»ÑÑÐµÐ½Ð¸Ð¹ Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-removesuffix.description=ÐÐ¸Ð»ÑÑÐ°Ñ ÑÑÑÑÐºÑ
luckperms.usage.meta-removesuffix.argument.priority=Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑ Ð²Ð¸Ð»ÑÑÐµÐ½Ð½Ñ ÑÑÑÑÐºÑÐ°
luckperms.usage.meta-removesuffix.argument.suffix=ÑÑÑÑÐºÑ
luckperms.usage.meta-removesuffix.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð²Ð¸Ð»ÑÑÐµÐ½Ð¸Ð¼ ÑÑÑÑÐºÑ
luckperms.usage.meta-addtemp-prefix.description=ÐÐ¾Ð´Ð°Ñ ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¸Ð¹ Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-addtemp-prefix.argument.priority=Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑ, Ð· ÑÐºÐ¸Ð¼ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð´Ð¾Ð´Ð°Ð½Ð¸Ð¼ Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-addtemp-prefix.argument.prefix=Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-addtemp-prefix.argument.duration=ÑÑÐ¸Ð²Ð°Ð»ÑÑÑÑ, Ð¿ÑÐ¾ÑÑÐ³Ð¾Ð¼ ÑÐºÐ¾Ñ Ð´ÑÑÑÐ¸Ð¼Ðµ Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-addtemp-prefix.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð´Ð¾Ð´Ð°Ð½Ð¸Ð¼ Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-addtemp-suffix.description=ÐÐ¾Ð´Ð°Ñ ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¸Ð¹ ÑÑÑÑÐºÑ
luckperms.usage.meta-addtemp-suffix.argument.priority=Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑ, Ñ ÑÐºÐ¾Ð¼Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð´Ð¾Ð´Ð°Ð½Ð¸Ð¹ ÑÑÑÑÐºÑ
luckperms.usage.meta-addtemp-suffix.argument.suffix=ÑÑÑÑÐºÑ
luckperms.usage.meta-addtemp-suffix.argument.duration=ÑÑÐ¸Ð²Ð°Ð»ÑÑÑÑ, Ð¿ÑÐ¾ÑÑÐ³Ð¾Ð¼ ÑÐºÐ¾Ñ Ð´ÑÑÑÐ¸Ð¼Ðµ ÑÑÑÑÐºÑ
luckperms.usage.meta-addtemp-suffix.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð´Ð¾Ð´Ð°Ð½Ð¸Ð¼ ÑÑÑÑÐºÑ
luckperms.usage.meta-settemp-prefix.description=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð»ÑÑ ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¸Ð¹ Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-settemp-prefix.argument.priority=Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑ, Ñ ÑÐºÐ¾Ð¼Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¼ Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-settemp-prefix.argument.prefix=Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-settemp-prefix.argument.duration=ÑÑÐ¸Ð²Ð°Ð»ÑÑÑÑ, Ð¿ÑÐ¾ÑÑÐ³Ð¾Ð¼ ÑÐºÐ¾Ñ Ð´ÑÑÑÐ¸Ð¼Ðµ Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-settemp-prefix.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¼ Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-settemp-suffix.description=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð»ÑÑ ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¸Ð¹ ÑÑÑÑÐºÑ
luckperms.usage.meta-settemp-suffix.argument.priority=Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑ, Ñ ÑÐºÐ¾Ð¼Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¼ ÑÑÑÑÐºÑ
luckperms.usage.meta-settemp-suffix.argument.suffix=ÑÑÑÑÐºÑ
luckperms.usage.meta-settemp-suffix.argument.duration=ÑÑÐ¸Ð²Ð°Ð»ÑÑÑÑ, Ð¿ÑÐ¾ÑÑÐ³Ð¾Ð¼ ÑÐºÐ¾Ñ Ð´ÑÑÑÐ¸Ð¼Ðµ ÑÑÑÑÐºÑ
luckperms.usage.meta-settemp-suffix.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¼ ÑÑÑÑÐºÑ
luckperms.usage.meta-removetemp-prefix.description=ÐÐ¸Ð»ÑÑÐ°Ñ ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¸Ð¹ Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-removetemp-prefix.argument.priority=Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑ, ÑÐºÐ¸Ð¹ Ð¼Ð°Ñ Ð¼Ð¾Ð¶Ð»Ð¸Ð²ÑÑÑÑ Ð²Ð¸Ð»ÑÑÐµÐ½Ð½Ñ Ð¿ÑÐµÑÑÐºÑÑ
luckperms.usage.meta-removetemp-prefix.argument.prefix=Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-removetemp-prefix.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð²Ð¸Ð´Ð°Ð»ÐµÐ½Ð¸Ð¼ Ð¿ÑÐµÑÑÐºÑ
luckperms.usage.meta-removetemp-suffix.description=ÐÐ¸Ð»ÑÑÐ°Ñ ÑÐ¸Ð¼ÑÐ°ÑÐ¾Ð²Ð¸Ð¹ ÑÑÑÑÐºÑ
luckperms.usage.meta-removetemp-suffix.argument.priority=Ð¿ÑÑÐ¾ÑÐ¸ÑÐµÑ, ÑÐºÐ¸Ð¹ Ð¼Ð°Ñ Ð¼Ð¾Ð¶Ð»Ð¸Ð²ÑÑÑÑ Ð²Ð¸Ð»ÑÑÐµÐ½Ð½Ñ ÑÑÑÑÐºÑÐ°
luckperms.usage.meta-removetemp-suffix.argument.suffix=ÑÑÑÑÐºÑ
luckperms.usage.meta-removetemp-suffix.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð²Ð¸Ð»ÑÑÐµÐ½Ð¸Ð¼ ÑÑÑÑÐºÑ
luckperms.usage.meta-clear.description=ÐÐ¸Ð»ÑÑÐ°Ñ Ð²ÑÑ Ð¼ÐµÑÐ° Ð·Ð½Ð°ÑÐµÐ½Ð½Ñ
luckperms.usage.meta-clear.argument.type=ÑÐ¸Ð¿ Ð¼ÐµÑÐ° Ð´Ð»Ñ Ð²Ð¸Ð»ÑÑÐµÐ½Ð½Ñ
luckperms.usage.meta-clear.argument.context=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸ Ð´Ð»Ñ ÑÑÐ»ÑÑÑÐ°ÑÑÑ
luckperms.usage.track-info.description=ÐÐ°Ñ ÑÐ½ÑÐ¾ÑÐ¼Ð°ÑÑÑ Ð¿ÑÐ¾ ÑÑÐµÐº
luckperms.usage.track-editor.description=Ð Ð¾Ð·Ð³Ð¾ÑÑÐ°Ñ Ð²ÐµÐ±ÑÐµÐ´Ð°ÐºÑÐ¾Ñ Ð´Ð¾Ð·Ð²Ð¾Ð»ÑÐ²
luckperms.usage.track-append.description=ÐÐ¾Ð´Ð°Ñ Ð³ÑÑÐ¿Ñ Ð² ÐºÑÐ½ÐµÑÑ ÑÑÐµÐºÑ
luckperms.usage.track-append.argument.group=Ð³ÑÑÐ¿Ð°, ÑÐºÑ Ð¿Ð¾ÑÑÑÐ±Ð½Ð¾ Ð´Ð¾Ð´Ð°ÑÐ¸
luckperms.usage.track-insert.description=ÐÐ¾Ð´Ð°Ñ Ð³ÑÑÐ¿Ñ ÑÐ· Ð·Ð°Ð´Ð°Ð½Ð¾Ñ Ð¿Ð¾Ð·Ð¸ÑÑÑÑ Ð² ÑÑÐµÐº
luckperms.usage.track-insert.argument.group=Ð³ÑÑÐ¿Ð°, ÑÐºÑ Ð¿Ð¾ÑÑÑÐ±Ð½Ð¾ Ð²ÑÑÐ°Ð²Ð¸ÑÐ¸
luckperms.usage.track-insert.argument.position=Ð¿Ð¾Ð·Ð¸ÑÑÑ Ð´Ð»Ñ Ð²ÑÑÐ°Ð²Ð»ÐµÐ½Ð½Ñ Ð³ÑÑÐ¿Ð¸(Ð¿Ð¾ÑÐ¸Ð½Ð°ÑÑÐ¸ ÑÐ· 1)
luckperms.usage.track-remove.description=ÐÐ¸Ð»ÑÑÐ°Ñ Ð³ÑÑÐ¿Ñ ÑÐ· ÑÑÐµÐºÑ
luckperms.usage.track-remove.argument.group=Ð³ÑÑÐ¿Ð° Ð´Ð»Ñ Ð²Ð¸Ð»ÑÑÐµÐ½Ð½Ñ
luckperms.usage.track-clear.description=ÐÐ¸Ð»ÑÑÐ°Ñ ÑÐ¿Ð¸ÑÐ¾Ðº Ð³ÑÑÐ¿ Ñ ÑÑÐµÑÑ
luckperms.usage.track-rename.description=ÐÐµÑÐµÐ¹Ð¼ÐµÐ½ÑÐ²Ð°ÑÐ¸ ÑÑÐµÐº
luckperms.usage.track-rename.argument.name=Ð½Ð¾Ð²Ðµ ÑÐ¼''Ñ
luckperms.usage.track-clone.description=ÐÐ»Ð¾Ð½ÑÐ²Ð°ÑÐ¸ ÑÑÐµÐº
luckperms.usage.track-clone.argument.name=Ð½Ð°Ð·Ð²Ð° ÑÑÐµÐºÑ, Ñ ÑÐºÐ¸Ð¹ Ð½ÐµÐ¾Ð±ÑÑÐ´Ð½Ð¾ Ð²Ð¸ÐºÐ¾Ð½Ð°ÑÐ¸ ÐºÐ»Ð¾Ð½ÑÐ²Ð°Ð½Ð½Ñ
luckperms.usage.log-recent.description=ÐÐµÑÐµÐ³Ð»ÑÐ´ Ð¾ÑÑÐ°Ð½Ð½ÑÑ Ð´ÑÐ¹
luckperms.usage.log-recent.argument.user=ÑÐ¼''Ñ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°/uuid Ð´Ð»Ñ ÑÑÐ»ÑÑÑÐ°ÑÑÑ Ð¿Ð¾
luckperms.usage.log-recent.argument.page=Ð½Ð¾Ð¼ÐµÑ ÑÑÐ¾ÑÑÐ½ÐºÐ¸ Ð´Ð»Ñ Ð¿ÐµÑÐµÐ³Ð»ÑÐ´Ñ
luckperms.usage.log-search.description=ÐÐ¾ÑÑÐº Ð·Ð°Ð¿Ð¸ÑÑ Ð² Ð»Ð¾Ð·Ñ
luckperms.usage.log-search.argument.query=Ð·Ð°Ð¿Ð¸Ñ Ð´Ð»Ñ Ð¿Ð¾ÑÑÐºÑ
luckperms.usage.log-search.argument.page=Ð½Ð¾Ð¼ÐµÑ ÑÑÐ¾ÑÑÐ½ÐºÐ¸ Ð´Ð»Ñ Ð¿ÐµÑÐµÐ³Ð»ÑÐ´Ñ
luckperms.usage.log-notify.description=Ð£Ð²ÑÐ¼ÐºÐ½ÑÑÐ¸ ÑÐ¿Ð¾Ð²ÑÑÐµÐ½Ð½Ñ Ð¿ÑÐ¾ Ð»Ð¾Ð³ÑÐ²Ð°Ð½Ð½Ñ
luckperms.usage.log-notify.argument.toggle=ÑÐ²ÑÐ¼ÐºÐ½ÑÑÐ¸/Ð²Ð¸Ð¼ÐºÐ½ÑÑÐ¸
luckperms.usage.log-user-history.description=ÐÐµÑÐµÐ³Ð»ÑÐ´ ÑÑÑÐ¾ÑÑÑ ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°
luckperms.usage.log-user-history.argument.user=ÑÐ¼''Ñ/uuid ÐºÐ¾ÑÐ¸ÑÑÑÐ²Ð°ÑÐ°
luckperms.usage.log-user-history.argument.page=Ð½Ð¾Ð¼ÐµÑ ÑÑÐ¾ÑÑÐ½ÐºÐ¸ Ð´Ð»Ñ Ð¿ÐµÑÐµÐ³Ð»ÑÐ´Ñ
luckperms.usage.log-group-history.description=ÐÐµÑÐµÐ³Ð»ÑÐ´ ÑÑÑÐ¾ÑÑÑ Ð´Ð»Ñ Ð³ÑÑÐ¿Ð¸
luckperms.usage.log-group-history.argument.group=ÑÐ¼''Ñ Ð³ÑÑÐ¿Ð¸
luckperms.usage.log-group-history.argument.page=Ð½Ð¾Ð¼ÐµÑ ÑÑÐ¾ÑÑÐ½ÐºÐ¸ Ð´Ð»Ñ Ð¿ÐµÑÐµÐ³Ð»ÑÐ´Ñ
luckperms.usage.log-track-history.description=ÐÐµÑÐµÐ³Ð»ÑÐ´ ÑÑÑÐ¾ÑÑÑ Ð´Ð»Ñ ÑÑÐµÐºÑ
luckperms.usage.log-track-history.argument.track=ÑÐ¼''Ñ ÑÑÐµÐºÑ
luckperms.usage.log-track-history.argument.page=Ð½Ð¾Ð¼ÐµÑ ÑÑÐ¾ÑÑÐ½ÐºÐ¸ Ð´Ð»Ñ Ð¿ÐµÑÐµÐ³Ð»ÑÐ´Ñ
luckperms.usage.sponge.description=Ð ÐµÐ´Ð°Ð³ÑÐ²Ð°ÑÐ¸ Ð´Ð¾Ð´Ð°ÑÐºÐ¾Ð²Ñ Ð´Ð°Ð½Ñ Sponge
luckperms.usage.sponge.argument.collection=Ð½Ð°Ð±ÑÑ Ð´Ð»Ñ Ð·Ð°Ð¿Ð¸ÑÑ
luckperms.usage.sponge.argument.subject=ÑÐµÐ¼Ð°, ÑÐ¾ Ð·Ð¼ÑÐ½ÑÑÑÑÑÑ
luckperms.usage.sponge-permission-info.description=ÐÐ¾ÐºÐ°Ð·ÑÑ ÑÐ½ÑÐ¾ÑÐ¼Ð°ÑÑÑ Ð¿ÑÐ¾ Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸ ÑÑÐ±''ÑÐºÑÑ
luckperms.usage.sponge-permission-info.argument.contexts=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸ Ð´Ð»Ñ ÑÑÐ»ÑÑÑÐ°ÑÑÑ
luckperms.usage.sponge-permission-set.description=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð»ÑÑ Ð´Ð¾Ð·Ð²ÑÐ» Ð´Ð»Ñ ÑÑÐ±''ÑÐºÑÐ°
luckperms.usage.sponge-permission-set.argument.node=Ð´Ð¾Ð·Ð²ÑÐ» Ð½Ð° Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ
luckperms.usage.sponge-permission-set.argument.tristate=Ð·Ð½Ð°ÑÐµÐ½Ð½Ñ Ð´Ð»Ñ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ Ð´Ð¾Ð·Ð²Ð¾Ð»Ñ
luckperms.usage.sponge-permission-set.argument.contexts=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¹ Ð´Ð¾Ð·Ð²ÑÐ»
luckperms.usage.sponge-permission-clear.description=ÐÐ¸Ð»ÑÑÐ°Ñ Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸ ÑÑÐ±''ÑÐºÑÐ°
luckperms.usage.sponge-permission-clear.argument.contexts=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½Ð½Ñ Ð±ÑÑÐ¸ Ð²Ð¸Ð»ÑÑÐµÐ½Ñ Ð´Ð¾Ð·Ð²Ð¾Ð»Ð¸
luckperms.usage.sponge-parent-info.description=ÐÐ¾ÐºÐ°Ð·ÑÑ ÑÐ½ÑÐ¾ÑÐ¼Ð°ÑÑÑ Ð¿ÑÐ¾ Ð±Ð°ÑÑÐºÑÐ²ÑÑÐºÑ Ð³ÑÑÐ¿Ð¸ ÑÑÐ±''ÑÐºÑÐ°
luckperms.usage.sponge-parent-info.argument.contexts=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸ Ð´Ð»Ñ ÑÑÐ»ÑÑÑÐ°ÑÑÑ
luckperms.usage.sponge-parent-add.description=ÐÐ¾Ð´Ð°Ñ Ð±Ð°ÑÑÐºÑÐ²ÑÑÐºÑ Ð³ÑÑÐ¿Ñ ÑÑÐ±''ÑÐºÑÐ°
luckperms.usage.sponge-parent-add.argument.collection=ÐºÐ¾Ð»ÐµÐºÑÑÑ ÑÑÐ±''ÑÐºÑÐ°, Ð´Ðµ Ð±Ð°ÑÑÐºÑÐ²ÑÑÐºÐ¸Ð¼ ÐµÐ»ÐµÐ¼ÐµÐ½ÑÐ¾Ð¼ Ñ ÑÑÐ±''ÑÐºÑ
luckperms.usage.sponge-parent-add.argument.subject=ÑÐ¼''Ñ Ð±Ð°ÑÑÐºÑÐ²ÑÑÐºÐ¾Ð³Ð¾ ÑÑÐ±''ÑÐºÑÐ°
luckperms.usage.sponge-parent-add.argument.contexts=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½Ð½Ð° Ð±ÑÑÐ¸ Ð´Ð¾Ð´Ð°Ð½Ð° Ð±Ð°ÑÑÐºÑÐ²ÑÑÐºÐ° Ð³ÑÑÐ¿Ð°
luckperms.usage.sponge-parent-remove.description=ÐÐ¸Ð»ÑÑÐ°Ñ Ð±Ð°ÑÑÐºÑÐ²ÑÑÐºÑ Ð³ÑÑÐ¿Ñ Ð² ÑÑÐ±''ÑÐºÑÐ°
luckperms.usage.sponge-parent-remove.argument.collection=ÐºÐ¾Ð»ÐµÐºÑÑÑ ÑÑÐ±''ÑÐºÑÐ°, Ð´Ðµ Ð±Ð°ÑÑÐºÑÐ²ÑÑÐºÐ¸Ð¼ ÐµÐ»ÐµÐ¼ÐµÐ½ÑÐ¾Ð¼ Ñ ÑÑÐ±''ÑÐºÑ
luckperms.usage.sponge-parent-remove.argument.subject=ÑÐ¼''Ñ Ð±Ð°ÑÑÐºÑÐ²ÑÑÐºÐ¾Ð³Ð¾ ÑÑÐ±''ÑÐºÑÐ°
luckperms.usage.sponge-parent-remove.argument.contexts=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½Ð½Ñ Ð±ÑÑÐ¸ Ð²Ð¸Ð»ÑÑÐµÐ½Ñ Ð±Ð°ÑÑÐºÑÐ²ÑÑÐºÑ Ð³ÑÑÐ¿Ð¸
luckperms.usage.sponge-parent-clear.description=ÐÑÐ¸ÑÐ°Ñ Ð±Ð°ÑÑÐºÑÐ²ÑÑÐºÑ Ð³ÑÑÐ¿Ð¸ ÑÑÐ±''ÑÐºÑÐ°
luckperms.usage.sponge-parent-clear.argument.contexts=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½Ð½Ñ Ð±ÑÑÐ¸ Ð²Ð¸Ð»ÑÑÐµÐ½Ñ Ð±Ð°ÑÑÐºÑÐ²ÑÑÐºÑ Ð³ÑÑÐ¿Ð¸
luckperms.usage.sponge-option-info.description=ÐÐ¾ÐºÐ°Ð·ÑÑ ÑÐ½ÑÐ¾ÑÐ¼Ð°ÑÑÑ Ð¿ÑÐ¾ Ð¿Ð°ÑÐ°Ð¼ÐµÑÑÐ¸ ÑÑÐ±''ÑÐºÑÐ°
luckperms.usage.sponge-option-info.argument.contexts=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸ Ð´Ð»Ñ ÑÑÐ»ÑÑÑÐ°ÑÑÑ
luckperms.usage.sponge-option-set.description=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð»ÑÑ Ð¿Ð°ÑÐ°Ð¼ÐµÑÑ Ð´Ð»Ñ ÑÑÐ±''ÑÐºÑÐ°
luckperms.usage.sponge-option-set.argument.key=ÐºÐ»ÑÑ Ð´Ð»Ñ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ
luckperms.usage.sponge-option-set.argument.value=Ð·Ð½Ð°ÑÐµÐ½Ð½Ñ ÐºÐ»ÑÑÐ° Ð´Ð»Ñ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ñ
luckperms.usage.sponge-option-set.argument.contexts=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½ÐµÐ½ Ð±ÑÑÐ¸ Ð²ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¹ Ð¿Ð°ÑÐ°Ð¼ÐµÑÑ
luckperms.usage.sponge-option-unset.description=Ð¡ÐºÐ°ÑÐ¾Ð²ÑÑ Ð¿Ð°ÑÐ°Ð¼ÐµÑÑÐ¸ Ð² ÑÑÐ±''ÑÐºÑÐ°
luckperms.usage.sponge-option-unset.argument.key=ÐºÐ»ÑÑ Ð´Ð»Ñ ÑÐºÐ¸Ð´Ð°Ð½Ð½Ñ
luckperms.usage.sponge-option-unset.argument.contexts=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½Ð½Ñ Ð±ÑÑÐ¸ ÑÐºÐ°ÑÐ¾Ð²Ð°Ð½Ñ ÐºÐ»ÑÑÑ
luckperms.usage.sponge-option-clear.description=ÐÐ¸Ð»ÑÑÐ°Ñ Ð¾Ð¿ÑÑÑ ÑÑÐ±''ÑÐºÑÑÐ²
luckperms.usage.sponge-option-clear.argument.contexts=ÐºÐ¾Ð½ÑÐµÐºÑÑÐ¸, Ñ ÑÐºÐ¸Ñ Ð¿Ð¾Ð²Ð¸Ð½Ð½Ñ Ð±ÑÑÐ¸ Ð²Ð¸Ð»ÑÑÐµÐ½Ñ Ð¾Ð¿ÑÑÑ
