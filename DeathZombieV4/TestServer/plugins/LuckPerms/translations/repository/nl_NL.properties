luckperms.logs.actionlog-prefix=LOGBOEK
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=EXPORTEREN
luckperms.commandsystem.available-commands=Gebruik {0} om beschikbare commando''s te bekijken
luckperms.commandsystem.command-not-recognised=Commando niet herkend
luckperms.commandsystem.no-permission=Je hebt geen toestemming om dit commando te gebruiken\!
luckperms.commandsystem.no-permission-subcommands=Je hebt geen toestemming om dit sub commando te gebruiken
luckperms.commandsystem.already-executing-command=Een ander commando wordt uitgevoerd, wacht tot het klaar is...
luckperms.commandsystem.usage.sub-commands-header=Sub Commando''s
luckperms.commandsystem.usage.usage-header=Commando Gebruik
luckperms.commandsystem.usage.arguments-header=Argumenten
luckperms.first-time.no-permissions-setup=Het lijkt erop dat er nog geen permissies zijn ingesteld\!
luckperms.first-time.use-console-to-give-access=Voordat je een van de LuckPerms commando''s kunt gebruiken in het spel, moet je de console gebruiken om jezelf toegang te geven
luckperms.first-time.console-command-prompt=Open je console en voer uit
luckperms.first-time.next-step=Nadat je dit gedaan hebt kan je beginnen met het instellen van de permissies en groepen
luckperms.first-time.wiki-prompt=Weet je niet waar je moet beginnen? Bekijk het hier\: {0}
luckperms.login.try-again=Probeer het later opnieuw
luckperms.login.loading-database-error=Er is een database fout opgetreden tijdens het inladen van permissiegegevens
luckperms.login.server-admin-check-console-errors=Als u een serverbeheerder bent, controleer dan de console op eventuele foutmeldingen
luckperms.login.server-admin-check-console-info=Controleer de server console voor meer informatie
luckperms.login.data-not-loaded-at-pre=Permissiegegevens voor uw gebruiker zijn niet geladen tijdens het pre-login stadium
luckperms.login.unable-to-continue=niet mogelijk om verder te gaan
luckperms.login.craftbukkit-offline-mode-error=dit is waarschijnlijk het gevolg van een conflict tussen CraftBukkit en de online-modus instelling
luckperms.login.unexpected-error=Er is een onbekende fout opgetreden tijdens het instellen van de permissies
luckperms.opsystem.disabled=Het vanilla OP systeem is uitgeschakeld op deze server
luckperms.opsystem.sponge-warning=Houd er rekening mee dat de Server Operator status geen effect heeft op de Sponge toestemming controles wanneer een permissie-plugin is geÃ¯nstalleerd, u moet de gebruikersgegevens direct bewerken
luckperms.duration.unit.years.plural={0} jaren
luckperms.duration.unit.years.singular={0} jaar
luckperms.duration.unit.years.short={0}j
luckperms.duration.unit.months.plural={0} maanden
luckperms.duration.unit.months.singular={0} maand
luckperms.duration.unit.months.short={0}mnd
luckperms.duration.unit.weeks.plural={0} weken
luckperms.duration.unit.weeks.singular={0} week
luckperms.duration.unit.weeks.short={0}w
luckperms.duration.unit.days.plural={0} dagen
luckperms.duration.unit.days.singular={0} dag
luckperms.duration.unit.days.short={0}d
luckperms.duration.unit.hours.plural={0} uren
luckperms.duration.unit.hours.singular={0} uur
luckperms.duration.unit.hours.short={0}u
luckperms.duration.unit.minutes.plural={0} minuten
luckperms.duration.unit.minutes.singular={0} minuut
luckperms.duration.unit.minutes.short={0}min
luckperms.duration.unit.seconds.plural={0} seconden
luckperms.duration.unit.seconds.singular={0} seconde
luckperms.duration.unit.seconds.short={0}s
luckperms.duration.since={0} geleden
luckperms.command.misc.invalid-code=Ongeldige code
luckperms.command.misc.response-code-key=response code
luckperms.command.misc.error-message-key=bericht
luckperms.command.misc.bytebin-unable-to-communicate=Kan niet communiceren met bytebin
luckperms.command.misc.webapp-unable-to-communicate=Kan niet communiceren met de web-app
luckperms.command.misc.check-console-for-errors=Controleer de console op foutmeldingen
luckperms.command.misc.file-must-be-in-data=Bestand {0} moet een direct onderdeel zijn van de gegevensmap
luckperms.command.misc.wait-to-finish=Wacht tot het voltooid is en probeer het opnieuw
luckperms.command.misc.invalid-priority=Ongeldige prioriteit {0}
luckperms.command.misc.expected-number=Verwachte een getal
luckperms.command.misc.date-parse-error=Kan datum {0} niet verwerken
luckperms.command.misc.date-in-past-error=Je kunt geen datum in het verleden instellen\!
luckperms.command.misc.page=pagina {0} van {1}
luckperms.command.misc.page-entries={0} items
luckperms.command.misc.none=Geen
luckperms.command.misc.loading.error.unexpected=Er is een onverwachte fout opgetreden
luckperms.command.misc.loading.error.user=Gebruiker niet geladen
luckperms.command.misc.loading.error.user-specific=Kan doelgebruiker {0} niet laden
luckperms.command.misc.loading.error.user-not-found=Een gebruiker voor {0} kon niet worden gevonden
luckperms.command.misc.loading.error.user-save-error=Er is een fout opgetreden tijdens het opslaan van gebruikersgegevens voor {0}
luckperms.command.misc.loading.error.user-not-online=Gebruiker {0} is niet online
luckperms.command.misc.loading.error.user-invalid={0} is geen geldige gebruikersnaam/uuid
luckperms.command.misc.loading.error.user-not-uuid=De gekozen gebruiker {0} is geen geldige uuid
luckperms.command.misc.loading.error.group=Groep niet geladen
luckperms.command.misc.loading.error.all-groups=Niet alle groepen konden worden geladen
luckperms.command.misc.loading.error.group-not-found=Een groep genaamd {0} kon niet worden gevonden
luckperms.command.misc.loading.error.group-save-error=Er is een fout opgetreden tijdens het opslaan van groepsgegevens voor {0}
luckperms.command.misc.loading.error.group-invalid={0} is geen geldige groepsnaam
luckperms.command.misc.loading.error.track=Ladder niet geladen
luckperms.command.misc.loading.error.all-tracks=Niet alle ladders konden worden geladen
luckperms.command.misc.loading.error.track-not-found=Een ladder genaamd {0} kon niet worden gevonden
luckperms.command.misc.loading.error.track-save-error=Er is een fout opgetreden tijdens het opslaan van de laddergegevens in {0}
luckperms.command.misc.loading.error.track-invalid={0} is een ongeldige ladder naam
luckperms.command.editor.no-match=Kan editor niet openen, geen objecten komen overeen met het gewenste type
luckperms.command.editor.start=Een nieuwe bewerkingssessie voorbereiden, even geduld...
luckperms.command.editor.url=Klik op de onderstaande link om de editor te openen
luckperms.command.editor.unable-to-communicate=Web-app niet bereikbaar
luckperms.command.editor.apply-edits.success=Webeditor gegevens zijn succesvol toegepast op {0} {1}
luckperms.command.editor.apply-edits.success-summary={0} {1} en {2} {3}
luckperms.command.editor.apply-edits.success.additions=toevoegingen
luckperms.command.editor.apply-edits.success.additions-singular=toevoeging
luckperms.command.editor.apply-edits.success.deletions=verwijderingen
luckperms.command.editor.apply-edits.success.deletions-singular=verwijdering
luckperms.command.editor.apply-edits.no-changes=Er zijn geen wijzigingen toegepast vanuit de webeditor, de teruggekomen gegevens bevatten geen bewerkingen
luckperms.command.editor.apply-edits.unknown-type=Niet in staat om wijziging toe te passen op het opgegeven objecttype
luckperms.command.editor.apply-edits.unable-to-read=Kan de gegevens niet inlezen met behulp van de opgegeven code
luckperms.command.search.searching.permission=Zoeken naar gebruikers en groepen met {0}
luckperms.command.search.searching.inherit=Zoeken naar gebruikers en groepen die overerven van {0}
luckperms.command.search.result={0} entries van {1} gebruikers en {2} groepen gevonden
luckperms.command.search.result.default-notice=Opmerking\: wanneer er gezocht wordt naar leden in de standaard groep worden offline spelers zonder andere permissies niet getoond\!
luckperms.command.search.showing-users=Tonen van gebruikersgegevens
luckperms.command.search.showing-groups=Tonen van groepsgegevens
luckperms.command.tree.start=Permissieschema genereren, even geduld alstublieft...
luckperms.command.tree.empty=Kan schema niet genereren, er zijn geen resultaten gevonden
luckperms.command.tree.url=Permissieschema URL
luckperms.command.verbose.invalid-filter={0} is een ongeldig uitgebreid filter
luckperms.command.verbose.enabled=Uitgebreide logboekregistratie {0} voor controles die overeenkomen met {1}
luckperms.command.verbose.command-exec={0} forceren om opdracht {1} uit te voeren en alle controles te rapporteren...
luckperms.command.verbose.off=Uitgebreide logboekregistratie {0}
luckperms.command.verbose.command-exec-complete=Commando uitvoeren voltooid
luckperms.command.verbose.command.no-checks=Het uitvoeren van het commando is voltooid, maar er zijn geen toestemmingscontroles uitgevoerd
luckperms.command.verbose.command.possibly-async=Dit kan komen doordat de plugin commando''s in de achtergrond uitvoert (async)
luckperms.command.verbose.command.try-again-manually=U kunt nog steeds handmatig loggen om controles zoals deze te detecteren
luckperms.command.verbose.enabled-recording=Uitgebreide opname {0} voor controles die overeenkomen met {1}
luckperms.command.verbose.uploading=Uitgebreide logboekregistratie {0}, resultaten worden geÃ¼pload...
luckperms.command.verbose.url=Uitgebreide resultaten URL
luckperms.command.verbose.enabled-term=ingeschakeld
luckperms.command.verbose.disabled-term=uitgeschakeld
luckperms.command.verbose.query-any=IEDER
luckperms.command.info.running-plugin=Actieve
luckperms.command.info.platform-key=Platform
luckperms.command.info.server-brand-key=Server Type
luckperms.command.info.server-version-key=Serverversie
luckperms.command.info.storage-key=Opslag
luckperms.command.info.storage-type-key=Type
luckperms.command.info.storage.meta.split-types-key=Types
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=Verbonden
luckperms.command.info.storage.meta.file-size-key=Bestandsgrootte
luckperms.command.info.extensions-key=Uitbreidingen
luckperms.command.info.messaging-key=Berichtgeving
luckperms.command.info.instance-key=Instantie
luckperms.command.info.static-contexts-key=Statische context
luckperms.command.info.online-players-key=Online spelers
luckperms.command.info.online-players-unique={0} uniek
luckperms.command.info.uptime-key=Uptime
luckperms.command.info.local-data-key=Lokale Gegevens
luckperms.command.info.local-data={0} gebruikers, {1} groepen, {2} ladders
luckperms.command.generic.create.success={0} was succesvol aangemaakt
luckperms.command.generic.create.error=Er is een fout opgetreden tijdens het aanmaken van {0}
luckperms.command.generic.create.error-already-exists={0} bestaat al\!
luckperms.command.generic.delete.success={0} was succesvol verwijderd
luckperms.command.generic.delete.error=Er is een fout opgetreden tijdens het verwijderen van {0}
luckperms.command.generic.delete.error-doesnt-exist={0} bestaat niet\!
luckperms.command.generic.rename.success={0} was succesvol hernoemd naar {1}
luckperms.command.generic.clone.success={0} was succesvol gedupliceerd naar {1}
luckperms.command.generic.info.parent.title=Bovenliggende Groepen
luckperms.command.generic.info.parent.temporary-title=Tijdelijke Bovenliggende Groepen
luckperms.command.generic.info.expires-in=verloopt over
luckperms.command.generic.info.inherited-from=overgeÃ«rfd van
luckperms.command.generic.info.inherited-from-self=jezelf
luckperms.command.generic.show-tracks.title={0}s Ladders
luckperms.command.generic.show-tracks.empty={0} is in geen enkele ladder
luckperms.command.generic.clear.node-removed={0} permissies zijn verwijderd
luckperms.command.generic.clear.node-removed-singular=permissie {0} is verwijderd
luckperms.command.generic.clear={0}''s permissies zijn gewist in context {1}
luckperms.command.generic.permission.info.title={0}''s Permissies
luckperms.command.generic.permission.info.empty={0} heeft nog geen ingestelde permissies
luckperms.command.generic.permission.info.click-to-remove=Klik om deze permissie te verwijderen van {0}
luckperms.command.generic.permission.check.info.title=Permissie informatie voor {0}
luckperms.command.generic.permission.check.info.directly={0} heeft {1} ingesteld op {2} in context {3}
luckperms.command.generic.permission.check.info.inherited={0} erft {1} over ingesteld op {2} van {3} in context {4}
luckperms.command.generic.permission.check.info.not-directly={0} heeft geen {1} ingesteld
luckperms.command.generic.permission.check.info.not-inherited={0} overerft geen {1}
luckperms.command.generic.permission.check.result.title=Permissie controle voor {0}
luckperms.command.generic.permission.check.result.result-key=Resultaat
luckperms.command.generic.permission.check.result.processor-key=Processor
luckperms.command.generic.permission.check.result.cause-key=Oorzaak
luckperms.command.generic.permission.check.result.context-key=Context
luckperms.command.generic.permission.set={0} ingesteld op {1} voor {2} in context {3}
luckperms.command.generic.permission.already-has={0} heeft {1} al ingesteld in context {2}
luckperms.command.generic.permission.set-temp={0} ingesteld op {1} voor {2} voor een duur van {3} in context {4}
luckperms.command.generic.permission.already-has-temp={0} heeft {1} al tijdelijk ingesteld in context {2}
luckperms.command.generic.permission.unset={0} weggehaald voor {1} in context {2}
luckperms.command.generic.permission.doesnt-have={0} heeft {1} niet ingesteld in context {2}
luckperms.command.generic.permission.unset-temp=Tijdelijke permissie {0} voor {1} in context {2} weggehaald
luckperms.command.generic.permission.subtract={0} ingesteld op {1} voor {2} voor een duur van {3} in context {4}, {5} minder dan voorheen
luckperms.command.generic.permission.doesnt-have-temp={0} heeft niet {1} tijdelijk ingesteld in context {2}
luckperms.command.generic.permission.clear={0}''s permissies zijn gewist in context {1}
luckperms.command.generic.parent.info.title={0}''s Bovenliggende groep
luckperms.command.generic.parent.info.empty={0} heeft geen bovenliggende groep gedefinieerd
luckperms.command.generic.parent.info.click-to-remove=Klik om deze bovenliggende van {0} te verwijderen
luckperms.command.generic.parent.add={0} erft nu permissies van {1} over in context {2}
luckperms.command.generic.parent.add-temp={0} erft nu permissies van {1} over voor een periode van {2} in context {3}
luckperms.command.generic.parent.set={0} heeft de bestaande bovenliggende groepen verwijderd, en erft nu alleen {1} over in context {2}
luckperms.command.generic.parent.set-track={0} heeft hun bestaande bovenliggende groepen op ladder {1} gewist, en erft nu alleen {2} over in context {3}
luckperms.command.generic.parent.remove={0} erft niet meer permissies van {1} over in context {2}
luckperms.command.generic.parent.remove-temp={0} neemt niet langer tijdelijk de permissies van {1} in context {2} over
luckperms.command.generic.parent.subtract={0} zal permissies overerven van {1} voor een periode van {2} in context {3}, {4} minder dan daarvoor
luckperms.command.generic.parent.clear={0}''s bovenliggende groepen zijn gewist in context {1}
luckperms.command.generic.parent.clear-track={0}''s bovenliggende groepen in ladder {1} zijn gewist in de context {2}
luckperms.command.generic.parent.already-inherits={0} erft al van {1} over in context {2}
luckperms.command.generic.parent.doesnt-inherit={0} erft niet van {1} over in de context {2}
luckperms.command.generic.parent.already-temp-inherits={0} erft al tijdelijk van {1} over in context {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} erft niet tijdelijk van {1} over in de context {2}
luckperms.command.generic.chat-meta.info.title-prefix={0}''s voorvoegsels
luckperms.command.generic.chat-meta.info.title-suffix={0}''s achtervoegsels
luckperms.command.generic.chat-meta.info.none-prefix={0} heeft geen voorvoegsels
luckperms.command.generic.chat-meta.info.none-suffix={0} heeft geen achtervoegsels
luckperms.command.generic.chat-meta.info.click-to-remove=Klik om {0} te verwijderen van {1}
luckperms.command.generic.chat-meta.already-has={0} heeft {1} {2} al ingesteld op een prioriteit van {3} in context {4}
luckperms.command.generic.chat-meta.already-has-temp={0} heeft {1} {2} al tijdelijk ingesteld op een prioriteit van {3} in context {4}
luckperms.command.generic.chat-meta.doesnt-have={0} heeft niet {1} {2} ingesteld op een prioriteit van {3} in context {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} heeft niet {1} {2} tijdelijk ingesteld op een prioriteit van {3} in context {4}
luckperms.command.generic.chat-meta.add={0} heeft {1} {2} al ingesteld op een prioriteit van {3} in context {4}
luckperms.command.generic.chat-meta.add-temp={0} had {1} {2} ingesteld op een prioriteit van {3} voor een duur van {4} in context {5}
luckperms.command.generic.chat-meta.remove={0} had {1} {2} op prioriteit {3} verwijderd in context {4}
luckperms.command.generic.chat-meta.remove-bulk={0} heeft alle {1} op prioriteit {2} verwijderd in de context {3}
luckperms.command.generic.chat-meta.remove-temp={0} had tijdelijk {1} {2} op prioriteit {3} verwijderd in context {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} heeft alle {1} op prioriteit {2} tijdelijk verwijderd in de context {3}
luckperms.command.generic.meta.info.title={0}''s Meta
luckperms.command.generic.meta.info.none={0} heeft geen metagegevens
luckperms.command.generic.meta.info.click-to-remove=Klik om deze meta-node van {0} te verwijderen
luckperms.command.generic.meta.already-has={0} heeft al de meta key {1} ingesteld op {2} in context {3}
luckperms.command.generic.meta.already-has-temp={0} heeft al de meta key {1} tijdelijk ingesteld op {2} in context {3}
luckperms.command.generic.meta.doesnt-have={0} heeft geen meta key {1} ingesteld in context {2}
luckperms.command.generic.meta.doesnt-have-temp={0} heeft geen tijdelijke meta key {1} ingesteld in context {2}
luckperms.command.generic.meta.set=Meta key {0} ingesteld op {1} voor {2} in context {3}
luckperms.command.generic.meta.set-temp=Meta key {0} ingesteld op {1} voor {2} voor een duur van {3} in context {4}
luckperms.command.generic.meta.unset=Verwijder meta key {0} voor {1} in context {2}
luckperms.command.generic.meta.unset-temp=Verwijder tijdelijke meta key {0} voor {1} in context {2}
luckperms.command.generic.meta.clear={0}''s meta-type {1} werd gewist in context {2}
luckperms.command.generic.contextual-data.title=Contextuele gegevens
luckperms.command.generic.contextual-data.mode.key=modus
luckperms.command.generic.contextual-data.mode.server=server
luckperms.command.generic.contextual-data.mode.active-player=actieve spelers
luckperms.command.generic.contextual-data.contexts-key=Context
luckperms.command.generic.contextual-data.prefix-key=Voorvoegsel
luckperms.command.generic.contextual-data.suffix-key=Achtervoegsel
luckperms.command.generic.contextual-data.primary-group-key=Primaire Groep
luckperms.command.generic.contextual-data.meta-key=Meta
luckperms.command.generic.contextual-data.null-result=Geen
luckperms.command.user.info.title=Gebruikersinfo
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=type
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=offline
luckperms.command.user.info.status-key=Status
luckperms.command.user.info.status.online=Online
luckperms.command.user.info.status.offline=Offline
luckperms.command.user.removegroup.error-primary=Je kunt een gebruiker niet verwijderen uit de primaire groep
luckperms.command.user.primarygroup.not-member={0} was nog geen lid van {1}, toevoegen
luckperms.command.user.primarygroup.already-has={0} heeft al {1} ingesteld als hun primaire groep
luckperms.command.user.primarygroup.warn-option=Waarschuwing\: De door deze server gebruikte primaire groep berekeningsmethode ({0}) beÃ¯nvloed deze wijziging mogelijk niet
luckperms.command.user.primarygroup.set={0}''s primaire groep is ingesteld naar {1}
luckperms.command.user.track.error-not-contain-group={0} zit nog niet in enige groepen op {1}
luckperms.command.user.track.unsure-which-track=Onzeker welke ladder u wilt gebruiken, geef het op als argument
luckperms.command.user.track.missing-group-advice=Maak de groep aan, of verwijder deze van een ladder en probeer het opnieuw
luckperms.command.user.promote.added-to-first={0} is niet in een groep op {1}, dus zijn ze toegevoegd aan de eerste groep, {2} in context {3}
luckperms.command.user.promote.not-on-track={0} zit niet in een groep op {1}, daarom is niet gepromoveerd
luckperms.command.user.promote.success=Het promoten van {0} op ladder {1} van {2} naar {3} in context {4}
luckperms.command.user.promote.end-of-track=Het einde van ladder {0} werd bereikt, niet mogelijk om {1} te promoten
luckperms.command.user.promote.next-group-deleted=De volgende groep op ladder {0} bestaat niet meer
luckperms.command.user.promote.unable-to-promote=Kan gebruiker niet promoten
luckperms.command.user.demote.success=Degraderen van {0} volgens ladder {1} van {2} naar {3} in context {4}
luckperms.command.user.demote.end-of-track=Het einde van ladder {0} is bereikt, dus {1} is verwijderd van {2}
luckperms.command.user.demote.end-of-track-not-removed=Het einde van ladder {0} is bereikt, maar {1} is niet verwijderd van de groep
luckperms.command.user.demote.previous-group-deleted=De vorige groep op de ladder {0} bestaat niet meer
luckperms.command.user.demote.unable-to-demote=Kan gebruiker niet degraderen
luckperms.command.group.list.title=Groepen
luckperms.command.group.delete.not-default=Je kunt de standaardgroep niet verwijderen
luckperms.command.group.info.title=Groepsinfo
luckperms.command.group.info.display-name-key=Weergave naam
luckperms.command.group.info.weight-key=Gewicht
luckperms.command.group.setweight.set=Gewicht instellen op {0} voor groep {1}
luckperms.command.group.setdisplayname.doesnt-have={0} heeft geen weergavenaam ingesteld
luckperms.command.group.setdisplayname.already-has={0} heeft al een weergavenaam van {1}
luckperms.command.group.setdisplayname.already-in-use=De weergavenaam {0} wordt al gebruikt door {1}
luckperms.command.group.setdisplayname.set=Weergavenaam ingesteld op {0} voor groep {1} in context {2}
luckperms.command.group.setdisplayname.removed=Weergavenaam voor groep {0} verwijderd in de context {1}
luckperms.command.track.list.title=Ladders
luckperms.command.track.path.empty=Geen
luckperms.command.track.info.showing-track=Laat de ladder zien
luckperms.command.track.info.path-property=Pad
luckperms.command.track.clear=de groepsladder van {0} is gewist
luckperms.command.track.append.success=Groep {0} werd toegevoegd aan ladder {1}
luckperms.command.track.insert.success=Groep {0} werd op positie {2} toegevoegd aan ladder {1}
luckperms.command.track.insert.error-number=Verwachte getal, maar in plaats daarvan ontvangen\: {0}
luckperms.command.track.insert.error-invalid-pos=Niet mogelijk om in te voegen op positie {0}
luckperms.command.track.insert.error-invalid-pos-reason=ongeldige positie
luckperms.command.track.remove.success=Groep {0} werd verwijderd uit ladder {1}
luckperms.command.track.error-empty={0} kan niet worden gebruikt omdat het leeg is of slechts Ã©Ã©n groep bevat
luckperms.command.track.error-multiple-groups={0} is een deelnemer van meerdere groepen op deze ladder
luckperms.command.track.error-ambiguous=Niet in staat om hun locatie te bepalen
luckperms.command.track.already-contains={0} bevat al {1}
luckperms.command.track.doesnt-contain={0} bevat niet {1}
luckperms.command.log.load-error=Het logboek kon niet worden geladen
luckperms.command.log.invalid-page=Ongeldig paginanummer
luckperms.command.log.invalid-page-range=Voer een waarde in tussen {0} en {1}
luckperms.command.log.empty=Geen logs om weer te geven
luckperms.command.log.notify.error-console=Kan meldingen voor console niet in- of uitschakelen
luckperms.command.log.notify.enabled-term=Ingeschakeld
luckperms.command.log.notify.disabled-term=Uitgeschakeld
luckperms.command.log.notify.changed-state={0} logboekuitvoer
luckperms.command.log.notify.already-on=Je ontvangt al meldingen
luckperms.command.log.notify.already-off=Je ontvangt momenteel geen meldingen
luckperms.command.log.notify.invalid-state=Status onbekend. Verwacht {0} of {1}
luckperms.command.log.show.search=Recente acties voor query {0} weergeven
luckperms.command.log.show.recent=Bekijk recente acties
luckperms.command.log.show.by=Bekijk recente acties voor {0}
luckperms.command.log.show.history=Toont geschiedenis voor {0} {1}
luckperms.command.export.error-term=Fout
luckperms.command.export.already-running=Er wordt al een ander exportproces uitgevoerd
luckperms.command.export.file.already-exists=Bestand {0} bestaat al
luckperms.command.export.file.not-writable=Bestand {0} is niet te schrijven
luckperms.command.export.file.success=Succesvol geÃ«xporteerd naar {0}
luckperms.command.export.file-unexpected-error-writing=Er is een onverwachte fout opgetreden tijdens het schrijven naar het bestand
luckperms.command.export.web.export-code=Exporteer code
luckperms.command.export.web.import-command-description=Gebruik het volgende commando om te importeren
luckperms.command.import.term=Importeren
luckperms.command.import.error-term=Error
luckperms.command.import.already-running=Er wordt al een ander importproces uitgevoerd
luckperms.command.import.file.doesnt-exist=Bestand {0} bestaat niet
luckperms.command.import.file.not-readable=Bestand {0} is niet leesbaar
luckperms.command.import.file.unexpected-error-reading=Er is een onverwachte fout opgetreden tijdens het lezen van het importbestand
luckperms.command.import.file.correct-format=is het het juiste formaat?
luckperms.command.import.web.unable-to-read=Kan de gegevens niet inlezen met behulp van de opgegeven code
luckperms.command.import.progress.percent={0}% voltooid
luckperms.command.import.progress.operations={0}/{1} bewerkingen voltooid
luckperms.command.import.starting=Importproces wordt gestart
luckperms.command.import.completed=VOLTOOID
luckperms.command.import.duration=duurde {0} secondes
luckperms.command.bulkupdate.must-use-console=Het bulk update commando kan alleen worden gebruikt vanuit de console
luckperms.command.bulkupdate.invalid-data-type=Ongeldig type, verwachtte {0}
luckperms.command.bulkupdate.invalid-constraint=Ongeldige restrictie {0}
luckperms.command.bulkupdate.invalid-constraint-format=Beperkingen moeten in het formaat {0} zijn
luckperms.command.bulkupdate.invalid-comparison=Ongeldige vergelijkingsoperator {0}
luckperms.command.bulkupdate.invalid-comparison-format=Verwacht een van de volgende\: {0}
luckperms.command.bulkupdate.queued=Bulk update bewerking is in de wachtrij gezet
luckperms.command.bulkupdate.confirm=Voer {0} uit om de update uit te voeren
luckperms.command.bulkupdate.unknown-id=Bewerking met id {0} bestaat niet of is verlopen
luckperms.command.bulkupdate.starting=Bulkupdate wordt uitgevoerd
luckperms.command.bulkupdate.success=Bulkupdate is succesvol voltooid
luckperms.command.bulkupdate.success.statistics.nodes=Totaal aantal gewijzigde items
luckperms.command.bulkupdate.success.statistics.users=Totaal aantal gewijzigde gebruikers
luckperms.command.bulkupdate.success.statistics.groups=Totaal aantal gewijzigde groepen
luckperms.command.bulkupdate.failure=Bulk update mislukt, controleer de console op fouten
luckperms.command.update-task.request=Er is een update taak aangevraagd, een moment geduld
luckperms.command.update-task.complete=Update taak voltooid
luckperms.command.update-task.push.attempting=Probeert nu te pushen naar andere servers
luckperms.command.update-task.push.complete=Andere servers werden succesvol op de hoogte gesteld via {0}
luckperms.command.update-task.push.error=Fout tijdens verzenden van veranderingen naar andere servers
luckperms.command.update-task.push.error-not-setup=Kan geen wijzigingen naar andere servers sturen omdat een berichtenservice niet is geconfigureerd
luckperms.command.reload-config.success=Het configuratiebestand is opnieuw geladen
luckperms.command.reload-config.restart-note=sommige opties worden pas toegepast nadat de server opnieuw is gestart
luckperms.command.translations.searching=Zoeken naar beschikbare vertalingen, even geduld...
luckperms.command.translations.searching-error=Kan geen lijst met beschikbare vertalingen verkrijgen
luckperms.command.translations.installed-translations=GeÃ¯nstalleerde Vertalingen
luckperms.command.translations.available-translations=Beschikbare Vertalingen
luckperms.command.translations.percent-translated={0}% vertaald
luckperms.command.translations.translations-by=door
luckperms.command.translations.installing=Vertalingen installeren, even geduld...
luckperms.command.translations.download-error=Kan vertaling voor {0} niet downloaden
luckperms.command.translations.installing-specific=Taal {0} installeren...
luckperms.command.translations.install-complete=Installatie voltooid
luckperms.command.translations.download-prompt=Gebruik {0} om actuele versies van deze vertalingen van de gemeenschap te downloaden en installeren
luckperms.command.translations.download-override-warning=Houd er rekening mee dat alle wijzigingen die je in deze talen hebt aangebracht hierdoor worden overschreven
luckperms.usage.user.description=Een set commando''s voor het beheren van gebruikers binnen LuckPerms. (Een ''gebruiker'' in LuckPerms is een speler, en kan verwijzen naar een UUID of gebruikersnaam)
luckperms.usage.group.description=Een set opdrachten voor het beheren van groepen binnen LuckPerms. Groepen zijn collecties van permissies die aan gebruikers kunnen worden gegeven. Nieuwe groepen worden gemaakt met behulp van het ''creategroup'' commando.
luckperms.usage.track.description=Een set opdrachten voor het beheren van ladders binnen LuckPerms. Ladders zijn een geordende verzameling van groepen die kunnen worden gebruikt voor het definiÃ«ren van promoties en demoties.
luckperms.usage.log.description=Een reeks opdrachten voor het beheren van de logboekfunctionaliteit binnen LuckPerms.
luckperms.usage.sync.description=Herlaad alle gegevens van de opslag van de plugin naar het geheugen, en past eventuele wijzigingen die worden gedetecteerd toe.
luckperms.usage.info.description=Toont algemene informatie over de actieve plugin instantie.
luckperms.usage.editor.description=Maakt een nieuwe webeditor sessie aan
luckperms.usage.editor.argument.type=de types om in de editor te laden. (''all'', ''users'' of ''groups'')
luckperms.usage.editor.argument.filter=toestemming om gebruikersitems te filteren op
luckperms.usage.verbose.description=Controleert het uitgebreide controlesysteem van de plugin voor de controle van het monitorsysteem.
luckperms.usage.verbose.argument.action=of de logboekregistratie moet worden ingeschakeld/uitgeschakeld, of de gelogde output moet worden geÃ¼pload
luckperms.usage.verbose.argument.filter=de filter om de invoer aan te passen
luckperms.usage.verbose.argument.commandas=de speler/opdracht om uit te voeren
luckperms.usage.tree.description=Genereert een boomweergave (geordende lijsthiÃ«rarchie) van alle permissies die bekend zijn bij LuckPerms.
luckperms.usage.tree.argument.scope=het begin van het schema. Specificeer "." om alle permissies toe te voegen
luckperms.usage.tree.argument.player=de naam van een online speler om te controleren
luckperms.usage.search.description=Zoekt voor alle gebruikers/groepen met een specifieke permissie
luckperms.usage.search.argument.permission=de permissie om naar te zoeken
luckperms.usage.search.argument.page=de pagina om te bekijken
luckperms.usage.network-sync.description=Synchroniseer wijzigingen met de opslag en vraag aan dat alle andere servers op het netwerk hetzelfde doen
luckperms.usage.import.description=Importeer gegevens van een (eerder aangemaakt) exportbestand
luckperms.usage.import.argument.file=het bestand om van te importeren
luckperms.usage.import.argument.replace=vervang bestaande gegevens in plaats van samenvoegen
luckperms.usage.import.argument.upload=upload de gegevens van een vorige export
luckperms.usage.export.description=Exporteert alle permissies gegevens naar een ''export'' bestand. Kan op een later tijdstip opnieuw worden geÃ¯mporteerd.
luckperms.usage.export.argument.file=het bestand om naar te exporteren
luckperms.usage.export.argument.without-users=gebruikers uitsluiten van de export
luckperms.usage.export.argument.without-groups=groepen uitsluiten van de export
luckperms.usage.export.argument.upload=Upload alle permissiegegevens naar de webeditor. Kan later opnieuw worden geÃ¯mporteerd.
luckperms.usage.reload-config.description=Herlaad sommige van de configuratie-opties
luckperms.usage.bulk-update.description=Voer bulk veranderingsquery''s uit op alle gegevens
luckperms.usage.bulk-update.argument.data-type=het type gegevens dat wordt gewijzigd. (''all'', ''users'' of ''groups'')
luckperms.usage.bulk-update.argument.action=de actie om gegevens uit te voeren. (''update'' of ''delete'')
luckperms.usage.bulk-update.argument.action-field=het veld om naar te handelen. alleen vereist voor ''update''. (''permission'', ''server'' of ''world'')
luckperms.usage.bulk-update.argument.action-value=de waarde om mee te vervangen. alleen vereist voor ''update''.
luckperms.usage.bulk-update.argument.constraint=de beperkingen die nodig zijn voor de update
luckperms.usage.translations.description=Beheer vertalingen
luckperms.usage.translations.argument.install=sub commando om vertalingen te installeren
luckperms.usage.apply-edits.description=Past veranderingen van permissies toe gemaakt in de webeditor
luckperms.usage.apply-edits.argument.code=de unieke code voor de data
luckperms.usage.apply-edits.argument.target=op wie de gegevens toepast worden
luckperms.usage.create-group.description=Nieuwe groep aanmaken
luckperms.usage.create-group.argument.name=de naam van de groep
luckperms.usage.create-group.argument.weight=het gewicht van de groep
luckperms.usage.create-group.argument.display-name=de weergavenaam van de groep
luckperms.usage.delete-group.description=Groep verwijderen
luckperms.usage.delete-group.argument.name=de naam van de groep
luckperms.usage.list-groups.description=Toon alle groepen op het platform
luckperms.usage.create-track.description=Maak een nieuwe ladder
luckperms.usage.create-track.argument.name=de naam van de ladder
luckperms.usage.delete-track.description=Verwijder een ladder
luckperms.usage.delete-track.argument.name=de naam van de ladder
luckperms.usage.list-tracks.description=Toon alle ladders op het platform
luckperms.usage.user-info.description=Toont informatie over de speler
luckperms.usage.user-switchprimarygroup.description=Verander de primaire groep van gebruiker
luckperms.usage.user-switchprimarygroup.argument.group=de groep om naar te wisselen
luckperms.usage.user-promote.description=Promoveert de gebruiker in een ladder
luckperms.usage.user-promote.argument.track=de ladder om de gebruiker te promoveren
luckperms.usage.user-promote.argument.context=de contexten om de gebruiker te promoten in
luckperms.usage.user-promote.argument.dont-add-to-first=promoot de gebruiker alleen als ze al op de ladder staan
luckperms.usage.user-demote.description=Degradeert de gebruiker in een ladder
luckperms.usage.user-demote.argument.track=de ladder om de gebruiker te degraderen
luckperms.usage.user-demote.argument.context=de context om de gebruiker te degraderen in
luckperms.usage.user-demote.argument.dont-remove-from-first=voorkomt dat de gebruiker wordt verwijderd uit de eerste groep
luckperms.usage.user-clone.description=Dupliceer de gebruiker
luckperms.usage.user-clone.argument.user=de naam/uuid van de gebruiker om te dupliceren
luckperms.usage.group-info.description=Geeft informatie over de groep
luckperms.usage.group-listmembers.description=Toon de gebruikers/groepen die deze groep overerven
luckperms.usage.group-listmembers.argument.page=de pagina om te bekijken
luckperms.usage.group-setweight.description=Stel het groepsgewicht in
luckperms.usage.group-setweight.argument.weight=het gewicht om in te stellen
luckperms.usage.group-set-display-name.description=Stel de weergavenaam van groepen in
luckperms.usage.group-set-display-name.argument.name=de naam om in te stellen
luckperms.usage.group-set-display-name.argument.context=de contexts om de naam in te stellen
luckperms.usage.group-rename.description=Hernoem de groep
luckperms.usage.group-rename.argument.name=de nieuwe naam
luckperms.usage.group-clone.description=Kopieer de groep
luckperms.usage.group-clone.argument.name=de naam van de groep om op te dupliceren
luckperms.usage.holder-editor.description=Opent de webpermissie editor
luckperms.usage.holder-showtracks.description=Geeft de ladders weer waar het object in staat
luckperms.usage.holder-clear.description=Verwijdert alle permissies, bovenliggende groepen en metagegevens
luckperms.usage.holder-clear.argument.context=de contexten die worden gefilterd door
luckperms.usage.permission.description=Wijzig permissies
luckperms.usage.parent.description=Bewerk overervingen
luckperms.usage.meta.description=Metagegevens bewerken
luckperms.usage.permission-info.description=Toont de permissienodes van het object
luckperms.usage.permission-info.argument.page=de pagina om te bekijken
luckperms.usage.permission-info.argument.sort-mode=hoe je de items sorteert
luckperms.usage.permission-set.description=Stelt een permissie in voor het object
luckperms.usage.permission-set.argument.node=de permissie node in te stellen
luckperms.usage.permission-set.argument.value=de waarde van de node
luckperms.usage.permission-set.argument.context=de context om een permissie in toe te voegen
luckperms.usage.permission-unset.description=Verwijderd de permissie voor het object
luckperms.usage.permission-unset.argument.node=de permissie node om uit te zetten
luckperms.usage.permission-unset.argument.context=de context om een permissie te verwijderen
luckperms.usage.permission-settemp.description=Stelt tijdelijk een permissie in voor het object
luckperms.usage.permission-settemp.argument.node=permissie node om in te stellen
luckperms.usage.permission-settemp.argument.value=de waarde van de node
luckperms.usage.permission-settemp.argument.duration=de duur tot de permissie node verloopt
luckperms.usage.permission-settemp.argument.temporary-modifier=hoe de tijdelijke permissie moet worden toegepast
luckperms.usage.permission-settemp.argument.context=de context om een permissies toe te voegen
luckperms.usage.permission-unsettemp.description=Verwijderd de tijdelijke permissie voor het onderwerp
luckperms.usage.permission-unsettemp.argument.node=de permissie node om te verwijderen
luckperms.usage.permission-unsettemp.argument.duration=de duur om eraf te trekken
luckperms.usage.permission-unsettemp.argument.context=de context om een permissie te verwijderen
luckperms.usage.permission-check.description=Controleert om te zien of het object een bepaalde permissie heeft
luckperms.usage.permission-check.argument.node=de permissie om op te controleren
luckperms.usage.permission-clear.description=Wis alle permissies
luckperms.usage.permission-clear.argument.context=de contexten die worden gefilterd door
luckperms.usage.parent-info.description=Geeft de lijst met groepen weer waarvan dit object overerft
luckperms.usage.parent-info.argument.page=de pagina om te bekijken
luckperms.usage.parent-info.argument.sort-mode=hoe je de items sorteert
luckperms.usage.parent-set.description=Verwijdert alle andere groepen die het object al overerft en voegt het toe aan de gegeven groep
luckperms.usage.parent-set.argument.group=de groep om te zetten op
luckperms.usage.parent-set.argument.context=de context om de groep in te stellen in
luckperms.usage.parent-add.description=Stelt een andere groep in voor het object om de permissies van over te erven
luckperms.usage.parent-add.argument.group=de groep om over te erven
luckperms.usage.parent-add.argument.context=de context die de groep overerft
luckperms.usage.parent-remove.description=Verwijdert een eerder ingestelde overerving
luckperms.usage.parent-remove.argument.group=de groep om te verwijderen
luckperms.usage.parent-remove.argument.context=de context om de groep te verwijderen
luckperms.usage.parent-set-track.description=Verwijdert alle andere groepen die het object al overerft op een ladder en voegt het toe aan de gegeven groep
luckperms.usage.parent-set-track.argument.track=de ladder om in te stellen
luckperms.usage.parent-set-track.argument.group=de groep om in te stellen, of het nummer van de positie van de groep op de gegeven ladder
luckperms.usage.parent-set-track.argument.context=de context om de groep in te stellen
luckperms.usage.parent-add-temp.description=Stelt tijdelijk een andere groep in om permissies over te erven voor het object
luckperms.usage.parent-add-temp.argument.group=de groep om van over te erven
luckperms.usage.parent-add-temp.argument.duration=de duur van het groepslidmaatschap
luckperms.usage.parent-add-temp.argument.temporary-modifier=hoe de tijdelijke permissie moet worden toegepast
luckperms.usage.parent-add-temp.argument.context=de context die de groep overerft
luckperms.usage.parent-remove-temp.description=Verwijdert een eerder ingestelde tijdelijke overerving
luckperms.usage.parent-remove-temp.argument.group=de groep om te verwijderen
luckperms.usage.parent-remove-temp.argument.duration=de duur om eraf te trekken
luckperms.usage.parent-remove-temp.argument.context=de context om de groep te verwijderen
luckperms.usage.parent-clear.description=Wist alle bovenliggende groepen
luckperms.usage.parent-clear.argument.context=de context om op te filteren
luckperms.usage.parent-clear-track.description=Verwijdert alle bovenliggende groepen op een bepaalde ladder
luckperms.usage.parent-clear-track.argument.track=de ladder om op te verwijderen
luckperms.usage.parent-clear-track.argument.context=de context om op te filteren
luckperms.usage.meta-info.description=Alle chat meta tonen
luckperms.usage.meta-set.description=Stelt een meta-waarde in
luckperms.usage.meta-set.argument.key=de sleutel om in te stellen
luckperms.usage.meta-set.argument.value=de waarde om in te stellen
luckperms.usage.meta-set.argument.context=de contexten om de meta-paar toe te voegen
luckperms.usage.meta-unset.description=Een meta-waarde verwijderen
luckperms.usage.meta-unset.argument.key=de sleutel om te verwijderen
luckperms.usage.meta-unset.argument.context=de contexten om het meta-paar te verwijderen
luckperms.usage.meta-settemp.description=Stelt tijdelijk een meta-waarde in
luckperms.usage.meta-settemp.argument.key=de sleutel om in te stellen
luckperms.usage.meta-settemp.argument.value=de waarde om in te stellen
luckperms.usage.meta-settemp.argument.duration=de duur tot de meta-waarde verloopt
luckperms.usage.meta-settemp.argument.context=de contexten om het meta-paar toe te voegen
luckperms.usage.meta-unsettemp.description=Verwijder een tijdelijke meta-waarde
luckperms.usage.meta-unsettemp.argument.key=de sleutel om te verwijderen
luckperms.usage.meta-unsettemp.argument.context=de contexten om de meta-paar te verwijderen
luckperms.usage.meta-addprefix.description=Voeg een voorvoegsel toe
luckperms.usage.meta-addprefix.argument.priority=de prioriteit om het voorvoegsel toe te voegen op
luckperms.usage.meta-addprefix.argument.prefix=het voorvoegsel
luckperms.usage.meta-addprefix.argument.context=de context om het voorvoegsel in toe te voegen
luckperms.usage.meta-addsuffix.description=Voeg een achtervoegsel toe
luckperms.usage.meta-addsuffix.argument.priority=de prioriteit om het achtervoegsel toe te voegen op
luckperms.usage.meta-addsuffix.argument.suffix=het achtervoegsel
luckperms.usage.meta-addsuffix.argument.context=de context om het achtervoegsel in toe te voegen
luckperms.usage.meta-setprefix.description=Stel een voorvoegsel in
luckperms.usage.meta-setprefix.argument.priority=de prioriteit om het voorvoegsel in te stellen op
luckperms.usage.meta-setprefix.argument.prefix=het voorvoegsel
luckperms.usage.meta-setprefix.argument.context=de context om het voorvoegsel in te stellen
luckperms.usage.meta-setsuffix.description=Stel een achtervoegsel in
luckperms.usage.meta-setsuffix.argument.priority=de prioriteit om het achtervoegsel in te stellen op
luckperms.usage.meta-setsuffix.argument.suffix=het achtervoegsel
luckperms.usage.meta-setsuffix.argument.context=de context om het achtervoegsel in te stellen
luckperms.usage.meta-removeprefix.description=Verwijdert een voorvoegsel
luckperms.usage.meta-removeprefix.argument.priority=de prioriteit om het voorvoegsel te verwijderen op
luckperms.usage.meta-removeprefix.argument.prefix=het voorvoegsel
luckperms.usage.meta-removeprefix.argument.context=de context om het voorvoegsel te verwijderen
luckperms.usage.meta-removesuffix.description=Verwijdert een achtervoegsel
luckperms.usage.meta-removesuffix.argument.priority=de prioriteit om het achtervoegsel te verwijderen op
luckperms.usage.meta-removesuffix.argument.suffix=het achtervoegsel
luckperms.usage.meta-removesuffix.argument.context=de context om het achtervoegsel te verwijderen
luckperms.usage.meta-addtemp-prefix.description=Voegt tijdelijk een voorvoegsel toe
luckperms.usage.meta-addtemp-prefix.argument.priority=de prioriteit om het voorvoegsel toe te voegen op
luckperms.usage.meta-addtemp-prefix.argument.prefix=het voorvoegsel
luckperms.usage.meta-addtemp-prefix.argument.duration=de duur tot het voorvoegsel verloopt
luckperms.usage.meta-addtemp-prefix.argument.context=de context om het voorvoegsel in toe te voegen
luckperms.usage.meta-addtemp-suffix.description=Voegt tijdelijk een achtervoegsel toe
luckperms.usage.meta-addtemp-suffix.argument.priority=de prioriteit om het achtervoegsel toe te voegen op
luckperms.usage.meta-addtemp-suffix.argument.suffix=het achtervoegsel
luckperms.usage.meta-addtemp-suffix.argument.duration=de duur tot het achtervoegsel verloopt
luckperms.usage.meta-addtemp-suffix.argument.context=de context om het achtervoegsel in toe te voegen
luckperms.usage.meta-settemp-prefix.description=Stelt tijdelijk een voorvoegsel in
luckperms.usage.meta-settemp-prefix.argument.priority=de prioriteit om het voorvoegsel in te stellen op
luckperms.usage.meta-settemp-prefix.argument.prefix=het voorvoegsel
luckperms.usage.meta-settemp-prefix.argument.duration=de duur tot het voorvoegsel verloopt
luckperms.usage.meta-settemp-prefix.argument.context=de context om het voorvoegsel in te stellen
luckperms.usage.meta-settemp-suffix.description=Stelt tijdelijk een achtervoegsel in
luckperms.usage.meta-settemp-suffix.argument.priority=de prioriteit om het achtervoegsel in te stellen op
luckperms.usage.meta-settemp-suffix.argument.suffix=het achtervoegsel
luckperms.usage.meta-settemp-suffix.argument.duration=de duur tot het achtervoegsel verloopt
luckperms.usage.meta-settemp-suffix.argument.context=de context om het achtervoegsel in te stellen
luckperms.usage.meta-removetemp-prefix.description=Verwijdert een tijdelijke voorvoegsel
luckperms.usage.meta-removetemp-prefix.argument.priority=de prioriteit om het voorvoegsel te verwijderen op
luckperms.usage.meta-removetemp-prefix.argument.prefix=het voorvoegsel
luckperms.usage.meta-removetemp-prefix.argument.context=de contexten om de voorvoegsel te verwijderen in
luckperms.usage.meta-removetemp-suffix.description=Verwijdert een tijdelijk achtervoegsel
luckperms.usage.meta-removetemp-suffix.argument.priority=de prioriteit om het achtervoegsel te verwijderen op
luckperms.usage.meta-removetemp-suffix.argument.suffix=het achtervoegsel
luckperms.usage.meta-removetemp-suffix.argument.context=de contexten om het achtervoegsel te verwijderen in
luckperms.usage.meta-clear.description=Wist alle metagegevens
luckperms.usage.meta-clear.argument.type=het metatype om te verwijderen
luckperms.usage.meta-clear.argument.context=de context om op te filteren
luckperms.usage.track-info.description=Geeft informatie over de ladder
luckperms.usage.track-editor.description=Opent de web permissie editor
luckperms.usage.track-append.description=Voegt een groep toe aan het einde van de ladder
luckperms.usage.track-append.argument.group=de groep om toe te voegen
luckperms.usage.track-insert.description=Voegt een groep toe op een bepaalde positie in de ladder
luckperms.usage.track-insert.argument.group=de groep om in te voegen
luckperms.usage.track-insert.argument.position=de positie om de groep in te voegen (de eerste positie van de ladder is 1)
luckperms.usage.track-remove.description=Verwijder een groep uit de ladder
luckperms.usage.track-remove.argument.group=de groep om te verwijderen
luckperms.usage.track-clear.description=Wist de groepen op de ladder
luckperms.usage.track-rename.description=Hernoem de ladder
luckperms.usage.track-rename.argument.name=de nieuwe naam
luckperms.usage.track-clone.description=Dupliceer de ladder
luckperms.usage.track-clone.argument.name=de naam van het ladder om te kopiÃ«ren
luckperms.usage.log-recent.description=Bekijk recente acties
luckperms.usage.log-recent.argument.user=de naam/uuid van de gebruiker om te filteren op
luckperms.usage.log-recent.argument.page=het pagina nummer om weer te geven
luckperms.usage.log-search.description=Doorzoek het logboek voor een item
luckperms.usage.log-search.argument.query=de query om op te zoeken
luckperms.usage.log-search.argument.page=het pagina nummer om weer te geven
luckperms.usage.log-notify.description=Schakel log-meldingen aan/uit
luckperms.usage.log-notify.argument.toggle=aan of uit te schakelen
luckperms.usage.log-user-history.description=Bekijk gebruikersgeschiedenis
luckperms.usage.log-user-history.argument.user=de naam/uuid van de gebruiker
luckperms.usage.log-user-history.argument.page=het pagina nummer om weer te geven
luckperms.usage.log-group-history.description=Bekijk groep geschiedenis
luckperms.usage.log-group-history.argument.group=de naam van de groep
luckperms.usage.log-group-history.argument.page=het pagina nummer om weer te geven
luckperms.usage.log-track-history.description=Bekijk de geschiedenis van een ladder
luckperms.usage.log-track-history.argument.track=de naam van de ladder
luckperms.usage.log-track-history.argument.page=het pagina nummer om weer te geven
luckperms.usage.sponge.description=Extra Sponge data bewerken
luckperms.usage.sponge.argument.collection=de verzameling om op te vragen
luckperms.usage.sponge.argument.subject=het object om te wijzigen
luckperms.usage.sponge-permission-info.description=Toont informatie over permissies van het object
luckperms.usage.sponge-permission-info.argument.contexts=de context om op te filteren
luckperms.usage.sponge-permission-set.description=Stelt permissie in voor het Object
luckperms.usage.sponge-permission-set.argument.node=permissie node om in te stellen
luckperms.usage.sponge-permission-set.argument.tristate=de waarde om de permissie in te stellen
luckperms.usage.sponge-permission-set.argument.contexts=de context om permissies in te stellen
luckperms.usage.sponge-permission-clear.description=Wist de permissies van het Object
luckperms.usage.sponge-permission-clear.argument.contexts=de context om permissies te verwijderen
luckperms.usage.sponge-parent-info.description=Toont informatie over bovenliggende groepen van het object
luckperms.usage.sponge-parent-info.argument.contexts=de context om op te filteren
luckperms.usage.sponge-parent-add.description=Voegt een parent toe aan het Onderwerp
luckperms.usage.sponge-parent-add.argument.collection=de verzameling waar de bovenliggende groep Onderwerp is
luckperms.usage.sponge-parent-add.argument.subject=de naam van het parent Onderwerp
luckperms.usage.sponge-parent-add.argument.contexts=de context om de bovenliggende groep in toe te voegen
luckperms.usage.sponge-parent-remove.description=Voegt een bovenliggende groep toe aan het Onderwerp
luckperms.usage.sponge-parent-remove.argument.collection=de verzameling waar de bovenliggende groep Onderwerp is
luckperms.usage.sponge-parent-remove.argument.subject=de naam van de bovenliggende groep van het Onderwerp
luckperms.usage.sponge-parent-remove.argument.contexts=de contexten om de bovenliggende groep mee te verwijderen
luckperms.usage.sponge-parent-clear.description=Wist de bovenliggende groep van het Object
luckperms.usage.sponge-parent-clear.argument.contexts=de contexten waarin bovenliggende groepen worden gewist in
luckperms.usage.sponge-option-info.description=Toont informatie over opties van het object
luckperms.usage.sponge-option-info.argument.contexts=de context om op te filteren
luckperms.usage.sponge-option-set.description=Stelt een optie voor het onderwerp in
luckperms.usage.sponge-option-set.argument.key=de sleutel om in te stellen
luckperms.usage.sponge-option-set.argument.value=de waarde om de sleutel in te stellen
luckperms.usage.sponge-option-set.argument.contexts=de context om de optie in te stellen
luckperms.usage.sponge-option-unset.description=Verwijderd een optie voor het Object
luckperms.usage.sponge-option-unset.argument.key=de sleutel om te verwijderen
luckperms.usage.sponge-option-unset.argument.contexts=de context om de sleutel te verwijderen
luckperms.usage.sponge-option-clear.description=Wist de opties van het Object
luckperms.usage.sponge-option-clear.argument.contexts=de context om de optie te verwijderen
