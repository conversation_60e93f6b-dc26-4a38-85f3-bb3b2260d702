luckperms.logs.actionlog-prefix=LOG
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=EKSPOR
luckperms.commandsystem.available-commands=Gunakan {0} untuk menampilkan command yang tersedia
luckperms.commandsystem.command-not-recognised=Perintah tidak dikenali
luckperms.commandsystem.no-permission=Anda tidak memiliki izin untuk menggunakan perintah ini\!
luckperms.commandsystem.no-permission-subcommands=Anda tidak memiliki izin untuk menggunakan anakan perintah apapun
luckperms.commandsystem.already-executing-command=Perintah yang lain sedang dijalankan, sedang menunggu perintah selesai...
luckperms.commandsystem.usage.sub-commands-header=Anakan perintah
luckperms.commandsystem.usage.usage-header=Penggunaan perintah
luckperms.commandsystem.usage.arguments-header=Argumen
luckperms.first-time.no-permissions-setup=Nampaknya belum ada izin yang disiapkan\!
luckperms.first-time.use-console-to-give-access=Sebelum kamu bisa menggunakan salah satu dari perintah LuckPerms di dalam game, kamu harus menggunakan konsol untuk mendapatkan akses
luckperms.first-time.console-command-prompt=Silahkan buka terminalmu dan jalankan perintah
luckperms.first-time.next-step=Setelah kamu melakukan ini, kamu bisa bisa mulai untuk menentukan tugas perizinan dan grup anda
luckperms.first-time.wiki-prompt=Apakah kamu tau bagaimana memulainya? Silahkan check disini\: {0}
luckperms.login.try-again=Silakan coba lagi nanti
luckperms.login.loading-database-error=Error pada database terjadi ketika memuat data tentang perizinan
luckperms.login.server-admin-check-console-errors=Jika kamu adalah admin pada server, silahkan gunakan konsol untuk mengecek error
luckperms.login.server-admin-check-console-info=Silahkan cek konsol pada server untuk informasi lebih lanjut
luckperms.login.data-not-loaded-at-pre=Data perizinan untuk pengguna anda tidak dimuat ketika tahap pre-login
luckperms.login.unable-to-continue=tidak dapat melanjutkan
luckperms.login.craftbukkit-offline-mode-error=nampaknya hal ini terjadi dikarenakan ada pertentangan antara CraftBukkit dan pengaturan mode online
luckperms.login.unexpected-error=Kesalahan yang tidak terduga terjadi ketika mengatur data perizinan kamu
luckperms.opsystem.disabled=Sistem Operator vanilla di non aktifkan di server ini
luckperms.opsystem.sponge-warning=Tolong diperhatikan bahwa status Operator Server tidak memiliki pengaruh pada pengecekan perizinan sponge ketika sebuah plugin perizinan diinstall, kamu harus mengedit data pengguna secara langsung
luckperms.duration.unit.years.plural={0} tahun
luckperms.duration.unit.years.singular={0} tahun
luckperms.duration.unit.years.short={0}thn
luckperms.duration.unit.months.plural={0} Bulan
luckperms.duration.unit.months.singular={0} Bulan
luckperms.duration.unit.months.short={0} B
luckperms.duration.unit.weeks.plural={0} Minggu
luckperms.duration.unit.weeks.singular={0} Minggu
luckperms.duration.unit.weeks.short={0}mgg
luckperms.duration.unit.days.plural={0} Hari
luckperms.duration.unit.days.singular={0} Hari
luckperms.duration.unit.days.short={0} H
luckperms.duration.unit.hours.plural={0} Jam
luckperms.duration.unit.hours.singular={0} Jam
luckperms.duration.unit.hours.short={0} J
luckperms.duration.unit.minutes.plural={0} Menit
luckperms.duration.unit.minutes.singular={0} Menit
luckperms.duration.unit.minutes.short={0} M
luckperms.duration.unit.seconds.plural={0} Detik
luckperms.duration.unit.seconds.singular={0} Detik
luckperms.duration.unit.seconds.short={0}D
luckperms.duration.since={0} yang lalu
luckperms.command.misc.invalid-code=Kode tidak valid
luckperms.command.misc.response-code-key=respon kode
luckperms.command.misc.error-message-key=pesan
luckperms.command.misc.bytebin-unable-to-communicate=Tidak dapat berkomunikasi dengan bytebin
luckperms.command.misc.webapp-unable-to-communicate=Tidak dapat berkomunikasi dengan aplikasi webnya
luckperms.command.misc.check-console-for-errors=Silahkan cek konsol untuk melihat error
luckperms.command.misc.file-must-be-in-data=File {0} harus merupakan turunan langsung dari direktori data tersebut
luckperms.command.misc.wait-to-finish=Silahkan tunggu sampai selesai dan coba lagi
luckperms.command.misc.invalid-priority=Prioritas tidak valid {0}
luckperms.command.misc.expected-number=Harus memiliki angka
luckperms.command.misc.date-parse-error=Tidak dapat menguraikan tanggal {0}
luckperms.command.misc.date-in-past-error=Kamu tidak bisa mengatur tanggal yang sudah lewat\!
luckperms.command.misc.page=halaman {0} dari {1}
luckperms.command.misc.page-entries={0} entri
luckperms.command.misc.none=Tidak ada
luckperms.command.misc.loading.error.unexpected=Terjadi error yang tidak terduga
luckperms.command.misc.loading.error.user=Pengguna tidak termuat
luckperms.command.misc.loading.error.user-specific=Tidak bisa memuat target pengguna {0}
luckperms.command.misc.loading.error.user-not-found=Pengguna {0} tidak dapat ditemukan
luckperms.command.misc.loading.error.user-save-error=Terdapat kesalahan ketika menyimpan data pengguna untuk {0}
luckperms.command.misc.loading.error.user-not-online=Pengguna {0} sedang tidak online
luckperms.command.misc.loading.error.user-invalid={0} bukan nama pengguna yang sah/uuid
luckperms.command.misc.loading.error.user-not-uuid=Target pengguna {0} bukan uuid yang sah
luckperms.command.misc.loading.error.group=Grup tidak dapat dimuat
luckperms.command.misc.loading.error.all-groups=Tidak dapat memuat semua grup
luckperms.command.misc.loading.error.group-not-found=Grup dengan nama {0} tidak ditemukan
luckperms.command.misc.loading.error.group-save-error=Terdapat error ketika menyimpan data grup untuk {0}
luckperms.command.misc.loading.error.group-invalid={0} bukanlah nama group yang valid
luckperms.command.misc.loading.error.track=Track tidak dapat dimuat
luckperms.command.misc.loading.error.all-tracks=Tidak dapat memuat semua track
luckperms.command.misc.loading.error.track-not-found=Track dengan nama {0} tidak ditemukan
luckperms.command.misc.loading.error.track-save-error=Terjadi kesalahan ketika menyimpan track data untuk {0}
luckperms.command.misc.loading.error.track-invalid={0} bukanlah nama track yang sah
luckperms.command.editor.no-match=Tidak dapat membuka editor, tidak ada objek yang cocok dengan tipe yang diinginkan
luckperms.command.editor.start=Sedang menyiapkan sesi editor yang baru, mohon tunggu...
luckperms.command.editor.url=Klik tautan dibawah ini untuk membuka editor
luckperms.command.editor.unable-to-communicate=Tidak dapat berkomunikasi dengan editor
luckperms.command.editor.apply-edits.success=Data editor web sudah diterapkan ke {0} {1} dengan sukses
luckperms.command.editor.apply-edits.success-summary={0} {1} dan {2} {3}
luckperms.command.editor.apply-edits.success.additions=tambahan
luckperms.command.editor.apply-edits.success.additions-singular=tambahan
luckperms.command.editor.apply-edits.success.deletions=penghapusan
luckperms.command.editor.apply-edits.success.deletions-singular=penghapusan
luckperms.command.editor.apply-edits.no-changes=Tidak ada perubahan yang diaplikasikan dari web editor, data yang dipulangkan tidak memiliki perubahan apapun
luckperms.command.editor.apply-edits.unknown-type=Tidak dapat mengaplikasikan perubahan untuk tipe objek yang spesifik
luckperms.command.editor.apply-edits.unable-to-read=Tidak dapat membaca data menggunakan kode yang diberikan
luckperms.command.search.searching.permission=Sedang mencari untuk pengguna dan group dengan {0}
luckperms.command.search.searching.inherit=Sedang mencari pengguna dan grup yang mewarisi {0}
luckperms.command.search.result=Menemukan {0} entri dari {1} pengguna dan {2} grup
luckperms.command.search.result.default-notice=Catatan\: ketika mencari member di grup default, pemain offline tanpa perizinan lain akan ditampilkan\!
luckperms.command.search.showing-users=Menampilakan entri pengguna
luckperms.command.search.showing-groups=Menampilkan entri pengguna
luckperms.command.tree.start=Membuat izin pohon, mohon ditunggu...
luckperms.command.tree.empty=Tidak dapat membuat pohon, tidak ada hasil yang ditemukan
luckperms.command.tree.url=URL Izin pohon
luckperms.command.verbose.invalid-filter={0} bukan filter verbose yang benar
luckperms.command.verbose.enabled=Verbose mencatat {0} untuk pemeriksaan sepadan {1}
luckperms.command.verbose.command-exec=Memaksa {0} untuk menjalankan command {1} dan melaporkan semua pemeriksaan...
luckperms.command.verbose.off=Verbose mencatat {0}
luckperms.command.verbose.command-exec-complete=Eksekusi perintah selesai
luckperms.command.verbose.command.no-checks=Ekseskusi perintah kemungkinan telah selesai, tapi tidak ada cek perizinan yang dibuat
luckperms.command.verbose.command.possibly-async=Ini bisa disebabkan karena plugin menjalankan perintah pada latar belakang (tidak sinkron)
luckperms.command.verbose.command.try-again-manually=Anda dapat menggunakan verbose secara manual untuk mendeteksi pemeriksaan yang dibuat seperti ini
luckperms.command.verbose.enabled-recording=Verbose merekam {0} untuk pemeriksaan yang cocok {1}
luckperms.command.verbose.uploading=Melog verbose {0}, Mengupload hasilnya...
luckperms.command.verbose.url=URL hasil Verbose
luckperms.command.verbose.enabled-term=diaktifkan
luckperms.command.verbose.disabled-term=dinonaktifkan
luckperms.command.verbose.query-any=APA SAJA
luckperms.command.info.running-plugin=Menjalankan
luckperms.command.info.platform-key=Platform
luckperms.command.info.server-brand-key=Merek Server
luckperms.command.info.server-version-key=Versi Server
luckperms.command.info.storage-key=Penyimpanan
luckperms.command.info.storage-type-key=Tipe
luckperms.command.info.storage.meta.split-types-key=Tipe
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=Terhubung
luckperms.command.info.storage.meta.file-size-key=Ukuran File
luckperms.command.info.extensions-key=Ekstensi
luckperms.command.info.messaging-key=Pesan
luckperms.command.info.instance-key=Contoh
luckperms.command.info.static-contexts-key=Konteks statis
luckperms.command.info.online-players-key=Pemain online
luckperms.command.info.online-players-unique={0} unik
luckperms.command.info.uptime-key=Masa aktif
luckperms.command.info.local-data-key=Data Lokal
luckperms.command.info.local-data={0} pengguna, {1} grup, {2} tracks
luckperms.command.generic.create.success={0} telah selesai dibuat
luckperms.command.generic.create.error=Terdapat kesalahan ketika membuat {0}
luckperms.command.generic.create.error-already-exists={0} sudah ada\!
luckperms.command.generic.delete.success={0} berhasil di hapus
luckperms.command.generic.delete.error=Terdapat kesalahan ketika menghapus {0}
luckperms.command.generic.delete.error-doesnt-exist={0} tidak ada\!
luckperms.command.generic.rename.success={0} telah berhasil diganti ke {1}
luckperms.command.generic.clone.success={0} telah berhasil di klon ke {1}
luckperms.command.generic.info.parent.title=Grup Induk
luckperms.command.generic.info.parent.temporary-title=Grup Induk Sementara
luckperms.command.generic.info.expires-in=kadaluarsa dalam
luckperms.command.generic.info.inherited-from=turunkan dari
luckperms.command.generic.info.inherited-from-self=diri sendiri
luckperms.command.generic.show-tracks.title={0}''s Tracks
luckperms.command.generic.show-tracks.empty={0} sedang tidak dalam track manapun
luckperms.command.generic.clear.node-removed={0} node telah dihapus
luckperms.command.generic.clear.node-removed-singular={0} node telah dihapus
luckperms.command.generic.clear=Kelompok {0} telah di hapus dalam konteks {1}
luckperms.command.generic.permission.info.title=Perizinan milik {0}
luckperms.command.generic.permission.info.empty={0} tidak memiliki set perizinan apapun
luckperms.command.generic.permission.info.click-to-remove=Klik untuk menghapus node ini dari {0}
luckperms.command.generic.permission.check.info.title=Informasi perizinan untuk {0}
luckperms.command.generic.permission.check.info.directly={0} harus {1} diatur ke {2} dalam konteks {3}
luckperms.command.generic.permission.check.info.inherited={0} inhertis {1} jadi {2} dari {3} di konteks {4}
luckperms.command.generic.permission.check.info.not-directly={0} tidak memiliki {1} pengaturan
luckperms.command.generic.permission.check.info.not-inherited={0} tidak mewarisi {1}
luckperms.command.generic.permission.check.result.title=Mengecek perizinan untuk {0}
luckperms.command.generic.permission.check.result.result-key=Hasil
luckperms.command.generic.permission.check.result.processor-key=Prosesor
luckperms.command.generic.permission.check.result.cause-key=Penyebab
luckperms.command.generic.permission.check.result.context-key=Konteks
luckperms.command.generic.permission.set=Mengatur kunci meta {0} ke {1} untuk {2} dalam konteks {3}
luckperms.command.generic.permission.already-has={0} telah memiliki {1} dalam konteks {2}
luckperms.command.generic.permission.set-temp=Mengatur {0} ke {1} untuk {2} selama {3} di konteks {4}
luckperms.command.generic.permission.already-has-temp={0} tidak memiliki {1} untuk sementara dalam konteks {2}
luckperms.command.generic.permission.unset=Merubah {0} ke {1} dalam konteks {2}
luckperms.command.generic.permission.doesnt-have={0} tidak memiliki {1} dalam konteks {2}
luckperms.command.generic.permission.unset-temp=Merubah sementara perizinan {0} ke {1} dalam konteks {2}
luckperms.command.generic.permission.subtract=Mengatur {0} ke {1} untuk {2} selama {3} di konteks {4}. {5} lebih sedikit dari sebelumnya
luckperms.command.generic.permission.doesnt-have-temp={0} tidak memiliki perizinan sementara {1} dalam konteks {2}
luckperms.command.generic.permission.clear=Perizinan {0} telah di hapus dalam konteks {1}
luckperms.command.generic.parent.info.title=Kelompok {0}
luckperms.command.generic.parent.info.empty={0} tidak memiliki kelompok yang ditentukan
luckperms.command.generic.parent.info.click-to-remove=Klik untuk menghapus kelompok ini dari {0}
luckperms.command.generic.parent.add={0} sekarang memiliki perizinan dari {1} dalam konteks {2}
luckperms.command.generic.parent.add-temp={0} sekarang memiliki perizinan dari {1} untuk jangka waktu {2} dalam konteks {3}
luckperms.command.generic.parent.set={0} mendapatkan parent groupsnya dihapus, sekarang hanya memiliki inherits {1} di konteks {2}
luckperms.command.generic.parent.set-track={0} mendapatkan groupnya di track {1} dihapuskan, dan sekarang hanya inherits {2} di konteks {3}
luckperms.command.generic.parent.remove={0} tidak lagi memiliki perizinan dari {1} dalam konteks {2}
luckperms.command.generic.parent.remove-temp={0} tidak lagi memiliki perizinan sementara dari {1} dalam konteks {2}
luckperms.command.generic.parent.subtract={0} akan mewarisi perizinan dari {1} untuk jangka waktu {2} dalam konteks {3}, {4} lebih sedikit dari sebelumnya
luckperms.command.generic.parent.clear=Kelompok {0} telah di hapus dalam konteks {1}
luckperms.command.generic.parent.clear-track=Kelompok {0} pada track {2} telah di hapus dalam konteks {1}
luckperms.command.generic.parent.already-inherits={0} telah mewarisi dari {1} dalam konteks {2}
luckperms.command.generic.parent.doesnt-inherit={0} tidak mewarisi dari {1} dalam konteks {2}
luckperms.command.generic.parent.already-temp-inherits={0} sementara sudah mewarisi dari {1} dalam konteks {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} sementara tidak mewarisi dari {1} dalam konteks {2}
luckperms.command.generic.chat-meta.info.title-prefix=Awalan {0}
luckperms.command.generic.chat-meta.info.title-suffix=Akhiran {0}
luckperms.command.generic.chat-meta.info.none-prefix={0} tidak memiliki awalan
luckperms.command.generic.chat-meta.info.none-suffix={0} tidak memiliki akhiran
luckperms.command.generic.chat-meta.info.click-to-remove=Klik untuk menghapus {0} dari {1}
luckperms.command.generic.chat-meta.already-has={0} telah memiliki {1} {2} diatur pada prioritas dari {3} dalam konteks {4}
luckperms.command.generic.chat-meta.already-has-temp={0} telah memiliki {1} {2} sementara diatur pada prioritas dari {3} dalam konteks {4}
luckperms.command.generic.chat-meta.doesnt-have={0} tidak memiliki {1} {2} diatur pada prioritas dari {3} dalam konteks {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} tidak memiliki {1} {2} untuk sementara yang diatur pada prioritas dari {3} dalam konteks {4}
luckperms.command.generic.chat-meta.add={0} memiliki {1} {2} diatur pada prioritas dari {3} dalam konteks {4}
luckperms.command.generic.chat-meta.add-temp={0} memiliki {1} {2} diatur pada prioritas dari {3} dengan durasi {4} dalam konteks {5}
luckperms.command.generic.chat-meta.remove={0} memiliki {1} {2} diatur pada prioritas dari {3} dan dihapus dalam konteks {4}
luckperms.command.generic.chat-meta.remove-bulk={0} memiliki semua {1} diatur pada prioritas dari {2} dan dihapus dalam konteks {3}
luckperms.command.generic.chat-meta.remove-temp={0} memiliki {1} {2} untuk sementara dan diatur pada prioritas dari {3} dan dihapus dalam konteks {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} memiliki semua {1} untuk sementara diatur pada prioritas dari {2} dan dihapus dalam konteks {3}
luckperms.command.generic.meta.info.title={0} Meta
luckperms.command.generic.meta.info.none={0} tidak memiliki meta
luckperms.command.generic.meta.info.click-to-remove=Klik untuk menghapus node meta ini dari {0}
luckperms.command.generic.meta.already-has={0} sudah memiliki kunci meta {1} diatur ke {2} dalam konteks {3}
luckperms.command.generic.meta.already-has-temp={0} sudah memiliki kunci meta {1} diatur sementara ke {2} dalam konteks {3}
luckperms.command.generic.meta.doesnt-have={0} tidak memiliki kunci meta {1} dalam konteks {2}
luckperms.command.generic.meta.doesnt-have-temp={0} tidak memiliki kunci meta {1} yang diatur sementara dalam konteks {2}
luckperms.command.generic.meta.set=Mengatur kunci meta {0} ke {1} untuk {2} dalam konteks {3}
luckperms.command.generic.meta.set-temp=Mengatur kunci meta {0} ke {1} untuk {2} dengan durasi {3} dalam konteks {4}
luckperms.command.generic.meta.unset=Merubah kunci meta {0} ke {1} dalam konteks {2}
luckperms.command.generic.meta.unset-temp=Merubah sementara kunci meta {0} ke {1} dalam konteks {2}
luckperms.command.generic.meta.clear={0} punya meta matching tipe {1} di hapus konteks {2}
luckperms.command.generic.contextual-data.title=Data yang berhubungan dengan konteks
luckperms.command.generic.contextual-data.mode.key=mode
luckperms.command.generic.contextual-data.mode.server=server
luckperms.command.generic.contextual-data.mode.active-player=pemain aktif
luckperms.command.generic.contextual-data.contexts-key=Konteks
luckperms.command.generic.contextual-data.prefix-key=Awalan
luckperms.command.generic.contextual-data.suffix-key=Akhiran
luckperms.command.generic.contextual-data.primary-group-key=Grup utama
luckperms.command.generic.contextual-data.meta-key=Meta
luckperms.command.generic.contextual-data.null-result=Tidak ada
luckperms.command.user.info.title=Info Pengguna
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=tipe
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=offline
luckperms.command.user.info.status-key=Status
luckperms.command.user.info.status.online=Online
luckperms.command.user.info.status.offline=Offline
luckperms.command.user.removegroup.error-primary=Kamu tidak dapat menghapus pengguna dari grup utamanya
luckperms.command.user.primarygroup.not-member={0} belum menjadi anggota dari {1}, sedang menambahkannya sekarang
luckperms.command.user.primarygroup.already-has={0} telah memiliki {1} sebagai kelompok utama
luckperms.command.user.primarygroup.warn-option=Peringatan\: Metode perhitungan group pertama yang digunakan oleh server ini ({0}) tidak akan mempengaruhi perubahan ini
luckperms.command.user.primarygroup.set=Grup utama {0} telah di atur ke {1}
luckperms.command.user.track.error-not-contain-group={0} sedang tidak berada dalam kelompok apapun pada {1}
luckperms.command.user.track.unsure-which-track=Tidak yakin track apa yang ingin digunakan, tolong menjelaskan itu sebangai argument
luckperms.command.user.track.missing-group-advice=Silahkan buat grup atau menghapusnya dari track dan coba lagi
luckperms.command.user.promote.added-to-first={0} tidak terdapat pada grup apapun dalam {1}, maka {0} ditambahkan ke grup pertama, {2} dalam konteks {3}
luckperms.command.user.promote.not-on-track={0} tidak terdapat dalam grup apapun dalam {1}, maka tidak di promosikan
luckperms.command.user.promote.success=Mempromote {0} dengan track {1} dari track {2} ke {3} di kontexs {4}
luckperms.command.user.promote.end-of-track=Akhir track {0} tercapai, tidak dapat mempromosikan {1}
luckperms.command.user.promote.next-group-deleted=Grup selanjutnya dalam track, {0}, sudah tidak ada
luckperms.command.user.promote.unable-to-promote=Tidak dapat mempromosikan pengguna
luckperms.command.user.demote.success=Mendemosikan {0} sepanjang track {1} dari {2} ke {3} dalam konteks {4}
luckperms.command.user.demote.end-of-track=Akhir dari track {0} sudah tercapai, maka {1} dihapus dari {2}
luckperms.command.user.demote.end-of-track-not-removed=Akhir dari track {0} sudah tercapai, tetapi {1} tidak dihapus dari grup pertama
luckperms.command.user.demote.previous-group-deleted=Grup sebelumnya dalam track, {0}, sudah tidak ada
luckperms.command.user.demote.unable-to-demote=Tidak dapat menurunkan pengguna
luckperms.command.group.list.title=Grup
luckperms.command.group.delete.not-default=Anda tidak bisa menghapus kelompok default
luckperms.command.group.info.title=Info Grup
luckperms.command.group.info.display-name-key=Nama Tampilan
luckperms.command.group.info.weight-key=Berat
luckperms.command.group.setweight.set=Mengatur berat ke {0} dari kelompok {1}
luckperms.command.group.setdisplayname.doesnt-have={0} tidak memiliki nama tampilan untuk diterapkan
luckperms.command.group.setdisplayname.already-has={0} sudah memiliki nama tampilan dari {1}
luckperms.command.group.setdisplayname.already-in-use=Nama tampilan {0} sedang digunakan oleh {1}
luckperms.command.group.setdisplayname.set=Mengatur nama tampilan ke {0} untuk kelompok {1} dalam konteks {2}
luckperms.command.group.setdisplayname.removed=Telah menghapus nama untuk kelompok {0} dalam konteks {1}
luckperms.command.track.list.title=Trek
luckperms.command.track.path.empty=Tidak ada
luckperms.command.track.info.showing-track=Menampilkan Track
luckperms.command.track.info.path-property=Jejak
luckperms.command.track.clear=kelompok track {0} telah di hapus
luckperms.command.track.append.success=Kelompok {0} telah ditambahkan ke track {1}
luckperms.command.track.insert.success=Kelompok {0} telah dimasukkan ke track {1} pada posisi {2}
luckperms.command.track.insert.error-number=Jumlah yang diharapkan tetapi mendapat\: {0}
luckperms.command.track.insert.error-invalid-pos=Tidak dapat memasukkan pada posisi {0}
luckperms.command.track.insert.error-invalid-pos-reason=posisi tidak sah
luckperms.command.track.remove.success=Kelompok {0} telah dihapus dari track {1}
luckperms.command.track.error-empty={0} tidak bisa digunakan ketika kosong atau hanya berisi satu kelompok
luckperms.command.track.error-multiple-groups={0} adalah anggota dari beberapa kelompok dalam track ini
luckperms.command.track.error-ambiguous=Tidak dapat menemukan lokasi mereka
luckperms.command.track.already-contains={0} sudah berisi {1}
luckperms.command.track.doesnt-contain={0} tidak berisi {1}
luckperms.command.log.load-error=Log nya tidak dapat dimuat
luckperms.command.log.invalid-page=Nomor halaman tidak sah
luckperms.command.log.invalid-page-range=Silahkan masukkan nilai antara {0} dan {1}
luckperms.command.log.empty=Tidak ada entri log untuk ditampilkan
luckperms.command.log.notify.error-console=Tidak dapat mengalihkan notifikasi ke konsol
luckperms.command.log.notify.enabled-term=Diaktifkan
luckperms.command.log.notify.disabled-term=Dimatikan
luckperms.command.log.notify.changed-state={0} mengeluarkan log
luckperms.command.log.notify.already-on=Kamu sudah menerima notifikasi
luckperms.command.log.notify.already-off=Kamu sedang tidak menerima notfikasi
luckperms.command.log.notify.invalid-state=State tidak diketahui. Mengharapkan {0} atau {1}
luckperms.command.log.show.search=Menampilkan tindakan terbaru untuk kueri {0}
luckperms.command.log.show.recent=Menampilkan tindakan terbaru
luckperms.command.log.show.by=Menampilkan tindakan terbaru berdasarkan {0}
luckperms.command.log.show.history=Menunjukkan histori dari {0} {1}
luckperms.command.export.error-term=Kesalahan
luckperms.command.export.already-running=Telah ada proses ekspor lain yang berjalan
luckperms.command.export.file.already-exists=File {0} telah ada
luckperms.command.export.file.not-writable=File {0} tidak dapat ditulis
luckperms.command.export.file.success=Berhasil diekspor ke {0}
luckperms.command.export.file-unexpected-error-writing=Sebuah kesalahan yang tidak terduga terjadi ketika menulis file Anda
luckperms.command.export.web.export-code=Ekspor kode
luckperms.command.export.web.import-command-description=Gunakan perintah berikut untuk mengimpor
luckperms.command.import.term=Impor
luckperms.command.import.error-term=Kesalahan
luckperms.command.import.already-running=Proses impor lain sedang berjalan
luckperms.command.import.file.doesnt-exist=File {0} tidak ada
luckperms.command.import.file.not-readable=File {0} tidak dapat dibaca
luckperms.command.import.file.unexpected-error-reading=Sebuah kesalahan tidak terduga terjadi ketika membaca file yang di import
luckperms.command.import.file.correct-format=apakan itu adalah format yang benar?
luckperms.command.import.web.unable-to-read=Tidak dapat membaca data menggunakan kode yang diberikan
luckperms.command.import.progress.percent={0}% telah Selesai
luckperms.command.import.progress.operations={0}/{1} operasi selesai
luckperms.command.import.starting=Memulai proses import
luckperms.command.import.completed=SELESAI
luckperms.command.import.duration=membutuhkan {0} detik
luckperms.command.bulkupdate.must-use-console=Perintah pembaruan massal hanya dapat digunakan dari konsol
luckperms.command.bulkupdate.invalid-data-type=Jenis tidak valid, mengharapkan {0}
luckperms.command.bulkupdate.invalid-constraint=Batasan tidak valid {0}
luckperms.command.bulkupdate.invalid-constraint-format=Batasan harus dalam format {0}
luckperms.command.bulkupdate.invalid-comparison=Operator perbandingan tidak valid {0}
luckperms.command.bulkupdate.invalid-comparison-format=Diharapkan salah satu dari yang berikut ini\: {0}
luckperms.command.bulkupdate.queued=Operasi pembaruan massal telah diantrekan
luckperms.command.bulkupdate.confirm=Menjalankan {0} untuk mengeksekusi update
luckperms.command.bulkupdate.unknown-id=Operasi dengan id {0} tidak ada atau sudah kadaluwarsa
luckperms.command.bulkupdate.starting=Menjalankan pembaruan massal
luckperms.command.bulkupdate.success=Pembaruan masal seelesai dengan sukses
luckperms.command.bulkupdate.success.statistics.nodes=Total node yang terpengaruh
luckperms.command.bulkupdate.success.statistics.users=Total pengguna yang terpengaruh
luckperms.command.bulkupdate.success.statistics.groups=Total grup yang terpengaruh
luckperms.command.bulkupdate.failure=Pembaruan masal gagal, periksa konsol untuk mengetahui kesalahan
luckperms.command.update-task.request=Proses update telah diminta, mohon tunggu
luckperms.command.update-task.complete=Tugas update selesai
luckperms.command.update-task.push.attempting=Memulai untuk mendorong ke server lain
luckperms.command.update-task.push.complete=Berhasil memberitahu server lainnya melalui {0}
luckperms.command.update-task.push.error=Error ketiak mengirim perubahan ke server lain
luckperms.command.update-task.push.error-not-setup=Tidak dapat mengirim perubahan ke server lain dikarenakan "messaging service" belum di configurasi
luckperms.command.reload-config.success=File konfigurasi telah dimuat ulang
luckperms.command.reload-config.restart-note=beberapa pengaturan hanya berlaku setelah server di restart
luckperms.command.translations.searching=Sedang mencari untuk terjemahan yang tersedia, mohon bersabar...
luckperms.command.translations.searching-error=Tidak dapat memperoleh daftar terjemahan yang tersedia
luckperms.command.translations.installed-translations=Terjemahan terinstal
luckperms.command.translations.available-translations=Terjemahan tersedia
luckperms.command.translations.percent-translated={0}% sudah diterjemahkan
luckperms.command.translations.translations-by=oleh
luckperms.command.translations.installing=Menginstall terjemahan, harap tunggu...
luckperms.command.translations.download-error=Tidak dapat mendownload terjemahan untuk {0}
luckperms.command.translations.installing-specific=Menginstall bahasa {0}...
luckperms.command.translations.install-complete=Instalasi selesai
luckperms.command.translations.download-prompt=Gunakan {0} untuk mendownload dan menginstal ke versi terbaru dari terjemahan yang disediakan oleh komunitas
luckperms.command.translations.download-override-warning=Harap dicatat bahwa ini akan mengesampingkan perubahan apa pun yang sudah Anda buat untuk bahasa-bahasa ini
luckperms.usage.user.description=Satu set perintah untuk mengelola pengguna dalam LuckPerms. (''Pengguna'' di LuckPerms hanyalah pemain, dan dapat merujuk ke UUID atau nama pengguna)
luckperms.usage.group.description=Satu set perintah untuk mengelola grup dalam LuckPerms. Grup hanyalah kumpulan penetapan izin yang dapat diberikan kepada pengguna. Grup baru dibuat menggunakan perintah ''creategroup''.
luckperms.usage.track.description=Seperangkat perintah untuk mengelola tracks dalam LuckPerms. Tracks adalah kumpulan grup yang dapat digunakan untuk mendefinisikan promosi dan demosi.
luckperms.usage.log.description=Seperangkat perintah untuk mengelola fungsionalitas log dalam LuckPerms.
luckperms.usage.sync.description=Memuat ulang semua data dari penyimpanan plugin ke memori dan mengaplikasikan semua perubahan yang terdeteksi.
luckperms.usage.info.description=Menyeteak semua informasi umum tentang plugin yang aktif.
luckperms.usage.editor.description=Buat sesi editor situs web baru
luckperms.usage.editor.argument.type=jenis yang akan dimuat ke dalam editor. (''all'', ''users'' atau ''grup'')
luckperms.usage.editor.argument.filter=izin untuk memfilter entri pengguna menurut
luckperms.usage.verbose.description=Mengontrol sistem pemantauan pemeriksaan izin verbose plugin.
luckperms.usage.verbose.argument.action=apakah ingin mengaktifkan/menonaktifkan logging, atau mengunggah output yang dicatat
luckperms.usage.verbose.argument.filter=filter untuk mencocokkan entri terhadap
luckperms.usage.verbose.argument.commandas=pemain/perintah untuk dijalankan
luckperms.usage.tree.description=Menghasilkan tampilan pohon (hierarki daftar yang diurutkan) dari semua izin yang diketahui oleh LuckPerms.
luckperms.usage.tree.argument.scope=akar pohon. Tentukan "." untuk memasukkan semua izin
luckperms.usage.tree.argument.player=nama pemain online yang diperiksa
luckperms.usage.search.description=Mencari semua pengguna/grup dengan izin spesifik
luckperms.usage.search.argument.permission=ijin untuk mencari
luckperms.usage.search.argument.page=halaman untuk ditampilkan
luckperms.usage.network-sync.description=Sinkronkan perubahan dengan penyimpanan dan minta agar semua server lain di jaringan melakukan hal yang sama
luckperms.usage.import.description=Import data dari file yang sudah di export
luckperms.usage.import.argument.file=file untuk di impor dari
luckperms.usage.import.argument.replace=mengganti data yang tersedia dari pada menggabungkan
luckperms.usage.import.argument.upload=mengupload data dari ekspor sebelumnya
luckperms.usage.export.description=Mengekspor semua data perizinan ke ''ekspor'' file. Dapat di impor kembali nantinya.
luckperms.usage.export.argument.file=file untuk di ekspor ke
luckperms.usage.export.argument.without-users=mengecualikan pengguna dari ekspor
luckperms.usage.export.argument.without-groups=mengecualikan kelompok dari ekspor
luckperms.usage.export.argument.upload=Mengupload semua data perizinan ke webeditor. Dapat di impor kembali nantinya.
luckperms.usage.reload-config.description=Memuat ulang beberapa opsi konfigurasi
luckperms.usage.bulk-update.description=Jalankan kueri perubahan massal pada semua data
luckperms.usage.bulk-update.argument.data-type=tipe data yang sedang diganti. (''semua'', ''pengguna'' atau ''kelompok'')
luckperms.usage.bulk-update.argument.action=tindakan yang akan dilakukan pada data. (''perbarui'' atau ''hapus'')
luckperms.usage.bulk-update.argument.action-field=bidang untuk bertindak. hanya diperlukan untuk ''pembaruan''. (''izin'', ''server'' atau ''dunia'')
luckperms.usage.bulk-update.argument.action-value=nilai yang akan diganti. hanya diperlukan untuk ''pembaruan''.
luckperms.usage.bulk-update.argument.constraint=kendala yang diperlukan untuk pembaruan
luckperms.usage.translations.description=Mengelola terjemahan
luckperms.usage.translations.argument.install=subperintah untuk memasang terjemahan
luckperms.usage.apply-edits.description=Menerapkan perubahan perzinan yang dibuat dari web editor
luckperms.usage.apply-edits.argument.code=kode unik untuk data
luckperms.usage.apply-edits.argument.target=kepada siapa data akan diterapkan
luckperms.usage.create-group.description=Membuat kelompok baru
luckperms.usage.create-group.argument.name=nama kelompok
luckperms.usage.create-group.argument.weight=berat kelompok
luckperms.usage.create-group.argument.display-name=nama tampilan kelompok
luckperms.usage.delete-group.description=Menghapus kelompok
luckperms.usage.delete-group.argument.name=nama kelompok
luckperms.usage.list-groups.description=Daftar semua kelompok pada platform
luckperms.usage.create-track.description=Membuat track baru
luckperms.usage.create-track.argument.name=nama track
luckperms.usage.delete-track.description=Menghapus track
luckperms.usage.delete-track.argument.name=nama track
luckperms.usage.list-tracks.description=Daftar semua track pada platform
luckperms.usage.user-info.description=Menampilkan info pengguna
luckperms.usage.user-switchprimarygroup.description=Mengganti kelompok utama pengguna
luckperms.usage.user-switchprimarygroup.argument.group=group untuk dialihkan
luckperms.usage.user-promote.description=Mempromosikan pengguna naik ke track
luckperms.usage.user-promote.argument.track=track telah mempromosikan pengguna naik
luckperms.usage.user-promote.argument.context=konteks untuk mempromosikan pengguna
luckperms.usage.user-promote.argument.dont-add-to-first=hanya promosikan pengguna apabila mereka telah berada pada track
luckperms.usage.user-demote.description=Menurunkan pengguna 1 kelompok
luckperms.usage.user-demote.argument.track=track untuk menurunkan pengguna
luckperms.usage.user-demote.argument.context=konteks untuk menurunkan pengguna
luckperms.usage.user-demote.argument.dont-remove-from-first=mencegah pengguna dihapus dari grup pertama
luckperms.usage.user-clone.description=Klon pengguna
luckperms.usage.user-clone.argument.user=nama/uuid dari pengguna untuk di klonkan
luckperms.usage.group-info.description=Memberikan tentang info kelompok
luckperms.usage.group-listmembers.description=Menampilkan pengguna/kelompok yang diturunkan dari kelompok ini
luckperms.usage.group-listmembers.argument.page=halaman untuk ditampilkan
luckperms.usage.group-setweight.description=Menetapkan berat kelompok
luckperms.usage.group-setweight.argument.weight=berat untuk ditetapkan
luckperms.usage.group-set-display-name.description=Menerapkan nama tampilan kelompok
luckperms.usage.group-set-display-name.argument.name=nama untuk diterapkan
luckperms.usage.group-set-display-name.argument.context=konteks untuk diatur ke nama
luckperms.usage.group-rename.description=Mengubah nama kelompok
luckperms.usage.group-rename.argument.name=nama baru
luckperms.usage.group-clone.description=Salin kelompok
luckperms.usage.group-clone.argument.name=nama dari kelompok untuk yang akan salin
luckperms.usage.holder-editor.description=Membukan perizinan web editor
luckperms.usage.holder-showtracks.description=Mendaftar track tempat objek berada
luckperms.usage.holder-clear.description=Menghapus semua perizinan, kelompok dan meta
luckperms.usage.holder-clear.argument.context=konteks untuk difilter berdasarkan
luckperms.usage.permission.description=Mengubah perizinan
luckperms.usage.parent.description=Merubah turunan
luckperms.usage.meta.description=Edit nilai metadata
luckperms.usage.permission-info.description=Mendaftar node perizinan yang dimiliki objek
luckperms.usage.permission-info.argument.page=halaman untuk ditampilkan
luckperms.usage.permission-info.argument.sort-mode=bagaimana cara mengurutkan entri
luckperms.usage.permission-set.description=Mengatur perizinan untuk objek
luckperms.usage.permission-set.argument.node=node perizinan yang akan diatur
luckperms.usage.permission-set.argument.value=nilai node
luckperms.usage.permission-set.argument.context=konteks untuk ditambahkan ke perizinan
luckperms.usage.permission-unset.description=Membatalkan perizinan untuk objek
luckperms.usage.permission-unset.argument.node=node perizinan yang akan dihapus
luckperms.usage.permission-unset.argument.context=konteks untuk menghapus izin di
luckperms.usage.permission-settemp.description=Mengatur perizinan untuk objek sementara
luckperms.usage.permission-settemp.argument.node=node perizinan yang akan diatur
luckperms.usage.permission-settemp.argument.value=nilai node
luckperms.usage.permission-settemp.argument.duration=durasi sampai node perizinan berakhir
luckperms.usage.permission-settemp.argument.temporary-modifier=bagaimana perizinan sementara seharusnya diterapkan
luckperms.usage.permission-settemp.argument.context=konteks untuk menambahkan izin di
luckperms.usage.permission-unsettemp.description=Membatalkan izin sementara untuk objek
luckperms.usage.permission-unsettemp.argument.node=simpul izin untuk dibatalkan pengaturannya
luckperms.usage.permission-unsettemp.argument.duration=durasi untuk mengurangi
luckperms.usage.permission-unsettemp.argument.context=konteks untuk menghapus izin di
luckperms.usage.permission-clear.description=Menghapus semua perizinan
luckperms.usage.parent-info.argument.page=halaman untuk ditampilkan
luckperms.usage.parent-info.argument.sort-mode=bagaimana cara mengurutkan entri
luckperms.usage.parent-set.argument.group=kelompok ditetapkan ke
luckperms.usage.parent-remove.argument.group=kelompok untuk dihapus
luckperms.usage.parent-remove-temp.argument.group=kelompok untuk dihapus
luckperms.usage.parent-clear.argument.context=konteks untuk difilter berdasarkan
luckperms.usage.parent-clear-track.argument.context=konteks untuk difilter berdasarkan
luckperms.usage.meta-info.description=Menampilkan semua meta chat
luckperms.usage.meta-set.description=Mengatur nilai meta
luckperms.usage.meta-set.argument.key=kunci untuk diterapkan
luckperms.usage.meta-set.argument.value=nilai yang kan diterapkan
luckperms.usage.meta-set.argument.context=konteks untuk ditambah pasangan meta di
luckperms.usage.meta-unset.description=Menghapus nilai meta
luckperms.usage.meta-unset.argument.key=kunci untuk dihapus
luckperms.usage.meta-unset.argument.context=konteks untuk dihapuskan pasangan meta di
luckperms.usage.meta-settemp.description=Mengatur nilai meta sementara
luckperms.usage.meta-settemp.argument.key=kunci untuk diterapkan
luckperms.usage.meta-settemp.argument.value=nilai yang kan diterapkan
luckperms.usage.meta-settemp.argument.duration=durasi sampai nilai meta berakhir
luckperms.usage.meta-settemp.argument.context=konteks untuk ditambah pasangan meta
luckperms.usage.meta-unsettemp.description=Menghapus nilai meta sementara
luckperms.usage.meta-unsettemp.argument.key=kunci untuk dihapus
luckperms.usage.meta-unsettemp.argument.context=konteks untuk dihapuskan pasangan meta di
luckperms.usage.meta-addprefix.description=Menambahkan awalan
luckperms.usage.meta-addprefix.argument.priority=prioritas untuk mengatur awalan pada
luckperms.usage.meta-addprefix.argument.prefix=string awalan
luckperms.usage.meta-addprefix.argument.context=konteks untuk ditambahkan di awalan
luckperms.usage.meta-addsuffix.description=Menambahkan akhiran
luckperms.usage.meta-addsuffix.argument.priority=prioritas untuk mengatur akhiran pada
luckperms.usage.meta-addsuffix.argument.suffix=string akhiran
luckperms.usage.meta-addsuffix.argument.context=konteks untuk ditambahkan di akhiran
luckperms.usage.meta-setprefix.description=Mengatur awalan
luckperms.usage.meta-setprefix.argument.priority=prioritas untuk mengatur awalan pada
luckperms.usage.meta-setprefix.argument.prefix=string awalan
luckperms.usage.meta-setsuffix.description=Mengatur akhiran
luckperms.usage.meta-setsuffix.argument.priority=prioritas untuk mengatur akhiran pada
luckperms.usage.meta-setsuffix.argument.suffix=string akhiran
luckperms.usage.meta-removeprefix.description=Menghapus awalan
luckperms.usage.meta-removeprefix.argument.priority=prioritas untuk mengatur awalan pada
luckperms.usage.meta-removeprefix.argument.prefix=string awalan
luckperms.usage.meta-removesuffix.description=Menghapus akhiran
luckperms.usage.meta-removesuffix.argument.priority=prioritas untuk menghapus akhiran pada
luckperms.usage.meta-removesuffix.argument.suffix=string akhiran
luckperms.usage.meta-addtemp-prefix.description=Menambahkan awalan sementara
luckperms.usage.meta-addtemp-prefix.argument.priority=prioritas untuk mengatur awalan pada
luckperms.usage.meta-addtemp-prefix.argument.prefix=string awalan
luckperms.usage.meta-addtemp-prefix.argument.duration=durasi sampai awalan berakhir
luckperms.usage.meta-addtemp-suffix.description=Menambahkan akhiran sementara
luckperms.usage.meta-addtemp-suffix.argument.priority=prioritas untuk mengatur akhiran pada
luckperms.usage.meta-addtemp-suffix.argument.suffix=string akhiran
luckperms.usage.meta-addtemp-suffix.argument.duration=durasi sampai akhiran berakhir
luckperms.usage.meta-settemp-prefix.description=Menetapkan awalan sementara
luckperms.usage.meta-settemp-prefix.argument.priority=prioritas untuk mengatur awalan pada
luckperms.usage.meta-settemp-prefix.argument.prefix=string awalan
luckperms.usage.meta-settemp-prefix.argument.duration=durasi sampai awalan berakhir
luckperms.usage.meta-settemp-suffix.description=Menetapkan akhiran sementara
luckperms.usage.meta-settemp-suffix.argument.priority=prioritas untuk mengatur akhiran pada
luckperms.usage.meta-settemp-suffix.argument.suffix=string akhiran
luckperms.usage.meta-settemp-suffix.argument.duration=durasi sampai akhiran berakhir
luckperms.usage.meta-removetemp-prefix.description=Menghapus awalan sementara
luckperms.usage.meta-removetemp-prefix.argument.priority=prioritas untuk menghapus awalan pada
luckperms.usage.meta-removetemp-prefix.argument.prefix=string awalan
luckperms.usage.meta-removetemp-suffix.description=Menghapus akhiran sementara
luckperms.usage.meta-removetemp-suffix.argument.priority=prioritas untuk menghapus akhiran pada
luckperms.usage.meta-removetemp-suffix.argument.suffix=string akhiran
luckperms.usage.meta-clear.description=Hapus semua meta
luckperms.usage.track-editor.description=Membukan perizinan web editor
luckperms.usage.track-append.argument.group=kelompok untuk ditambahkan
luckperms.usage.track-insert.argument.group=kelompok untuk dimasukkan
luckperms.usage.track-remove.description=Menghapus kelompok dari track
luckperms.usage.track-remove.argument.group=kelompok untuk dihapus
luckperms.usage.track-clear.description=Menghapus kelompok pada track
luckperms.usage.track-rename.description=Mengubah nama track
luckperms.usage.track-rename.argument.name=nama baru
luckperms.usage.track-clone.description=Salin track
luckperms.usage.track-clone.argument.name=nama dari track yang akan salin
luckperms.usage.log-recent.description=Meliihat tindakan terbaru
luckperms.usage.log-recent.argument.user=nama/uuid dari pengguna untuk di filtel berdasarkan
luckperms.usage.log-recent.argument.page=nomor halaman untuk ditampilkan
luckperms.usage.log-search.argument.page=nomor halaman untuk ditampilkan
luckperms.usage.log-user-history.description=Tampilkan histori pengguna
luckperms.usage.log-user-history.argument.user=nama/uuid dari pengguna
luckperms.usage.log-user-history.argument.page=nomor halaman untuk ditampilkan
luckperms.usage.log-group-history.description=Lihat riwayat grup
luckperms.usage.log-group-history.argument.group=nama kelompok
luckperms.usage.log-group-history.argument.page=nomor halaman untuk ditampilkan
luckperms.usage.log-track-history.description=Lihat riwayat track
luckperms.usage.log-track-history.argument.track=nama track
luckperms.usage.log-track-history.argument.page=nomor halaman untuk ditampilkan
luckperms.usage.sponge.description=Mengatur data ekstra Sponge
luckperms.usage.sponge.argument.subject=subjek yang akan di modifikasi
luckperms.usage.sponge-permission-info.description=Menampilkan informasi tentang perizinan subjek
luckperms.usage.sponge-permission-info.argument.contexts=konteks untuk memfilter
luckperms.usage.sponge-permission-set.description=Mengatur perizinan untuk subjek
luckperms.usage.sponge-permission-set.argument.node=node perizinan yang akan diatur
luckperms.usage.sponge-permission-set.argument.tristate=nilai yang akan ditetapkan untuk izin
luckperms.usage.sponge-permission-set.argument.contexts=konteks untuk ditambahkan ke perizinan
luckperms.usage.sponge-permission-clear.description=Membersihkan perizinan Subjek
luckperms.usage.sponge-permission-clear.argument.contexts=konteks untuk dihapuskan ke perizinan
luckperms.usage.sponge-parent-info.description=Menampilkan informasi tentang kelompok subjek
luckperms.usage.sponge-parent-info.argument.contexts=konteks untuk diurutkan berdasarkan
luckperms.usage.sponge-parent-add.description=Menambahkan kelompok ke Subjek
luckperms.usage.sponge-parent-add.argument.collection=kumpulan subjek tempat Subjek induk berada
luckperms.usage.sponge-parent-add.argument.subject=nama dari subjek kelompok
luckperms.usage.sponge-parent-add.argument.contexts=konteks untuk ditambahkan ke kelompok
luckperms.usage.sponge-parent-remove.description=Menghapus kelompok dari Subjek
luckperms.usage.sponge-parent-remove.argument.collection=kumpulan subjek tempat Subjek induk berada
luckperms.usage.sponge-parent-remove.argument.subject=nama dari subjek kelompok
luckperms.usage.sponge-parent-remove.argument.contexts=konteks untuk dihapuskan ke kelompok
luckperms.usage.sponge-parent-clear.description=Membersihkan kelompok Subjek
luckperms.usage.sponge-parent-clear.argument.contexts=konteks untuk dihapuskan ke kelompok
luckperms.usage.sponge-option-info.description=Menampilkan informasi tentang opsi subjek
luckperms.usage.sponge-option-info.argument.contexts=konteks untuk diurutkan berdasarkan
luckperms.usage.sponge-option-set.description=Mengatur opsi untuk subjek
luckperms.usage.sponge-option-set.argument.key=kunci untuk diterapkan
luckperms.usage.sponge-option-set.argument.value=nilai untuk mengatur kunci
luckperms.usage.sponge-option-set.argument.contexts=konteks untuk mengatur opsi
luckperms.usage.sponge-option-unset.description=Membatalkan pengaturan opsi untuk Subjek
luckperms.usage.sponge-option-unset.argument.key=kunci untuk dihapus
luckperms.usage.sponge-option-unset.argument.contexts=konteks untuk membatalkan pengaturan kunci
luckperms.usage.sponge-option-clear.description=Menghapus opsi Subjek
luckperms.usage.sponge-option-clear.argument.contexts=konteks untuk menghapus opsi di
