worlds:
  '111':
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 0.0
      y: -60.0
      z: 0.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: NORMAL
    seed: '-5275607839530720781'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world:
    ==: MVWorld
    hidden: 'false'
    alias: world
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 538.0
      y: 39.0
      z: 421.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: NORMAL
    seed: '-7914350190436031977'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world_the_end:
    ==: MVWorld
    hidden: 'false'
    alias: world_the_end
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '16.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 0.0
      y: 61.0
      z: 0.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: THE_END
    seed: '-8671074321194083589'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  Lobby:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 0.0
      y: -60.0
      z: 0.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: NORMAL
    seed: '-1278055990068221728'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world_nether:
    ==: MVWorld
    hidden: 'false'
    alias: world_nether
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '8.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 0.0
      y: 65.0
      z: 11.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: NETHER
    seed: '-8671074321194083589'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  Peak-Time:
    ==: MVWorld
    hidden: 'false'
    alias: Peak-Time
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 3.031585299551098
      y: -39.0
      z: -7.7375662473990845
      pitch: 20.250006
      yaw: -179.69994
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: NORMAL
    seed: '-5579026089211860495'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  testWorld:
    ==: MVWorld
    hidden: 'false'
    alias: testWorld
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'false'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 0.0
      y: -60.0
      z: 0.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: NORMAL
    seed: '-2639084081805998214'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  CenterRoad:
    ==: MVWorld
    hidden: 'false'
    alias: CenterRoad
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'false'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'false'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -8.716898286238411
      y: -60.0
      z: -15.782635075777245
      pitch: 9.449997
      yaw: 105.75019
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: NORMAL
    seed: '437033315899354125'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
