#
# WorldEdit's Configuration File
#
# About editing this file:
# - DO NOT USE TABS. You MUST use spaces or Buk<PERSON>t will complain and post
#   errors. If you use an editor, like Notepad++ (recommended for Windows 
#   users), you must configure it to "replace tabs with spaces."
#   This can be changed in Settings > Preferences > Language Menu.
# - Don't get rid of indentations. They are indented so some entries that are
#   in categories, like "max-blocks-changed", are placed in the "limits"
#   category.
# - If you want to check the format of this file before putting it
#   into WorldEdit, paste it into https://yaml-online-parser.appspot.com/
#   and see if it gives you "ERROR:".
# - Lines starting with # are comments, so they are ignored.
# - If you want to allow blocks, make sure to change "disallowed-blocks" to []
#

limits:
  max-blocks-changed:
    # Ignored, use FAWE config limits
    default: -1
    maximum: -1
  max-polygonal-points:
    default: -1
    maximum: 20
  # radius, superpickaxe, brush radius are ignored, use FAWE config limits
  max-radius: -1
  max-super-pickaxe-size: 5
  max-brush-radius: 100
  butcher-radius:
    default: -1
    # Ignored, use FAWE config limits
    maximum: -1
  disallowed-blocks:
    - "minecraft:wheat"
    - "minecraft:fire"
    - "minecraft:redstone_wire"

use-inventory:
  enable: false
  allow-override: true
  creative-mode-overrides: false

logging:
  log-commands: false
  file: worldedit.log
  # The format of custom log message. This is java general format string (java.util.Formatter). Arguments are:
  #   1$ : date - a Date object representing event time of the log record.
  #   2$ : source - a string representing the caller, if available; otherwise, the logger's name.
  #   3$ : logger - the logger's name.
  #   4$ : level - the log level.
  #   5$ : message - the formatted log message returned from the Formatter.formatMessage(LogRecord) method. It uses java.text formatting and does not use the java.util.Formatter format argument.
  #   6$ : thrown - a string representing the throwable associated with the log record and its backtrace beginning with a newline character, if any; otherwise, an empty string.
  # For details see:
  #   https://docs.oracle.com/javase/8/docs/api/java/util/Formatter.html
  #   https://docs.oracle.com/javase/8/docs/api/java/util/logging/SimpleFormatter.html#format-java.util.logging.LogRecord-
  format: "[%1$tY-%1$tm-%1$td %1$tH:%1$tM:%1$tS %4$s]: %5$s%6$s%n"

super-pickaxe:
  drop-items: true
  many-drop-items: false

snapshots:
  directory:

navigation-wand:
  item: minecraft:compass
  max-distance: 100

scripting:
  timeout: 3000
  dir: craftscripts

saving:
  dir: schematics

files:
  allow-symbolic-links: false

history:
  size: 15
  expiration: 10

calculation:
  timeout: 100

debugging:
  trace-unflushed-sessions: false

wand-item: minecraft:wooden_axe
shell-save-type:
no-double-slash: false
no-op-permissions: false
debug: false
show-help-on-first-use: true
server-side-cui: true
command-block-support: false
