{"prefix": "&8(&4&lFAWE&8)&7 {0}", "fawe.worldedit.history.find.element": "&2{0} {1} &7ago &3{2}m &6{3} &c/{4}", "fawe.worldedit.history.find.element.more": " - Changes: {0}\n - Bounds: {1} -> {2}\n - Extra: {3}\n - Size on Disk: {4}", "fawe.worldedit.history.find.hover": "{0} blocks changed, click for more info", "fawe.worldedit.history.distr.summary_null": "Could not find edit summary for inputs.", "fawe.info.lighting.propagate.selection": "Lighting has been propagated in {0} chunks.", "fawe.info.updated.lighting.selection": "Lighting has been updated in {0} chunks. (It may take a second for the packets to send)", "fawe.info.set.region": "Selection set to your current allowed region", "fawe.info.worldedit.toggle.tips.on": "Disabled FAWE tips.", "fawe.info.worldedit.toggle.tips.off": "Enabled FAWE tips.", "fawe.info.worldedit.bypassed": "Currently bypassing FAWE restriction.", "fawe.info.worldedit.restricted": "Your FAWE edits are now restricted.", "fawe.info.worldedit.oom.admin": "Possible options:\n - //fast\n - Do smaller edits\n - Allocate more memory\n - Disable `max-memory-percent`", "fawe.info.temporarily-not-working": "Temporarily not working", "fawe.info.light-blocks": "Light blocks are more reliable than light sources, please use the blocks. This command is deprecated and will be removed in a future version.", "fawe.info.update-available.build": "An update for FastAsyncWorldEdit is available. You are {0} build(s) behind.\nYou are running build {1}, the latest build is {2}.\nUpdate at {3}", "fawe.info.update-available.release": "A new release for FastAsyncWorldEdit is available: {0}. You are currently on {1}. Download from {2}, {3} or {4}.", "fawe.web.generating.link": "Uploading {0}, please wait...", "fawe.web.generating.link.failed": "Failed to generate download link!", "fawe.web.download.link": "{0}", "fawe.web.image.load.timeout": "Image load attempt timed out, max time: {0}s. Please try a smaller-resolution image.", "fawe.web.image.load.size.too-large": "Image dimensions too large! Max allowable size (width x height): {0} pixels.", "fawe.worldedit.general.texture.disabled": "Texturing reset", "fawe.worldedit.general.texture.set": "Set texturing to {1}", "fawe.worldedit.general.source.mask.disabled": "Global source mask disabled", "fawe.worldedit.general.source.mask": "Global source mask set", "fawe.worldedit.general.transform.disabled": "Global transform disabled", "fawe.worldedit.general.transform": "Global transform set", "fawe.worldedit.copy.command.copy": "{0} blocks were copied.", "fawe.worldedit.cut.command.cut.lazy": "{0} blocks will be removed on paste", "fawe.worldedit.paste.command.paste": "The clipboard has been pasted at {0}", "fawe.worldedit.history.command.undo.disabled": "Undo disabled, use: //fast", "fawe.worldedit.selection.selection.count": "Counted {0} blocks.", "fawe.worldedit.anvil.world.is.loaded": "The world shouldn't be in use when executing. Unload the world, or use -f to override (save first)", "fawe.worldedit.brush.brush.reset": "Reset your brush. (SHIFT + Click)", "fawe.worldedit.brush.brush.none": "You aren't holding a brush!", "fawe.worldedit.brush.brush.scroll.action.set": "Set scroll action to {0}", "fawe.worldedit.brush.brush.scroll.action.unset": "Removed scroll action", "fawe.worldedit.brush.brush.visual.mode.set": "Set visual mode to {0}", "fawe.worldedit.brush.brush.target.mode.set": "Set target mode to {0}", "fawe.worldedit.brush.brush.target.offset.set": "Set target offset to {0}", "fawe.worldedit.brush.brush.equipped": "Equipped brush {0}", "fawe.worldedit.brush.brush.try.other": "There are other more suitable brushes e.g.,\n - //br height [radius=5] [#clipboard|file=null] [rotation=0] [yscale=1.00]", "fawe.worldedit.brush.brush.copy": "Left click the base of an object to copy, right click to paste. Increase the brush radius if necessary.", "fawe.worldedit.brush.brush.height.invalid": "Invalid height map file ({0})", "fawe.worldedit.brush.brush.spline": "Click to add a point, click the same spot to finish", "fawe.worldedit.brush.brush.line.primary": "Added point {0}, click another position to create the line", "fawe.worldedit.brush.brush.catenary.direction": "Added point {0}, click the direction you want to create the spline", "fawe.worldedit.brush.brush.line.secondary": "Created spline", "fawe.worldedit.brush.spline.primary.2": "Added position, Click the same spot to join!", "fawe.worldedit.brush.brush.spline.secondary.error": "Not enough positions set!", "fawe.worldedit.brush.spline.secondary": "Created spline", "fawe.worldedit.brush.brush.source.mask.disabled": "Brush source mask disabled", "fawe.worldedit.brush.brush.source.mask": "Brush source mask set", "fawe.worldedit.brush.brush.transform.disabled": "Brush transform disabled", "fawe.worldedit.brush.brush.transform": "Brush transform set", "fawe.worldedit.rollback.rollingback.index": "Undoing {0} ...", "fawe.worldedit.rollback.rollback.element": "{0} undone.", "fawe.worldedit.tool.tool.inspect": "Inspect tool bound to {0}.", "fawe.worldedit.tool.tool.inspect.info": "{0} changed {1} to {2} {3} ago", "fawe.worldedit.tool.tool.inspect.info.footer": "Total: {0} changes", "fawe.worldedit.tool.tool.range.error": "Maximum range: {0}.", "fawe.worldedit.tool.tool.lrbuild.info": "Left-click set to {0}; right-click set to {1}.", "fawe.worldedit.utility.nothing.confirmed": "You have no actions pending confirmation.", "fawe.worldedit.schematic.schematic.move.exists": "{0} already exists", "fawe.worldedit.schematic.schematic.move.success": "{0} -> {1}", "fawe.worldedit.schematic.schematic.move.failed": "{0} no moved: {1}", "fawe.worldedit.schematic.schematic.loaded": "{0} loaded. Paste it with //paste", "fawe.worldedit.schematic.schematic.saved": "{0} saved.", "fawe.worldedit.schematic.schematic.none": "No files found.", "fawe.worldedit.schematic.schematic.load-failure": "File could not be read or it does not exist: {0}. If you are specifying a format, you may not be specifying the correct one. Sponge schematic v2 and v3 both use the .schem file extension. To allow FAWE to select the format, do not specify one. If you are using a litematica schematic, it is not supported!", "fawe.worldedit.clipboard.clipboard.uri.not.found": "You do not have {0} loaded", "fawe.worldedit.clipboard.clipboard.cleared": "Clipboard cleared", "fawe.worldedit.clipboard.clipboard.invalid.format": "Unknown clipboard format:  {0}", "fawe.worldedit.visitor.visitor.block": "{0} blocks affected", "fawe.worldedit.selector.selector.fuzzy.pos1": "Region set and expanded from {0} {1}.", "fawe.worldedit.selector.selector.fuzzy.pos2": "Added expansion of {0} {1}.", "fawe.progress.progress.message": "{1}/{0} ({2}%) @{3}cps {4}s left", "fawe.progress.progress.finished": "[ Done! ]", "fawe.error.command.syntax": "Usage: {0}", "fawe.error.no-perm": "You are lacking the permission node: {0}", "fawe.error.block.not.allowed": "You are not allowed to use: {0}", "fawe.error.setting.disable": "Lacking setting: {0}", "fawe.error.brush.not.found": "Available brushes: {0}", "fawe.error.brush.incompatible": "Brush not compatible with this version", "fawe.error.no.region": "You have no current allowed region", "fawe.error.player.not.found": "Player not found: {0}", "fawe.error.worldedit.some.fails": "{0} blocks weren't placed because they were outside your allowed region.", "fawe.error.worldedit.some.fails.blockbag": "Missing blocks: {0}", "fawe.error.mask.angle": "Cannot combine degree with block-step", "fawe.error.invalid-flag": "The flag {0} is not applicable here", "fawe.error.lighting": "Error when attempting lighting. You may need to reload the chunks to see the edit.", "fawe.error.parser.invalid-data": "Invalid data: {0}", "fawe.error.unsupported": "Unsupported!", "fawe.error.invalid-block-type": "Does not match a valid block type: {0}", "fawe.error.invalid-block-state-property": "Cannot parse value `{0}` for property `{1}`, block state: `{2}`", "fawe.error.nbt.forbidden": "You are not allowed to use nbt. Lacking permission: {0}", "fawe.error.invalid-arguments": "Invalid amount of arguments. Expected: {0}", "fawe.error.unrecognised-tag": "Unrecognised tag: {0} {1}", "fawe.error.unknown-block-tag": "Unknown block tag: {0}", "fawe.error.block-tag-no-blocks": "Block tag '{0}' had no blocks.", "fawe.error.no-block-found": "No block found for '{0}'.", "fawe.error.invalid-states": "Invalid states: {0}", "fawe.error.no-session": "No session is available, so no clipboard is available.", "fawe.error.empty-clipboard": "To use '{0}', please first copy something to your clipboard", "fawe.error.selection-expand": "Selection cannot expand.", "fawe.error.selection-contract": "Selection cannot expand.", "fawe.error.selection-shift": "Selection cannot be shifted.", "fawe.error.invalid-user": "User must be provided.", "fawe.error.radius-too-small": "Radius must be >=0", "fawe.error.time-too-less": "Time must be >=0", "fawe.error.invalid-image": "Invalid image: {0}", "fawe.error.image-dimensions": "Dimensions given for image too large, max allowable size (width x height): {0} pixels.", "fawe.error.file-not-found": "File not found: {0}", "fawe.error.file-is-invalid-directory": "File is a directory: {0}", "fawe.error.stacktrace": "===============---=============", "fawe.error.no-failure": "This shouldn't result in any failure", "fawe.error.invalid-bracketing": "Invalid bracketing, are you missing a '{0}'.", "fawe.error.too-simple": "Complexity must be in the range 0-100", "fawe.error.outside-range": "Argument {0} outside of range {1}-{2}.", "fawe.error.outside-range-lower": "Argument {0} may not be less than {1}", "fawe.error.outside-range-upper": "Argument {0} may not be greater than {1}", "fawe.error.argument-size-mismatch": "Argument {0} may not be greater than argument {1}", "fawe.error.input-parser-exception": "Invalid empty string instead of boolean.", "fawe.error.invalid-boolean": "Invalid boolean {0}", "fawe.error.schematic.not.found": "Schematic {0} not found.", "fawe.error.parse.invalid-dangling-character": "Invalid dangling character {0}.", "fawe.error.parse.unknown-mask": "Unknown mask: {0}, See: {1}", "fawe.error.parse.unknown-pattern": "Unknown pattern: {0}, See: {1}", "fawe.error.parse.unknown-transform": "Unknown transform: {0}, See: {1}", "fawe.error.parse.no-clipboard": "To use {0}, please first copy something to your clipboard", "fawe.error.parse.no-clipboard-source": "No clipboards found at given source: {0}", "fawe.error.clipboard.invalid": "====== INVALID CLIPBOARD ======", "fawe.error.clipboard.invalid.info": "File: {0} (len: {1})", "fawe.error.clipboard.load.failure": "Unexpected failure loading clipboard from disk!", "fawe.error.clipboard.on.disk.version.mismatch": "Clipboard version mismatch: expected {0} but got {1}. It is recommended you delete the clipboard folder and restart the server.\nYour clipboard folder is located at {2}.", "fawe.error.limit.disallowed-block": "Your limit disallows use of block '{0}'", "fawe.error.limit.disallowed-property": "Your limit disallows use of property '{0}'", "fawe.error.region-mask-invalid": "Invalid region mask: {0}", "fawe.error.occurred-continuing": "Ignorable error occurred during edit: {0}", "fawe.error.limit.max-brush-radius": "Maximum brush radius in limit: {0}", "fawe.error.limit.max-radius": "Maximum radius in limit: {0}", "fawe.error.no-valid-on-hotbar": "No valid block types on hotbar", "fawe.error.no-process-non-synchronous-edit": "No processor holder was found but edit is non-synchronous", "fawe.cancel.count": "Cancelled {0} edits.", "fawe.cancel.reason.confirm": "Use //confirm to execute {0}", "fawe.cancel.reason.confirm.region": "Your selection is large ({0} -> {1}, containing {3} blocks). Use //confirm to execute {2}", "fawe.cancel.reason.confirm.radius": "Your radius is large ({0} > {1}). Use //confirm to execute {2}", "fawe.cancel.reason.confirm.limit": "You're exceeding your limit for this action ({0} > {1}). Use //confirm to execute {2}", "fawe.cancel.reason": "Your WorldEdit action was cancelled: {0}.", "fawe.cancel.reason.manual": "Manual cancellation", "fawe.cancel.reason.low.memory": "Low memory", "fawe.cancel.reason.max.changes": "Too many blocks changed", "fawe.cancel.reason.max.checks": "Too many block checks", "fawe.cancel.reason.max.fails": "Too many fails", "fawe.cancel.reason.max.tiles": "Too many block entities", "fawe.cancel.reason.max.entities": "Too many entities", "fawe.cancel.reason.max.iterations": "Max iterations", "fawe.cancel.reason.outside.level": "Outside world", "fawe.cancel.reason.outside.region": "Outside allowed region (bypass with /wea, or disable `region-restrictions` in config.yml)", "fawe.cancel.reason.outside.safe.region": "Outside safe edit region of +/- 30,000,000 blocks.", "fawe.cancel.reason.no.region": "No allowed region (bypass with /wea, or disable `region-restrictions` in config.yml)", "fawe.cancel.reason.no.region.reason": "No allowed region: {0}", "fawe.cancel.reason.no.region.plot.noworldeditflag": "Plot flag NoWorldeditFlag set", "fawe.cancel.reason.no.region.plot.owner.offline": "Region owner offline", "fawe.cancel.reason.no.region.plot.owner.only": "Only region owners may edit them", "fawe.cancel.reason.no.region.not.added": "Not added to region", "fawe.cancel.reason.player-only": "This operation requires a player, and cannot be executed from console, or without an actor.", "fawe.cancel.reason.actor-required": "This operation requires an actor.", "fawe.cancel.reason.world.limit": "This operation cannot be performed at y={0} as it is outside world limits.", "fawe.cancel.worldedit.failed.load.chunk": "Skipped loading chunk: {0};{1}. Try increasing chunk-wait.", "fawe.navigation.no.block": "No block in sight! (or too far)", "fawe.selection.sel.max": "{0} points maximum.", "fawe.selection.sel.fuzzy": "Fuzzy selector: Left click to select all contingent blocks, right click to add. To select an air cavity, use //pos1.", "fawe.selection.sel.fuzzy-instruction": "Select all connected blocks (magic wand)", "fawe.selection.sel.convex.polyhedral": "Convex polyhedral selector: Left click=First vertex, right click to add more.", "fawe.selection.sel.polyhedral": "Select a hollow polyhedral", "fawe.selection.sel.list": "For a list of selection types use: //sel list", "fawe.tips.tip.sel.list": "Tip: See the different selection modes with //sel list", "fawe.tips.tip.select.connected": "Tip: Select all connected blocks with //sel fuzzy", "fawe.tips.tip.set.pos1": "Tip: Use pos1 as a pattern with //set pos1", "fawe.tips.tip.farwand": "Tip: Select distant points with //farwand", "fawe.tips.tip.discord": "Need help using FAWE? https://discord.gg/intellectualsites", "fawe.tips.tip.lazycut": "Tip: It is safer to use //lazycut", "fawe.tips.tip.fast": "Tip: Set fast and without undo using //fast", "fawe.tips.tip.cancel": "Tip: You can //cancel an edit in progress", "fawe.tips.tip.mask": "Tip: Set a global destination mask with /gmask", "fawe.tips.tip.mask.angle": "Tip: Replace upward slopes of 3-20 blocks using //replace /[-20][-3] bedrock", "fawe.tips.tip.set.linear": "Tip: Set blocks linearly with //set #l3d[wood,bedrock]", "fawe.tips.tip.surface.spread": "Tip: Spread a flat surface with //set #surfacespread[5][0][5][#existing]", "fawe.tips.tip.set.hand": "Tip: Use your current hand with //set hand", "fawe.tips.tip.replace.regex": "Tip: Replace using regex: //replace .*_log <pattern>", "fawe.tips.tip.replace.regex.2": "Tip: Replace using regex: //replace .*stairs[facing=(north|south)] <pattern>", "fawe.tips.tip.replace.regex.3": "Tip: Replace using operators: //replace water[level>2] sand", "fawe.tips.tip.replace.regex.4": "Tip: Replace using operators: //replace true *[waterlogged=false]", "fawe.tips.tip.replace.regex.5": "Tip: Replace using operators: //replace true *[level-=1]", "fawe.tips.tip.replace.id": "Tip: Replace only the block id: //replace woodenstair #id[cobblestair]", "fawe.tips.tip.replace.light": "Tip: Remove light sources with //replace #brightness[1][15] 0", "fawe.tips.tip.tab.complete": "Tip: The replace command supports tab completion", "fawe.tips.tip.flip": "Tip: Mirror with //flip", "fawe.tips.tip.deform": "Tip: Reshape with //deform", "fawe.tips.tip.transform": "Tip: Set a transform with //gtransform", "fawe.tips.tip.copypaste": "Tip: Paste on click with //br copypaste", "fawe.tips.tip.source.mask": "Tip: Set a source mask with /gsmask <mask>", "fawe.tips.tip.replace.marker": "Tip: Replace a block with your full clipboard using //replace wool #fullcopy", "fawe.tips.tip.paste": "Tip: Place with //paste", "fawe.tips.tip.lazycopy": "Tip: lazycopy is faster", "fawe.tips.tip.download": "Tip: Try out //download", "fawe.tips.tip.rotate": "Tip: Orientate with //rotate", "fawe.tips.tip.copy.pattern": "Tip: To use as a pattern try #copy", "fawe.tips.tip.regen.0": "Tip: Use a biome with /regen [biome]", "fawe.tips.tip.regen.1": "Tip: Use a seed with /regen [biome] [seed]", "fawe.tips.tip.biome.pattern": "Tip: The #biome[forest] pattern can be used in any command", "fawe.tips.tip.biome.mask": "Tip: Restrict to a biome with the `$jungle` mask", "fawe.regen.time": "Regenerating region, this might take a while!", "worldedit.expand.description.vert": "Vertically expand the selection to world limits.", "worldedit.expand.expanded": "Region expanded {0} blocks", "worldedit.expand.expanded.vert": "Region expanded {0} blocks (top-to-bottom).", "worldedit.biomeinfo.lineofsight": "Biomes at line of sight point: {0}", "worldedit.biomeinfo.position": "Biomes at your position: {0}", "worldedit.biomeinfo.selection": "Biomes in your selection: {0}", "worldedit.biomeinfo.not-locatable": "Command sender must be present in the world to use the -p flag.", "worldedit.error.disabled": "This functionality is disabled (see WorldEdit configuration).", "worldedit.error.no-match": "No match for '{0}'.", "worldedit.error.unknown": "Unknown error occurred: {0}", "worldedit.error.parser.player-only": "Input '{0}' requires a player!", "worldedit.error.parser.bad-state-format": "Bad state format in {0}", "worldedit.error.parser.unknown-property": "Unknown property '{0}' for block '{1}'", "worldedit.error.parser.duplicate-property": "Duplicate property: {0}", "worldedit.error.parser.unknown-value": "Unknown value '{0}' for property '{1}'", "worldedit.error.parser.invalid-colon": "Invalid colon.", "worldedit.error.parser.hanging-lbracket": "Invalid format. Hanging bracket at '{0}'.", "worldedit.error.parser.missing-rbracket": "State is missing trailing ']'", "worldedit.error.incomplete-region": "Make a region selection first.", "worldedit.error.not-a-block": "This item is not a block.", "worldedit.error.unknown-entity": "Entity name '{0}' was not recognized.", "worldedit.error.unknown-mob": "Mob name '{0}' was not recognized.", "worldedit.error.parser.clipboard.missing-offset": "Offset specified with @ but no offset given. Use '#copy@[x,y,z]'.", "worldedit.error.parser.clipboard.missing-coordinates": "Clipboard offset needs x,y,z coordinates.", "worldedit.error.unknown-item": "Item name '{0}' was not recognized.", "worldedit.error.parser.invalid-expression": "Invalid expression: {0}", "worldedit.error.parser.negate-nothing": "Cannot negate nothing!", "worldedit.error.invalid-page": "Invalid page number", "worldedit.error.missing-extent": "No Extent is known", "worldedit.error.missing-session": "No LocalSession is known", "worldedit.error.missing-world": "You need to provide a world (Try //world)", "worldedit.error.missing-actor": "No actor is known", "worldedit.error.missing-player": "No player is known", "worldedit.error.no-file-selected": "No file selected.", "worldedit.error.file-resolution.outside-root": "Path is outside allowable root", "worldedit.error.file-resolution.resolve-failed": "Failed to resolve path", "worldedit.error.invalid-filename.invalid-characters": "Invalid characters or extension missing", "worldedit.error.invalid-number.matches": "Number expected; string \"{0}\" given.", "worldedit.error.invalid-number": "Number expected; string given.", "worldedit.error.unknown-block": "Block name '{0}' was not recognized.", "worldedit.error.disallowed-block": "Block '{0}' not allowed (see WorldEdit configuration).", "worldedit.error.max-changes": "Max blocks changed in an operation reached ({0}).", "worldedit.error.max-brush-radius": "Maximum brush radius (in worldedit-config.yml): {0}", "worldedit.error.max-radius": "Maximum radius (in worldedit-config.yml): {0}", "worldedit.error.unknown-direction": "Unknown direction: {0}", "worldedit.error.empty-clipboard": "Your clipboard is empty. Use //copy first.", "worldedit.error.invalid-filename": "Filename '{0}' invalid: {1}", "worldedit.error.file-resolution": "File '{0}' resolution error: {1}", "worldedit.tool.error.cannot-bind": "Can't bind tool to {0}: {1}", "worldedit.error.file-aborted": "File selection aborted.", "worldedit.error.world-unloaded": "The world was unloaded already.", "worldedit.error.named-world-unloaded": "The world '{0}' was unloaded already.", "worldedit.error.blocks-cant-be-used": "Blocks can't be used", "worldedit.error.unknown-tag": "Tag name '{0}' was not recognized.", "worldedit.error.empty-tag": "Tag name '{0}' has no contents.", "worldedit.error.unknown-biome": "Biome name '{0}' was not recognized.", "worldedit.brush.radius-too-large": "Maximum allowed brush radius: {0}", "worldedit.brush.apply.description": "Apply brush, apply a function to every block", "worldedit.brush.apply.radius": "The size of the brush", "worldedit.brush.apply.shape": "The shape of the region", "worldedit.brush.apply.type": "Type of brush to use", "worldedit.brush.apply.item.warning": "This brush simulates item usages. Its effects may not work on all platforms, may not be undo-able, and may cause strange interactions with other mods/plugins. Use at your own risk.", "worldedit.brush.paint.description": "Paint brush, apply a function to a surface", "worldedit.brush.paint.size": "The size of the brush", "worldedit.brush.paint.shape": "The shape of the region", "worldedit.brush.paint.density": "The density of the brush", "worldedit.brush.paint.type": "Type of brush to use", "worldedit.brush.paint.item.warning": "This brush simulates item usages. Its effects may not work on all platforms, may not be undo-able, and may cause strange interactions with other mods/plugins. Use at your own risk.", "worldedit.brush.sphere.equip": "Sphere brush shape equipped ({0}).", "worldedit.brush.cylinder.equip": "Cylinder brush shape equipped ({0} by {1}).", "worldedit.brush.clipboard.equip": "Clipboard brush shape equipped.", "worldedit.brush.smooth.equip": "Smooth brush equipped ({0} x {1}x using {2}).", "worldedit.brush.smooth.nofilter": "any block", "worldedit.brush.smooth.filter": "filter", "worldedit.brush.snowsmooth.equip": "SnowSmooth brush equipped ({0} x {1}x using {2}), {3} snow blocks.", "worldedit.brush.snowsmooth.nofilter": "any block", "worldedit.brush.snowsmooth.filter": "filter", "worldedit.brush.extinguish.equip": "Extinguisher equipped ({0}).", "worldedit.brush.gravity.equip": "Gravity brush equipped ({0}).", "worldedit.brush.butcher.equip": "Butcher brush equipped ({0}).", "worldedit.brush.operation.equip": "Set brush to {0}.", "worldedit.brush.morph.equip": "Morph brush shape equipped: {0}.", "worldedit.brush.biome.column-supported-types": "This brush shape is not supported with whole-column brushing, try the cylinder shape.", "worldedit.brush.none.equip": "Brush unbound from your current item.", "worldedit.brush.none.equipped": "You have no brush bound to your current item. Try /brush sphere for a basic brush.", "worldedit.setbiome.changed": "Biomes were changed in {0} columns. You may have to rejoin your game (or close and reopen your world) to see a change.", "worldedit.setbiome.warning": "You may have to re-join your game (or close and re-open your world) to see changes.", "worldedit.setbiome.not-locatable": "Command sender must be present in the world to use the -p flag.", "worldedit.drawsel.disabled": "Server CUI disabled.", "worldedit.drawsel.enabled": "Server CUI enabled. This only supports cuboid regions, with a maximum size of {0}x{1}x{2}.", "worldedit.drawsel.disabled.already": "Server CUI already disabled.", "worldedit.drawsel.enabled.already": "Server CUI already enabled.", "worldedit.limit.too-high": "Your maximum allowable limit is {0}.", "worldedit.limit.set": "Block change limit set to {0}.", "worldedit.limit.return-to-default": "(Use //limit to go back to the default.)", "worldedit.timeout.too-high": "Your maximum allowable timeout is {0}ms.", "worldedit.timeout.set": "Timeout time set to {0}ms.", "worldedit.timeout.return-to-default": " (Use //timeout to go back to the default.)", "worldedit.fast.disabled": "Fast mode disabled.", "worldedit.fast.enabled": "Fast mode enabled. Changes won't be written to history (//undo is disabled). Lighting in the affected chunks may be wrong and/or you may need to rejoin to see changes.", "worldedit.fast.disabled.already": "Fast mode already disabled.", "worldedit.fast.enabled.already": "Fast mode already enabled.", "worldedit.perf.sideeffect.set": "Side effect \"{0}\" set to {1}", "worldedit.perf.sideeffect.get": "Side effect \"{0}\" is set to {1}", "worldedit.perf.sideeffect.already-set": "Side effect \"{0}\" is already {1}", "worldedit.perf.sideeffect.set-all": "All side effects set to {0}", "worldedit.reorder.current": "The reorder mode is {0}", "worldedit.reorder.set": "The reorder mode is now {0}", "worldedit.gmask.disabled": "Global mask disabled.", "worldedit.gmask.set": "Global mask set.", "worldedit.toggleplace.pos1": "Now placing at pos #1.", "worldedit.toggleplace.player": "Now placing at the block you stand in.", "worldedit.toggleplace.not-locatable": "Cannot toggle placing in this context.", "worldedit.searchitem.too-short": "Enter a longer search string (len > 2).", "worldedit.searchitem.either-b-or-i": "You cannot use both the 'b' and 'i' flags simultaneously.", "worldedit.searchitem.searching": "(Please wait... searching items.)", "worldedit.watchdog.no-hook": "This platform has no watchdog hook.", "worldedit.watchdog.active.already": "Watchdog hook already active.", "worldedit.watchdog.inactive.already": "Watchdog hook already inactive.", "worldedit.watchdog.active": "Watchdog hook now active.", "worldedit.watchdog.inactive": "Watchdog hook now inactive.", "worldedit.world.remove": "Removed world override.", "worldedit.world.set": "Set the world override to {0}. (Use //world to go back to default)", "worldedit.undo.undone": "Undid {0} available edits.", "worldedit.undo.none": "Nothing left to undo.", "worldedit.redo.redone": "Redid {0} available edits.", "worldedit.redo.none": "Nothing left to redo.", "worldedit.clearhistory.cleared": "History cleared.", "worldedit.raytrace.noblock": "No block in sight!", "worldedit.raytrace.require-player": "Raytracing commands require a player!", "worldedit.restore.not-configured": "Snapshot/backup restore is not configured.", "worldedit.restore.not-available": "That snapshot does not exist or is not available.", "worldedit.restore.failed": "Failed to load snapshot: {0}", "worldedit.restore.loaded": "Snapshot '{0}' loaded; now restoring...", "worldedit.restore.restored": "Restored; {0} missing chunks and {1} other errors.", "worldedit.restore.none-for-specific-world": "No snapshots were found for world '{0}'.", "worldedit.restore.none-for-world": "No snapshots were found for this world.", "worldedit.restore.none-found": "No snapshots were found.", "worldedit.restore.none-found-console": "No snapshots were found. See console for details.", "worldedit.restore.chunk-not-present": "Chunks were not present in snapshot.", "worldedit.restore.chunk-load-failed": "No chunks could be loaded. (Bad archive?)", "worldedit.restore.block-place-failed": "Errors prevented any blocks from being restored.", "worldedit.restore.block-place-error": "Last error: {0}", "worldedit.snapshot.use.newest": "Now using newest snapshot.", "worldedit.snapshot.use": "Snapshot set to: {0}", "worldedit.snapshot.none-before": "Couldn't find a snapshot before {0}.", "worldedit.snapshot.none-after": "Couldn't find a snapshot after {0}.", "worldedit.snapshot.index-above-0": "Invalid index, must be greater than or equal to 1.", "worldedit.snapshot.index-oob": "Invalid index, must be between 1 and {0}.", "worldedit.schematic.unknown-format": "Unknown schematic format: {0}.", "worldedit.schematic.load.does-not-exist": "Schematic {0} does not exist!", "worldedit.schematic.load.loading": "(Please wait... loading schematic.)", "worldedit.schematic.load.unsupported-version": "This schematic is not supported. Version: {0}. If you are using a litematica schematic, it is not supported!", "worldedit.schematic.save.already-exists": "That schematic already exists. Use the -f flag to overwrite it.", "worldedit.schematic.save.failed-directory": "Could not create folder for schematics!", "worldedit.schematic.save.saving": "(Please wait... saving schematic.)", "worldedit.schematic.save.still-saving": "(Please wait... still saving schematic.)", "worldedit.schematic.share.unsupported-format": "The schematic share destination \"{0}\" does not support the \"{1}\" format.", "worldedit.schematic.share.response.arkitektonika.download": "Download: {0}", "worldedit.schematic.share.response.arkitektonika.delete": "Delete: {0}", "worldedit.schematic.share.response.arkitektonika.click-here": "[Click here]", "worldedit.schematic.delete.empty": "Schematic {0} not found!", "worldedit.schematic.delete.does-not-exist": "Schematic {0} does not exist!", "worldedit.schematic.delete.failed": "Deletion of {0} failed! Is it read-only?", "worldedit.schematic.delete.deleted": "{0} has been deleted.", "worldedit.schematic.formats.title": "Available clipboard formats (Name: Lookup names)", "worldedit.schematic.load.symbol": "[L]", "worldedit.schematic.plus.symbol": "[+]", "worldedit.schematic.minus.symbol": "[-]", "worldedit.schematic.x.symbol": "[X]", "worldedit.schematic.0.symbol": "[O]", "worldedit.schematic.dash.symbol": " - ", "worldedit.schematic.click-to-load": "Click to load", "worldedit.schematic.load": "Load", "worldedit.schematic.list": "List", "worldedit.schematic.available": "Available schematics", "worldedit.schematic.unload": "Unload", "worldedit.schematic.delete": "delete", "worldedit.schematic.visualize": "visualize", "worldedit.schematic.clipboard": "Add to (multi-)clipboard", "worldedit.schematic.unknown-filename": "Unknown filename: {0}", "worldedit.schematic.file-not-exist": "File could not be read or it does not exist: {0}", "worldedit.schematic.already-exists": "That schematic already exists!", "worldedit.schematic.failed-to-save": "Failed to save schematic", "worldedit.schematic.directory-does-not-exist": "Directory '{0}' does not exist!", "worldedit.schematic.file-perm-fail": "Creation of '{0}' failed! Check file permission.", "worldedit.schematic.sorting-old-new": "Cannot sort by oldest and newest.", "worldedit.schematic.unsupported-minecraft-version": "This version of WorldEdit does not support your Minecraft version. Schematics will not work until this is resolved.", "worldedit.pos.already-set": "Position already set.", "worldedit.pos.console-require-coords": "You must provide coordinates as console.", "worldedit.hpos.no-block": "No block in sight!", "worldedit.hpos.already-set": "Position already set.", "worldedit.chunk.selected-multiple": "Chunks selected: ({0}, {1}, {2}) - ({3}, {4}, {5})", "worldedit.chunk.selected": "<PERSON><PERSON> selected: {0}, {1}, {2}", "worldedit.wand.invalid": "Wand item is mis-configured or disabled.", "worldedit.wand.selwand.info": "Left click: select pos #1; Right click: select pos #2", "worldedit.wand.selwand.now.tool": "The selection wand is now a normal tool. You can disable it with {0} and rebind it to any item with {1} or get a new wand with {2}.", "worldedit.wand.navwand.info": "Left click: jump to location; Right click: pass through walls", "worldedit.contract.contracted": "Region contracted {0} blocks.", "worldedit.shift.shifted": "Region shifted.", "worldedit.outset.outset": "Region outset.", "worldedit.inset.inset": "Region inset.", "worldedit.trim.trim": "Region trimmed.", "worldedit.trim.no-blocks": "No blocks matched the trim mask.", "worldedit.size.offset": "{0}: {1} @ {2} ({3} blocks)", "worldedit.size.type": "Type: {0}", "worldedit.size.size": "Size: {0}", "worldedit.size.distance": "Cuboid distance: {0}", "worldedit.size.blocks": "# of blocks: {0}", "worldedit.count.counted": "Counted: {0}", "worldedit.distr.no-blocks": "No blocks counted.", "worldedit.distr.no-previous": "No previous distribution.", "worldedit.distr.total": "Total Block Count: {0}", "worldedit.select.cleared": "Selection cleared.", "worldedit.select.cuboid.message": "Cuboid: left click for point 1, right click for point 2", "worldedit.select.cuboid.description": "Select two corners of a cuboid", "worldedit.select.extend.message": "Cuboid: left click for a starting point, right click to extend", "worldedit.select.extend.description": "Fast cuboid selection mode", "worldedit.select.poly.message": "2D polygon selector: Left/right click to add a point.", "worldedit.select.poly.limit-message": "{0} points maximum.", "worldedit.select.poly.description": "Select a 2D polygon with height", "worldedit.select.ellipsoid.message": "Ellipsoid selector: left click=center, right click to extend", "worldedit.select.ellipsoid.description": "Select an ellipsoid", "worldedit.select.sphere.message": "Sphere selector: left click=center, right click to set radius", "worldedit.select.sphere.description": "Select a sphere", "worldedit.select.cyl.message": "Cylindrical selector: Left click=center, right click to extend", "worldedit.select.cyl.description": "Select a cylinder", "worldedit.select.convex.message": "Convex polyhedral selector: Left click=First vertex, right click to add more.", "worldedit.select.convex.limit-message": "{0} points maximum.", "worldedit.select.convex.description": "Select a convex polyhedral", "worldedit.select.default-set": "Your default region selector is now {0}.", "worldedit.chunkinfo.chunk": "Chunk: {0}, {1}", "worldedit.chunkinfo.old-filename": "Old format: {0}", "worldedit.chunkinfo.mcregion-filename": "McRegion: region/{0}", "worldedit.listchunks.listfor": "Listing chunks for: {0}", "worldedit.drain.drained": "{0} blocks have been drained.", "worldedit.fill.created": "{0} blocks have been filled.", "worldedit.fillr.created": "{0} blocks have been filled.", "worldedit.fixlava.fixed": "{0} blocks have been fixed.", "worldedit.fixwater.fixed": "{0} blocks have been fixed.", "worldedit.removeabove.removed": "{0} blocks have been removed.", "worldedit.removebelow.removed": "{0} blocks have been removed.", "worldedit.removenear.removed": "{0} blocks have been removed.", "worldedit.replacenear.replaced": "{0} blocks have been replaced.", "worldedit.snow.created": "{0} surfaces have been covered.", "worldedit.thaw.removed": "{0} blocks have been thawed.", "worldedit.green.changed": "{0} blocks have been greened.", "worldedit.extinguish.removed": "{0} fires have been extinguished.", "worldedit.butcher.killed": "{0} mobs have been killed in a radius of {1}.", "worldedit.butcher.explain-all": "Use -1 to remove all mobs in loaded chunks", "worldedit.remove.removed": "{0} entities have been marked for removal.", "worldedit.remove.explain-all": "Use -1 to remove all entities in loaded chunks", "worldedit.calc.invalid": "'{0}' could not be parsed as a valid expression", "worldedit.calc.invalid.with-error": "'{0}' could not be parsed as a valid expression: '{1}'", "worldedit.paste.pasted": "The clipboard has been pasted at {0}", "worldedit.paste.selected": "Selected clipboard paste region.", "worldedit.rotate.no-interpolation": "Note: Interpolation is not yet supported, so angles that are multiples of 90 is recommended.", "worldedit.rotate.rotated": "The clipboard copy has been rotated.", "worldedit.flip.flipped": "The clipboard copy has been flipped.", "worldedit.clearclipboard.cleared": "Clipboard cleared.", "worldedit.set.done": "Operation completed ({0}).", "worldedit.set.done.verbose": "Operation completed ({0}).", "worldedit.line.changed": "{0} blocks have been changed.", "worldedit.line.invalid-type": "//line only works with cuboid selections or convex polyhedral selections", "worldedit.line.cuboid-only": "//line only works with cuboid selections", "worldedit.curve.changed": "{0} blocks have been changed.", "worldedit.curve.invalid-type": "//curve only works with convex polyhedral selections", "worldedit.curve.convex-only": "//curve only works with convex polyhedral selections", "worldedit.replace.replaced": "{0} blocks have been replaced.", "worldedit.stack.changed": "{0} blocks changed. Undo with //undo", "worldedit.stack.intersecting-region": "Stack offset must not collide with the region when using block units", "worldedit.regen.regenerated": "Region regenerated.", "worldedit.regen.failed": "Unable to regenerate chunks. Check console for details.", "worldedit.walls.changed": "{0} blocks have been changed.", "worldedit.faces.changed": "{0} blocks have been changed.", "worldedit.overlay.overlaid": "{0} blocks have been overlaid.", "worldedit.naturalize.naturalized": "{0} block(s) have been made to look more natural.", "worldedit.center.changed": "Center set. ({0} blocks changed)", "worldedit.smooth.changed": "Terrain's height map smoothed. {0} blocks changed.", "worldedit.snowsmooth.changed": "Snow's height map smoothed. {0} blocks changed.", "worldedit.move.moved": "{0} blocks moved.", "worldedit.deform.deformed": "{0} blocks have been deformed.", "worldedit.hollow.changed": "{0} blocks have been changed.", "worldedit.forest.created": "{0} trees created.", "worldedit.flora.created": "{0} flora created.", "worldedit.unstuck.moved": "There you go!", "worldedit.ascend.obstructed": "No free spot above you found.", "worldedit.ascend.moved": "Ascended {0} levels.", "worldedit.descend.obstructed": "No free spot below you found.", "worldedit.descend.moved": "Descended {0} levels.", "worldedit.ceil.obstructed": "No free spot above you found.", "worldedit.ceil.moved": "Woosh!", "worldedit.thru.obstructed": "No free spot ahead of you found.", "worldedit.thru.moved": "Woosh!", "worldedit.jumpto.moved": "Poof!", "worldedit.jumpto.none": "No block in sight (or too far away)!", "worldedit.up.obstructed": "You would hit something above you.", "worldedit.up.moved": "Woosh!", "worldedit.cone.invalid-radius": "You must either specify 1 or 2 radius values.", "worldedit.cone.created": "{0} blocks have been created.", "worldedit.cyl.invalid-radius": "You must either specify 1 or 2 radius values.", "worldedit.cyl.created": "{0} blocks have been created.", "worldedit.hcyl.thickness-too-large": "Thickness cannot be larger than x or z radii.", "worldedit.sphere.invalid-radius": "You must either specify 1 or 3 radius values.", "worldedit.sphere.created": "{0} blocks have been created.", "worldedit.forestgen.created": "{0} trees created.", "worldedit.pumpkins.created": "{0} pumpkin patches created.", "worldedit.pyramid.created": "{0} blocks have been created.", "worldedit.generate.created": "{0} blocks have been created.", "worldedit.generatebiome.changed": "{0} biomes affected.", "worldedit.reload.config": "Configuration reloaded!", "worldedit.report.written": "FAWE report written to {0}", "worldedit.report.error": "Failed to write report: {0}", "worldedit.report.callback": "FAWE report: {0}.report", "worldedit.timezone.invalid": "Invalid timezone", "worldedit.timezone.set": "Timezone set for this session to: {0}", "worldedit.timezone.current": "The current time in that timezone is: {0}", "worldedit.version.version": "FAWE version:\n - Date {0}\n - Commit {1}\n - Build {2}\n - Platform {3}", "worldedit.trace.no-tracing-extents": "Trace: No extent was used.", "worldedit.trace.action-failed": "Trace: Action(s) {0} at {1} discarded by extent {2}", "worldedit.trace.active.already": "Trace mode already active.", "worldedit.trace.inactive.already": "Trace mode already inactive.", "worldedit.trace.active": "Trace mode now active.", "worldedit.trace.inactive": "Trace mode now inactive.", "worldedit.command.time-elapsed": "{0}s elapsed (history: {1} changed; {2} blocks/sec).", "worldedit.command.permissions": "You are not permitted to do that. Are you in the right mode?", "worldedit.command.player-only": "This command must be used with a player.", "worldedit.command.error.report": "&cPlease report this error: [See console]", "worldedit.command.deprecation": "This command is deprecated.", "worldedit.command.deprecation-message": "Please use '{0}' instead.", "worldedit.pastebin.uploading": "(Please wait... sending output to paste service...)", "worldedit.session.cant-find-session": "Unable to find session for {0}", "worldedit.platform.no-file-dialog": "File dialogs are not supported in your environment.", "worldedit.tool.max-block-changes": "Max blocks change limit reached.", "worldedit.tool.no-block": "No block in sight!", "worldedit.tool.repl.equip": "Block replacer tool bound to {0}.", "worldedit.tool.repl.switched": "Replacer tool switched to: {0}", "worldedit.tool.data-cycler.equip": "Block data cycler tool bound to {0}.", "worldedit.tool.data-cycler.block-not-permitted": "You are not permitted to cycle the data value of that block.", "worldedit.tool.data-cycler.cant-cycle": "That block's data cannot be cycled!", "worldedit.tool.data-cycler.new-value": "Value of {0} is now {1}.", "worldedit.tool.data-cycler.cycling": "Now cycling {0}.", "worldedit.tool.deltree.equip": "Floating tree remover tool bound to {0}.", "worldedit.tool.deltree.not-tree": "That's not a tree.", "worldedit.tool.deltree.not-floating": "That's not a floating tree.", "worldedit.tool.tree.equip": "Tree tool bound to {0}.", "worldedit.tool.tree.obstructed": "A tree can't go there.", "worldedit.tool.info.equip": "Info tool bound to {0}.", "worldedit.tool.inspect.equip": "Inspect tool bound to {0}.", "worldedit.tool.info.blockstate.hover": "Block state (click to copy)", "worldedit.tool.info.internalid.hover": "Internal ID", "worldedit.tool.info.legacy.hover": "Legacy id:data", "worldedit.tool.info.light.hover": "Block Light/Light Above", "worldedit.tool.none.equip": "Tool unbound from your current item.", "worldedit.tool.selwand.equip": "Selection wand bound to {0}.", "worldedit.tool.navwand.equip": "Navigation wand bound to {0}.", "worldedit.tool.floodfill.equip": "Block flood fill tool bound to {0}.", "worldedit.tool.farwand.equip": "Far wand tool bound to {0}.", "worldedit.tool.lrbuild.equip": "Long-range building tool bound to {0}.", "worldedit.tool.lrbuild.set": "Left-click set to {0}; right-click set to {1}.", "worldedit.tool.stack.equip": "Stack tool bound to {0}.", "worldedit.tool.unbind-instruction": "Run {0} while holding the item to unbind it.", "worldedit.tool.superpickaxe.mode.single": "Mode is now single. Left click with a pickaxe. // to disable.", "worldedit.tool.superpickaxe.mode.area": "Mode is now area. Left click with a pickaxe. // to disable.", "worldedit.tool.superpickaxe.mode.recursive": "Mode is now recursive. Left click with a pickaxe. // to disable.", "worldedit.tool.superpickaxe.max-range": "Maximum range is {0}.", "worldedit.tool.superpickaxe.enabled.already": "Super pickaxe already enabled.", "worldedit.tool.superpickaxe.disabled.already": "Super pickaxe already disabled.", "worldedit.tool.superpickaxe.enabled": "Super pickaxe enabled.", "worldedit.tool.superpickaxe.disabled": "Super pickaxe disabled.", "worldedit.tool.mask.set": "Brush mask set.", "worldedit.tool.mask.disabled": "Brush mask disabled.", "worldedit.tool.material.set": "Brush material set.", "worldedit.tool.range.set": "Brush range set.", "worldedit.tool.size.set": "Brush size set.", "worldedit.tool.tracemask.set": "Trace mask set.", "worldedit.tool.tracemask.disabled": "Trace mask disabled.", "worldedit.execute.script-permissions": "You don't have permission to use that script.", "worldedit.executelast.no-script": "Use /cs with a script name first.", "worldedit.script.read-error": "Script read error: {0}", "worldedit.script.unsupported": "Only .js scripts are currently supported", "worldedit.script.file-not-found": "Script does not exist: {0}", "worldedit.script.no-script-engine": "Failed to find an installed script engine.\nPlease see https://worldedit.enginehub.org/en/latest/usage/other/craftscripts/", "worldedit.script.failed": "Failed to execute: {0}", "worldedit.script.failed-console": "Failed to execute (see console): {0}", "worldedit.operation.affected.biome": "{0} biomes affected", "worldedit.operation.affected.block": "{0} blocks affected", "worldedit.operation.affected.column": "{0} columns affected", "worldedit.operation.affected.entity": "{0} entities affected", "worldedit.operation.deform.expression": "deformed using {0}", "worldedit.error.parser.invalid-nbt": "Invalid NBT Data in input: '{0}'. Error: {1}", "worldedit.selection.convex.info.vertices": "Vertices: {0}", "worldedit.selection.convex.info.triangles": "Triangles: {0}", "worldedit.selection.convex.explain.primary": "Started new selection with vertex {0}.", "worldedit.selection.convex.explain.secondary": "Added vertex {0} to the selection.", "worldedit.selection.cuboid.info.pos1": "Position 1: {0}", "worldedit.selection.cuboid.info.pos2": "Position 2: {0}", "worldedit.selection.cuboid.explain.primary": "First position set to {0}.", "worldedit.selection.cuboid.explain.primary-area": "First position set to {0} ({1}).", "worldedit.selection.cuboid.explain.secondary": "Second position set to {0}.", "worldedit.selection.cuboid.explain.secondary-area": "Second position set to {0} ({1}).", "worldedit.selection.extend.explain.primary": "Started selection at {0} ({1}).", "worldedit.selection.extend.explain.secondary": "Extended selection to encompass {0} ({1}).", "worldedit.selection.ellipsoid.info.center": "Center: {0}", "worldedit.selection.ellipsoid.info.radius": "X/Y/Z Radius: {0}", "worldedit.selection.ellipsoid.explain.primary": "Center position set to {0}.", "worldedit.selection.ellipsoid.explain.primary-area": "Center position set to {0} ({1}).", "worldedit.selection.ellipsoid.explain.secondary": "<PERSON><PERSON> set to {0}.", "worldedit.selection.ellipsoid.explain.secondary-area": "<PERSON><PERSON> set to {0} ({1}).", "worldedit.selection.cylinder.info.center": "Center: {0}", "worldedit.selection.cylinder.info.radius": "Radius: {0}", "worldedit.selection.cylinder.explain.primary": "Starting a new cylindrical selection at {0}.", "worldedit.selection.cylinder.explain.secondary": "Radius set to {0}/{1} blocks. ({2})", "worldedit.selection.cylinder.explain.secondary-missing": "You must select the center point before setting the radius.", "worldedit.selection.polygon2d.info": "# points: {0}", "worldedit.selection.polygon2d.explain.primary": "Starting a new polygon at {0}.", "worldedit.selection.polygon2d.explain.secondary": "Added point #{0} at {1}.", "worldedit.selection.sphere.explain.secondary": "<PERSON><PERSON> set to {0}.", "worldedit.selection.sphere.explain.secondary-defined": "<PERSON><PERSON> set to {0} ({1}).", "worldedit.sideeffect.history": "History", "worldedit.sideeffect.history.description": "Writes history of the change", "worldedit.sideeffect.heightmaps": "Heightmaps", "worldedit.sideeffect.heightmaps.description": "Updates heightmaps", "worldedit.sideeffect.lighting": "Lighting", "worldedit.sideeffect.lighting.description": "Updates block lighting", "worldedit.sideeffect.neighbors": "Neighbors", "worldedit.sideeffect.neighbors.description": "Updates shapes of blocks in the edit", "worldedit.sideeffect.update": "Update", "worldedit.sideeffect.update.description": "Notifies the changed block", "worldedit.sideeffect.validation": "Validation", "worldedit.sideeffect.validation.description": "Validates and fixes inconsistent world state, such as disconnected blocks", "worldedit.sideeffect.entity_ai": "Entity AI", "worldedit.sideeffect.entity_ai.description": "Updates Entity AI paths for the block changes", "worldedit.sideeffect.events": "Mod/Plugin Events", "worldedit.sideeffect.events.description": "Tells other mods/plugins about these changes when applicable", "worldedit.sideeffect.state.on": "On", "worldedit.sideeffect.state.delayed": "Delayed", "worldedit.sideeffect.state.off": "Off", "worldedit.sideeffect.box.current": "Current", "worldedit.sideeffect.box.change-to": "Click to set to {0}", "worldedit.help.command-not-found": "The command '{0}' could not be found.", "worldedit.help.no-subcommands": "'{0}' has no sub-commands. (Maybe '{1}' is for a parameter?)", "worldedit.help.subcommand-not-found": "The sub-command '{0}' under '{1}' could not be found.", "worldedit.cli.stopping": "Stopping!", "worldedit.cli.unknown-command": "Unknown command!", "worldedit.version.bukkit.unsupported-adapter": "This FastAsyncWorldEdit version does not fully support your version of Bukkit. Block entities (e.g. chests) will be empty, block properties (e.g. rotation) will be missing, and other things may not work. Update FastAsyncWorldEdit to restore this functionality:\n{0}", "worldedit.bukkit.no-edit-without-adapter": "Editing on unsupported versions is disabled."}