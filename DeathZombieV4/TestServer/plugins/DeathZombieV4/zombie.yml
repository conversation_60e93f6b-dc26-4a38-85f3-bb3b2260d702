# ========================================
# DeathZombieV4 双CustomZombie生成系统配置文件
# 作者: Ver_zhzh
# 版本: 1.0
# 符合阿里巴巴编码规范
# ========================================

# ========================================
# 系统总开关配置
# ========================================
system_settings:
  # 是否启用默认CustomZombie设置（原有硬编码系统）
  use_default_settings: false

  # 是否启用用户自定义设置（新的配置驱动系统）
  use_user_custom_settings: true

  # 当两个系统都启用时的优先级策略
  # "default_first" - 优先使用默认设置，失败时使用用户自定义
  # "user_first" - 优先使用用户自定义，失败时使用默认设置
  # "both" - 同时使用两套系统（实验性功能）
  priority_strategy: "user_first"

  # 调试模式开关
  debug_mode: true

  # 是否在控制台输出详细日志
  verbose_logging: true

# ========================================
# 游戏级别的开关配置
# ========================================
game_level_settings:
  # 按游戏名称配置不同的僵尸系统
  # 格式: 游戏名称: { use_default: true/false, use_user_custom: true/false }
  
  # 默认游戏配置（当游戏名称未在下方列表中时使用）
  default_game:
    use_default: true
    use_user_custom: false
    
  # 特定游戏配置示例
  # "测试游戏":
  #   use_default: false
  #   use_user_custom: true
  # "经典模式":
  #   use_default: true
  #   use_user_custom: false

# ========================================
# 用户自定义僵尸配置覆盖
# 这些配置将覆盖默认CustomZombie的硬编码设置
# ========================================
user_custom_overrides:
  # 全局默认覆盖设置
  global_defaults:
    # 生命值倍数（基于原始值）
    health_multiplier: 1.0
    
    # 伤害倍数（基于原始值）
    damage_multiplier: 1.0
    
    # 技能冷却时间倍数（基于原始值）
    skill_cooldown_multiplier: 1.0
    
    # 是否启用所有技能
    enable_all_skills: true
    
    # 默认移动速度倍数
    speed_multiplier: 1.0

  # 特定僵尸ID的覆盖配置（ID1-ID4先期实现）
  specific_overrides:
    # ID1 - 普通僵尸覆盖示例
    id1:
      enabled: true                    # 是否启用此覆盖配置
      health_override: 25.0           # 覆盖生命值（原值20.0）
      damage_override: 6.0            # 覆盖伤害值（原值默认）
      speed_multiplier: 1.2           # 速度倍数
      custom_name_override: "§a§l强化普通僵尸"  # 覆盖显示名称
      weapon_override: "STONE_AXE"    # 覆盖武器（原值WOODEN_AXE）
      skill_cooldown_overrides:
        # 技能冷却时间覆盖（如果有技能的话）
        basic_attack: 20              # 基础攻击冷却时间（tick）
      
    # ID2 - 小僵尸覆盖示例  
    id2:
      enabled: true
      health_override: 15.0           # 覆盖生命值（原值10.0）
      damage_override: 4.0            # 覆盖伤害值
      speed_multiplier: 1.5           # 更快的速度倍数
      custom_name_override: "§b§l强化小僵尸"
      weapon_override: "STONE_SWORD"  # 覆盖武器（原值STICK）
      potion_effects:
        # 药水效果覆盖
        speed:
          level: 2                    # 速度等级（原值2，0-based）
          duration: -1                # 持续时间（-1表示永久）
      
    # ID3 - 路障僵尸覆盖示例
    id3:
      enabled: true
      health_override: 40.0           # 覆盖生命值（原值30.0）
      damage_override: 8.0            # 覆盖伤害值
      custom_name_override: "§c§l强化路障僵尸"
      weapon_override: "STONE_AXE"    # 覆盖武器（原值WOODEN_AXE）
      armor_overrides:
        helmet: "DIAMOND_HELMET"      # 覆盖头盔（原值IRON_HELMET）
        helmet_enchantments:
          protection: 2               # 保护附魔等级（原值1）
          
    # ID4 - 钻斧僵尸覆盖示例
    id4:
      enabled: true
      health_override: 25.0           # 覆盖生命值（原值20.0）
      damage_override: 10.0           # 覆盖伤害值（钻石斧高伤害）
      custom_name_override: "§d§l强化钻斧僵尸"
      weapon_override: "NETHERITE_AXE"  # 覆盖武器（原值DIAMOND_AXE）
      armor_overrides:
        leggings: "DIAMOND_LEGGINGS"  # 升级护腿为钻石护腿（原值IRON_LEGGINGS）
        leggings_enchantments:
          protection: 3               # 升级保护附魔等级（原值1）
          unbreaking: 2               # 添加耐久附魔

    # ID5 - 剧毒僵尸覆盖示例
    id5:
      enabled: true
      health_override: 15.0           # 覆盖生命值（原值10.0）
      damage_override: 2.0            # 覆盖伤害值（原值1.0）
      speed_multiplier: 1.3           # 速度倍数（原有速度2效果）
      custom_name_override: "§5§l强化剧毒僵尸"
      weapon_override: "STONE_SWORD"  # 覆盖武器（原值STICK）
      armor_overrides:
        helmet: "LEATHER_HELMET"      # 保持皮革头盔
        chestplate: "CHAINMAIL_CHESTPLATE"  # 升级胸甲为锁链甲
        leggings: "LEATHER_LEGGINGS"  # 保持皮革护腿
        boots: "LEATHER_BOOTS"        # 保持皮革靴子
      potion_effects:
        speed:
          level: 2                    # 速度等级（保持原有效果）
          duration: -1                # 持续时间（永久）
      special_abilities:
        poison_attack_enabled: true   # 是否启用毒性攻击
        poison_duration: 80           # 中毒持续时间（tick，原值60）
        poison_level: 1               # 中毒等级（原值0，0-based）

    # ID6 - 双生僵尸覆盖示例（注意：这是ID6，不是ID4）
    id6:
      enabled: true
      health_override: 18.0           # 覆盖生命值（原值15.0）
      damage_override: 3.0            # 覆盖伤害值（原值2.0）
      custom_name_override: "§e§l多生僵尸"  # 修正颜色为黄色（原值§e）
      weapon_override: "STONE_HOE"    # 覆盖武器（原值WOODEN_HOE）
      skill_cooldown_overrides:
        twin_task_interval: 80        # 双生任务检查间隔（tick，原值100）
      special_abilities:
        twin_zombie_enabled: true     # 是否启用双生僵尸能力
        twin_spawn_on_death: true     # 死亡时是否生成普通僵尸
        twin_spawn_count: 8           # 死亡时生成的普通僵尸数量

    # ID7 - 骷髅僵尸覆盖示例
    id7:
      enabled: true
      health_override: 40.0           # 覆盖生命值（原值30.0）
      damage_override: 6.0            # 覆盖伤害值（原值4.0）
      custom_name_override: "§7§l强化骷髅僵尸"
      weapon_override: "NETHERITE_SWORD"  # 覆盖武器（原值DIAMOND_SWORD）
      armor_overrides:
        helmet: "DIAMOND_HELMET"      # 升级头盔为钻石头盔（原值IRON_HELMET）
        leggings: "DIAMOND_LEGGINGS"  # 升级护腿为钻石护腿（原值IRON_LEGGINGS）
        helmet_enchantments:
          protection: 3               # 保护附魔等级
          unbreaking: 2               # 耐久附魔等级
        leggings_enchantments:
          protection: 3               # 保护附魔等级
          unbreaking: 2               # 耐久附魔等级
      skill_cooldown_overrides:
        arrow_shooting: 40            # 射箭技能冷却时间（tick，原值60）
        arrow_burst_count: 5          # 每次射击的箭矢数量（原值3）
      special_abilities:
        shooting_enabled: true        # 是否启用射箭能力
        arrow_despawn_time: 300       # 箭矢消失时间（tick，原值200）
        target_range: 20.0            # 目标搜索范围（格，原值无限制）

# ========================================
# 用户自定义新僵尸配置
# 完全由配置文件定义的新僵尸类型（暂未实现，预留）
# ========================================
user_custom_zombies:
  # 用户自定义僵尸示例（预留功能）
  uc1:
    enabled: false                   # 暂时禁用，等待后续实现
    name: "自定义战士僵尸"
    display_name: "§e§l自定义战士僵尸"
    health: 80.0
    damage: 10.0
    speed: 1.2
    baby: false
    entity_type: "ZOMBIE"
    equipment:
      helmet: "DIAMOND_HELMET"
      chestplate: "DIAMOND_CHESTPLATE"
      leggings: "DIAMOND_LEGGINGS"
      boots: "DIAMOND_BOOTS"
      mainhand: "DIAMOND_SWORD"
      offhand: "SHIELD"

# ========================================
# 生成系统配置
# ========================================
spawn_system:
  # 生成延迟配置
  spawn_delays:
    default_delay: 0                 # 默认生成延迟（tick）
    user_custom_delay: 5             # 用户自定义僵尸额外延迟
    
  # 生成效果配置
  spawn_effects:
    enable_particles: true           # 是否启用粒子效果
    enable_sounds: true              # 是否启用音效
    particle_type: "VILLAGER_HAPPY"  # 粒子类型
    sound_type: "ENTITY_ZOMBIE_AMBIENT"  # 音效类型
    
  # 元数据标记配置
  metadata_tags:
    default_zombie_tag: "defaultCustomZombie"
    user_custom_tag: "userCustomZombie"
    system_tag: "dualZombieSystem"

# ========================================
# 性能优化配置
# ========================================
performance:
  # 最大同时存在的用户自定义僵尸数量
  max_user_custom_zombies: 50
  
  # 配置文件重载间隔（秒）
  config_reload_interval: 300
  
  # 是否启用配置缓存
  enable_config_cache: true

# ========================================
# 兼容性配置
# ========================================
compatibility:
  # 与原有CustomZombie系统的兼容模式
  legacy_compatibility: true
  
  # 是否保持原有的元数据标记
  preserve_original_metadata: true
  
  # 是否保持原有的命名规则
  preserve_original_naming: true

# ========================================
# 版本信息
# ========================================
version_info:
  config_version: "1.0"
  last_updated: "2025-07-22"
  author: "Ver_zhzh"
  description: "DeathZombieV4双CustomZombie生成系统配置文件"
