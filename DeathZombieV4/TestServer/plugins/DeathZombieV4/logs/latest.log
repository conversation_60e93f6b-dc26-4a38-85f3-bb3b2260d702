=== DeathZombieV4 日志 ===
开始时间: 2025-07-29 13:37:39
日志级别: INFO
===========================
[13:37:39 INFO] 全局日志管理器已初始化，DeathZombieV4日志将输出到: D:\all-projeck\Spigot plugins\DeathZombieV4 - 副本 (Shoot合并) - 副本\DeathZombieV4\TestServer\plugins\DeathZombieV4\logs\latest.log
[13:37:39 INFO] 已重新加载消息配置文件
[13:37:39 INFO] 已加载启用的游戏: DeathZombie实验室
[13:37:39 INFO] 已加载启用的游戏: VillageAttacking
[13:37:39 INFO] 已加载启用的游戏: 尖峰时刻
[13:37:39 INFO] 已加载启用的游戏: 雪屋+_
[13:37:39 INFO] 共加载了 4 个游戏，其中 4 个游戏已启用
[13:37:39 INFO] 已加载 0 个游戏会话和 0 个玩家游戏关联
[13:37:39 INFO] 初始化ZombieHelper...
[13:37:39 INFO] 使用默认配置值初始化CustomZombie组件
[13:37:39 INFO] 开始初始化CustomZombie组件...
[13:37:39 INFO] 正在初始化ParticleHelper...
[13:37:39 INFO] §a成功初始化ParticleHelper
[13:37:39 INFO] 正在初始化ZombieGUIManager...
[13:37:39 INFO] §a成功初始化ZombieGUIManager
[13:37:39 INFO] 正在初始化MutantIronGolem...
[13:37:39 INFO] §a成功初始化MutantIronGolem
[13:37:39 INFO] 正在初始化SoulGuardian...
[13:37:39 INFO] §a成功初始化SoulGuardian
[13:37:39 INFO] 正在初始化MutationKing...
[13:37:39 INFO] MutationKing管理器已初始化
[13:37:39 INFO] §a成功初始化MutationKing
[13:37:39 INFO] 正在注册事件监听器...
[13:37:39 INFO] 成功注册事件监听器
[13:37:39 INFO] §a[CustomZombie] 组件已成功初始化!
[13:37:39 SEVERE] 初始化ZombieHelper时出错: The embedded resource 'zombie.yml' cannot be found in plugins\.paper-remapped\deathzombiev4-1.2.jar
[13:37:39 INFO] 已加载 0 个玩家的显示设置
[13:37:39 INFO] 已加载 0 个玩家的统计数据
[13:37:39 INFO] 成功加载gameKit.yml: 24个武器, 13个护甲, 29个物品, 4个特殊物品, 1个其他物品
[13:37:39 INFO] 正在尝试从config.yml加载大厅位置...
[13:37:39 INFO] 未在config.yml中找到大厅位置配置
[13:37:39 INFO] 大厅管理器初始化完成
[13:37:40 INFO] 幸运箱管理器初始化完成
[13:37:40 INFO] 初始装备编辑器GUI初始化完成
[13:37:40 INFO] 成功连接到Shoot插件
[13:37:40 INFO] 使用原生标题显示功能
[13:37:40 INFO] 没有需要重置的已结束游戏
[13:37:40 INFO] 正在重置所有游戏的生成点状态...
[13:37:40 INFO] 正在处理游戏: 雪屋+_
[13:37:40 INFO] 游戏 雪屋+_ 有 15 个生成点需要重置
[13:37:40 INFO] 将生成点 Power.a 设置为禁用: 成功
[13:37:40 INFO] 将生成点 Power.b 设置为禁用: 成功
[13:37:40 INFO] 将生成点 Power.c 设置为禁用: 成功
[13:37:40 INFO] 将生成点 Bedroom.Head 设置为禁用: 成功
[13:37:40 INFO] 将生成点 Lobby.ZL 设置为禁用: 成功
[13:37:40 INFO] 将生成点 Bedroom.SM 设置为禁用: 成功
[13:37:40 INFO] 将生成点 HOME.AN 设置为禁用: 成功
[13:37:40 INFO] 将生成点 Core.KM 设置为禁用: 成功
[13:37:40 INFO] 将生成点 Lobby 设置为禁用: 成功
[13:37:40 INFO] 将生成点 Lobby.SM 设置为禁用: 成功
[13:37:40 INFO] 将生成点 HOME.KM 设置为禁用: 成功
[13:37:40 INFO] 将生成点 Core.WIN 设置为禁用: 成功
[13:37:40 INFO] 将生成点 HOME 设置为禁用: 成功
[13:37:40 INFO] 将生成点 Lobby.KM 设置为禁用: 成功
[13:37:40 INFO] 将生成点 Core.ZJ 设置为禁用: 成功
[13:37:40 INFO] 生成点 Power.a 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 Power.a 为 禁用: 成功
[13:37:40 INFO] 生成点 Power.b 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 Power.b 为 禁用: 成功
[13:37:40 INFO] 生成点 Power.c 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 Power.c 为 禁用: 成功
[13:37:40 INFO] 生成点 Bedroom.Head 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 Bedroom.Head 为 禁用: 成功
[13:37:40 INFO] 生成点 Lobby.ZL 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 Lobby.ZL 为 禁用: 成功
[13:37:40 INFO] 生成点 Bedroom.SM 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 Bedroom.SM 为 禁用: 成功
[13:37:40 INFO] 生成点 HOME.AN 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 HOME.AN 为 禁用: 成功
[13:37:40 INFO] 生成点 Core.KM 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 Core.KM 为 禁用: 成功
[13:37:40 INFO] 生成点 Lobby 关联的门 暗道 解锁状态: false
[13:37:40 INFO] 生成点 Lobby 的所有关联门都未解锁，保持禁用
[13:37:40 INFO] 设置生成点 Lobby 为 禁用: 成功
[13:37:40 INFO] 生成点 Lobby.SM 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 Lobby.SM 为 禁用: 成功
[13:37:40 INFO] 生成点 HOME.KM 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 HOME.KM 为 禁用: 成功
[13:37:40 INFO] 生成点 Core.WIN 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 Core.WIN 为 禁用: 成功
[13:37:40 INFO] 生成点 HOME 关联的门 出生点大门 解锁状态: true
[13:37:40 INFO] 生成点 HOME 至少有一个关联门已解锁，设置为启用
[13:37:40 INFO] 设置生成点 HOME 为 启用: 成功
[13:37:40 INFO] 生成点 Lobby.KM 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 Lobby.KM 为 禁用: 成功
[13:37:40 INFO] 生成点 Core.ZJ 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 Core.ZJ 为 禁用: 成功
[13:37:40 INFO] 已重置游戏 雪屋+_ 的 15 个生成点状态
[13:37:40 INFO] 正在处理游戏: VillageAttacking
[13:37:40 INFO] 游戏 VillageAttacking 有 2 个生成点需要重置
[13:37:40 INFO] 将生成点 外僵尸 设置为禁用: 成功
[13:37:40 INFO] 将生成点 门里僵尸 设置为禁用: 成功
[13:37:40 INFO] 生成点 外僵尸 关联的门 解锁门 解锁状态: true
[13:37:40 INFO] 生成点 外僵尸 至少有一个关联门已解锁，设置为启用
[13:37:40 INFO] 设置生成点 外僵尸 为 启用: 成功
[13:37:40 INFO] 生成点 门里僵尸 关联的门 不解锁门 解锁状态: false
[13:37:40 INFO] 生成点 门里僵尸 的所有关联门都未解锁，保持禁用
[13:37:40 INFO] 设置生成点 门里僵尸 为 禁用: 成功
[13:37:40 INFO] 已重置游戏 VillageAttacking 的 2 个生成点状态
[13:37:40 INFO] 正在处理游戏: DeathZombie实验室
[13:37:40 INFO] 游戏 DeathZombie实验室 有 16 个生成点需要重置
[13:37:40 INFO] 将生成点 小实验室 设置为禁用: 成功
[13:37:40 INFO] 将生成点 生物仓库近2 设置为禁用: 成功
[13:37:40 INFO] 将生成点 实验室大厅 设置为禁用: 成功
[13:37:40 INFO] 将生成点 飞机残骸 设置为禁用: 成功
[13:37:40 INFO] 将生成点 后实验室 设置为禁用: 成功
[13:37:40 INFO] 将生成点 实验室大厅靠仓库 设置为禁用: 成功
[13:37:40 INFO] 将生成点 天台 设置为禁用: 成功
[13:37:40 INFO] 将生成点 靠生物仓库远 设置为禁用: 成功
[13:37:40 INFO] 将生成点 仓库最大窗户 设置为禁用: 成功
[13:37:40 INFO] 将生成点 实验体78 设置为禁用: 成功
[13:37:40 INFO] 将生成点 实验体77 设置为禁用: 成功
[13:37:40 INFO] 将生成点 实验体76 设置为禁用: 成功
[13:37:40 INFO] 将生成点 仓库最大窗户旁边 设置为禁用: 成功
[13:37:40 INFO] 将生成点 靠生物仓库近 设置为禁用: 成功
[13:37:40 INFO] 将生成点 生物仓库远 设置为禁用: 成功
[13:37:40 INFO] 将生成点 靠窗口大门仓库 设置为禁用: 成功
[13:37:40 INFO] 生成点 小实验室 关联的门 小实验室 解锁状态: false
[13:37:40 INFO] 生成点 小实验室 的所有关联门都未解锁，保持禁用
[13:37:40 INFO] 设置生成点 小实验室 为 禁用: 成功
[13:37:40 INFO] 生成点 生物仓库近2 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 生物仓库近2 为 禁用: 成功
[13:37:40 INFO] 生成点 实验室大厅 关联的门 实验室大厅大门 解锁状态: true
[13:37:40 INFO] 生成点 实验室大厅 至少有一个关联门已解锁，设置为启用
[13:37:40 INFO] 设置生成点 实验室大厅 为 启用: 成功
[13:37:40 INFO] 生成点 飞机残骸 关联的门 飞机大门 解锁状态: true
[13:37:40 INFO] 生成点 飞机残骸 至少有一个关联门已解锁，设置为启用
[13:37:40 INFO] 设置生成点 飞机残骸 为 启用: 成功
[13:37:40 INFO] 生成点 后实验室 关联的门 后实验室大门 解锁状态: false
[13:37:40 INFO] 生成点 后实验室 的所有关联门都未解锁，保持禁用
[13:37:40 INFO] 设置生成点 后实验室 为 禁用: 成功
[13:37:40 INFO] 生成点 实验室大厅靠仓库 关联的门 实验室桥大门 解锁状态: true
[13:37:40 INFO] 生成点 实验室大厅靠仓库 至少有一个关联门已解锁，设置为启用
[13:37:40 INFO] 设置生成点 实验室大厅靠仓库 为 启用: 成功
[13:37:40 INFO] 生成点 天台 关联的门 天台 解锁状态: false
[13:37:40 INFO] 生成点 天台 的所有关联门都未解锁，保持禁用
[13:37:40 INFO] 设置生成点 天台 为 禁用: 成功
[13:37:40 INFO] 生成点 靠生物仓库远 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 靠生物仓库远 为 禁用: 成功
[13:37:40 INFO] 生成点 仓库最大窗户 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 仓库最大窗户 为 禁用: 成功
[13:37:40 INFO] 生成点 实验体78 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 实验体78 为 禁用: 成功
[13:37:40 INFO] 生成点 实验体77 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 实验体77 为 禁用: 成功
[13:37:40 INFO] 生成点 实验体76 关联的门 前实验室大门 解锁状态: false
[13:37:40 INFO] 生成点 实验体76 的所有关联门都未解锁，保持禁用
[13:37:40 INFO] 设置生成点 实验体76 为 禁用: 成功
[13:37:40 INFO] 生成点 仓库最大窗户旁边 关联的门 仓库大门 解锁状态: false
[13:37:40 INFO] 生成点 仓库最大窗户旁边 的所有关联门都未解锁，保持禁用
[13:37:40 INFO] 设置生成点 仓库最大窗户旁边 为 禁用: 成功
[13:37:40 INFO] 生成点 靠生物仓库近 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 靠生物仓库近 为 禁用: 成功
[13:37:40 INFO] 生成点 生物仓库远 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 生物仓库远 为 禁用: 成功
[13:37:40 INFO] 生成点 靠窗口大门仓库 没有关联的门，保持禁用状态
[13:37:40 INFO] 设置生成点 靠窗口大门仓库 为 禁用: 成功
[13:37:40 INFO] 已重置游戏 DeathZombie实验室 的 16 个生成点状态
[13:37:40 INFO] 正在处理游戏: 尖峰时刻
[13:37:40 INFO] 游戏 尖峰时刻 有 21 个生成点需要重置
[13:37:40 INFO] 将生成点 5门车 设置为禁用: 成功
[13:37:40 INFO] 将生成点 2门地上 设置为禁用: 成功
[13:37:41 INFO] 将生成点 2门地下 设置为禁用: 成功
[13:37:41 INFO] 将生成点 3门马路 设置为禁用: 成功
[13:37:41 INFO] 将生成点 6门车 设置为禁用: 成功
[13:37:41 INFO] 将生成点 5门马路 设置为禁用: 成功
[13:37:41 INFO] 将生成点 2门地下石头 设置为禁用: 成功
[13:37:41 INFO] 将生成点 6门石头 设置为禁用: 成功
[13:37:41 INFO] 将生成点 出生点附近2 设置为禁用: 成功
[13:37:41 INFO] 将生成点 4门马路 设置为禁用: 成功
[13:37:41 INFO] 将生成点 出生点附近 设置为禁用: 成功
[13:37:41 INFO] 将生成点 6门马路 设置为禁用: 成功
[13:37:41 INFO] 将生成点 4门石头 设置为禁用: 成功
[13:37:41 INFO] 将生成点 3门石头 设置为禁用: 成功
[13:37:41 INFO] 将生成点 3门马路2 设置为禁用: 成功
[13:37:41 INFO] 将生成点 出生点大门上方 设置为禁用: 成功
[13:37:41 INFO] 将生成点 出生点大门 设置为禁用: 成功
[13:37:41 INFO] 将生成点 2门地下2 设置为禁用: 成功
[13:37:41 INFO] 将生成点 4门洞 设置为禁用: 成功
[13:37:41 INFO] 将生成点 3门地上 设置为禁用: 成功
[13:37:41 INFO] 将生成点 6门洞 设置为禁用: 成功
[13:37:41 INFO] 生成点 5门车 关联的门 大门5 解锁状态: false
[13:37:41 INFO] 生成点 5门车 的所有关联门都未解锁，保持禁用
[13:37:41 INFO] 设置生成点 5门车 为 禁用: 成功
[13:37:41 INFO] 生成点 2门地上 关联的门 大门1 解锁状态: false
[13:37:41 INFO] 生成点 2门地上 的所有关联门都未解锁，保持禁用
[13:37:41 INFO] 设置生成点 2门地上 为 禁用: 成功
[13:37:41 INFO] 生成点 2门地下 关联的门 大门1 解锁状态: false
[13:37:41 INFO] 生成点 2门地下 的所有关联门都未解锁，保持禁用
[13:37:41 INFO] 设置生成点 2门地下 为 禁用: 成功
[13:37:41 INFO] 生成点 3门马路 关联的门 大门2 解锁状态: false
[13:37:41 INFO] 生成点 3门马路 的所有关联门都未解锁，保持禁用
[13:37:41 INFO] 设置生成点 3门马路 为 禁用: 成功
[13:37:41 INFO] 生成点 6门车 关联的门 大门4 解锁状态: false
[13:37:41 INFO] 生成点 6门车 的所有关联门都未解锁，保持禁用
[13:37:41 INFO] 设置生成点 6门车 为 禁用: 成功
[13:37:41 INFO] 生成点 5门马路 关联的门 大门5 解锁状态: false
[13:37:41 INFO] 生成点 5门马路 的所有关联门都未解锁，保持禁用
[13:37:41 INFO] 设置生成点 5门马路 为 禁用: 成功
[13:37:41 INFO] 生成点 2门地下石头 关联的门 大门1 解锁状态: false
[13:37:41 INFO] 生成点 2门地下石头 的所有关联门都未解锁，保持禁用
[13:37:41 INFO] 设置生成点 2门地下石头 为 禁用: 成功
[13:37:41 INFO] 生成点 6门石头 关联的门 大门4 解锁状态: false
[13:37:41 INFO] 生成点 6门石头 的所有关联门都未解锁，保持禁用
[13:37:41 INFO] 设置生成点 6门石头 为 禁用: 成功
[13:37:41 INFO] 生成点 出生点附近2 关联的门 出生点木头 解锁状态: true
[13:37:41 INFO] 生成点 出生点附近2 至少有一个关联门已解锁，设置为启用
[13:37:41 INFO] 设置生成点 出生点附近2 为 启用: 成功
[13:37:41 INFO] 生成点 4门马路 关联的门 大门3 解锁状态: false
[13:37:41 INFO] 生成点 4门马路 的所有关联门都未解锁，保持禁用
[13:37:41 INFO] 设置生成点 4门马路 为 禁用: 成功
[13:37:41 INFO] 生成点 出生点附近 关联的门 出生点木头 解锁状态: true
[13:37:41 INFO] 生成点 出生点附近 至少有一个关联门已解锁，设置为启用
[13:37:41 INFO] 设置生成点 出生点附近 为 启用: 成功
[13:37:41 INFO] 生成点 6门马路 关联的门 大门4 解锁状态: false
[13:37:41 INFO] 生成点 6门马路 的所有关联门都未解锁，保持禁用
[13:37:41 INFO] 设置生成点 6门马路 为 禁用: 成功
[13:37:41 INFO] 生成点 4门石头 关联的门 大门3 解锁状态: false
[13:37:41 INFO] 生成点 4门石头 的所有关联门都未解锁，保持禁用
[13:37:41 INFO] 设置生成点 4门石头 为 禁用: 成功
[13:37:41 INFO] 生成点 3门石头 关联的门 大门2 解锁状态: false
[13:37:41 INFO] 生成点 3门石头 的所有关联门都未解锁，保持禁用
[13:37:41 INFO] 设置生成点 3门石头 为 禁用: 成功
[13:37:41 INFO] 生成点 3门马路2 关联的门 大门2 解锁状态: false
[13:37:41 INFO] 生成点 3门马路2 的所有关联门都未解锁，保持禁用
[13:37:41 INFO] 设置生成点 3门马路2 为 禁用: 成功
[13:37:41 INFO] 生成点 出生点大门上方 关联的门 出生点木头 解锁状态: true
[13:37:41 INFO] 生成点 出生点大门上方 至少有一个关联门已解锁，设置为启用
[13:37:41 INFO] 设置生成点 出生点大门上方 为 启用: 成功
[13:37:41 INFO] 生成点 出生点大门 关联的门 出生点木头 解锁状态: true
[13:37:41 INFO] 生成点 出生点大门 至少有一个关联门已解锁，设置为启用
[13:37:41 INFO] 设置生成点 出生点大门 为 启用: 成功
[13:37:41 INFO] 生成点 2门地下2 关联的门 大门1 解锁状态: false
[13:37:41 INFO] 生成点 2门地下2 的所有关联门都未解锁，保持禁用
[13:37:41 INFO] 设置生成点 2门地下2 为 禁用: 成功
[13:37:41 INFO] 生成点 4门洞 关联的门 大门3 解锁状态: false
[13:37:41 INFO] 生成点 4门洞 的所有关联门都未解锁，保持禁用
[13:37:41 INFO] 设置生成点 4门洞 为 禁用: 成功
[13:37:41 INFO] 生成点 3门地上 关联的门 大门2 解锁状态: false
[13:37:41 INFO] 生成点 3门地上 的所有关联门都未解锁，保持禁用
[13:37:41 INFO] 设置生成点 3门地上 为 禁用: 成功
[13:37:41 INFO] 生成点 6门洞 关联的门 大门4 解锁状态: false
[13:37:41 INFO] 生成点 6门洞 的所有关联门都未解锁，保持禁用
[13:37:41 INFO] 设置生成点 6门洞 为 禁用: 成功
[13:37:41 INFO] 已重置游戏 尖峰时刻 的 21 个生成点状态
[13:37:41 INFO] 已完成 4 个游戏的生成点状态重置
[13:37:41 INFO] 已启动idc类型实体名称隐藏任务
[13:37:41 INFO] 已将ScoreboardManager设置到ZombieSpawnManager
[13:37:41 INFO] 检测到DecentHolograms插件，将使用悬浮文字功能
[13:37:41 INFO] 全息排行榜管理器初始化完成
[13:37:41 INFO] 敌对AI管理器初始化完成
[13:37:41 INFO] 幸运箱点击监听器已注册
[13:37:41 INFO] 敌对AI监听器已注册
[13:37:41 INFO] [STDOUT] ========================================================
[13:37:41 INFO] [STDOUT]                 DeathZombieV4 插件启动中...
[13:37:41 INFO] [STDOUT] ========================================================
[13:37:41 INFO] [STDOUT] 
[13:37:41 INFO] [STDOUT]     ####  #####   ###  ##### #   #
[13:37:41 INFO] [STDOUT]     #   # #      #   #   #   #   #
[13:37:41 INFO] [STDOUT]     #   # ###    #####   #   #####
[13:37:41 INFO] [STDOUT]     #   # #      #   #   #   #   #
[13:37:41 INFO] [STDOUT]     ####  ##### #   #   #   #   #
[13:37:41 INFO] [STDOUT] 
[13:37:41 INFO] [STDOUT]     ##### ##### #   # ##### ##### #####
[13:37:41 INFO] [STDOUT]       #   #   # ## ## #   #   #   #
[13:37:41 INFO] [STDOUT]       #   #   # # # # ##### ###   ###
[13:37:41 INFO] [STDOUT]       #   #   # #   # #   #   #   #
[13:37:41 INFO] [STDOUT]       #   ##### #   # ##### ##### #####
[13:37:41 INFO] [STDOUT] 
[13:37:41 INFO] [STDOUT]                     V4 版本
[13:37:41 INFO] [STDOUT] 
[13:37:41 INFO] [STDOUT] ========================================================
[13:37:41 INFO] [STDOUT]   插件信息:
[13:37:41 INFO] [STDOUT]     版本: 1.2
[13:37:41 INFO] [STDOUT]     作者: Ver_zhzh
[13:37:41 INFO] [STDOUT] 
[13:37:41 INFO] [STDOUT]   核心功能:
[13:37:41 INFO] [STDOUT]     + 多种僵尸类型支持
[13:37:41 INFO] [STDOUT]     + 回合制游戏系统
[13:37:41 INFO] [STDOUT]     + 实时计分板显示
[13:37:41 INFO] [STDOUT]     + 金钱奖励系统
[13:37:41 INFO] [STDOUT]     + 购买点与幸运箱
[13:37:41 INFO] [STDOUT]     + 门锁与电源系统
[13:37:41 INFO] [STDOUT]     + Web管理界面
[13:37:41 INFO] [STDOUT] 
[13:37:41 INFO] [STDOUT] ========================================================
[13:37:41 INFO] [STDOUT]               DeathZombieV4 插件启动完成！
[13:37:41 INFO] [STDOUT] ========================================================
[13:37:41 INFO] DeathZombieV4 v1.2 插件已成功启动！
[13:37:41 INFO] Web服务器将绑定到指定地址: 127.0.0.1
[13:37:41 INFO] 成功注册装备API端点
[13:37:41 INFO] 没有找到怪物预设文件
[13:37:41 INFO] 成功注册怪物预设API端点
[13:37:41 INFO] 成功注册僵尸生成设置API端点
[13:37:41 INFO] WebUI服务器已启动（直接使用JAR包中的WebUI文件）
[13:37:41 INFO] 绑定地址: 127.0.0.1:1777
[13:37:41 INFO] WebUI密码验证已启用，密码: admin123
[13:37:41 INFO] 访问地址: http://127.0.0.1:1777
[13:37:41 INFO] Web服务器启动成功
[13:37:43 INFO] 已同步 0 个在线玩家的显示设置到Shoot插件
