name: 雪屋+_
maxPlayers: 4
spawn:
  world: testWorld
  x: -0.359363660964302
  y: -61.0
  z: 40.78084861227799
  yaw: 176.41742
  pitch: 4.500094
rounds: 20
zombieCount:
  round1: 10
  round2: 15
  round3: 20
  round4: 25
  round5: 30
  round6: 35
  round7: 40
  round8: 45
  round9: 50
  round10: 55
  round11: 60
  round12: 65
  round13: 70
  round14: 75
  round15: 80
  round16: 85
  round17: 90
  round18: 95
  round19: 35
  round20: 5
powerButton:
  enabled: false
minPlayers: 1
power_button:
  world: testWorld
  x: 33.0
  y: -59.0
  z: 117.0
  price: 1000
  unlocked: false
luckyBoxes:
  box1:
    world: testWorld
    x: 24.5
    y: -61.0
    z: 42.0
    type: local
    openRound: 1
    openCost: 1000
    enabled: true
  box2:
    world: testWorld
    x: 36.0
    y: -61.0
    z: 71.0
    type: local
    openRound: 1
    openCost: 1000
    enabled: true
buyPoints:
  f7d9e0b1-a461-4f37-8c2f-da792624b05b:
    type: it
    itemId: id1
    name: 速度buff1药水
    location:
      world: testWorld
      x: 7.812809620455101
      y: -59.99641799083073
      z: 42.54252043451745
      yaw: 179.40015
      pitch: -6.450002
  5c63ebc3-61f3-4ba1-b80d-36f9f22e13d8:
    type: ar
    itemId: id1
    name: 皮革套装(上)
    location:
      world: testWorld
      x: 21.69999998807907
      y: -59.62771653617985
      z: 31.540152086317075
      yaw: 93.90015
      pitch: -3.3000064
  047f9cc6-579a-4c4e-9767-8c987ba09a0a:
    type: sp
    itemId: id1
    name: 全体加速
    location:
      world: testWorld
      x: 34.735908411871435
      y: -59.99435694245192
      z: 28.30000001192093
      yaw: 2.852295
      pitch: -1.0500064
  06fad4a3-49e2-44cc-8222-a1f1b2c7b1d8:
    type: ar
    itemId: id2
    name: 皮革套装(下)
    location:
      world: testWorld
      x: 36.69999998807907
      y: -59.99435694245192
      z: 48.72106816831799
      yaw: 93.302
      pitch: 4.6499953
  37db28a7-476c-4117-a8e8-4c482946edda:
    type: wp
    itemId: id2
    name: 制式步枪
    location:
      world: testWorld
      x: 29.815371361007152
      y: -59.99435694245192
      z: 42.697694212022164
      yaw: -178.198
      pitch: 1.0499952
  369b625c-72ab-4757-955c-141027acdea5:
    type: wp
    itemId: id3
    name: 霰弹枪
    location:
      world: testWorld
      x: 1.6557800764551402
      y: -59.99435694245192
      z: 45.30000001192093
      yaw: -2.397705
      pitch: -7.9500017
  52f33a55-1976-4e45-95fb-862d47731ca0:
    type: it
    itemId: id3
    name: 手枪弹药
    location:
      world: testWorld
      x: 21.56115346335571
      y: -59.5
      z: 55.284851653472536
      yaw: 1.951416
      pitch: -1.8000163
  d18ee9dc-9a21-4196-a1f0-1bdd51aae94a:
    type: ar
    itemId: id3
    name: 锁链套装(上)
    location:
      world: testWorld
      x: 29.30000001192093
      y: -51.0
      z: 57.369349046738954
      yaw: -88.04883
      pitch: 3.1499798
  a16b9b60-80d7-4386-8d2e-de9d5e0319d0:
    type: sp
    itemId: id3
    name: 全体回复(2)
    location:
      world: testWorld
      x: 5.88072048025745
      y: -50.53831893589209
      z: 45.30000001192093
      yaw: 1.0449219
      pitch: -6.1500363
  63fa2314-f6c6-4922-ace5-6d6e47d091c6:
    type: sp
    itemId: id4
    name: 大量牛奶
    location:
      world: testWorld
      x: -3.699999988079071
      y: -59.586190124336845
      z: 74.9922754400071
      yaw: -85.9541
      pitch: -8.100017
  56d0dc0a-d441-4fb5-b0d8-73f73de4f055:
    type: it
    itemId: id4
    name: 步枪弹药
    location:
      world: testWorld
      x: 11.628675168924842
      y: -59.586190124336845
      z: 67.34705963476833
      yaw: 4.194336
      pitch: -9.150014
  fa0c1684-e3f0-4db5-a974-4b12f173c7bc:
    type: ar
    itemId: id4
    name: 锁链套装(下)
    location:
      world: testWorld
      x: 32.936833063514364
      y: -59.586190124336845
      z: 67.30000001192093
      yaw: 7.0444336
      pitch: -5.55001
  1bf9f1ee-c558-4d37-8f0a-55ecca6dff75:
    type: ar
    itemId: id7
    name: 钻石套装(上)
    location:
      world: testWorld
      x: 13.277665652267673
      y: -59.586190124336845
      z: 94.69999998807907
      yaw: -178.65674
      pitch: 1.200006
  86e77ce7-76c7-4f25-8707-09d923c8f2e2:
    type: ar
    itemId: id8
    name: 钻石套装(下)
    location:
      world: testWorld
      x: 19.315440719225442
      y: -59.99542356583895
      z: 97.30000001192093
      yaw: 11.095215
      pitch: -5.399994
  6b620806-65f3-4327-894c-0102ea76bc9d:
    type: wp
    itemId: id20
    name: 彩虹喷射器
    location:
      world: testWorld
      x: 36.69999998807907
      y: -59.99627993317507
      z: 104.4134776575911
      yaw: 100.944336
      pitch: 3.1500068
  27a6cb17-7698-43d2-9436-1c08e7a6de1b:
    type: sp
    itemId: id8
    name: 全体力量
    location:
      world: testWorld
      x: 29.433971240422334
      y: -58.999364450970475
      z: 108.69999998807907
      yaw: 178.79932
      pitch: 4.199996
  bab964fa-20c5-4930-a5ac-2ddde97d42c8:
    type: wp
    itemId: id24
    name: 循声炮
    location:
      world: testWorld
      x: -3.699999988079071
      y: -59.74331786293252
      z: 118.3165838020229
      yaw: -92.40234
      pitch: -1.5000039
  6ba9c9a1-a887-4277-8465-356302433825:
    type: sp
    itemId: id9
    name: 生命提升(3)
    location:
      world: testWorld
      x: 5.1787900968228895
      y: -59.74331786293252
      z: 111.33095378509876
      yaw: -7.0512695
      pitch: -8.250007
  659d035d-224d-4a6f-9e85-93914ad35257:
    type: it
    itemId: id5
    name: 霰弹枪弹药
    location:
      world: testWorld
      x: -3.699999988079071
      y: -61.0
      z: 37.115088358857264
      yaw: -86.832825
      pitch: -3.7499158
waitTime: 10
zombieSpawns:
  HOME:
    type: id
    world: testWorld
    x: -6.842447378564439
    y: -60.0
    z: 32.71340637213543
    enabled: true
  HOME_AN:
    type: id
    world: testWorld
    x: 13.900549732108885
    y: -60.0
    z: 24.494557954015512
    enabled: false
  HOME_KM:
    type: id
    world: testWorld
    x: 29.447506814111076
    y: -60.0
    z: 25.901184270938618
    enabled: false
  Lobby:
    type: id
    world: testWorld
    x: 38.89040973732254
    y: -60.0
    z: 60.1471286020921
    enabled: false
  Lobby_KM:
    type: id
    world: testWorld
    x: -5.898181821140952
    y: -60.0
    z: 50.87223453311144
    enabled: false
  Lobby_SM:
    type: id
    world: testWorld
    x: -6.394058946941061
    y: -52.0
    z: 50.3776163680544
    enabled: false
  Lobby_ZL:
    type: id
    world: testWorld
    x: 21.526226385408858
    y: -60.0
    z: 67.8814307497087
    enabled: false
  Core_ZJ:
    type: id
    world: testWorld
    x: 16.68381051481801
    y: -56.168701406967166
    z: 80.59273495700164
    enabled: false
  Core_KM:
    type: id
    world: testWorld
    x: -6.338120792717884
    y: -60.0
    z: 85.85987869424032
    enabled: false
  Core_WIN:
    type: id
    world: testWorld
    x: 39.26466062959672
    y: -61.0
    z: 78.64527515095409
    enabled: false
  Bedroom_SM:
    type: id
    world: testWorld
    x: 6.725558012711778
    y: -52.21609880836242
    z: 103.1258571500602
    enabled: false
  Bedroom_Head:
    type: id
    world: testWorld
    x: -6.165848685079484
    y: -60.0
    z: 103.6402701763025
    enabled: false
  Power_a:
    type: id
    world: testWorld
    x: 0.06006881204041621
    y: -61.0
    z: 123.69999998807907
    enabled: false
  Power_b:
    type: id
    world: testWorld
    x: 17.461379069122238
    y: -61.0
    z: 123.69999998807907
    enabled: false
  Power_c:
    type: id
    world: testWorld
    x: 28.731570717424525
    y: -61.0
    z: 113.76912135646
    enabled: false
doors:
  内部大门:
    locked: true
    unlockPrice: 1000
    region:
      world: testWorld
      minX: -4.0
      minY: -61.0
      minZ: 65.0
      maxX: 1.0
      maxY: -49.0
      maxZ: 66.0
    linkedSpawns:
    - Core_ZJ
    - Core_KM
    - Core_WIN
    InGameOpening: false
  暗道:
    locked: true
    unlockPrice: 100
    region:
      world: testWorld
      minX: 11.0
      minY: -61.0
      minZ: 43.0
      maxX: 11.0
      maxY: -58.0
      maxZ: 44.0
    linkedSpawns:
    - Lobby
    - Lobby_KM
    InGameOpening: false
  大厅:
    locked: true
    unlockPrice: 500
    region:
      world: testWorld
      minX: 34.0
      minY: -61.0
      minZ: 43.0
      maxX: 36.0
      maxY: -56.0
      maxZ: 43.0
    linkedSpawns:
    - Lobby_SM
    - Lobby_ZL
    InGameOpening: false
  卧室:
    locked: true
    unlockPrice: 2000
    region:
      world: testWorld
      minX: 32.0
      minY: -61.0
      minZ: 95.0
      maxX: 34.0
      maxY: -59.0
      maxZ: 96.0
    linkedSpawns:
    - Bedroom_SM
    - Bedroom_Head
    InGameOpening: false
  动力室:
    locked: true
    unlockPrice: 3000
    region:
      world: testWorld
      minX: 15.0
      minY: -61.0
      minZ: 109.0
      maxX: 19.0
      maxY: -52.0
      maxZ: 110.0
    linkedSpawns:
    - Power_a
    - Power_b
    - Power_c
    InGameOpening: false
  出生点大门:
    locked: false
    region:
      world: testWorld
      minX: 3.0
      minY: -61.0
      minZ: 28.0
      maxX: 3.0
      maxY: -50.0
      maxZ: 30.0
    linkedSpawns:
    - HOME
    - HOME_AN
    - HOME_KM
    InGameOpening: true
roundModes:
  round17:
    HOME_AN:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id3
        count: '5'
        type: id
        number: '1'
    Core_ZJ:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc11
        count: '5'
        type: id
        number: '1'
    Core_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id12
        count: '5'
        type: id
        number: '1'
    Lobby:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id8
        count: '10'
        type: id
        number: '1'
    Lobby_SM:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc18
        count: '10'
        type: id
        number: '1'
    Core_WIN:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id18
        count: '5'
        type: id
        number: '1'
    HOME:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id21
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    Lobby_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id21
        count: '20'
        type: id
        number: '1'
    Lobby_ZL:
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn5
        count: '10'
        type: id
        number: '1'
    Bedroom_SM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id20
        count: '5'
        type: id
        number: '1'
    HOME_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id7
        count: '5'
        type: id
        number: '1'
  round18:
    HOME_AN:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id3
        count: '5'
        type: id
        number: '1'
    Core_ZJ:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc1
        count: '15'
        type: id
        number: '1'
    Core_KM:
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn2
        count: '5'
        type: id
        number: '1'
    Lobby:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id8
        count: '10'
        type: id
        number: '1'
    Lobby_SM:
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn2
        count: '5'
        type: id
        number: '1'
    Bedroom_Head:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id18
        count: '5'
        type: id
        number: '1'
    HOME:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id21
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    Lobby_KM:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc15
        count: '15'
        type: id
        number: '1'
    Lobby_ZL:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id17
        count: '20'
        type: id
        number: '1'
    HOME_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id7
        count: '5'
        type: id
        number: '1'
  round19:
    HOME_AN:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id3
        count: '5'
        type: id
        number: '1'
    Power_b:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc18
        count: '10'
        type: id
        number: '1'
    Core_ZJ:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id23
        count: '5'
        type: id
        number: '1'
    Lobby:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id8
        count: '10'
        type: id
        number: '1'
    Bedroom_Head:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id24
        count: '5'
        type: id
        number: '1'
    HOME:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id21
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id18
        count: '10'
        type: id
        number: '1'
    Lobby_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id22
        count: '5'
        type: id
        number: '1'
    HOME_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id7
        count: '5'
        type: id
        number: '1'
  round13:
    HOME_AN:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id3
        count: '5'
        type: id
        number: '1'
    Core_ZJ:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc6
        count: '5'
        type: id
        number: '1'
    Core_KM:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc3
        count: '25'
        type: id
        number: '1'
    Lobby:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id8
        count: '10'
        type: id
        number: '1'
    Core_WIN:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id16
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn4
        count: '15'
        type: id
        number: '1'
    HOME:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id21
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    HOME_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id7
        count: '5'
        type: id
        number: '1'
  round14:
    HOME_AN:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id17
        count: '5'
        type: id
        number: '1'
    Core_ZJ:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc1
        count: '35'
        type: id
        number: '1'
    Lobby:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id8
        count: '10'
        type: id
        number: '1'
    Lobby_SM:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc12
        count: '3'
        type: id
        number: '1'
    Core_WIN:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc18
        count: '5'
        type: id
        number: '1'
    HOME:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id21
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    Lobby_KM:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc15
        count: '10'
        type: id
        number: '1'
    Lobby_ZL:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc10
        count: '2'
        type: id
        number: '1'
    HOME_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id7
        count: '5'
        type: id
        number: '1'
  round15:
    HOME_AN:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id3
        count: '5'
        type: id
        number: '1'
    Core_ZJ:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id23
        count: '1'
        type: id
        number: '1'
    Core_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id17
        count: '9'
        type: id
        number: '1'
    Lobby:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id8
        count: '10'
        type: id
        number: '1'
    Lobby_SM:
      刷怪逻辑2:
        monsterType: entity
        monsterId: idc7
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc8
        count: '5'
        type: id
        number: '1'
    HOME:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id21
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    Lobby_KM:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc15
        count: '20'
        type: id
        number: '1'
    Lobby_ZL:
      刷怪逻辑2:
        monsterType: entity
        monsterId: idc9
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc16
        count: '5'
        type: id
        number: '1'
    HOME_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id7
        count: '5'
        type: id
        number: '1'
  round16:
    HOME_AN:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id3
        count: '5'
        type: id
        number: '1'
    Core_ZJ:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc15
        count: '15'
        type: id
        number: '1'
    Core_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id17
        count: '10'
        type: id
        number: '1'
    Lobby:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id8
        count: '10'
        type: id
        number: '1'
    Lobby_SM:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc17
        count: '10'
        type: id
        number: '1'
    Core_WIN:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc12
        count: '5'
        type: id
        number: '1'
    HOME:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id21
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    Lobby_KM:
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn5
        count: '10'
        type: id
        number: '1'
    Lobby_ZL:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc18
        count: '5'
        type: id
        number: '1'
    HOME_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id7
        count: '5'
        type: id
        number: '1'
  round9:
    HOME_AN:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id3
        count: '5'
        type: id
        number: '1'
    Lobby:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id8
        count: '10'
        type: id
        number: '1'
    Lobby_SM:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc3
        count: '5'
        type: id
        number: '1'
    HOME:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id21
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    Lobby_KM:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc6
        count: '5'
        type: id
        number: '1'
    Lobby_ZL:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc1
        count: '15'
        type: id
        number: '1'
    HOME_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id7
        count: '5'
        type: id
        number: '1'
  round8:
    HOME_AN:
      刷怪逻辑2:
        monsterType: entity
        monsterId: idc2
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc6
        count: '5'
        type: id
        number: '1'
    Lobby:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc4
        count: '5'
        type: id
        number: '1'
    HOME:
      刷怪逻辑3:
        monsterType: zombie
        monsterId: id12
        count: '5'
        type: id
        number: '1'
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id5
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    Lobby_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id8
        count: '5'
        type: id
        number: '1'
    HOME_KM:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id20
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc5
        count: '5'
        type: id
        number: '1'
  round7:
    HOME_AN:
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn1
        count: '5'
        type: id
        number: '1'
    Lobby:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc4
        count: '5'
        type: id
        number: '1'
    HOME:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '20'
        type: id
        number: '1'
    Lobby_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id10
        count: '5'
        type: id
        number: '1'
    HOME_KM:
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn1
        count: '5'
        type: id
        number: '1'
  round6:
    HOME_AN:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id11
        count: '5'
        type: id
        number: '1'
    Lobby:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id8
        count: '5'
        type: id
        number: '1'
    HOME:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id2
        count: '10'
        type: id
        number: '1'
    Lobby_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id9
        count: '5'
        type: id
        number: '1'
    HOME_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
  round1:
    HOME:
      刷怪逻辑2:
        monsterType: entity
        monsterId: idc1
        count: '5'
        type: idc
        number: c1
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
  round20:
    HOME_AN:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id25
        count: '1'
        type: id
        number: '1'
    Lobby:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id8
        count: '10'
        type: id
        number: '1'
    HOME:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id21
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    HOME_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id7
        count: '5'
        type: id
        number: '1'
    Power_b:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id23
        count: '5'
        type: id
        number: '1'
    Core_KM:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc20
        count: '1'
        type: id
        number: '1'
    Bedroom_Head:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc21
        count: '1'
        type: id
        number: '1'
    Lobby_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id19
        count: '1'
        type: id
        number: '1'
  round10:
    HOME_AN:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id3
        count: '5'
        type: id
        number: '1'
    Lobby:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id8
        count: '10'
        type: id
        number: '1'
    Lobby_SM:
      刷怪逻辑2:
        monsterType: entity
        monsterId: idc3
        count: '10'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc8
        count: '2'
        type: id
        number: '1'
    HOME:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id21
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    Lobby_KM:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id12
        count: '10'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc7
        count: '1'
        type: id
        number: '1'
    Lobby_ZL:
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn2
        count: '2'
        type: id
        number: '1'
    HOME_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id7
        count: '5'
        type: id
        number: '1'
  round11:
    HOME_AN:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id3
        count: '5'
        type: id
        number: '1'
    Lobby:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id8
        count: '10'
        type: id
        number: '1'
    Lobby_SM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id15
        count: '5'
        type: id
        number: '1'
    HOME:
      刷怪逻辑3:
        monsterType: npc
        monsterId: idn3
        count: '20'
        type: id
        number: '1'
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id21
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    Lobby_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id14
        count: '5'
        type: id
        number: '1'
    HOME_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id7
        count: '5'
        type: id
        number: '1'
  round12:
    HOME_AN:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id3
        count: '5'
        type: id
        number: '1'
    Lobby:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id8
        count: '10'
        type: id
        number: '1'
    Lobby_SM:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc13
        count: '5'
        type: id
        number: '1'
    HOME:
      刷怪逻辑3:
        monsterType: entity
        monsterId: idc4
        count: '20'
        type: id
        number: '1'
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id21
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    Lobby_KM:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc15
        count: '5'
        type: id
        number: '1'
    Lobby_ZL:
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn3
        count: '5'
        type: id
        number: '1'
    HOME_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id7
        count: '5'
        type: id
        number: '1'
  round5:
    HOME_AN:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id18
        count: '1'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc5
        count: '5'
        type: id
        number: '1'
    HOME:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id7
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id11
        count: '5'
        type: id
        number: '1'
    HOME_KM:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
  round4:
    HOME_AN:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    HOME:
      刷怪逻辑2:
        monsterType: entity
        monsterId: idc2
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc1
        count: '5'
        type: id
        number: '1'
    HOME_KM:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id3
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id6
        count: '5'
        type: id
        number: '1'
  round3:
    HOME_AN:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id4
        count: '5'
        type: id
        number: '1'
    HOME:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    HOME_KM:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id5
        count: '5'
        type: id
        number: '1'
  round2:
    HOME_AN:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id2
        count: '10'
        type: id
        number: '2'
    HOME:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    HOME_KM:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc14
        count: '5'
        type: idc
        number: '14'
windows:
  WINDOWNS1:
    world: testWorld
    minX: -5.0
    minY: -60.0
    minZ: 31.0
    maxX: -5.0
    maxY: -58.0
    maxZ: 33.0
  WINDOWNS2:
    world: testWorld
    minX: 12.0
    minY: -60.0
    minZ: 27.0
    maxX: 15.0
    maxY: -58.0
    maxZ: 27.0
  WINDOWNS3:
    world: testWorld
    minX: 28.0
    minY: -60.0
    minZ: 27.0
    maxX: 30.0
    maxY: -58.0
    maxZ: 27.0
  WINDOWNS4:
    world: testWorld
    minX: 36.0
    minY: -60.0
    minZ: 59.0
    maxX: 36.0
    maxY: -58.0
    maxZ: 61.0
  WINDOWNS5:
    world: testWorld
    minX: -5.0
    minY: -60.0
    minZ: 49.0
    maxX: -5.0
    maxY: -58.0
    maxZ: 51.0
  WINDOWNS6:
    world: testWorld
    minX: -5.0
    minY: -52.0
    minZ: 49.0
    maxX: -5.0
    maxY: -50.0
    maxZ: 51.0
  WINDOWNS7:
    world: testWorld
    minX: -5.0
    minY: -60.0
    minZ: 84.0
    maxX: -5.0
    maxY: -58.0
    maxZ: 86.0
  WINDOWNS8:
    world: testWorld
    minX: 20.0
    minY: -60.0
    minZ: 65.0
    maxX: 22.0
    maxY: -58.0
    maxZ: 65.0
  WINDOWNS9:
    world: testWorld
    minX: 37.0
    minY: -61.0
    minZ: 76.0
    maxX: 37.0
    maxY: -56.0
    maxZ: 81.0
  WINDOWNS10:
    world: testWorld
    minX: -5.0
    minY: -60.0
    minZ: 102.0
    maxX: -5.0
    maxY: -58.0
    maxZ: 104.0
enabled: true
initialEquipment:
  '1': id70
  slot_type_1: item_slot
  '2': id5
  '3': id4
  slot_type_3: weapon_slot
  '4': id6
  slot_type_4: weapon_slot
  '5': id5
  '9': id69
  slot_type_9: locked_slot
  '10': id69
  slot_type_10: locked_slot
  '11': id69
  slot_type_11: locked_slot
  '12': id69
  slot_type_12: locked_slot
  '13': id69
  slot_type_13: locked_slot
  '14': id69
  slot_type_14: locked_slot
  '15': id69
  slot_type_15: locked_slot
  '16': id69
  slot_type_16: locked_slot
  '17': id69
  slot_type_17: locked_slot
  '18': id69
  slot_type_18: locked_slot
  '19': id69
  slot_type_19: locked_slot
  '20': id69
  slot_type_20: locked_slot
  '21': id69
  slot_type_21: locked_slot
  '22': id69
  slot_type_22: locked_slot
  '23': id69
  slot_type_23: locked_slot
  '24': id69
  slot_type_24: locked_slot
  '25': id69
  slot_type_25: locked_slot
  '26': id69
  slot_type_26: locked_slot
  '27': id69
  slot_type_27: locked_slot
  '28': id69
  slot_type_28: locked_slot
  '29': id69
  slot_type_29: locked_slot
  '30': id69
  slot_type_30: locked_slot
  '31': id69
  slot_type_31: locked_slot
  '32': id69
  slot_type_32: locked_slot
  '33': id69
  slot_type_33: locked_slot
  '34': id69
  slot_type_34: locked_slot
  '35': id69
  slot_type_35: locked_slot
  '36': id69
  slot_type_36: locked_slot
armorEquipment:
  upper: id27
  lower: id28





