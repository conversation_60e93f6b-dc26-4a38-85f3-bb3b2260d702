name: <PERSON><PERSON><PERSON><PERSON>实验室
maxPlayers: 4
spawn:
  world: world
  x: 539.2135460664232
  y: 39.0
  z: 420.3812432450655
  yaw: 3.2995605
  pitch: 9.750051
rounds: 20
zombieCount:
  round1: 15
  round2: 20
  round3: 25
  round4: 30
  round5: 25
  round6: 30
  round7: 35
  round8: 45
  round9: 50
  round10: 40
  round11: 50
  round12: 55
  round13: 60
  round14: 65
  round15: 65
  round16: 70
  round17: 75
  round18: 85
  round19: 95
  round20: 100
windows:
  飞机窗户:
    world: world
    minX: 532.0
    minY: 41.0
    minZ: 423.0
    maxX: 532.0
    maxY: 43.0
    maxZ: 424.0
  实验室大厅窗户:
    world: world
    minX: 517.0
    minY: 40.0
    minZ: 459.0
    maxX: 517.0
    maxY: 42.0
    maxZ: 461.0
  仓库靠门窗户:
    world: world
    minX: 580.0
    minY: 37.0
    minZ: 443.0
    maxX: 582.0
    maxY: 40.0
    maxZ: 443.0
  仓库楼梯二窗户:
    world: world
    minX: 614.0
    minY: 37.0
    minZ: 431.0
    maxX: 614.0
    maxY: 39.0
    maxZ: 433.0
  仓库楼梯一窗户:
    world: world
    minX: 614.0
    minY: 37.0
    minZ: 413.0
    maxX: 614.0
    maxY: 39.0
    maxZ: 415.0
  仓库靠楼梯窗户:
    world: world
    minX: 579.0
    minY: 37.0
    minZ: 398.0
    maxX: 581.0
    maxY: 39.0
    maxZ: 398.0
  生物仓库靠楼梯一窗户:
    world: world
    minX: 594.0
    minY: 47.0
    minZ: 399.0
    maxX: 596.0
    maxY: 50.0
    maxZ: 399.0
  生物仓库靠楼梯二窗户:
    world: world
    minX: 578.0
    minY: 48.0
    minZ: 399.0
    maxX: 580.0
    maxY: 50.0
    maxZ: 399.0
  仓库最大窗户:
    world: world
    minX: 561.0
    minY: 38.0
    minZ: 418.0
    maxX: 561.0
    maxY: 40.0
    maxZ: 423.0
  前实验室78窗户:
    world: world
    minX: 531.0
    minY: 47.0
    minZ: 506.0
    maxX: 531.0
    maxY: 49.0
    maxZ: 507.0
  前实验室76窗户:
    world: world
    minX: 542.0
    minY: 47.0
    minZ: 507.0
    maxX: 542.0
    maxY: 49.0
    maxZ: 508.0
  前实验室77窗户:
    world: world
    minX: 542.0
    minY: 47.0
    minZ: 520.0
    maxX: 542.0
    maxY: 49.0
    maxZ: 521.0
  后实验室窗户:
    world: world
    minX: 526.0
    minY: 48.0
    minZ: 541.0
    maxX: 526.0
    maxY: 50.0
    maxZ: 544.0
  小实验室窗户:
    world: world
    minX: 561.0
    minY: 39.0
    minZ: 479.0
    maxX: 562.0
    maxY: 41.0
    maxZ: 480.0
  实验室大厅靠仓库窗户:
    world: world
    minX: 557.0
    minY: 40.0
    minZ: 466.0
    maxX: 557.0
    maxY: 43.0
    maxZ: 468.0
zombieSpawns:
  飞机残骸:
    type: id
    world: world
    x: 530.8678173780381
    y: 41.0
    z: 424.1598847176124
    enabled: true
  实验室大厅:
    type: id
    world: world
    x: 514.2792519425684
    y: 40.0
    z: 460.4501179450125
    enabled: true
  实验室大厅靠仓库:
    type: id
    world: world
    x: 559.9961382780198
    y: 40.0
    z: 467.3868694929536
    enabled: true
  小实验室:
    type: id
    world: world
    x: 564.1632696167411
    y: 39.0
    z: 478.1584300678716
    enabled: false
  仓库最大窗户:
    type: id
    world: world
    x: 556.4242083071468
    y: 38.0
    z: 421.2848622712393
    enabled: false
  仓库最大窗户旁边:
    type: id
    world: world
    x: 580.616848409535
    y: 37.0
    z: 396.54209462720047
    enabled: false
  靠窗口大门仓库:
    type: id
    world: world
    x: 581.401299318319
    y: 37.0
    z: 446.9701053233811
    enabled: false
  靠生物仓库远:
    type: id
    world: world
    x: 617.7850951996974
    y: 37.0
    z: 432.6908218383663
    enabled: false
  靠生物仓库近:
    type: id
    world: world
    x: 617.4188572510287
    y: 37.0
    z: 414.56871673289675
    enabled: false
  实验体76:
    type: id
    world: world
    x: 545.0840092080412
    y: 47.0
    z: 507.9335259314657
    enabled: false
  实验体78:
    type: id
    world: world
    x: 529.6642521065253
    y: 47.0
    z: 507.04089726931113
    enabled: false
  实验体77:
    type: id
    world: world
    x: 544.4826377052134
    y: 47.0
    z: 520.9990386241976
    enabled: false
  后实验室:
    type: id
    world: world
    x: 523.9133886550699
    y: 48.0
    z: 543.2264718860408
    enabled: false
  生物仓库远:
    type: id
    world: world
    x: 579.7227310218624
    y: 48.0
    z: 397.8032396219555
    enabled: false
  生物仓库近2:
    type: id
    world: world
    x: 595.6861109761901
    y: 47.0
    z: 396.9176668861503
    enabled: false
  天台:
    type: id
    world: world
    x: 531.3888249200867
    y: 61.0
    z: 533.8895489989752
    enabled: false
doors:
  仓库大门:
    locked: true
    unlockPrice: 1000
    region:
      world: world
      minX: 561.0
      minY: 36.0
      minZ: 439.0
      maxX: 561.0
      maxY: 40.0
      maxZ: 442.0
    linkedSpawns:
    - 仓库最大窗户旁边
    InGameOpening: false
  生物仓库大门:
    locked: true
    unlockPrice: 1500
    region:
      world: world
      minX: 607.0
      minY: 40.0
      minZ: 400.0
      maxX: 607.0
      maxY: 45.0
      maxZ: 403.0
    InGameOpening: false
  武器仓库大门:
    locked: true
    unlockPrice: 4500
    region:
      world: world
      minX: 573.0
      minY: 46.0
      minZ: 422.0
      maxX: 573.0
      maxY: 52.0
      maxZ: 442.0
    InGameOpening: false
  前实验室大门:
    locked: true
    unlockPrice: 3500
    region:
      world: world
      minX: 534.0
      minY: 39.0
      minZ: 486.0
      maxX: 539.0
      maxY: 43.0
      maxZ: 486.0
    linkedSpawns:
    - 实验体76
    InGameOpening: false
  后实验室大门:
    locked: true
    unlockPrice: 8500
    region:
      world: world
      minX: 532.0
      minY: 47.0
      minZ: 529.0
      maxX: 536.0
      maxY: 49.0
      maxZ: 529.0
    linkedSpawns:
    - 后实验室
    InGameOpening: false
  配电室:
    locked: true
    unlockPrice: 8500
    region:
      world: world
      minX: 525.0
      minY: 47.0
      minZ: 556.0
      maxX: 525.0
      maxY: 49.0
      maxZ: 560.0
    InGameOpening: false
  天台:
    locked: true
    unlockPrice: 750
    region:
      world: world
      minX: 529.0
      minY: 60.0
      minZ: 560.0
      maxX: 533.0
      maxY: 60.0
      maxZ: 560.0
    linkedSpawns:
    - 天台
    InGameOpening: false
  小实验室:
    locked: true
    unlockPrice: 1750
    region:
      world: world
      minX: 556.0
      minY: 39.0
      minZ: 484.0
      maxX: 556.0
      maxY: 41.0
      maxZ: 485.0
    linkedSpawns:
    - 小实验室
    InGameOpening: false
  实验室大厅大门:
    locked: false
    region:
      world: world
      minX: 538.0
      minY: 39.0
      minZ: 436.0
      maxX: 539.0
      maxY: 41.0
      maxZ: 436.0
    linkedSpawns:
    - 实验室大厅
    InGameOpening: true
  飞机大门:
    locked: false
    region:
      world: world
      minX: 538.0
      minY: 39.0
      minZ: 426.0
      maxX: 539.0
      maxY: 41.0
      maxZ: 426.0
    linkedSpawns:
    - 飞机残骸
    InGameOpening: true
  实验室桥大门:
    locked: false
    region:
      world: world
      minX: 540.0
      minY: 47.0
      minZ: 466.0
      maxX: 540.0
      maxY: 50.0
      maxZ: 468.0
    linkedSpawns:
    - 实验室大厅靠仓库
    InGameOpening: true
roundModes:
  round17:
    仓库最大窗户:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id18
        count: '4'
        type: id
        number: '18'
    实验体77:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc1
        count: '20'
        type: idc
        number: c1
    实验体76:
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn2
        count: '15'
        type: idn
        number: n2
    飞机残骸:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id10
        count: '15'
        type: id
        number: '10'
    后实验室:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id22
        count: '1'
        type: id
        number: '22'
    靠窗口大门仓库:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id11
        count: '10'
        type: id
        number: '11'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id17
        count: '10'
        type: id
        number: '17'
  round18:
    仓库最大窗户:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id15
        count: '15'
        type: id
        number: '15'
    实验体78:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc6
        count: '10'
        type: idc
        number: c6
    实验体77:
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn2
        count: '5'
        type: idn
        number: n2
    实验体76:
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn5
        count: '5'
        type: idn
        number: n5
    生物仓库近2:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id18
        count: '2'
        type: id
        number: '18'
    小实验室:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc3
        count: '10'
        type: idc
        number: c3
    飞机残骸:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id12
        count: '5'
        type: id
        number: '12'
    后实验室:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id23
        count: '1'
        type: id
        number: '23'
    靠生物仓库近:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc5
        count: '10'
        type: idc
        number: c5
    生物仓库远:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id18
        count: '2'
        type: id
        number: '18'
    靠窗口大门仓库:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc1
        count: '20'
        type: idc
        number: c1
  round19:
    实验体77:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc6
        count: '15'
        type: idc
        number: c6
    实验体76:
      刷怪逻辑2:
        monsterType: entity
        monsterId: idc4
        count: '10'
        type: idc
        number: c4
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn5
        count: '5'
        type: idn
        number: n5
    生物仓库近2:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id19
        count: '4'
        type: id
        number: '19'
    小实验室:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id13
        count: '30'
        type: id
        number: '13'
    实验室大厅:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id21
        count: '10'
        type: id
        number: '21'
    后实验室:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id24
        count: '1'
        type: id
        number: '24'
    生物仓库远:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id18
        count: '5'
        type: id
        number: '18'
    实验室大厅靠仓库:
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn2
        count: '10'
        type: idn
        number: n2
    靠窗口大门仓库:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id17
        count: '15'
        type: id
        number: '17'
  round13:
    仓库最大窗户:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id7
        count: '5'
        type: id
        number: '7'
    实验体78:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id10
        count: '5'
        type: id
        number: '10'
    实验体77:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id20
        count: '5'
        type: id
        number: '20'
    实验体76:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc4
        count: '5'
        type: idc
        number: c4
    小实验室:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc5
        count: '5'
        type: idc
        number: c5
    实验室大厅:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc2
        count: '5'
        type: idc
        number: c2
    仓库最大窗户旁边:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id12
        count: '5'
        type: id
        number: '12'
    飞机残骸:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc1
        count: '5'
        type: idc
        number: c1
    靠生物仓库近:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id15
        count: '5'
        type: id
        number: '15'
    实验室大厅靠仓库:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc3
        count: '5'
        type: idc
        number: c3
    靠生物仓库远:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id16
        count: '5'
        type: id
        number: '16'
    靠窗口大门仓库:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id13
        count: '5'
        type: id
        number: '13'
  round14:
    实验体78:
      刷怪逻辑2:
        monsterType: entity
        monsterId: idc1
        count: '20'
        type: idc
        number: c1
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc5
        count: '10'
        type: idc
        number: c5
    实验体77:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc3
        count: '15'
        type: idc
        number: c3
    实验体76:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id21
        count: '20'
        type: id
        number: '21'
  round15:
    仓库最大窗户:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id16
        count: '10'
        type: id
        number: '16'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id17
        count: '20'
        type: id
        number: '17'
    实验体78:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc1
        count: '11'
        type: idc
        number: c1
    实验体77:
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn2
        count: '5'
        type: idn
        number: n2
    实验体76:
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn1
        count: '10'
        type: idn
        number: n1
    小实验室:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc4
        count: '5'
        type: idc
        number: c4
    后实验室:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id19
        count: '2'
        type: id
        number: '19'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id18
        count: '2'
        type: id
        number: '18'
  round16:
    仓库最大窗户:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id15
        count: '14'
        type: id
        number: '15'
    实验体78:
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn3
        count: '10'
        type: idn
        number: n3
    实验体77:
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn4
        count: '10'
        type: idn
        number: n4
    小实验室:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id10
        count: '10'
        type: id
        number: '10'
    飞机残骸:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id13
        count: '5'
        type: id
        number: '13'
    后实验室:
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn5
        count: '1'
        type: idn
        number: n5
    实验室大厅靠仓库:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc1
        count: '20'
        type: idc
        number: c1
  round9:
    仓库最大窗户:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id6
        count: '5'
        type: id
        number: '6'
    小实验室:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id9
        count: '10'
        type: id
        number: '9'
    实验室大厅:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id7
        count: '5'
        type: id
        number: '7'
    仓库最大窗户旁边:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id8
        count: '10'
        type: id
        number: '8'
    飞机残骸:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id6
        count: '5'
        type: id
        number: '6'
    靠生物仓库近:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id10
        count: '5'
        type: id
        number: '10'
    实验室大厅靠仓库:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id11
        count: '5'
        type: id
        number: '11'
    靠生物仓库远:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id10
        count: '5'
        type: id
        number: '10'
  round8:
    仓库最大窗户:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id2
        count: '5'
        type: id
        number: '2'
    小实验室:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id7
        count: '5'
        type: id
        number: '7'
    实验室大厅:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id7
        count: '5'
        type: id
        number: '7'
    飞机残骸:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    靠生物仓库近:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id4
        count: '5'
        type: id
        number: '4'
    实验室大厅靠仓库:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id5
        count: '20'
        type: id
        number: '5'
  round7:
    仓库最大窗户:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id5
        count: '5'
        type: id
        number: '5'
    小实验室:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id7
        count: '5'
        type: id
        number: '7'
    仓库最大窗户旁边:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id5
        count: '5'
        type: id
        number: '5'
    靠生物仓库近:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id5
        count: '5'
        type: id
        number: '5'
    靠生物仓库远:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id5
        count: '5'
        type: id
        number: '5'
    靠窗口大门仓库:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id5
        count: '5'
        type: id
        number: '5'
  round6:
    小实验室:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id5
        count: '5'
        type: id
        number: '5'
    实验室大厅:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id5
        count: '5'
        type: id
        number: '5'
    飞机残骸:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id6
        count: '5'
        type: id
        number: '6'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id4
        count: '5'
        type: id
        number: '4'
    实验室大厅靠仓库:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id5
        count: '10'
        type: id
        number: '5'
  round1:
    实验室大厅:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    飞机残骸:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id2
        count: '5'
        type: id
        number: '2'
    实验室大厅靠仓库:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id3
        count: '5'
        type: id
        number: '3'
  round20:
    生物仓库近2:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id18
        count: '5'
        type: id
        number: '18'
    实验室大厅:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id2
        count: '5'
        type: id
        number: '2'
    飞机残骸:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    后实验室:
      刷怪逻辑2:
        monsterType: npc
        monsterId: idn2
        count: '5'
        type: idn
        number: n2
      刷怪逻辑1:
        monsterType: npc
        monsterId: idn5
        count: '10'
        type: idn
        number: n5
    天台:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id13
        count: '5'
        type: id
        number: '13'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id25
        count: '2'
        type: id
        number: '25'
    实验室大厅靠仓库:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id4
        count: '1'
        type: id
        number: '4'
    靠生物仓库远:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id11
        count: '5'
        type: id
        number: '11'
    仓库最大窗户:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc6
        count: '15'
        type: idc
        number: c6
    实验体78:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id19
        count: '2'
        type: id
        number: '19'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id23
        count: '2'
        type: id
        number: '23'
    实验体77:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id14
        count: '5'
        type: id
        number: '14'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id24
        count: '2'
        type: id
        number: '24'
    实验体76:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id22
        count: '2'
        type: id
        number: '22'
    仓库最大窗户旁边:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id7
        count: '5'
        type: id
        number: '7'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id5
        count: '4'
        type: id
        number: '5'
    靠生物仓库近:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id12
        count: '5'
        type: id
        number: '12'
    生物仓库远:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id18
        count: '5'
        type: id
        number: '18'
    靠窗口大门仓库:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id10
        count: '10'
        type: id
        number: '10'
  round10:
    仓库最大窗户:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id18
        count: '2'
        type: id
        number: '18'
    小实验室:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id12
        count: '8'
        type: id
        number: '12'
    实验室大厅:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id10
        count: '10'
        type: id
        number: '10'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id13
        count: '5'
        type: id
        number: '13'
    靠生物仓库近:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id8
        count: '5'
        type: id
        number: '8'
    实验室大厅靠仓库:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id14
        count: '5'
        type: id
        number: '14'
    靠生物仓库远:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id5
        count: '5'
        type: id
        number: '5'
  round11:
    仓库最大窗户:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id13
        count: '5'
        type: id
        number: '13'
    实验体78:
      刷怪逻辑3:
        monsterType: entity
        monsterId: idc1
        count: '5'
        type: idc
        number: c1
      刷怪逻辑2:
        monsterType: entity
        monsterId: idc2
        count: '5'
        type: idc
        number: c2
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id11
        count: '5'
        type: id
        number: '11'
    实验体77:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id14
        count: '5'
        type: id
        number: '14'
    实验体76:
      刷怪逻辑2:
        monsterType: entity
        monsterId: idc1
        count: '5'
        type: idc
        number: c1
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id10
        count: '5'
        type: id
        number: '10'
    飞机残骸:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id8
        count: '10'
        type: id
        number: '8'
  round12:
    实验体78:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc5
        count: '10'
        type: idc
        number: c5
    实验体77:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc1
        count: '10'
        type: idc
        number: c1
    实验体76:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc3
        count: '10'
        type: idc
        number: c3
    小实验室:
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc2
        count: '10'
        type: idc
        number: c2
    仓库最大窗户旁边:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id15
        count: '5'
        type: id
        number: '15'
    靠生物仓库远:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id10
        count: '10'
        type: id
        number: '10'
  round5:
    实验室大厅:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id16
        count: '1'
        type: id
        number: '16'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    飞机残骸:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id2
        count: '4'
        type: id
        number: '2'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id3
        count: '5'
        type: id
        number: '3'
    实验室大厅靠仓库:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id4
        count: '10'
        type: id
        number: '4'
  round4:
    实验室大厅:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id4
        count: '5'
        type: id
        number: '4'
    飞机残骸:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id4
        count: '5'
        type: id
        number: '4'
    实验室大厅靠仓库:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id2
        count: '10'
        type: id
        number: '2'
  round3:
    实验室大厅:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id2
        count: '8'
        type: id
        number: '2'
    飞机残骸:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    实验室大厅靠仓库:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id3
        count: '10'
        type: id
        number: '3'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id4
        count: '2'
        type: id
        number: '4'
  round2:
    实验室大厅:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    飞机残骸:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id2
        count: '5'
        type: id
        number: '2'
    实验室大厅靠仓库:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id3
        count: '10'
        type: id
        number: '3'
minPlayers: 1
buyPoints:
  ce30305d-a2a4-4ee5-af14-2865b74ec417:
    type: wp
    itemId: id4
    name: 重型机枪
    location:
      world: world
      x: 528.578626616408
      y: 48.0
      z: 455.6969093375038
      yaw: 178.9476
      pitch: 8.10002
  4be7c029-48dc-4aca-a0fa-766089f49c10:
    type: ar
    itemId: id1
    name: 皮革套装(上)
    location:
      world: world
      x: 554.9093236436119
      y: 41.11766146085232
      z: 447.91309683138235
      yaw: 89.04492
      pitch: 0.3000155
  b1f880a2-136c-4192-baf1-ec2065eef8ac:
    type: ar
    itemId: id2
    name: 皮革套装(下)
    location:
      world: world
      x: 550.0883842207502
      y: 40.41419334927456
      z: 485.69621524038376
      yaw: -178.41211
      pitch: -2.9999754
  da07a0b5-649c-4846-bd05-54bc977d82b3:
    type: ar
    itemId: id3
    name: 锁链套装(上)
    location:
      world: world
      x: 563.8464265206895
      y: 36.628594544004045
      z: 400.3013622455683
      yaw: 0.8466797
      pitch: 4.9501057
  626f8f16-e207-401e-9137-2e767180c364:
    type: ar
    itemId: id4
    name: 锁链套装(下)
    location:
      world: world
      x: 612.6998261692378
      y: 37.41397940839495
      z: 422.0932257544886
      yaw: 91.90234
      pitch: 5.25011
  eb8af29a-f9fb-4299-a4a0-42aa322be52c:
    type: wp
    itemId: id2
    name: 制式步枪
    location:
      world: world
      x: 593.2589647457297
      y: 36.63111225178272
      z: 441.6991403318013
      yaw: -178.99316
      pitch: 1.5000917
  7af39ef7-a8a7-4b1a-aa52-2fa098efe69a:
    type: it
    itemId: id1
    name: 速度buff1药水
    location:
      world: world
      x: 585.6999999880791
      y: 37.00457643416105
      z: 420.8414895986799
      yaw: 92.51465
      pitch: -1.0499095
  f91e3260-a31a-4e2c-a59b-3cdd78510047:
    type: it
    itemId: id2
    name: 跳跃buff1药水
    location:
      world: world
      x: 535.7431089885483
      y: 39.0
      z: 446.38921508511993
      yaw: -179.44238
      pitch: -8.849931
  67025b1d-6c88-47e1-b9eb-c1136d9c40c5:
    type: sp
    itemId: id1
    name: 全体加速
    location:
      world: world
      x: 558.8922780213312
      y: 39.0
      z: 476.354519699558
      yaw: 2.2089844
      pitch: -6.749936
  94d568d2-ab35-464e-86a3-c38c87780afa:
    type: wp
    itemId: id3
    name: 霰弹枪
    location:
      world: world
      x: 563.6976278627828
      y: 40.74293160172488
      z: 483.9910266005355
      yaw: 92.81738
      pitch: -1.9499317
  12940f18-1473-4733-abfa-78ee50ee1816:
    type: wp
    itemId: id4
    name: 重型机枪
    location:
      world: world
      x: 536.8492966733369
      y: 47.45604663572167
      z: 512.5233929352796
      yaw: -176.44629
      pitch: -4.199982
  30f5e974-4b86-4fb0-b65e-c4ad1a74aa94:
    type: sp
    itemId: id2
    name: 全体回复(1)
    location:
      world: world
      x: 537.0859000087702
      y: 47.51205213032887
      z: 521.6999999880791
      yaw: 179.05078
      pitch: 4.9500074
  eae0a83c-39c3-44a0-8b06-dfc07e8cc2bb:
    type: wp
    itemId: id5
    name: 火箭筒
    location:
      world: world
      x: 541.6999999880791
      y: 47.521523471281384
      z: 526.3624993030736
      yaw: 89.35254
      pitch: 10.049993
  9e2b75e7-f1e8-4897-96e6-d948399a4d95:
    type: wp
    itemId: id6
    name: 电击枪
    location:
      world: world
      x: 536.6836078085727
      y: 47.66934564302298
      z: 541.7490673092738
      yaw: 93.109375
      pitch: -4.200045
  22c85559-d373-4ebc-a1cf-ebc91f15a3d2:
    type: wp
    itemId: id10
    name: 压强枪
    location:
      world: world
      x: 528.303119055926
      y: 47.50770959529176
      z: 533.9449110872146
      yaw: -88.25195
      pitch: 1.9499537
  abaa3f89-1028-4e19-bed3-191ebdcdab8f:
    type: it
    itemId: id3
    name: 手枪弹药
    location:
      world: world
      x: 520.5034305378177
      y: 48.0
      z: 558.5179331132092
      yaw: -90.21289
      pitch: 4.3499804
  06ee69de-ab59-44e1-bb89-fc3e051b7e33:
    type: sp
    itemId: id7
    name: 生命提升(2)
    location:
      world: world
      x: 522.5444644054078
      y: 47.56517469498785
      z: 560.6999999880791
      yaw: -178.26758
      pitch: 10.80002
  1bdd5e3b-1d3a-4542-9c20-5b021606324b:
    type: sp
    itemId: id9
    name: 生命提升(3)
    location:
      world: world
      x: 534.6474772826874
      y: 47.67435224178001
      z: 559.6999999880791
      yaw: -179.15625
      pitch: 1.3499988
  9d9383f9-7a8f-48d4-aa50-27ddd2df842c:
    type: sp
    itemId: id10
    name: 快速传送
    location:
      world: world
      x: 531.738384286743
      y: 62.42027340873196
      z: 531.0042487387971
      yaw: 2.7939453
      pitch: -2.100053
  8f051f11-3eab-4c36-b5d7-019e0a46deec:
    type: it
    itemId: id12
    name: 压强枪弹药
    location:
      world: world
      x: 526.3000000119209
      y: 62.00439378467278
      z: 541.47581269755
      yaw: -90.640625
      pitch: 5.849944
  79f77f1a-b08d-45fb-bcee-3ec3866d5dcc:
    type: wp
    itemId: id12
    name: 冲锋枪
    location:
      world: world
      x: 537.6999999880791
      y: 61.51383503477197
      z: 540.9695890718895
      yaw: 89.95801
      pitch: 4.64993
  e6d060ed-9ab2-46a1-a664-bc667e08a123:
    type: wp
    itemId: id11
    name: 手枪
    location:
      world: world
      x: 555.6336455390951
      y: 39.259606692298334
      z: 457.735475588486
      yaw: 89.384766
      pitch: -6.8999677
  a830055f-994d-4205-82fe-5b23a429161b:
    type: sp
    itemId: id1
    name: 全体加速
    location:
      world: world
      x: 548.3000000119209
      y: 39.0
      z: 476.0598277113965
      yaw: -103.20056
      pitch: -20.249945
enabled: true
power_button:
  world: world
  x: 519.0
  y: 49.0
  z: 558.0
  price: 1000
  unlocked: false
initialEquipment:
  '22': id69
  '23': id69
  '24': id69
  '25': id69
  '26': id69
  '27': id69
  '28': id69
  '29': id69
  '30': id69
  '31': id69
  '10': id69
  '32': id69
  '11': id69
  '33': id69
  '12': id69
  '34': id69
  '13': id69
  '35': id69
  '14': id69
  '36': id69
  '15': id69
  '16': id69
  '17': id69
  '18': id69
  '19': id69
  '1': id70
  '2': id1
  '3': id2
  '4': id67
  '5': id69
  '6': id69
  '7': id68
  '8': id68
  '9': id68
  '20': id69
  '21': id69
armorEquipment:
  upper: '25'
  lower: '26'
waitTime: 15
buffs:
  health+++: true
  KeepFood: true
  speed1: true
