name: VillageAttacking
maxPlayers: 16
spawn:
  world: CenterRoad
  x: 130.17792067303105
  y: -60.0
  z: 4.139323252850913
  yaw: 84.599976
  pitch: -9.4499655
waitTime: 10
windows:
  窗户:
    world: CenterRoad
    minX: 122.0
    minY: -59.0
    minZ: 6.0
    maxX: 122.0
    maxY: -56.0
    maxZ: 9.0
buffs:
  health+: true
zombieSpawns:
  门里僵尸:
    type: id
    world: CenterRoad
    x: 151.5129833234247
    y: -59.0
    z: 18.471726749127733
    enabled: false
  外僵尸:
    type: id
    world: CenterRoad
    x: 118.00975477753916
    y: -60.0
    z: 8.174158584176974
    enabled: true
rounds: 7
zombieCount:
  round1: 5
  round2: 7
  round3: 9
  round4: 11
  round5: 13
  round6: 20
  round7: 30
powerButton:
  enabled: false
buyPoints:
  7d034bbb-0f10-467e-9f0c-068a31da384f:
    type: sp
    itemId: id1
    name: 全体加速
    location:
      world: CenterRoad
      x: 120.42499594758696
      y: -60.0
      z: 9.340385177571756
      yaw: 80.70038
      pitch: 5.099953
enabled: true
armorEquipment:
  upper: id35
  lower: id36
power_button:
  world: CenterRoad
  x: 127.0
  y: -58.0
  z: 1.0
  price: 500
  unlocked: false
doors:
  解锁门:
    locked: false
    region:
      world: CenterRoad
      minX: 125.0
      minY: -59.0
      minZ: 6.0
      maxX: 125.0
      maxY: -56.0
      maxZ: 9.0
    linkedSpawns:
    - 外僵尸
    InGameOpening: true
  不解锁门:
    locked: true
    unlockPrice: 200
    region:
      world: CenterRoad
      minX: 150.0
      minY: -59.0
      minZ: 15.0
      maxX: 152.0
      maxY: -57.0
      maxZ: 15.0
    linkedSpawns:
    - 门里僵尸
    InGameOpening: false
initialEquipment:
  '2': id2
  '3': id9
roundModes:
  round1:
    外僵尸:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    门里僵尸:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
  round5:
    外僵尸:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    门里僵尸:
      刷怪逻辑2:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
  round4:
    外僵尸:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    门里僵尸:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
  round3:
    外僵尸:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    门里僵尸:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
  round2:
    外僵尸:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
    门里僵尸:
      刷怪逻辑1:
        monsterType: zombie
        monsterId: id1
        count: '5'
        type: id
        number: '1'
  round6:
    外僵尸:
      刷怪逻辑4:
        monsterType: entity
        monsterId: idc6
        count: '5'
        type: id
        number: '1'
      刷怪逻辑3:
        monsterType: entity
        monsterId: idc5
        count: '5'
        type: id
        number: '1'
      刷怪逻辑2:
        monsterType: entity
        monsterId: idc4
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc3
        count: '5'
        type: id
        number: '1'
    门里僵尸:
      刷怪逻辑2:
        monsterType: entity
        monsterId: idc2
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc1
        count: '5'
        type: id
        number: '1'
  round7:
    外僵尸:
      刷怪逻辑3:
        monsterType: entity
        monsterId: idc22
        count: '1'
        type: id
        number: '1'
      刷怪逻辑2:
        monsterType: entity
        monsterId: idc18
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc8
        count: '5'
        type: id
        number: '1'
    门里僵尸:
      刷怪逻辑2:
        monsterType: entity
        monsterId: idc10
        count: '5'
        type: id
        number: '1'
      刷怪逻辑1:
        monsterType: entity
        monsterId: idc7
        count: '5'
        type: id
        number: '1'
