#
# WEPIF Configuration File
#
# This file handles permissions configuration for every plugin using WEPIF
#
# About editing this file:
# - DO NOT USE TABS. You MUST use spaces or <PERSON><PERSON><PERSON><PERSON> will complain. If
#   you use an editor like Notepad++ (recommended for Windows users), you
#   must configure it to "replace tabs with spaces." In Notepad++, this can
#   be changed in Settings > Preferences > Language Menu.
# - Don't get rid of the indents. They are indented so some entries are
#   in categories (like "enforce-single-session" is in the "protection"
#   category.
# - If you want to check the format of this file before putting it
#   into WEPIF, paste it into https://yaml-online-parser.appspot.com/
#   and see if it gives "ERROR:".
# - Lines starting with # are comments and so they are ignored.


ignore-nijiperms-bridges: true
resolvers:
    enabled:
    - PluginPermissionsResolver
    - PermissionsExResolver
    - bPermissionsResolver
    - GroupManagerResolver
    - NijiPermissionsResolver
    - VaultResolver
    - DinnerPermsResolver
    - FlatFilePermissionsResolver
permissions:
    groups:
        default:
            permissions:
            - worldedit.reload
            - worldedit.selection
            - worlds.creative.worldedit.region
        admins:
            permissions:
            - '*'
    users:
        sk89q:
            permissions:
            - worldedit
            groups:
            - admins
