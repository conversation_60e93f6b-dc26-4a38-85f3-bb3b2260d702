package org.Ver_zhzh.deathZombieV4.commands;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.UUID;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.enums.PlayerStatus;
import org.Ver_zhzh.deathZombieV4.game.GameManager;
import org.Ver_zhzh.deathZombieV4.game.GameSessionManager;
import org.Ver_zhzh.deathZombieV4.game.LuckyBoxManager;
import org.Ver_zhzh.deathZombieV4.utils.PlayerInteractionManager;
import org.Ver_zhzh.deathZombieV4.utils.Region;
import org.Ver_zhzh.deathZombieV4.utils.RegionSelector;
import org.Ver_zhzh.deathZombieV4.utils.ZombieHelper;
import org.Ver_zhzh.deathZombieV4.utils.LeaderboardManager;
import org.Ver_zhzh.deathZombieV4.utils.HologramLeaderboardManager;
import org.Ver_zhzh.deathZombieV4.web.WebServer;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.MemoryConfiguration;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.entity.Zombie;

public class CommandManager implements CommandExecutor, TabCompleter {

    private final DeathZombieV4 plugin;
    private final GameManager gameManager;
    private final RegionSelector regionSelector;
    private WebServer webServer;

    private static final List<String> COMMANDS = Arrays.asList(
            "buy", "enable", "join", "jump", "stop", "help", "remove", "reload", "kitgui", "kit",
            "create", "game", "setminplayer", "test", "setspawn", "setmaxplayers", "edit",
            "setwindows", "setround", "setcount", "check", "setdoor", "unlock", "web",
            "setzombiespawn", "with", "setmode", "cleannpc", "SetWaitTime", "leave", "buff",
            "selectnpc", "Lobby", "lucky", "cleanup", "clean", "displaySettings", "power", "nextRound",
            "testmoney", "rejoin", "rej", "top", "topHolo"
    );

    private static final List<String> ITEMS = Arrays.asList(
            "zombie", "skeleton", "creeper"
    );

    public CommandManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.gameManager = plugin.getGameManager();
        this.regionSelector = plugin.getRegionSelector();
        this.webServer = new WebServer(plugin);
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // 检查是否是web命令，web命令允许非玩家使用
        if (args.length > 0 && args[0].equalsIgnoreCase("web")) {
            return handleWebCommandForAnyUser(sender, args);
        }

        // 检查是否是clean命令，clean命令允许终端执行
        if (args.length > 0 && args[0].equalsIgnoreCase("clean")) {
            return handleCleanCommandForAnyUser(sender, args);
        }

        // 其他命令需要玩家身份
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "只有玩家可以使用此命令！");
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            sendHelp(player, 1);
            return true;
        }

        String subCommand = args[0].toLowerCase();

        // 检查权限 - displaysettings、join、leave、rejoin、rej、top命令允许普通玩家使用
        // topHolo命令需要管理员权限，在具体处理时检查
        if (!subCommand.equals("displaysettings") && !subCommand.equals("join") && !subCommand.equals("leave")
                && !subCommand.equals("rejoin") && !subCommand.equals("rej") && !subCommand.equals("top")
                && !subCommand.equals("topholo") && !player.hasPermission("deathzombie.admin")) {
            player.sendMessage(ChatColor.RED + "你没有权限使用此命令！");
            return true;
        }

        switch (subCommand) {
            case "game":
                if (args.length < 4) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs game <游戏名称> <spawn|with|setcount> <回合数/门名称> [其他参数]");
                    return true;
                }
                return handleGameCommand(player, args);
            case "create":
                if (args.length < 2) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs create <游戏名>");
                    return true;
                }
                return handleCreateCommand(player, args[1]);
            case "jump":
                if (args.length < 2) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs jump <游戏名>");
                    return true;
                }
                // 检查管理员权限
                if (!player.hasPermission("deathzombiev4.admin")) {
                    player.sendMessage(ChatColor.RED + "你需要管理员权限来使用此指令！");
                    return true;
                }
                return handleJumpCommand(player, args[1]);
            case "stop":
                if (args.length < 2) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs stop <游戏名>");
                    return true;
                }
                // 检查管理员权限
                if (!player.hasPermission("deathzombiev4.admin")) {
                    player.sendMessage(ChatColor.RED + "你需要管理员权限来使用此指令！");
                    return true;
                }
                return handleStopCommand(player, args[1]);
            case "setminplayer":
                if (args.length < 3) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs setminplayer <游戏名> <最小玩家数>");
                    return true;
                }
                try {
                    int minPlayers = Integer.parseInt(args[2]);
                    return handleSetMinPlayersCommand(player, args[1], minPlayers);
                } catch (NumberFormatException e) {
                    player.sendMessage(ChatColor.RED + "最小玩家数必须是一个有效的整数！");
                    return true;
                }
            case "test":
                if (args.length < 3) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs test <游戏名称> <回合数> [怪物出生点名称]");
                    return true;
                }
                try {
                    String gameName = args[1];
                    int round = Integer.parseInt(args[2]);
                    String spawnName = args.length > 3 ? args[3] : null;
                    return handleTestCommand(player, gameName, round, spawnName);
                } catch (NumberFormatException e) {
                    player.sendMessage(ChatColor.RED + "回合数必须是一个有效的整数！");
                    return true;
                }
            case "setspawn":
                if (args.length < 2) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs setspawn <游戏名>");
                    return true;
                }
                return handleSetSpawnCommand(player, args[1]);
            case "setmaxplayers":
                if (args.length < 3) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs setMaxPlayers <游戏名> <最大玩家数>");
                    return true;
                }
                try {
                    int maxPlayers = Integer.parseInt(args[2]);
                    return handleSetMaxPlayersCommand(player, args[1], maxPlayers);
                } catch (NumberFormatException e) {
                    player.sendMessage(ChatColor.RED + "最大玩家数必须是一个整数！");
                    return true;
                }
            case "edit":
                if (args.length < 2) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs edit <游戏名>");
                    return true;
                }
                return handleEditCommand(player, args[1]);
            case "setwindows":
                if (args.length < 3) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs setWindows <游戏名> <窗户名称>");
                    return true;
                }
                return handleSetWindowsCommand(player, args[1], args[2]);
            case "setround":
                if (args.length < 3) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs setround <游戏名> <回合数>");
                    return true;
                }
                try {
                    int rounds = Integer.parseInt(args[2]);
                    return handleSetRoundCommand(player, rounds);
                } catch (NumberFormatException e) {
                    player.sendMessage(ChatColor.RED + "回合数必须是一个整数！");
                    return true;
                }
            case "setcount":
                if (args.length < 4) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs setCount <游戏名> <回合> <僵尸数量>");
                    return true;
                }
                try {
                    int round = Integer.parseInt(args[2]);
                    int count = Integer.parseInt(args[3]);
                    return handleSetCountCommand(player, round, count);
                } catch (NumberFormatException e) {
                    player.sendMessage(ChatColor.RED + "回合和僵尸数量必须是整数！");
                    return true;
                }
            case "check":
                if (args.length < 2) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs check <游戏名>");
                    return true;
                }
                return handleCheckCommand(player, args[1]);
            case "enable":
                if (args.length < 2) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs enable <游戏名>");
                    return true;
                }
                return handleEnableCommand(player, args[1]);
            case "join":
                // join命令允许玩家加入游戏，不需要管理员权限
                // 移除权限检查，允许所有玩家加入游戏
                // 记录调试信息
                plugin.getLogger().info("玩家 " + player.getName() + " 尝试加入游戏，权限状态: admin="
                        + player.hasPermission("deathzombie.admin") + ", player="
                        + player.hasPermission("deathzombie.player"));

                String gameName = args.length > 1 ? args[1] : null;
                return handleJoinCommand(player, gameName);
            case "setdoor":
                if (args.length < 4) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs setdoor <游戏名> <门名称> <lock|unlock> [解锁价格]");
                    return true;
                }
                return handleSetDoorCommand(player, args);
            case "unlock":
                return handleUnlockCommand(player);
            case "remove":
                if (args.length < 3) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs remove <游戏名> <shop|door|windows>");
                    return true;
                }
                return handleRemoveCommand(player, args[1], args[2]);
            case "setzombiespawn":
                if (args.length < 3) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs setzombiespawn <游戏名> <生成点名称> [僵尸类型]");
                    return true;
                }
                String spawnName = args[2];
                String zombieType = args.length > 3 ? args[3] : "id"; // 默认使用id类型
                return handleSetZombieSpawnCommand(player, spawnName, zombieType, args[1]);
            case "with":
                if (args.length < 4) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs with <游戏名称> <门名称> <怪物出生点名称>");
                    return true;
                }
                return handleWithCommand(player, args[1], args[2], args[3]);


            case "setwaittime":
                if (!player.hasPermission("deathzombie.setwaittime")) {
                    player.sendMessage(ChatColor.RED + "你没有权限使用此命令！");
                    return true;
                }
                if (args.length < 3) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs setwaittime <游戏名称> <时间(单位秒)>");
                    return true;
                }
                try {
                    int waitTime = Integer.parseInt(args[2]);
                    return handleSetWaitTimeCommand(player, args[1], waitTime);
                } catch (NumberFormatException e) {
                    player.sendMessage(ChatColor.RED + "等待时间必须是一个有效的整数！");
                    return true;
                }
            case "leave":
                return handleLeaveCommand(player);
            case "rejoin":
            case "rej":
                return handleRejoinCommand(player);
            case "buff":
                if (args.length < 4) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs buff <游戏名称> <add|remove> <buff的id>");
                    player.sendMessage(ChatColor.YELLOW + "可用的buff ID: speed1(速度1), speed2(速度2), KeepFood(饱食度), jump1(跳跃1)");
                    player.sendMessage(ChatColor.YELLOW + "生命相关: health1(生命恢复1), health2(生命恢复2), health3(生命恢复3)");
                    player.sendMessage(ChatColor.YELLOW + "额外生命: health+(额外生命1), health++(额外生命2), health+++(额外生命3)");
                    return true;
                }
                return handleBuffCommand(player, args[1], args[2], args[3]);
            case "buy":
                if (args.length < 3) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs buy <游戏名称> <物品类型|all> <物品名称> [解锁金额]");
                    player.sendMessage(ChatColor.YELLOW + "物品类型: 护甲, 武器, 道具, 特殊功能 (或 ar, wp, it, sp)");
                    player.sendMessage(ChatColor.YELLOW + "示例: /dzs buy 游戏1 武器 手枪");
                    player.sendMessage(ChatColor.YELLOW + "示例: /dzs buy 游戏1 all (设置全局电源按钮)");
                    player.sendMessage(ChatColor.GRAY + "兼容旧格式: /dzs buy <游戏名称> <ar_id|wp_id|it_id|sp_id> [解锁金额]");
                    return true;
                }

                try {
                    String buyGameName = args[1];

                    // 检查游戏是否存在
                    if (!gameManager.gameExists(buyGameName)) {
                        // 尝试查找游戏文件
                        File gameFile = new File(plugin.getDataFolder(), "game/" + buyGameName + ".yml");
                        if (gameFile.exists()) {
                            // 游戏文件存在，但没有加载，重新加载游戏
                            plugin.getLogger().info("游戏文件存在，但没有加载，正在重新加载: " + buyGameName);
                            plugin.getGameManager().loadAllGames();

                            // 再次检查游戏是否存在
                            if (!gameManager.gameExists(buyGameName)) {
                                player.sendMessage(ChatColor.RED + "游戏 " + ChatColor.YELLOW + buyGameName + ChatColor.RED + " 不存在！");
                                return true;
                            }
                        } else {
                            player.sendMessage(ChatColor.RED + "游戏 " + ChatColor.YELLOW + buyGameName + ChatColor.RED + " 不存在！");
                            return true;
                        }
                    }

                    // 支持新的中文格式和旧的ID格式
                    if (args.length >= 4 && !args[2].equalsIgnoreCase("all") && !args[2].contains("_")) {
                        // 新格式: /dzs buy <游戏名称> <物品类型> <物品名称> [解锁金额]
                        String itemType = args[2];
                        String itemName = args[3];
                        int price = args.length > 4 ? Integer.parseInt(args[4]) : 0;
                        return handleBuyCommandWithName(player, buyGameName, itemType, itemName, price);
                    } else {
                        // 兼容旧格式: /dzs buy <游戏名称> <all|ar_id|it_id|wp_id|sp_id> [解锁金额]
                        String typeAndId = args[2];
                        int price = args.length > 3 ? Integer.parseInt(args[3]) : 0;
                        return handleBuyCommand(player, buyGameName, typeAndId, price);
                    }
                } catch (NumberFormatException e) {
                    player.sendMessage(ChatColor.RED + "解锁金额必须是有效的整数！");
                    return true;
                }
            case "help":
                if (args.length > 1) {
                    try {
                        int page = Integer.parseInt(args[1]);
                        sendHelp(player, page);
                    } catch (NumberFormatException e) {
                        sendHelp(player, 1);
                    }
                } else {
                    sendHelp(player, 1);
                }
                return true;
            case "reload":
                // 检查管理员权限
                if (!player.hasPermission("deathzombiev4.admin")) {
                    player.sendMessage(ChatColor.RED + "你需要管理员权限来使用此指令！");
                    return true;
                }
                return handleReloadCommand(player);

            case "kit":
                // 检查管理员权限
                if (!player.hasPermission("deathzombiev4.admin")) {
                    player.sendMessage(ChatColor.RED + "你需要管理员权限来使用此指令！");
                    return true;
                }
                if (args.length < 4) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs kit <游戏名称> <槽位位置> <set|remove> [物品ID]");
                    player.sendMessage(ChatColor.YELLOW + "物品ID可以是gameKit.yml中定义的idx格式ID (id1-id69)");
                    player.sendMessage(ChatColor.YELLOW + "例如: /dzs kit 尖峰时刻 1 set id1 (设置槽位1为铁剑)");
                    player.sendMessage(ChatColor.YELLOW + "例如: /dzs kit 尖峰时刻 2 set id2 (设置槽位2为手枪)");
                    return true;
                }
                try {
                    String kitGameName = args[1];
                    int slot = Integer.parseInt(args[2]);
                    String action = args[3].toLowerCase();

                    // 根据不同的操作类型处理
                    if (action.equals("set")) {
                        if (args.length < 5) {
                            player.sendMessage(ChatColor.RED + "用法: /dzs kit <游戏名称> <槽位位置> set <物品ID>");
                            player.sendMessage(ChatColor.YELLOW + "物品ID可以是gameKit.yml中定义的idx格式ID (id1-id69)");
                            return true;
                        }
                        String itemId = args[4];
                        // 如果输入的是纯数字ID，自动添加"id"前缀
                        try {
                            int numId = Integer.parseInt(itemId);
                            // 根据槽位位置和ID范围判断物品类型前缀
                            String prefix = "id"; // 默认前缀

                            // 根据ID范围判断可能的物品类型
                            if (numId >= 1 && numId <= 24) {
                                // 武器类型 (id1-id24)
                                prefix = "id";
                            } else if (numId >= 25 && numId <= 37) {
                                // 护甲类型 (id25-id37)
                                prefix = "id";
                            } else if (numId >= 38 && numId <= 67) {
                                // 物品类型 (id38-id67)
                                prefix = "id";
                            } else if (numId >= 67 && numId <= 69) {
                                // 特殊物品 (id67-id69)
                                prefix = "id";
                            } else if (numId >= 1 && numId <= 11 && slot >= 6 && slot <= 8) {
                                // 如果是6-8号槽位（物品槽），且ID在1-11范围内，可能是特殊能力物品(sp)
                                prefix = "sp.id";
                                plugin.getLogger().info("检测到可能的特殊能力物品ID: " + numId + "，在物品槽位: " + slot);
                                player.sendMessage(ChatColor.YELLOW + "提示：您设置的ID " + numId + " 可能是特殊能力物品(sp)，如果是武器请忽略此消息");
                            }

                            itemId = prefix + numId;
                            plugin.getLogger().info("转换物品ID: " + args[4] + " -> " + itemId);
                        } catch (NumberFormatException e) {
                            // 不是纯数字ID，保持原样
                        }
                        return handleKitCommand(player, kitGameName, slot, "set", itemId, null);
                    } else if (action.equals("remove")) {
                        return handleKitCommand(player, kitGameName, slot, "remove", null, null);
                    } else {
                        player.sendMessage(ChatColor.RED + "无效的操作类型！必须是 set 或 remove");
                        return true;
                    }
                } catch (NumberFormatException e) {
                    player.sendMessage(ChatColor.RED + "槽位位置必须是一个有效的整数！");
                    return true;
                }
            case "kitgui":
                // 检查管理员权限
                if (!player.hasPermission("deathzombiev4.admin")) {
                    player.sendMessage(ChatColor.RED + "你需要管理员权限来使用此指令！");
                    return true;
                }
                if (args.length < 2) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs kitgui <游戏名称>");
                    return true;
                }
                return handleKitGUICommand(player, args[1]);
            case "selectnpc":
                // 检查管理员权限
                if (!player.hasPermission("deathzombiev4.admin")) {
                    player.sendMessage(ChatColor.RED + "你需要管理员权限来使用此指令！");
                    return true;
                }
                if (args.length < 2) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs selectnpc <NPC ID>");
                    return true;
                }
                try {
                    int npcId = Integer.parseInt(args[1]);
                    return handleSelectNPCCommand(player, npcId);
                } catch (NumberFormatException e) {
                    player.sendMessage(ChatColor.RED + "NPC ID必须是一个有效的整数！");
                    return true;
                }
            case "lobby":
                // 检查管理员权限
                if (!player.hasPermission("deathzombiev4.admin")) {
                    player.sendMessage(ChatColor.RED + "你需要管理员权限来使用此指令！");
                    return true;
                }
                return handleLobbyCommand(player);
            case "lucky":
                // 检查管理员权限
                if (!player.hasPermission("deathzombiev4.admin")) {
                    player.sendMessage(ChatColor.RED + "你需要管理员权限来使用此指令！");
                    return true;
                }
                if (args.length < 3) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs lucky <游戏名称> <Change|Add|Set|open|remove> <箱子名称> [参数A] [参数B]");
                    return true;
                }
                return handleLuckyCommand(player, args);
            case "cleanup":
                // cleanup命令需要管理员权限
                if (!player.hasPermission("deathzombiev4.admin")) {
                    player.sendMessage(ChatColor.RED + "你需要管理员权限来使用此指令！");
                    return true;
                }
                if (args.length < 2) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs cleanup <player|all> [玩家名称]");
                    player.sendMessage(ChatColor.YELLOW + "player - 清理指定玩家的幸运箱抽奖全息图");
                    player.sendMessage(ChatColor.YELLOW + "all - 清理所有玩家的幸运箱抽奖全息图");
                    return true;
                }
                return handleCleanupCommand(player, args);
            case "clean":
                // clean命令已在前面处理，这里不应该到达
                sender.sendMessage(ChatColor.RED + "内部错误：clean命令处理异常！");
                return true;
            case "displaysettings":
                if (args.length < 3) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs displaySettings <kill|Shoot|hit> <on|off>");
                    player.sendMessage(ChatColor.YELLOW + "kill - 控制击杀僵尸后显示金钱的title");
                    player.sendMessage(ChatColor.YELLOW + "Shoot - 控制射击时的子弹发射title提示");
                    player.sendMessage(ChatColor.YELLOW + "hit - 控制命中时的title提示");
                    return true;
                }
                return handleDisplaySettingsCommand(player, args[1], args[2]);
            case "power":
                if (args.length < 2) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs power <游戏名称> [解锁金额]");
                    player.sendMessage(ChatColor.YELLOW + "破坏一个方块来设置全局电源按钮位置");
                    player.sendMessage(ChatColor.YELLOW + "默认解锁金额: 1000 金币");
                    return true;
                }
                // 解析可选的解锁金额参数
                int unlockPrice = 1000; // 默认价格
                if (args.length >= 3) {
                    try {
                        unlockPrice = Integer.parseInt(args[2]);
                        if (unlockPrice < 0) {
                            player.sendMessage(ChatColor.RED + "解锁金额不能为负数！");
                            return true;
                        }
                    } catch (NumberFormatException e) {
                        player.sendMessage(ChatColor.RED + "解锁金额必须是有效的整数！");
                        return true;
                    }
                }
                return handlePowerCommand(player, args[1], unlockPrice);
            case "nextround":
                // nextRound命令需要管理员权限
                if (!player.hasPermission("deathzombiev4.admin")) {
                    player.sendMessage(ChatColor.RED + "你需要管理员权限来使用此指令！");
                    return true;
                }
                if (args.length < 2) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs nextRound <游戏名称>");
                    return true;
                }
                return handleNextRoundCommand(player, args[1]);
            case "top":
                // top命令允许普通玩家使用
                if (args.length < 2) {
                    showTopCommandHelp(player);
                    return true;
                }
                return handleTopCommand(player, args);
            case "topholo":
                // topHolo命令需要管理员权限
                if (!player.hasPermission("deathzombiev4.admin")) {
                    player.sendMessage(ChatColor.RED + "你需要管理员权限来使用此指令！");
                    return true;
                }
                if (args.length < 2) {
                    showTopHoloCommandHelp(player);
                    return true;
                }
                return handleTopHoloCommand(player, args);
            case "testmoney":
                // testmoney命令需要管理员权限
                if (!player.hasPermission("deathzombiev4.admin")) {
                    player.sendMessage(ChatColor.RED + "你需要管理员权限来使用此指令！");
                    return true;
                }
                if (args.length < 2) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs testmoney <shoot|kill|debug>");
                    player.sendMessage(ChatColor.YELLOW + "shoot - 测试射击命中金钱奖励");
                    player.sendMessage(ChatColor.YELLOW + "kill - 测试击杀金钱奖励");
                    player.sendMessage(ChatColor.YELLOW + "debug - 切换头部命中检测调试模式");
                    return true;
                }
                return handleTestMoneyCommand(player, args[1]);
            default:
                sendHelp(player, 1);
                return true;
        }
    }

    private boolean handleCreateCommand(Player player, String gameName) {
        if (gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 已经存在！");
            return true;
        }

        if (gameManager.createGame(gameName)) {
            player.sendMessage(ChatColor.GREEN + "成功创建游戏 '" + gameName + "'！");
        } else {
            player.sendMessage(ChatColor.RED + "创建游戏 '" + gameName + "' 失败！");
        }
        return true;
    }

    private boolean handleSetSpawnCommand(Player player, String gameName) {
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return true;
        }

        if (gameManager.setSpawnLocation(gameName, player.getLocation())) {
            player.sendMessage(ChatColor.GREEN + "成功设置游戏 '" + gameName + "' 的出生点！");
        } else {
            player.sendMessage(ChatColor.RED + "设置游戏 '" + gameName + "' 的出生点失败！");
        }
        return true;
    }

    private boolean handleSetMaxPlayersCommand(Player player, String gameName, int maxPlayers) {
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return true;
        }

        if (maxPlayers < 1) {
            player.sendMessage(ChatColor.RED + "最大玩家数必须大于0！");
            return true;
        }

        if (gameManager.setMaxPlayers(gameName, maxPlayers)) {
            player.sendMessage(ChatColor.GREEN + "成功设置游戏 '" + gameName + "' 的最大玩家数为 " + maxPlayers + "！");
        } else {
            player.sendMessage(ChatColor.RED + "设置游戏 '" + gameName + "' 的最大玩家数失败！");
        }
        return true;
    }

    private boolean handleEditCommand(Player player, String gameName) {
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return true;
        }

        regionSelector.giveSelectionWand(player, gameName);
        // 保存当前编辑的游戏名称
        plugin.getPlayerInteractionManager().setEditingGame(player, gameName);
        player.sendMessage(ChatColor.GREEN + "已获得区域选择棒，左键选择第一个点，右键选择第二个点。");
        return true;
    }

    private boolean handleSetWindowsCommand(Player player, String gameName, String windowName) {
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return true;
        }

        if (!regionSelector.hasSelection(player)) {
            player.sendMessage(ChatColor.RED + "请先使用区域选择棒选择一个区域！");
            return true;
        }

        Region region = regionSelector.getSelection(player);
        if (gameManager.addWindowsRegion(gameName, windowName, region)) {
            player.sendMessage(ChatColor.GREEN + "成功设置游戏 '" + gameName + "' 的窗户 '" + windowName + "' 区域！");
            regionSelector.clearSelection(player);

            // 注册窗户区域到WindowManager
            plugin.getWindowManager().registerWindowRegion(gameName, windowName, region);
        } else {
            player.sendMessage(ChatColor.RED + "设置游戏 '" + gameName + "' 的窗户 '" + windowName + "' 区域失败！");
        }
        return true;
    }

    private boolean handleSetRoundCommand(Player player, int rounds) {
        if (!gameManager.gameExists(player.getName())) {
            player.sendMessage(ChatColor.RED + "游戏 '" + player.getName() + "' 不存在！");
            return true;
        }

        if (rounds < 1) {
            player.sendMessage(ChatColor.RED + "回合数必须大于0！");
            return true;
        }

        if (gameManager.setRounds(player.getName(), rounds)) {
            player.sendMessage(ChatColor.GREEN + "成功设置游戏 '" + player.getName() + "' 的回合数为 " + rounds + "！");
        } else {
            player.sendMessage(ChatColor.RED + "设置游戏 '" + player.getName() + "' 的回合数失败！");
        }
        return true;
    }

    private boolean handleSetCountCommand(Player player, int round, int count) {
        if (!gameManager.gameExists(player.getName())) {
            player.sendMessage(ChatColor.RED + "游戏 '" + player.getName() + "' 不存在！");
            return true;
        }

        if (round < 1 || count < 1) {
            player.sendMessage(ChatColor.RED + "回合数和僵尸数量必须大于0！");
            return true;
        }

        if (gameManager.setZombieCount(player.getName(), round, count)) {
            player.sendMessage(ChatColor.GREEN + "成功设置游戏 '" + player.getName() + "' 第 " + round + " 回合的僵尸数量为 " + count + "！");
        } else {
            player.sendMessage(ChatColor.RED + "设置游戏 '" + player.getName() + "' 的僵尸数量失败！请先设置总回合数，并确保指定的回合不超过总回合数。");
        }
        return true;
    }

    private boolean handleCheckCommand(Player player, String gameName) {
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return true;
        }

        Map<String, Boolean> results = gameManager.checkGameConfig(gameName);

        player.sendMessage(ChatColor.GOLD + "===== 游戏 '" + gameName + "' 配置检查 =====");

        // 必需配置项
        player.sendMessage(ChatColor.AQUA + "【必需配置】");
        player.sendMessage(ChatColor.YELLOW + "出生点: " + getStatusString(results.get("spawnPoint")));
        player.sendMessage(ChatColor.YELLOW + "最大玩家数: " + getStatusString(results.get("maxPlayers")));
        player.sendMessage(ChatColor.YELLOW + "窗户区域: " + getStatusString(results.get("windows")));
        player.sendMessage(ChatColor.YELLOW + "回合数: " + getStatusString(results.get("rounds")));
        player.sendMessage(ChatColor.YELLOW + "僵尸数量: " + getStatusString(results.get("zombieCount")));
        player.sendMessage(ChatColor.YELLOW + "僵尸生成点: " + getStatusString(results.get("zombieSpawns")));

        // 可选配置项
        player.sendMessage(ChatColor.LIGHT_PURPLE + "【可选配置】");
        player.sendMessage(ChatColor.YELLOW + "门设置: " + getStatusString(results.get("doors")));
        player.sendMessage(ChatColor.YELLOW + "购买点: " + getStatusString(results.get("buyPoints")));
        player.sendMessage(ChatColor.YELLOW + "幸运箱: " + getStatusString(results.get("luckyBoxes")));
        player.sendMessage(ChatColor.YELLOW + "电源按钮: " + getStatusString(results.get("powerButton")));
        player.sendMessage(ChatColor.YELLOW + "回合模式: " + getStatusString(results.get("roundModes")));
        player.sendMessage(ChatColor.YELLOW + "初始装备: " + getStatusString(results.get("initialEquipment")));
        player.sendMessage(ChatColor.YELLOW + "护甲装备: " + getStatusString(results.get("armorEquipment")));

        player.sendMessage(ChatColor.GOLD + "总体状态: " + getStatusString(results.get("complete")));

        // 添加详细统计信息
        addDetailedStats(player, gameName, results);

        return true;
    }

    /**
     * 处理设置门的命令
     *
     * @param player 玩家
     * @param args 命令参数
     * @return 命令是否成功
     */
    private boolean handleSetDoorCommand(Player player, String[] args) {
        String gameName = args[1];
        String doorName = args[2];
        String lockStatus = args[3].toLowerCase();

        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return true;
        }

        if (!lockStatus.equals("lock") && !lockStatus.equals("unlock")) {
            player.sendMessage(ChatColor.RED + "锁定状态必须为 'lock' 或 'unlock'！");
            return true;
        }

        int unlockPrice = 0;
        if (lockStatus.equals("lock")) {
            if (args.length < 5) {
                player.sendMessage(ChatColor.RED + "锁定门时必须指定解锁价格！");
                return true;
            }

            try {
                unlockPrice = Integer.parseInt(args[4]);
                if (unlockPrice < 0) {
                    player.sendMessage(ChatColor.RED + "解锁价格不能为负数！");
                    return true;
                }
            } catch (NumberFormatException e) {
                player.sendMessage(ChatColor.RED + "解锁价格必须是有效的整数！");
                return true;
            }
        }

        // 获取玩家的区域选择
        Region region = null;
        if (regionSelector.hasSelection(player)) {
            region = regionSelector.getSelection(player);
            player.sendMessage(ChatColor.YELLOW + "已使用你的区域选择作为门区域！");
        } else {
            player.sendMessage(ChatColor.YELLOW + "提示：你可以使用区域选择工具选择门区域。没有选择区域将导致门无法正常工作。");
        }

        // 使用新的setDoor方法，包含区域信息
        boolean success;
        if (region != null) {
            success = gameManager.setDoor(gameName, doorName, lockStatus, unlockPrice, region);

            // 如果成功设置了门，同时注册到DoorManager
            if (success) {
                plugin.getDoorManager().registerDoorRegion(gameName, doorName, region);
            }
        } else {
            success = gameManager.setDoor(gameName, doorName, lockStatus, unlockPrice);
        }

        if (success) {
            if (lockStatus.equals("lock")) {
                player.sendMessage(ChatColor.GREEN + "成功设置游戏 '" + gameName + "' 的门 '" + doorName + "' 为锁定状态，解锁价格为 " + unlockPrice + " 金币！");
            } else {
                player.sendMessage(ChatColor.GREEN + "成功设置游戏 '" + gameName + "' 的门 '" + doorName + "' 为解锁状态！");
            }
        } else {
            player.sendMessage(ChatColor.RED + "设置游戏 '" + gameName + "' 的门 '" + doorName + "' 失败！");
        }

        return true;
    }

    /**
     * 处理门解锁命令
     *
     * @param player 玩家
     * @return 命令是否成功
     */
    private boolean handleUnlockCommand(Player player) {
        // 获取玩家最后交互的门信息
        Map.Entry<String, String> doorInfo = plugin.getPlayerInteractionManager().getLastInteractedDoor(player);

        if (doorInfo == null) {
            player.sendMessage(ChatColor.RED + "你没有要解锁的门！请先与一个门交互。");
            return true;
        }

        String gameName = doorInfo.getKey();
        String doorName = doorInfo.getValue();

        // 尝试解锁门
        boolean success = plugin.getDoorManager().unlockDoor(player, gameName, doorName);

        // 清除玩家最后交互的门信息
        plugin.getPlayerInteractionManager().clearLastInteractedDoor(player);

        return success;
    }

    /**
     * 创建僵尸生成点，仅当生成点不存在时才创建
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param spawnName 生成点名称
     * @param zombieType 僵尸类型
     * @return 是否成功创建或生成点已存在
     */
    private boolean createZombieSpawnIfNotExists(Player player, String gameName, String spawnName, String zombieType) {
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return false;
        }

        // 检查生成点是否已存在
        FileConfiguration config = gameManager.getGameConfig(gameName);
        String spawnPath = "zombieSpawns." + spawnName.replace(".", "_");

        if (config.isSet(spawnPath)) {
            // 生成点已存在，不需要创建
            return true;
        }

        // 生成点不存在，创建新的生成点
        if (gameManager.setZombieSpawn(gameName, spawnName, zombieType, player.getLocation())) {
            player.sendMessage(ChatColor.GREEN + "成功创建僵尸生成点 '" + spawnName + "' (类型: " + zombieType + ")！");
            return true;
        } else {
            player.sendMessage(ChatColor.RED + "创建僵尸生成点 '" + spawnName + "' 失败！");
            return false;
        }
    }

    /**
     * 处理设置僵尸生成点的命令
     *
     * @param player 玩家
     * @param spawnName 生成点名称
     * @param zombieType 僵尸类型，可选参数，默认为"id"
     * @param gameName 游戏名称
     * @return 命令是否成功
     */
    private boolean handleSetZombieSpawnCommand(Player player, String spawnName, String zombieType, String gameName) {
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return true;
        }

        // 如果没有指定僵尸类型或类型无效，使用默认类型 "id"
        String finalZombieType = "id"; // 默认类型

        if (zombieType != null && !zombieType.isEmpty()) {
            // 检查僵尸类型是否有效
            if (zombieType.equals("id") || zombieType.equals("idn") || zombieType.equals("idc")) {
                finalZombieType = zombieType;
            } else {
                player.sendMessage(ChatColor.YELLOW + "无效的僵尸类型 '" + zombieType + "'，使用默认类型 'id'。");
            }
        }

        // 检查生成点是否已存在
        FileConfiguration config = gameManager.getGameConfig(gameName);
        String spawnPath = "zombieSpawns." + spawnName.replace(".", "_");

        if (config.isSet(spawnPath)) {
            // 生成点已存在，只更新位置
            player.sendMessage(ChatColor.YELLOW + "生成点 '" + spawnName + "' 已存在，将更新其位置。");

            // 保存原始类型，不覆盖
            String originalType = config.getString(spawnPath + ".type", finalZombieType);

            // 更新位置信息
            config.set(spawnPath + ".world", player.getLocation().getWorld().getName());
            config.set(spawnPath + ".x", player.getLocation().getX());
            config.set(spawnPath + ".y", player.getLocation().getY());
            config.set(spawnPath + ".z", player.getLocation().getZ());

            try {
                // 直接使用config.save方法保存到游戏文件
                File gameFile = new File(plugin.getDataFolder(), "game/" + gameName + ".yml");
                config.save(gameFile);
                player.sendMessage(ChatColor.GREEN + "成功更新游戏 '" + gameName + "' 的僵尸生成点 '" + spawnName + "' 的位置！");
            } catch (IOException e) {
                player.sendMessage(ChatColor.RED + "更新生成点位置失败！");
                e.printStackTrace();
            }
        } else {
            // 生成点不存在，创建新的生成点
            if (gameManager.setZombieSpawn(gameName, spawnName, finalZombieType, player.getLocation())) {
                player.sendMessage(ChatColor.GREEN + "成功为游戏 '" + gameName + "' 创建僵尸生成点 '" + spawnName + "' (类型: " + finalZombieType + ")！");
            } else {
                player.sendMessage(ChatColor.RED + "为游戏 '" + gameName + "' 创建僵尸生成点失败！");
            }
        }

        return true;
    }

    /**
     * 处理设置门与僵尸生成点关联的命令
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @param spawnName 生成点名称
     * @return 命令是否成功
     */
    /**
     * 处理综合游戏设置命令
     *
     * @param player 玩家
     * @param args 命令参数
     * @return 命令执行结果
     */
    private boolean handleGameCommand(Player player, String[] args) {
        // 参数格式: /dzs game <游戏名称> <spawn|with|setcount> <回合数/门名称> [其他参数]
        String gameName = args[1];
        String subCommand = args[2].toLowerCase();

        // 检查游戏是否存在
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return true;
        }

        switch (subCommand) {
            case "spawn":
                // /dzs game <游戏名称> spawn <回合数> <怪物出生点名称> [怪物类型] [生成数量]
                if (args.length < 5) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs game " + gameName + " spawn <回合数> <怪物出生点名称> [怪物类型] [生成数量]");
                    return true;
                }

                try {
                    int round = Integer.parseInt(args[3]);
                    String spawnName = args[4];

                    // 获取游戏配置
                    FileConfiguration config = gameManager.getGameConfig(gameName);

                    // 检查是否是链接门的命令格式
                    // 格式: /dzs game <游戏名> spawn <回合数> <链接出生点名称> <链接类型>
                    // 其中链接类型可能是门名称
                    boolean isLinkingDoor = false;
                    String doorName = null;

                    if (args.length > 5) {
                        // 检查第6个参数是否可能是门名称
                        String possibleDoorName = args[5];

                        // 检查门是否存在
                        if (config.isConfigurationSection("doors." + possibleDoorName.replace(".", "_"))) {
                            isLinkingDoor = true;
                            doorName = possibleDoorName;
                            plugin.getLogger().info("检测到门关联命令，将生成点 '" + spawnName + "' 与门 '" + doorName + "' 关联");
                        }
                    }

                    String monsterInput = !isLinkingDoor && args.length > 5 ? args[5] : "普通僵尸"; // 默认为普通僵尸
                    String count = !isLinkingDoor && args.length > 6 ? args[6] : "5";      // 默认生成 5 个

                    // 将中文名称转换为ID
                    String monsterType = convertMonsterNameToId(monsterInput);

                    // 检查生成点是否存在，如果不存在则自动创建
                    String spawnPath = "zombieSpawns." + spawnName.replace(".", "_");

                    if (!config.isSet(spawnPath)) {
                        // 确定僵尸类型
                        String zombieType = "id"; // 默认类型
                        if (monsterType.startsWith("idn")) {
                            zombieType = "idn";
                        } else if (monsterType.startsWith("idc")) {
                            zombieType = "idc";
                        }

                        // 自动创建生成点
                        if (gameManager.setZombieSpawn(gameName, spawnName, zombieType, player.getLocation())) {
                            player.sendMessage(ChatColor.GREEN + "已自动创建生成点 '" + spawnName + "'！");
                        } else {
                            player.sendMessage(ChatColor.RED + "无法自动创建生成点 '" + spawnName + "'！");
                            return true;
                        }
                    } else {
                        // 生成点已存在，只添加新的刷怪逻辑
                        player.sendMessage(ChatColor.YELLOW + "生成点 '" + spawnName + "' 已存在，将添加新的刷怪逻辑。");
                    }

                    // 如果是链接门的命令，处理门与生成点的关联
                    if (isLinkingDoor && doorName != null) {
                        // 设置门与生成点的关联
                        if (gameManager.setDoorZombieSpawn(gameName, doorName, spawnName)) {
                            player.sendMessage(ChatColor.GREEN + "成功将游戏 '" + gameName + "' 中的门 '" + doorName + "' 与僵尸生成点 '" + spawnName + "' 关联！");
                            player.sendMessage(ChatColor.YELLOW + "当门被解锁后，关联的生成点将被自动启用。");
                        } else {
                            player.sendMessage(ChatColor.RED + "设置关联失败！请确认门和生成点都已存在。");
                        }
                    }

                    // 处理怪物类型 - 根据转换后的ID确定正确的怪物类型
                    String actualMonsterType = "zombie"; // 默认类型
                    if (monsterType.startsWith("idc")) {
                        actualMonsterType = "entity";
                    } else if (monsterType.startsWith("idn")) {
                        actualMonsterType = "npc";
                    } else if (monsterType.startsWith("id")) {
                        actualMonsterType = "zombie";
                    }

                    // 调用设置回合模式的方法
                    return handleSetModeCommand(player, gameName, round, spawnName, actualMonsterType, monsterType, count);
                } catch (NumberFormatException e) {
                    player.sendMessage(ChatColor.RED + "回合数必须是一个有效的整数！");
                    return true;
                }

            case "with":
                // /dzs game <游戏名称> with <门名称> <怪物出生点名称>
                if (args.length < 5) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs game " + gameName + " with <门名称> <怪物出生点名称>");
                    return true;
                }

                String doorName = args[3];
                String spawnName = args[4];
                return handleWithCommand(player, gameName, doorName, spawnName);

            case "setcount":
                // /dzs game <游戏名称> setcount <回合数> <僵尸数量>
                if (args.length < 5) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs game " + gameName + " setcount <回合数> <僵尸数量>");
                    return true;
                }

                try {
                    int round = Integer.parseInt(args[3]);
                    int count = Integer.parseInt(args[4]);

                    if (gameManager.setZombieCount(gameName, round, count)) {
                        player.sendMessage(ChatColor.GREEN + "成功设置游戏 '" + gameName + "' 第 " + round + " 回合的僵尸数量为 " + count + "！");
                    } else {
                        player.sendMessage(ChatColor.RED + "设置游戏 '" + gameName + "' 的僵尸数量失败！请先设置总回合数，并确保指定的回合不超过总回合数。");
                    }
                    return true;
                } catch (NumberFormatException e) {
                    player.sendMessage(ChatColor.RED + "回合数和僵尸数量必须是整数！");
                    return true;
                }

            default:
                player.sendMessage(ChatColor.RED + "未知的子命令: " + subCommand);
                player.sendMessage(ChatColor.YELLOW + "可用的子命令: spawn, with, setcount");
                return true;
        }
    }

    private boolean handleWithCommand(Player player, String gameName, String doorName, String spawnName) {
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return true;
        }

        // 设置门与生成点的关联
        if (gameManager.setDoorZombieSpawn(gameName, doorName, spawnName)) {
            player.sendMessage(ChatColor.GREEN + "成功将游戏 '" + gameName + "' 中的门 '" + doorName + "' 与僵尸生成点 '" + spawnName + "' 关联！");
            player.sendMessage(ChatColor.YELLOW + "当门被解锁后，关联的生成点将被自动启用。");
        } else {
            player.sendMessage(ChatColor.RED + "设置关联失败！请确认门和生成点都已存在。");
        }

        return true;
    }

    /**
     * 处理设置回合怪物生成模式的命令
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param round 回合数
     * @param spawnName 生成点名称
     * @param monsterType 怪物类型
     * @param monsterId 怪物ID
     * @param count 生成数量，可以是数字或"random"
     * @return 命令是否成功
     */
    private boolean handleSetModeCommand(Player player, String gameName, int round, String spawnName, String monsterType, String monsterId, String count) {
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return true;
        }

        // 检查回合数是否在有效范围内
        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (!config.contains("rounds")) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 尚未设置回合数！请先设置回合数。");
            return true;
        }

        int totalRounds = config.getInt("rounds");
        if (round < 1 || round > totalRounds) {
            player.sendMessage(ChatColor.RED + "回合数必须在1到" + totalRounds + "之间！");
            return true;
        }

        // 检查生成点是否存在
        if (!config.isSet("zombieSpawns." + spawnName.replace(".", "_"))) {
            player.sendMessage(ChatColor.RED + "生成点 '" + spawnName + "' 不存在！");
            return true;
        }

        // 验证怪物ID格式（现在支持中文名称和ID格式）
        if (!monsterId.matches("^(id\\d+|idc\\d+|idn\\d+|\\d+)$") && convertMonsterNameToId(monsterId).equals("id1") && !monsterId.equals("普通僵尸")) {
            player.sendMessage(ChatColor.RED + "无效的怪物名称或ID！请使用正确的中文名称或ID格式。");
            player.sendMessage(ChatColor.YELLOW + "例如：'普通僵尸'、'变异苦力怕'、'感染者农民' 或 'id1'、'idc2'、'idn3'");
            return true;
        }

        // 验证数量
        if (!count.equalsIgnoreCase("random")) {
            try {
                int spawnCount = Integer.parseInt(count);
                if (spawnCount < 1) {
                    player.sendMessage(ChatColor.RED + "生成数量必须大于0！");
                    return true;
                }
            } catch (NumberFormatException e) {
                player.sendMessage(ChatColor.RED + "生成数量必须是数字或 'random'！");
                return true;
            }
        }

        // 设置回合模式
        if (gameManager.setRoundMode(gameName, round, spawnName, monsterType, monsterId, count)) {
            // 获取怪物的中文名称用于显示
            String displayName = getMonsterNameById(monsterId);
            if (displayName == null) {
                displayName = monsterId; // 如果找不到中文名称，使用ID
            }

            if (count.equalsIgnoreCase("random")) {
                player.sendMessage(ChatColor.GREEN + "成功设置游戏 '" + gameName + "' 第 " + round + " 回合生成点 '" + spawnName
                        + "' 的怪物生成模式: 怪物=" + displayName + " (" + monsterId + "), 数量=随机");
            } else {
                player.sendMessage(ChatColor.GREEN + "成功设置游戏 '" + gameName + "' 第 " + round + " 回合生成点 '" + spawnName
                        + "' 的怪物生成模式: 怪物=" + displayName + " (" + monsterId + "), 数量=" + count);
            }

            // 提醒玩家关于出怪逻辑的信息
            player.sendMessage(ChatColor.YELLOW + "注意: 如果生成点关联的门未解锁，则不会在此生成点生成怪物，"
                    + "这些怪物会被平分到其他已解锁的生成点。");
        } else {
            player.sendMessage(ChatColor.RED + "设置怪物生成模式失败！请检查参数是否正确。");
        }

        return true;
    }

    /**
     * 处理测试指令
     *
     * @param player 执行指令的玩家
     * @param gameName 游戏名称
     * @param round 要测试的回合数
     * @param spawnName 可选的出生点名称
     * @return 操作是否成功
     */
    private boolean handleTestCommand(Player player, String gameName, int round, String spawnName) {
        try {
            // 检查游戏是否存在
            if (!gameManager.gameExists(gameName)) {
                player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在");
                return false;
            }

            // 检查ZombieHelper实例状态
            ZombieHelper zombieHelper = plugin.getZombieHelper();
            if (zombieHelper == null || !zombieHelper.isCustomZombieAvailable()) {
                player.sendMessage(ChatColor.RED + "ZombieHelper 实例未初始化或CustomZombie不可用，无法生成怪物！");
                if (zombieHelper != null && !zombieHelper.isCustomZombieAvailable()) {
                    player.sendMessage(ChatColor.YELLOW + "CustomZombie插件未正确加载，请确保CustomZombie已正确启用。");
                }
                return false;
            }

            // 获取游戏配置
            // 对于测试命令，可以使用默认或临时配置
            Map<String, Map<String, Object>> roundModesMap = null;

            if (gameName != null) {
                // 尝试从游戏配置中读取回合模式
                FileConfiguration gameConfig = gameManager.getGameConfig(gameName);
                if (gameConfig != null && gameConfig.contains("roundModes.round" + round)) {
                    plugin.getLogger().info("获取游戏 '" + gameName + "' 第 " + round + " 回合的怪物生成模式");
                    roundModesMap = gameManager.getRoundModes(gameName, round);
                }
            }

            if (roundModesMap == null || roundModesMap.isEmpty()) {
                player.sendMessage(ChatColor.YELLOW + "警告: 无法找到第 " + round + " 回合的配置，使用默认配置");
                // 创建默认配置用于测试
                roundModesMap = new HashMap<>();
                Map<String, Object> defaultSpawnConfig = new HashMap<>();
                defaultSpawnConfig.put("monsterType", "zombie");
                defaultSpawnConfig.put("monsterId", "id1");
                defaultSpawnConfig.put("count", "5");

                roundModesMap.put("default", defaultSpawnConfig);
            }

            // 转换为可读格式并显示给玩家
            StringBuilder configInfo = new StringBuilder();
            configInfo.append(ChatColor.GREEN + "第 " + round + " 回合配置: \n");

            for (Map.Entry<String, Map<String, Object>> entry : roundModesMap.entrySet()) {
                String spawn = entry.getKey();
                Map<String, Object> spawnConfig = entry.getValue();
                configInfo.append(ChatColor.YELLOW + "- 出生点 '" + spawn + "': " + formatConfigInfo(spawnConfig) + "\n");
            }

            player.sendMessage(configInfo.toString());

            // 获取指定出生点的配置
            Map<String, Object> spawnConfig = null;
            Location spawnLocation = null;
            String spawnPointName = null;

            if (spawnName != null && roundModesMap.containsKey(spawnName)) {
                // 使用指定的出生点
                Map<String, Object> spawnPointConfig = roundModesMap.get(spawnName);

                // 处理嵌套结构，先检查是否有刷怪逻辑子节点
                if (spawnPointConfig instanceof Map) {
                    // 先检查是否有刷怪逻辑子节点
                    for (Object key : ((Map<?, ?>) spawnPointConfig).keySet()) {
                        Object value = ((Map<?, ?>) spawnPointConfig).get(key);
                        if (value instanceof Map && key instanceof String && ((String) key).startsWith("刷怪逻辑")) {
                            // 找到刷怪逻辑
                            @SuppressWarnings("unchecked")
                            Map<String, Object> logicMap = (Map<String, Object>) value;
                            spawnConfig = logicMap;
                            plugin.getLogger().info("找到刷怪逻辑: " + key);
                            break;
                        }
                    }
                }

                spawnPointName = spawnName;

                // 尝试从游戏配置中获取出生点坐标
                if (gameName != null) {
                    spawnLocation = gameManager.getZombieSpawnLocation(gameName, spawnName);
                    if (spawnLocation != null) {
                        plugin.getLogger().info("找到出生点 '" + spawnName + "' 的位置");
                    }
                }

                if (spawnLocation == null) {
                    // 如果找不到出生点位置，使用玩家位置
                    spawnLocation = player.getLocation();
                    plugin.getLogger().warning("无法找到出生点 '" + spawnName + "' 的位置，使用玩家位置");
                }

                // 添加坐标信息到配置中
                spawnConfig.put("spawnLocation", spawnLocation);
                spawnConfig.put("spawnName", spawnName);

                // 生成该出生点的怪物
                testSpawnMonsters(player, new MemoryConfiguration().createSection("test", spawnConfig), gameName);
            } else {
                // 测试所有出生点
                if (spawnName != null) {
                    player.sendMessage(ChatColor.YELLOW + "找不到出生点 '" + spawnName + "'，将测试所有出生点");
                }

                int successfulSpawns = 0;
                for (Map.Entry<String, Map<String, Object>> entry : roundModesMap.entrySet()) {
                    String spawn = entry.getKey();
                    Map<String, Object> spawnPointConfig = entry.getValue();

                    // 处理嵌套结构，先检查是否有刷怪逻辑子节点
                    ConfigurationSection logicsSection = null;
                    if (spawnPointConfig instanceof Map) {
                        // 先检查是否有刷怪逻辑子节点
                        for (Object key : ((Map<?, ?>) spawnPointConfig).keySet()) {
                            Object value = ((Map<?, ?>) spawnPointConfig).get(key);
                            if (value instanceof Map && key instanceof String && ((String) key).startsWith("刷怪逻辑")) {
                                // 找到刷怪逻辑
                                @SuppressWarnings("unchecked")
                                Map<String, Object> logicMap = (Map<String, Object>) value;
                                spawnConfig = logicMap;
                                plugin.getLogger().info("找到刷怪逻辑: " + key);
                                break;
                            }
                        }
                    }

                    // 如果没有找到刷怪逻辑，则使用默认配置
                    if (spawnConfig == null) {
                        spawnConfig = new HashMap<>();
                        spawnConfig.put("monsterType", "zombie");
                        spawnConfig.put("monsterId", "id1");
                        spawnConfig.put("count", "5");
                        plugin.getLogger().warning("没有找到刷怪逻辑，使用默认配置");
                    }

                    // 尝试从游戏配置中获取出生点坐标
                    if (gameName != null) {
                        spawnLocation = gameManager.getZombieSpawnLocation(gameName, spawn);
                    }

                    if (spawnLocation == null) {
                        // 如果找不到出生点位置，使用玩家位置加随机偏移
                        spawnLocation = player.getLocation().clone();
                        // 添加随机偏移避免实体堆叠
                        Random random = new Random();
                        double offsetX = (random.nextDouble() - 0.5) * 5;
                        double offsetZ = (random.nextDouble() - 0.5) * 5;
                        spawnLocation.add(offsetX, 0, offsetZ);

                        plugin.getLogger().warning("无法找到出生点 '" + spawn + "' 的位置，使用玩家位置加随机偏移");
                    }

                    // 确保 spawnConfig 不为 null
                    if (spawnConfig == null) {
                        spawnConfig = new HashMap<>();
                        spawnConfig.put("monsterType", "zombie");
                        spawnConfig.put("monsterId", "id1");
                        spawnConfig.put("count", "5");
                    }

                    // 添加坐标信息到配置中
                    spawnConfig.put("spawnLocation", spawnLocation);
                    spawnConfig.put("spawnName", spawn);

                    // 不再显示详细配置信息
                    // player.sendMessage(ChatColor.YELLOW + "出生点 '" + spawn + "' 配置: " + formatConfigInfo(spawnConfig));
                    testSpawnMonsters(player, new MemoryConfiguration().createSection("test", spawnConfig), gameName);
                    successfulSpawns++;
                }

                player.sendMessage(ChatColor.GREEN + "有僵尸生成！");
            }

            return true;
        } catch (Exception e) {
            player.sendMessage(ChatColor.RED + "测试命令执行失败: " + e.getMessage());
            plugin.getLogger().severe("测试命令执行失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 格式化配置信息为可读字符串
     */
    private String formatConfigInfo(Map<String, Object> config) {
        StringBuilder sb = new StringBuilder();
        sb.append("{");
        boolean first = true;

        if (config.containsKey("monsterType")) {
            sb.append("类型=").append(config.get("monsterType"));
            first = false;
        }

        if (config.containsKey("monsterId")) {
            if (!first) {
                sb.append(", ");
            }
            sb.append("ID=").append(config.get("monsterId"));
            first = false;
        }

        if (config.containsKey("count")) {
            if (!first) {
                sb.append(", ");
            }
            sb.append("数量=").append(config.get("count"));
        }

        sb.append("}");
        return sb.toString();
    }

    /**
     * 测试生成怪物
     *
     * @param player 玩家
     * @param spawnConfig 生成配置
     * @param gameName 游戏名称
     */
    public void testSpawnMonsters(Player player, ConfigurationSection spawnConfig, String gameName) {
        // 基本日志信息
        plugin.getLogger().info("执行testSpawnMonsters命令，玩家: " + player.getName());

        // 参数验证
        if (spawnConfig == null) {
            player.sendMessage(ChatColor.RED + "错误: 怪物生成配置为null");
            plugin.getLogger().severe("testSpawnMonsters命令失败: 怪物生成配置为null");
            return;
        }

        // 获取基本参数
        String configPath = spawnConfig.getCurrentPath();
        plugin.getLogger().info("使用配置路径: " + configPath);

        // 获取正确的生成位置 - 优先使用传入的出生点
        Location spawnLocation = null;
        String spawnName = null;

        if (spawnConfig.contains("spawnLocation")) {
            spawnLocation = (Location) spawnConfig.get("spawnLocation");
            spawnName = spawnConfig.getString("spawnName", "未知");
            plugin.getLogger().info("使用配置的出生点位置: " + formatLocation(spawnLocation) + " (出生点名称: " + spawnName + ")");
            player.sendMessage(ChatColor.GREEN + "使用配置的出生点位置: " + spawnName);
        } else {
            spawnLocation = player.getLocation();
            plugin.getLogger().warning("未找到出生点位置配置，使用玩家当前位置: " + formatLocation(spawnLocation));
            player.sendMessage(ChatColor.YELLOW + "未找到出生点位置配置，使用玩家当前位置");
        }

        // 如果没有传入游戏名称，使用默认值
        if (gameName == null || gameName.isEmpty()) {
            gameName = "Test";
        }
        plugin.getLogger().info("测试游戏: " + gameName);

        // 获取怪物类型和ID
        String monsterType = spawnConfig.getString("monsterType", "zombie");
        String monsterId = spawnConfig.getString("monsterId", "id1"); // 默认使用id1普通僵尸
        String countValue = spawnConfig.getString("count", "5"); // 默认生成数量

        // 确保我们有有效的怪物ID
        if (monsterId == null || monsterId.isEmpty()) {
            monsterId = "id1"; // 默认使用id1
            plugin.getLogger().warning("未指定怪物ID，使用默认值: " + monsterId);
        }

        // 检查是否存在新增的type和number字段
        String type = spawnConfig.getString("type", null);
        String number = spawnConfig.getString("number", null);

        // 如果type和number未在配置中明确指定，则需要解析
        if (type == null || number == null) {
            plugin.getLogger().info("配置中未找到type或number字段，将从monsterId解析");

            // 从monsterId解析
            if (monsterId.startsWith("id")) {
                type = "id";
                number = monsterId.substring(2);
            } else if (monsterId.startsWith("idc")) {
                type = "idc";
                number = monsterId.substring(3);
            } else if (monsterId.startsWith("idn")) {
                type = "idn";
                number = monsterId.substring(3);
            } else {
                type = "id"; // 默认类型为id
                number = monsterId; // 假设直接是数字
            }
        }

        // 如果配置中有type和number，但没有monsterId，则根据type和number构建完整的monsterId
        if (type != null && number != null && (monsterId == null || monsterId.isEmpty() || monsterId.equals("id1"))) {
            monsterId = type + number;
            plugin.getLogger().info("根据type和number构建完整的monsterId: " + monsterId);
        }

        // 输出详细的调试信息
        plugin.getLogger().info("最终使用的怪物类型: " + type + ", 编号: " + number + ", 原始ID: " + monsterId);

        // 详细记录配置信息
        plugin.getLogger().info("配置中读取到的值: monsterType=" + monsterType + ", monsterId=" + monsterId
                + ", type=" + type + ", number=" + number);

        // 获取生成数量
        int count = 5; // 默认生成5个
        if (countValue != null) {
            if (countValue.equalsIgnoreCase("random")) {
                // 随机生成1-10个
                count = new Random().nextInt(10) + 1;
                plugin.getLogger().info("随机生成数量: " + count);
            } else {
                try {
                    count = Integer.parseInt(countValue);
                } catch (NumberFormatException e) {
                    plugin.getLogger().warning("无法解析数量值: " + countValue + ", 使用默认值5");
                    count = 5;
                }
            }
        }

        // 获取配置文件中设置的最大生成数量
        int maxZombiesPerRound = 0;
        if (gameName != null) {
            FileConfiguration gameConfig = gameManager.getGameConfig(gameName);
            if (gameConfig != null) {
                maxZombiesPerRound = gameConfig.getInt("maxZombiesPerRound", 0);
                if (maxZombiesPerRound > 0 && count > maxZombiesPerRound) {
                    plugin.getLogger().info("生成数量超过配置的最大值，限制为: " + maxZombiesPerRound);
                    count = maxZombiesPerRound;
                }
            }
        }

        // 如果数量无效，设为1
        if (count < 1) {
            count = 1;
        }

        // 使用配置文件中的ID，而不是默认的id5
        // 构建最终要使用的怪物ID
        String finalMonsterId = type + number;

        // 输出生成计划信息
        player.sendMessage(ChatColor.GREEN + "尝试在" + (spawnName != null ? "出生点[" + spawnName + "]" : "当前位置") + "生成 " + count + " 个怪物: 类型=" + monsterType + ", ID=" + finalMonsterId);
        plugin.getLogger().info("计划生成: 类型=" + monsterType + ", ID=" + finalMonsterId + ", 数量=" + count);

        // 获取CustomZombie插件实例
        ZombieHelper zombieHelper = plugin.getZombieHelper();
        if (zombieHelper == null || !zombieHelper.isCustomZombieAvailable()) {
            player.sendMessage(ChatColor.RED + "错误: ZombieHelper实例未初始化或CustomZombie不可用");
            plugin.getLogger().severe("ZombieHelper实例未初始化或CustomZombie不可用，无法生成怪物");
            if (zombieHelper != null) {
                player.sendMessage(ChatColor.YELLOW + "CustomZombie插件未正确加载，请确保CustomZombie已正确启用。");
            }
            return;
        }

        // 预加载出生点列表
        Map<Integer, Location> spawnPoints = new HashMap<>();

        // 添加主出生点
        spawnPoints.put(0, spawnLocation);
        plugin.getLogger().info("主出生点 (ID:0): " + formatLocation(spawnLocation));

        // 检索并添加附属出生点
        if (gameName != null && spawnName != null) {
            // 获取游戏配置
            FileConfiguration gameConfig = gameManager.getGameConfig(gameName);

            if (gameConfig != null && gameConfig.contains("zombieSpawns." + spawnName.replace(".", "_") + ".attachments")) {
                ConfigurationSection attachments = gameConfig.getConfigurationSection("zombieSpawns." + spawnName.replace(".", "_") + ".attachments");

                if (attachments != null) {
                    for (String key : attachments.getKeys(false)) {
                        try {
                            int attachId = Integer.parseInt(key);
                            String worldName = attachments.getString(key + ".world");
                            double x = attachments.getDouble(key + ".x");
                            double y = attachments.getDouble(key + ".y");
                            double z = attachments.getDouble(key + ".z");

                            World world = Bukkit.getWorld(worldName);
                            if (world != null) {
                                Location attachLocation = new Location(world, x, y, z);
                                spawnPoints.put(attachId, attachLocation);
                                plugin.getLogger().info("加载附属出生点 (ID:" + attachId + "): " + formatLocation(attachLocation));
                            }
                        } catch (Exception e) {
                            plugin.getLogger().warning("加载附属出生点 " + key + " 失败: " + e.getMessage());
                        }
                    }
                }
            }
        }

        // 如果没有找到任何附属出生点，输出警告
        if (spawnPoints.size() <= 1) {
            plugin.getLogger().warning("未找到任何附属出生点，将仅使用主出生点");
        }

        try {
            // 简化输出消息
            player.sendMessage(ChatColor.GREEN + "有僵尸生成！");

            // 根据不同的类型调用不同的生成方法
            int successCount = 0;
            for (int i = 0; i < count; i++) {
                boolean success = false;

                // 选择出生点 - 循环使用主出生点和附属出生点
                int maxSpawnPoints = spawnPoints.size();
                int spawnPointIndex = (maxSpawnPoints > 0) ? (i % maxSpawnPoints) : 0;

                // 获取对应的出生点位置
                Location currentSpawnLocation = spawnPoints.getOrDefault(spawnPointIndex, spawnLocation);

                plugin.getLogger().info("使用出生点 " + spawnPointIndex + " 生成第 " + (i + 1) + " 个怪物: " + formatLocation(currentSpawnLocation));

                // 根据类型调用不同的生成方法
                switch (type) {
                    case "id":  // 普通僵尸类型
                        // 使用构建好的finalMonsterId而不是重新构建ID
                        plugin.getLogger().info("生成普通僵尸: " + finalMonsterId);
                        Zombie zombie = zombieHelper.spawnCustomZombie(currentSpawnLocation, finalMonsterId);
                        if (zombie != null) {
                            success = true;
                        }
                        break;

                    case "idc":  // 实体类型
                        // 使用finalMonsterId
                        plugin.getLogger().info("生成实体怪物: " + finalMonsterId);
                        success = zombieHelper.spawnOtherEntity(currentSpawnLocation, finalMonsterId);
                        break;

                    case "idn":  // NPC类型
                        // 使用finalMonsterId
                        plugin.getLogger().info("生成NPC怪物: " + finalMonsterId);

                        // 根据不同的NPC ID选择对应的创建方法
                        switch (finalMonsterId) {
                            case "idn1":
                                success = zombieHelper.createInfectedNPC1(currentSpawnLocation);
                                break;
                            case "idn2":
                                success = zombieHelper.createInfectedNPC2(currentSpawnLocation);
                                break;
                            case "idn3":
                                success = zombieHelper.createInfectedFarmer(currentSpawnLocation);
                                break;
                            case "idn4":
                                success = zombieHelper.createInfectedResident(currentSpawnLocation);
                                break;
                            case "idn5":
                                success = zombieHelper.createInfectedPig(currentSpawnLocation);
                                break;
                            default:
                                plugin.getLogger().warning("未知的NPC ID: " + finalMonsterId);
                        }
                        break;
                }

                if (success) {
                    successCount++;
                    plugin.getLogger().info("成功生成第 " + (i + 1) + "/" + count + " 个怪物: 类型=" + type + " ID=" + finalMonsterId);
                    // 不再向玩家发送详细消息
                } else {
                    plugin.getLogger().warning("生成第 " + (i + 1) + " 个怪物失败: 类型=" + type + " ID=" + finalMonsterId);
                }
            }
        } catch (Exception e) {
            player.sendMessage(ChatColor.RED + "生成怪物时出错: " + e.getMessage());
            plugin.getLogger().severe("生成怪物时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取随机偏移量
     *
     * @param config 偏移配置
     * @param axis 轴（x、y、z）
     * @return 随机偏移量
     */
    private double getRandomOffset(ConfigurationSection config, String axis) {
        if (config.isDouble(axis)) {
            // 固定偏移
            return config.getDouble(axis);
        } else if (config.isString(axis)) {
            String offsetStr = config.getString(axis);
            if (offsetStr != null && offsetStr.contains("-")) {
                // 范围偏移 (例如: "-5-5")
                String[] parts = offsetStr.split("-");
                if (parts.length == 2) {
                    try {
                        double min = Double.parseDouble(parts[0].trim());
                        double max = Double.parseDouble(parts[1].trim());
                        // 确保最小值不大于最大值
                        if (min > max) {
                            double temp = min;
                            min = max;
                            max = temp;
                        }
                        // 返回范围内的随机值
                        Random random = new Random();
                        return min + (max - min) * random.nextDouble();
                    } catch (NumberFormatException e) {
                        plugin.getLogger().warning("无法解析 " + axis + " 轴的偏移范围: " + offsetStr);
                    }
                }
            } else {
                // 尝试解析单一数值
                try {
                    return Double.parseDouble(offsetStr);
                } catch (NumberFormatException e) {
                    plugin.getLogger().warning("无法解析 " + axis + " 轴的偏移值: " + offsetStr);
                }
            }
        }

        // 默认无偏移
        return 0;
    }

    /**
     * 格式化位置信息为可读字符串
     */
    private String formatLocation(Location loc) {
        if (loc == null) {
            return "null";
        }
        return String.format("世界=%s, x=%.2f, y=%.2f, z=%.2f",
                loc.getWorld() != null ? loc.getWorld().getName() : "null",
                loc.getX(), loc.getY(), loc.getZ());
    }

    private String getStatusString(Boolean status) {
        return status != null && status ? ChatColor.GREEN + "已设置" : ChatColor.RED + "未设置";
    }

    /**
     * 添加详细的统计信息
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param results 检查结果
     */
    private void addDetailedStats(Player player, String gameName, Map<String, Boolean> results) {
        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (config == null) {
            return;
        }

        player.sendMessage(ChatColor.GRAY + "==========================================");
        player.sendMessage(ChatColor.AQUA + "【详细统计信息】");

        // 统计窗户数量
        if (results.get("windows")) {
            ConfigurationSection windowsSection = config.getConfigurationSection("windows");
            if (windowsSection != null) {
                int windowCount = windowsSection.getKeys(false).size();
                player.sendMessage(ChatColor.YELLOW + "窗户数量: " + ChatColor.WHITE + windowCount + " 个");
            }
        }

        // 统计僵尸生成点数量
        if (results.get("zombieSpawns")) {
            ConfigurationSection zombieSpawnsSection = config.getConfigurationSection("zombieSpawns");
            if (zombieSpawnsSection != null) {
                int spawnCount = zombieSpawnsSection.getKeys(false).size();
                int enabledSpawns = 0;
                for (String spawnName : zombieSpawnsSection.getKeys(false)) {
                    if (zombieSpawnsSection.getBoolean(spawnName + ".enabled", false)) {
                        enabledSpawns++;
                    }
                }
                player.sendMessage(ChatColor.YELLOW + "僵尸生成点: " + ChatColor.WHITE + spawnCount + " 个 (已启用: " + enabledSpawns + " 个)");
            }
        }

        // 统计门数量
        if (results.get("doors")) {
            ConfigurationSection doorsSection = config.getConfigurationSection("doors");
            if (doorsSection != null) {
                int doorCount = doorsSection.getKeys(false).size();
                int lockedDoors = 0;
                for (String doorName : doorsSection.getKeys(false)) {
                    if (doorsSection.getBoolean(doorName + ".locked", false)) {
                        lockedDoors++;
                    }
                }
                player.sendMessage(ChatColor.YELLOW + "门数量: " + ChatColor.WHITE + doorCount + " 个 (锁定: " + lockedDoors + " 个)");
            }
        }

        // 统计购买点数量
        if (results.get("buyPoints")) {
            ConfigurationSection buyPointsSection = config.getConfigurationSection("buyPoints");
            if (buyPointsSection != null) {
                int buyPointCount = buyPointsSection.getKeys(false).size();
                player.sendMessage(ChatColor.YELLOW + "购买点数量: " + ChatColor.WHITE + buyPointCount + " 个");
            }
        }

        // 统计幸运箱数量
        if (results.get("luckyBoxes")) {
            ConfigurationSection luckyBoxesSection = config.getConfigurationSection("luckyBoxes");
            if (luckyBoxesSection != null) {
                int luckyBoxCount = luckyBoxesSection.getKeys(false).size();
                player.sendMessage(ChatColor.YELLOW + "幸运箱数量: " + ChatColor.WHITE + luckyBoxCount + " 个");
            }
        }

        // 统计回合模式配置
        if (results.get("roundModes")) {
            ConfigurationSection roundModesSection = config.getConfigurationSection("roundModes");
            if (roundModesSection != null) {
                int configuredRounds = roundModesSection.getKeys(false).size();
                int totalRounds = config.getInt("rounds", 0);
                player.sendMessage(ChatColor.YELLOW + "回合模式配置: " + ChatColor.WHITE + configuredRounds + "/" + totalRounds + " 回合");
            }
        }

        // 电源按钮状态
        if (results.get("powerButton")) {
            boolean powerEnabled = false;
            int powerPrice = 0;

            if (config.contains("power_button")) {
                powerEnabled = !config.getBoolean("power_button.unlocked", true);
                powerPrice = config.getInt("power_button.price", 0);
            } else if (config.contains("powerButton")) {
                powerEnabled = config.getBoolean("powerButton.enabled", false);
            }

            String status = powerEnabled ? "需要解锁" : "已解锁";
            if (powerPrice > 0) {
                status += " (价格: " + powerPrice + ")";
            }
            player.sendMessage(ChatColor.YELLOW + "电源按钮状态: " + ChatColor.WHITE + status);
        }

        player.sendMessage(ChatColor.GRAY + "==========================================");
    }

    /**
     * 发送帮助信息，支持分页显示
     *
     * @param player 玩家
     * @param page 页码，默认为1
     */
    private void sendHelp(Player player, int page) {
        // 定义所有命令帮助
        List<String[]> helpCommands = new ArrayList<>();

        // 添加所有命令及其描述
        helpCommands.add(new String[]{"/dzs create <游戏名>", "创建新游戏"});
        helpCommands.add(new String[]{"/dzs setspawn <游戏名>", "设置玩家出生点"});
        helpCommands.add(new String[]{"/dzs setMaxPlayers <游戏名> <最大玩家数>", "设置最大玩家数"});
        helpCommands.add(new String[]{"/dzs edit <游戏名>", "编辑游戏"});
        helpCommands.add(new String[]{"/dzs setWindows <游戏名>", "设置窗户"});
        helpCommands.add(new String[]{"/dzs setround <游戏名> <回合数>", "设置回合数"});
        helpCommands.add(new String[]{"/dzs game <游戏名称> spawn <回合数> <出生点> <类型> <ID> <数量/random>", "设置回合出怪模式"});
        helpCommands.add(new String[]{"/dzs game <游戏名称> with <门名称> <出生点名称>", "关联门与出生点"});
        helpCommands.add(new String[]{"/dzs game <游戏名称> setcount <回合数>", "设置游戏回合数"});
        helpCommands.add(new String[]{"/dzs check <游戏名>", "检查游戏设置"});
        helpCommands.add(new String[]{"/dzs setdoor <游戏名> <门名称> <lock|unlock> [解锁价格]", "设置门"});
        helpCommands.add(new String[]{"/dzs unlock", "解锁门"});
        helpCommands.add(new String[]{"/dzs web [stop|port <端口>]", "管理WebUI"});
        helpCommands.add(new String[]{"/dzs setZombieSpawn <游戏名> <生成点名称> [僵尸类型]", "设置僵尸生成点"});
        helpCommands.add(new String[]{"/dzs with <游戏名称> <门名称> <怪物出生点名称>", "将门与出生点关联"});

        helpCommands.add(new String[]{"/dzs buy <游戏名称> <物品类型> <物品名称> [解锁金额]", "设置购买点（支持中文名称）"});
        helpCommands.add(new String[]{"  示例: /dzs buy 游戏1 武器 手枪", "使用中文类型和名称"});
        helpCommands.add(new String[]{"  兼容: /dzs buy 游戏1 wp_id1", "兼容旧格式"});
        helpCommands.add(new String[]{"/dzs power <游戏名称>", "设置全局电源按钮（破坏方块设置位置）"});
        helpCommands.add(new String[]{"/dzs kit <游戏名称> <槽位位置> <set|remove> [物品ID]", "管理初始装备"});
        helpCommands.add(new String[]{"/dzs enable <游戏名>", "启用游戏"});
        helpCommands.add(new String[]{"/dzs join [游戏名]", "加入游戏"});
        helpCommands.add(new String[]{"/dzs jump <游戏名>", "跳转到游戏"});
        helpCommands.add(new String[]{"/dzs setminplayer <游戏名> <最小玩家数>", "设置最小玩家数"});
        helpCommands.add(new String[]{"/dzs kitgui <游戏名>", "打开装备GUI"});
        helpCommands.add(new String[]{"/dzs reload", "重新加载插件"});
        helpCommands.add(new String[]{"/dzs remove <游戏名>", "删除游戏"});
        helpCommands.add(new String[]{"/dzs setwaittime <游戏名> <等待时间(秒)>", "设置游戏等待时间"});
        helpCommands.add(new String[]{"/dzs leave", "离开当前游戏"});
        helpCommands.add(new String[]{"/dzs rejoin (rej)", "重新连接到之前的游戏（玩家可用）"});
        helpCommands.add(new String[]{"/dzs lobby", "设置全局大厅位置，所有游戏结束后玩家都会被传送到此位置"});
        helpCommands.add(new String[]{"/dzs displaySettings <kill|Shoot|hit> <on|off>", "控制各种title显示设置（玩家可用）"});
        helpCommands.add(new String[]{"/dzs lucky <游戏名称> <Change|Add|Set|open|remove> <箱子名称> [参数A] [参数B]", "管理幸运箱"});
        helpCommands.add(new String[]{"/dzs cleanup <player|all> [玩家名称]", "清理幸运箱抽奖全息图"});
        helpCommands.add(new String[]{"/dzs clean [游戏名称]", "清理游戏（结束进行中的游戏并转为等待模式）"});
        helpCommands.add(new String[]{"/dzs testmoney <shoot|kill|debug>", "测试金钱获取方式和头部检测调试（管理员）"});
        helpCommands.add(new String[]{"/dzs top <统计类型> [地图名称] [查询范围]", "查看排行榜（玩家可用）"});
        helpCommands.add(new String[]{"  支持: games, kills, rounds, rescues, money, maprounds, maptime", "统计类型支持中英文"});
        helpCommands.add(new String[]{"/dzs topHolo <set|remove|list> [参数...]", "管理全息排行榜（管理员）"});
        helpCommands.add(new String[]{"  示例: /dzs topHolo set games TopGames 10", "创建全息排行榜"});
        helpCommands.add(new String[]{"  示例: /dzs topHolo list", "列出所有全息排行榜"});

        // 计算总页数
        int commandsPerPage = 8; // 每页显示的命令数
        int totalPages = (int) Math.ceil((double) helpCommands.size() / commandsPerPage);

        // 确保页码在有效范围内
        if (page < 1) {
            page = 1;
        }
        if (page > totalPages) {
            page = totalPages;
        }

        // 计算当前页的起始和结束索引
        int startIndex = (page - 1) * commandsPerPage;
        int endIndex = Math.min(startIndex + commandsPerPage, helpCommands.size());

        // 发送帮助标题
        player.sendMessage(ChatColor.GREEN + "=== DeathZombieV4 命令帮助 (第 " + page + "/" + totalPages + " 页) ===");

        // 发送当前页的命令帮助
        for (int i = startIndex; i < endIndex; i++) {
            String[] cmdHelp = helpCommands.get(i);
            player.sendMessage(ChatColor.YELLOW + cmdHelp[0] + " " + ChatColor.GRAY + "- " + cmdHelp[1]);
        }

        // 发送翻页提示
        player.sendMessage(ChatColor.GREEN + "使用 " + ChatColor.YELLOW + "/dzs help <页码> " + ChatColor.GREEN + "查看更多命令");
    }

    /**
     * 处理kitGUI命令
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @return 命令是否成功
     */
    private boolean handleKitGUICommand(Player player, String gameName) {
        // 检查游戏是否存在
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return true;
        }

        // 检查管理员权限
        if (!player.hasPermission("deathzombiev4.admin")) {
            player.sendMessage(ChatColor.RED + "你需要管理员权限来使用此指令！");
            return true;
        }

        try {
            // 获取KitEditorGUI实例并打开编辑器
            if (plugin.getKitEditorGUI() != null) {
                plugin.getKitEditorGUI().openKitEditor(player, gameName);
                player.sendMessage(ChatColor.GREEN + "已打开游戏 '" + gameName + "' 的初始装备编辑器！");
            } else {
                player.sendMessage(ChatColor.RED + "初始装备编辑器未初始化，请联系管理员！");
            }
        } catch (Exception e) {
            player.sendMessage(ChatColor.RED + "打开初始装备编辑器时发生错误：" + e.getMessage());
            plugin.getLogger().severe("打开KitGUI时发生错误: " + e.getMessage());
            e.printStackTrace();
        }

        return true;
    }

    // 获取本机IP地址
    private String getLocalIpAddress() {
        try {
            return java.net.InetAddress.getLocalHost().getHostAddress();
        } catch (java.net.UnknownHostException e) {
            return "127.0.0.1";
        }
    }

    private String getCurrentEditingGame(Player player) {
        return plugin.getPlayerInteractionManager().getEditingGame(player);
    }

    private boolean handleZombieSpawnMoreCommand(Player player, String spawnName, String attachmentId, String gameName) {
        if (!player.hasPermission("deathzombie.admin")) {
            player.sendMessage(ChatColor.RED + "你没有权限执行此操作！");
            return true;
        }

        // 验证额外出生点ID是否为有效格式，如数字
        try {
            Integer.parseInt(attachmentId);
        } catch (NumberFormatException e) {
            player.sendMessage(ChatColor.RED + "额外出生点ID必须是数字！");
            return true;
        }

        boolean result = plugin.getGameManager().setZombieSpawnAttachment(
                gameName, spawnName, attachmentId, player.getLocation());

        if (result) {
            player.sendMessage(ChatColor.GREEN + "已成功为生成点 " + spawnName
                    + " 设置额外出生点(ID: " + attachmentId + ")！位置: "
                    + String.format("%.2f, %.2f, %.2f",
                            player.getLocation().getX(),
                            player.getLocation().getY(),
                            player.getLocation().getZ()));
            return true;
        } else {
            player.sendMessage(ChatColor.RED + "设置失败！请确认游戏 " + gameName
                    + " 存在并且生成点 " + spawnName + " 已经设置。");
            return true;
        }
    }

    private boolean handleTeleportCommand(CommandSender sender, String gameNameArg) {
        // Implementation of handleTeleportCommand method
        return false; // Placeholder return, actual implementation needed
    }

    /**
     * 处理移除命令，移除游戏中的购买点、门或窗户
     *
     * @param player 执行命令的玩家
     * @param gameName 游戏名称
     * @param type 移除类型（shop、door或windows）
     * @return 命令执行结果
     */
    private boolean handleRemoveCommand(Player player, String gameName, String type) {
        // 检查游戏是否存在
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return true;
        }

        // 检查游戏是否正在运行
        if (plugin.getGameSessionManager().getGameState(gameName) == GameSessionManager.GameState.RUNNING) {
            player.sendMessage(ChatColor.RED + "游戏正在运行中，无法移除元素！");
            return true;
        }

        switch (type.toLowerCase()) {
            case "shop":
                return handleRemoveShop(player, gameName);
            case "door":
                return handleRemoveDoor(player, gameName);
            case "windows":
                return handleRemoveWindow(player, gameName);
            default:
                player.sendMessage(ChatColor.RED + "无效的移除类型！有效类型: shop, door, windows");
                return true;
        }
    }

    /**
     * 移除购买点
     *
     * @param player 执行命令的玩家
     * @param gameName 游戏名称
     * @return 命令执行结果
     */
    private boolean handleRemoveShop(Player player, String gameName) {
        // 获取游戏配置
        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (config == null || !config.isConfigurationSection("buyPoints")) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 没有购买点！");
            return true;
        }

        // 获取玩家位置
        Location playerLocation = player.getLocation();

        // 找到最近的购买点
        ConfigurationSection buyPointsSection = config.getConfigurationSection("buyPoints");
        String nearestBuyPointId = null;
        double minDistance = Double.MAX_VALUE;

        for (String buyPointId : buyPointsSection.getKeys(false)) {
            ConfigurationSection buyPointSection = buyPointsSection.getConfigurationSection(buyPointId);
            if (buyPointSection == null || !buyPointSection.isConfigurationSection("location")) {
                continue;
            }

            ConfigurationSection locSection = buyPointSection.getConfigurationSection("location");
            String worldName = locSection.getString("world");
            if (!player.getWorld().getName().equals(worldName)) {
                continue; // 跳过不同世界的购买点
            }

            double x = locSection.getDouble("x");
            double y = locSection.getDouble("y");
            double z = locSection.getDouble("z");

            Location buyPointLocation = new Location(player.getWorld(), x, y, z);
            double distance = playerLocation.distance(buyPointLocation);

            if (distance < minDistance && distance <= 5) { // 5格范围内
                minDistance = distance;
                nearestBuyPointId = buyPointId;
            }
        }

        if (nearestBuyPointId == null) {
            player.sendMessage(ChatColor.RED + "附近没有购买点！");
            return true;
        }

        // 获取购买点信息
        String itemName = buyPointsSection.getString(nearestBuyPointId + ".name", "未命名购买点");

        // 如果使用悬浮文字，先移除悬浮文字
        if (plugin.isDecentHologramsAvailable()) {
            // 获取悬浮文字ID
            Map<String, Map<String, String>> shopHolograms = plugin.getShopHologramManager().getShopHolograms();
            if (shopHolograms.containsKey(gameName) && shopHolograms.get(gameName).containsKey(nearestBuyPointId)) {
                String holoId = shopHolograms.get(gameName).get(nearestBuyPointId);
                plugin.getHologramHelper().removeHologram(holoId);
            }
        }

        // 从配置中移除购买点
        config.set("buyPoints." + nearestBuyPointId, null);

        try {
            // 保存配置
            config.save(new File(plugin.getDataFolder(), "game/" + gameName + ".yml"));
            player.sendMessage(ChatColor.GREEN + "成功移除购买点: " + ChatColor.GOLD + itemName);
            return true;
        } catch (IOException e) {
            player.sendMessage(ChatColor.RED + "移除购买点失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 移除门
     *
     * @param player 执行命令的玩家
     * @param gameName 游戏名称
     * @return 命令执行结果
     */
    private boolean handleRemoveDoor(Player player, String gameName) {
        // 获取游戏配置
        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (config == null || !config.isConfigurationSection("doors")) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 没有门！");
            return true;
        }

        // 获取玩家位置
        Location playerLocation = player.getLocation();

        // 找到最近的门
        ConfigurationSection doorsSection = config.getConfigurationSection("doors");
        String nearestDoorName = null;
        double minDistance = Double.MAX_VALUE;

        for (String doorName : doorsSection.getKeys(false)) {
            // 检查门是否有区域信息
            if (!doorsSection.isSet(doorName + ".region")) {
                continue;
            }

            String worldName = doorsSection.getString(doorName + ".region.world");
            if (!player.getWorld().getName().equals(worldName)) {
                continue; // 跳过不同世界的门
            }

            double minX = doorsSection.getDouble(doorName + ".region.minX");
            double minY = doorsSection.getDouble(doorName + ".region.minY");
            double minZ = doorsSection.getDouble(doorName + ".region.minZ");
            double maxX = doorsSection.getDouble(doorName + ".region.maxX");
            double maxY = doorsSection.getDouble(doorName + ".region.maxY");
            double maxZ = doorsSection.getDouble(doorName + ".region.maxZ");

            // 计算门的中心点
            double centerX = (minX + maxX) / 2;
            double centerY = (minY + maxY) / 2;
            double centerZ = (minZ + maxZ) / 2;

            Location doorLocation = new Location(player.getWorld(), centerX, centerY, centerZ);
            double distance = playerLocation.distance(doorLocation);

            if (distance < minDistance && distance <= 5) { // 5格范围内
                minDistance = distance;
                nearestDoorName = doorName;
            }
        }

        if (nearestDoorName == null) {
            player.sendMessage(ChatColor.RED + "附近没有门！");
            return true;
        }

        // 如果使用悬浮文字，先移除悬浮文字
        if (plugin.isDecentHologramsAvailable()) {
            // 移除与门相关的悬浮文字（如果有）
            plugin.getDoorHologramManager().removeDoorHologram(gameName, nearestDoorName);
        }

        // 从配置中移除门
        config.set("doors." + nearestDoorName, null);

        try {
            // 保存配置
            config.save(new File(plugin.getDataFolder(), "game/" + gameName + ".yml"));
            player.sendMessage(ChatColor.GREEN + "成功移除门: " + ChatColor.GOLD + nearestDoorName);
            return true;
        } catch (IOException e) {
            player.sendMessage(ChatColor.RED + "移除门失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 移除窗户
     *
     * @param player 执行命令的玩家
     * @param gameName 游戏名称
     * @return 命令执行结果
     */
    private boolean handleRemoveWindow(Player player, String gameName) {
        // 获取游戏配置
        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (config == null || !config.isConfigurationSection("windows")) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 没有窗户！");
            return true;
        }

        // 获取玩家位置
        Location playerLocation = player.getLocation();

        // 找到最近的窗户
        ConfigurationSection windowsSection = config.getConfigurationSection("windows");
        String nearestWindowName = null;
        double minDistance = Double.MAX_VALUE;

        for (String windowName : windowsSection.getKeys(false)) {
            String worldName = windowsSection.getString(windowName + ".world");
            if (!player.getWorld().getName().equals(worldName)) {
                continue; // 跳过不同世界的窗户
            }

            double minX = windowsSection.getDouble(windowName + ".minX");
            double minY = windowsSection.getDouble(windowName + ".minY");
            double minZ = windowsSection.getDouble(windowName + ".minZ");
            double maxX = windowsSection.getDouble(windowName + ".maxX");
            double maxY = windowsSection.getDouble(windowName + ".maxY");
            double maxZ = windowsSection.getDouble(windowName + ".maxZ");

            // 计算窗户的中心点
            double centerX = (minX + maxX) / 2;
            double centerY = (minY + maxY) / 2;
            double centerZ = (minZ + maxZ) / 2;

            Location windowLocation = new Location(player.getWorld(), centerX, centerY, centerZ);
            double distance = playerLocation.distance(windowLocation);

            if (distance < minDistance && distance <= 5) { // 5格范围内
                minDistance = distance;
                nearestWindowName = windowName;
            }
        }

        if (nearestWindowName == null) {
            player.sendMessage(ChatColor.RED + "附近没有窗户！");
            return true;
        }

        // 从配置中移除窗户
        config.set("windows." + nearestWindowName, null);

        try {
            // 保存配置
            config.save(new File(plugin.getDataFolder(), "game/" + gameName + ".yml"));
            player.sendMessage(ChatColor.GREEN + "成功移除窗户: " + ChatColor.GOLD + nearestWindowName);
            return true;
        } catch (IOException e) {
            player.sendMessage(ChatColor.RED + "移除窗户失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }



    /**
     * 处理WebUI命令（支持非玩家使用）
     *
     * @param sender 命令发送者
     * @param args 命令参数
     * @return 命令执行结果
     */
    private boolean handleWebCommandForAnyUser(CommandSender sender, String[] args) {
        // 检查权限（如果是玩家）
        if (sender instanceof Player) {
            Player player = (Player) sender;
            if (!player.hasPermission("deathzombie.admin")) {
                sender.sendMessage(ChatColor.RED + "你没有权限使用此命令！");
                return true;
            }
        }
        // 控制台和命令方块默认有权限

        if (args.length == 1) {
            if (webServer.isRunning()) {
                sender.sendMessage("§c[DeathZombieV4] WebUI服务器已在端口 " + webServer.getPort() + " 运行");
                sender.sendMessage("§e[DeathZombieV4] 本地访问地址: http://localhost:" + webServer.getPort());
                sender.sendMessage("§e[DeathZombieV4] 局域网访问地址: http://" + getLocalIpAddress() + ":" + webServer.getPort());
            } else {
                sender.sendMessage("§c[DeathZombieV4] WebUI服务器未运行，请使用 /dzs web <端口> [IP地址] 启动");
            }
            return true;
        } else if (args.length == 2) {
            try {
                int port = Integer.parseInt(args[1]);
                if (port < 1024 || port > 65535) {
                    sender.sendMessage("§c[DeathZombieV4] 端口号必须在1024-65535之间");
                    return true;
                }

                // 停止当前运行的服务器
                if (webServer.isRunning()) {
                    webServer.stop();
                }

                // 预加载所有游戏配置以便WebUI使用
                plugin.getLogger().info("正在预加载所有游戏配置...");
                plugin.getGameManager().loadAllGames();

                // 启动服务器
                boolean success = webServer.start(port);
                if (success) {
                    sender.sendMessage("§a[DeathZombieV4] 正在启动WebUI服务器, 端口: " + port);
                    sender.sendMessage("§e[DeathZombieV4] 本地访问地址: http://localhost:" + port);
                    sender.sendMessage("§e[DeathZombieV4] 局域网访问地址: http://" + getLocalIpAddress() + ":" + port);
                } else {
                    sender.sendMessage("§c[DeathZombieV4] 启动WebUI服务器失败");
                }
                return true;
            } catch (NumberFormatException e) {
                sender.sendMessage("§c[DeathZombieV4] 端口必须是数字");
                return true;
            }
        } else if (args.length == 3 && !args[1].equalsIgnoreCase("send")) {
            // 处理 /dzs web <端口> <IP地址> 命令
            try {
                int port = Integer.parseInt(args[1]);
                String host = args[2];

                if (port < 1024 || port > 65535) {
                    sender.sendMessage("§c[DeathZombieV4] 端口号必须在1024-65535之间");
                    return true;
                }

                // 停止当前运行的服务器
                if (webServer.isRunning()) {
                    webServer.stop();
                }

                // 预加载所有游戏配置以便WebUI使用
                plugin.getLogger().info("正在预加载所有游戏配置...");
                plugin.getGameManager().loadAllGames();

                // 启动服务器
                boolean success = webServer.start(port, host);
                if (success) {
                    sender.sendMessage("§a[DeathZombieV4] 正在启动WebUI服务器");
                    sender.sendMessage("§e[DeathZombieV4] 端口: " + port);
                    sender.sendMessage("§e[DeathZombieV4] 绑定地址: " + host);
                } else {
                    sender.sendMessage("§c[DeathZombieV4] 启动WebUI服务器失败");
                }
                return true;
            } catch (NumberFormatException e) {
                sender.sendMessage("§c[DeathZombieV4] 端口必须是数字");
                return true;
            }
        } else if (args.length == 3 && args[1].equalsIgnoreCase("send")) {
            String gameName = args[2];
            sender.sendMessage("§a[DeathZombieV4] 已将游戏 '" + gameName + "' 的信息发送, 等待Web界面获取");
            webServer.prepareGameData(gameName);
            return true;
        } else if (args.length == 2 && args[1].equalsIgnoreCase("reload")) {
            // 重新加载所有游戏配置
            plugin.getGameManager().loadAllGames();
            sender.sendMessage("§a[DeathZombieV4] 已重新加载所有游戏配置");
            return true;
        } else {
            sender.sendMessage("§c[DeathZombieV4] 用法: /dzs web [端口] [IP地址] | send <游戏名> | reload");
            sender.sendMessage("§e[DeathZombieV4] 示例:");
            sender.sendMessage("§e[DeathZombieV4]   /dzs web 8081 - 使用默认地址启动");
            sender.sendMessage("§e[DeathZombieV4]   /dzs web 8081 ************* - 绑定到指定IP");
            sender.sendMessage("§e[DeathZombieV4]   /dzs web 8081 0.0.0.0 - 绑定到所有网络接口");
            return true;
        }
    }

    /**
     * 处理WebUI命令（仅限玩家使用，保留向后兼容性）
     *
     * @param player 玩家
     * @param args 命令参数
     * @return 命令执行结果
     */
    private boolean handleWebCommand(Player player, String[] args) {
        if (args.length == 1) {
            if (webServer.isRunning()) {
                player.sendMessage("§c[DeathZombieV4] WebUI服务器已在端口 " + webServer.getPort() + " 运行");
                player.sendMessage("§e[DeathZombieV4] 本地访问地址: http://localhost:" + webServer.getPort());
                player.sendMessage("§e[DeathZombieV4] 局域网访问地址: http://" + getLocalIpAddress() + ":" + webServer.getPort());
            } else {
                player.sendMessage("§c[DeathZombieV4] WebUI服务器未运行，请使用 /dzs web <端口> [IP地址] 启动");
            }
            return true;
        } else if (args.length == 2) {
            try {
                int port = Integer.parseInt(args[1]);
                if (port < 1024 || port > 65535) {
                    player.sendMessage("§c[DeathZombieV4] 端口号必须在1024-65535之间");
                    return true;
                }

                // 停止当前运行的服务器
                if (webServer.isRunning()) {
                    webServer.stop();
                }

                // 预加载所有游戏配置以便WebUI使用
                plugin.getLogger().info("正在预加载所有游戏配置...");
                plugin.getGameManager().loadAllGames();

                // 启动服务器
                boolean success = webServer.start(port);
                if (success) {
                    player.sendMessage("§a[DeathZombieV4] 正在启动WebUI服务器, 端口: " + port);
                    player.sendMessage("§e[DeathZombieV4] 本地访问地址: http://localhost:" + port);
                    player.sendMessage("§e[DeathZombieV4] 局域网访问地址: http://" + getLocalIpAddress() + ":" + port);
                } else {
                    player.sendMessage("§c[DeathZombieV4] 启动WebUI服务器失败");
                }
                return true;
            } catch (NumberFormatException e) {
                player.sendMessage("§c[DeathZombieV4] 端口必须是数字");
                return true;
            }
        } else if (args.length == 3 && !args[1].equalsIgnoreCase("send")) {
            // 处理 /dzs web <端口> <IP地址> 命令
            try {
                int port = Integer.parseInt(args[1]);
                String host = args[2];

                if (port < 1024 || port > 65535) {
                    player.sendMessage("§c[DeathZombieV4] 端口号必须在1024-65535之间");
                    return true;
                }

                // 停止当前运行的服务器
                if (webServer.isRunning()) {
                    webServer.stop();
                }

                // 预加载所有游戏配置以便WebUI使用
                plugin.getLogger().info("正在预加载所有游戏配置...");
                plugin.getGameManager().loadAllGames();

                // 启动服务器
                boolean success = webServer.start(port, host);
                if (success) {
                    player.sendMessage("§a[DeathZombieV4] 正在启动WebUI服务器");
                    player.sendMessage("§e[DeathZombieV4] 端口: " + port);
                    player.sendMessage("§e[DeathZombieV4] 绑定地址: " + host);
                } else {
                    player.sendMessage("§c[DeathZombieV4] 启动WebUI服务器失败");
                }
                return true;
            } catch (NumberFormatException e) {
                player.sendMessage("§c[DeathZombieV4] 端口必须是数字");
                return true;
            }
        } else if (args.length == 3 && args[1].equalsIgnoreCase("send")) {
            String gameName = args[2];
            player.sendMessage("§a[DeathZombieV4] 已将游戏 '" + gameName + "' 的信息发送, 等待Web界面获取");
            webServer.prepareGameData(gameName);
            return true;
        } else if (args.length == 2 && args[1].equalsIgnoreCase("reload")) {
            // 重新加载所有游戏配置
            plugin.getGameManager().loadAllGames();
            player.sendMessage("§a[DeathZombieV4] 已重新加载所有游戏配置");
            return true;
        } else {
            player.sendMessage("§c[DeathZombieV4] 用法: /dzs web [端口] [IP地址] | send <游戏名> | reload");
            player.sendMessage("§e[DeathZombieV4] 示例:");
            player.sendMessage("§e[DeathZombieV4]   /dzs web 8081 - 使用默认地址启动");
            player.sendMessage("§e[DeathZombieV4]   /dzs web 8081 ************* - 绑定到指定IP");
            player.sendMessage("§e[DeathZombieV4]   /dzs web 8081 0.0.0.0 - 绑定到所有网络接口");
            return true;
        }
    }

    /**
     * 处理 Buy 命令，设置购买点
     *
     * @param player 执行命令的玩家
     * @param gameName 游戏名称
     * @param typeAndId 物品类型和ID（例如 all, ar_id1, wp_id2）
     * @param price 解锁金额（只在typeAndId为"all"时使用）
     * @return 命令执行结果
     */
    private boolean handleBuyCommand(Player player, String gameName, String typeAndId, int price) {
        plugin.getLogger().info("执行Buy命令: gameName=" + gameName + ", typeAndId=" + typeAndId + ", price=" + price);

        // 检查游戏是否存在
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 " + ChatColor.YELLOW + gameName + ChatColor.RED + " 不存在！");
            return true;
        }

        // 如果是全局电源按钮
        if (typeAndId.equalsIgnoreCase("all")) {
            player.sendMessage(ChatColor.GREEN + "请破坏一个方块来设置全局电源按钮位置");
            player.sendMessage(ChatColor.YELLOW + "这个按钮将作为全局电源按钮，只有解锁后玩家才能购买sp类型的全体增益效果");

            // 设置玩家正在编辑的游戏和特殊标记
            plugin.getPlayerInteractionManager().setEditingGame(player, gameName);
            plugin.getPlayerInteractionManager().setPlayerData(player, "setting_power_button", "true");
            plugin.getPlayerInteractionManager().setPlayerData(player, "power_button_price", String.valueOf(price));

            return true;
        }

        // 检查是否使用悬浮文字
        if (plugin.isDecentHologramsAvailable()) {
            // 使用悬浮文字创建购买点
            return plugin.getShopHologramManager().addBuyPoint(player, typeAndId, gameName);
        } else {
            // 使用传统NPC创建购买点
            return handleBuyCommandWithNPC(player, typeAndId, gameName);
        }
    }

    /**
     * 使用传统NPC处理Buy命令
     *
     * @param player 执行命令的玩家
     * @param typeAndId 物品类型和ID（例如 ar_id1, wp_id2）
     * @param gameName 游戏名称
     * @return 命令执行结果
     */
    private boolean handleBuyCommandWithNPC(Player player, String typeAndId, String gameName) {
        // 检查游戏是否存在
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 " + ChatColor.YELLOW + gameName + ChatColor.RED + " 不存在！");
            return true;
        }

        // 解析物品类型和ID
        String[] parts = typeAndId.split("_", 2); // 限制分割为最多2部分
        if (parts.length != 2) {
            player.sendMessage(ChatColor.RED + "物品格式错误！正确格式: 类型_ID (例如: ar_id1, wp_id2)");
            return true;
        }

        String type = parts[0].toLowerCase(); // 转为小写
        String itemId = parts[1];

        // 如果物品ID不是以"id"开头，则自动添加
        if (!itemId.startsWith("id")) {
            if (Character.isDigit(itemId.charAt(0))) {
                itemId = "id" + itemId;
                plugin.getLogger().info("自动修正物品ID: " + itemId);
            }
        }

        plugin.getLogger().info("解析后的物品类型和ID: type=" + type + ", itemId=" + itemId);

        // 验证类型是否有效
        if (!type.equals("ar") && !type.equals("wp") && !type.equals("it") && !type.equals("sp")) {
            player.sendMessage(ChatColor.RED + "无效的物品类型！有效类型: ar(护甲), wp(武器), it(道具), sp(特殊功能)");
            return true;
        }

        // 获取Shoot插件帮助类
        org.Ver_zhzh.deathZombieV4.utils.ShootPluginHelper shootHelper = plugin.getShootPluginHelper();

        // 检查物品是否存在
        if (!shootHelper.itemExists(type, itemId)) {
            player.sendMessage(ChatColor.RED + "物品 " + type + "_" + itemId + " 在Shoot插件中不存在！");
            plugin.getLogger().warning("物品不存在: type=" + type + ", itemId=" + itemId);
            player.sendMessage(ChatColor.YELLOW + "请确认此物品在Shoot插件的Buy.yml中定义。");
            return true;
        }

        // 获取玩家当前位置
        Location location = player.getLocation();

        // 生成NPC名称
        String itemName = shootHelper.getItemName(type, itemId);
        if (itemName == null) {
            itemName = type + "_" + itemId;
        }
        String npcName = "购买 " + itemName;

        // 创建NPC
        net.citizensnpcs.api.npc.NPC npc = shootHelper.createBuyPointNPC(location, npcName, type, itemId);
        if (npc == null) {
            player.sendMessage(ChatColor.RED + "创建购买点NPC失败！");
            return true;
        }

        // 更新游戏配置
        File gameFile = new File(plugin.getDataFolder(), "game/" + gameName + ".yml");
        if (!gameFile.exists()) {
            player.sendMessage(ChatColor.RED + "游戏配置文件不存在！路径: " + gameFile.getAbsolutePath());
            return true;
        }

        FileConfiguration gameConfig = YamlConfiguration.loadConfiguration(gameFile);

        // 设置购买点信息
        String npcUUID = npc.getUniqueId().toString();
        ConfigurationSection buyPointsSection = gameConfig.getConfigurationSection("buyPoints");
        if (buyPointsSection == null) {
            buyPointsSection = gameConfig.createSection("buyPoints");
        }

        ConfigurationSection buyPointSection = buyPointsSection.createSection(npcUUID);
        buyPointSection.set("type", type);
        buyPointSection.set("itemId", itemId);
        buyPointSection.set("name", itemName);
        buyPointSection.set("location.world", location.getWorld().getName());
        buyPointSection.set("location.x", location.getX());
        buyPointSection.set("location.y", location.getY());
        buyPointSection.set("location.z", location.getZ());
        buyPointSection.set("location.yaw", location.getYaw());
        buyPointSection.set("location.pitch", location.getPitch());

        try {
            gameConfig.save(gameFile);
            player.sendMessage(ChatColor.GREEN + "成功创建购买点 " + ChatColor.GOLD + itemName + ChatColor.GREEN + " 在游戏 " + ChatColor.YELLOW + gameName + ChatColor.GREEN + " 中！");
        } catch (IOException e) {
            player.sendMessage(ChatColor.RED + "保存游戏配置时出错：" + e.getMessage());
            e.printStackTrace();
        }

        return true;
    }

    /**
     * 处理使用中文名称的Buy命令
     *
     * @param player 执行命令的玩家
     * @param gameName 游戏名称
     * @param itemType 物品类型（中文或英文）
     * @param itemName 物品名称（中文）
     * @param price 解锁金额
     * @return 命令执行结果
     */
    private boolean handleBuyCommandWithName(Player player, String gameName, String itemType, String itemName, int price) {
        plugin.getLogger().info("执行中文Buy命令: gameName=" + gameName + ", itemType=" + itemType + ", itemName=" + itemName + ", price=" + price);

        // 检查游戏是否存在
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 " + ChatColor.YELLOW + gameName + ChatColor.RED + " 不存在！");
            return true;
        }

        // 转换中文物品类型到英文缩写
        String type = convertItemTypeToEnglish(itemType);
        if (type == null) {
            player.sendMessage(ChatColor.RED + "无效的物品类型！有效类型: 护甲(ar), 武器(wp), 道具(it), 特殊功能(sp)");
            return true;
        }

        // 获取Shoot插件帮助类
        org.Ver_zhzh.deathZombieV4.utils.ShootPluginHelper shootHelper = plugin.getShootPluginHelper();

        // 根据中文名称查找物品ID
        String itemId = shootHelper.getItemIdByName(type, itemName);
        if (itemId == null) {
            player.sendMessage(ChatColor.RED + "未找到物品 '" + itemName + "' 在类型 '" + itemType + "' 中！");
            player.sendMessage(ChatColor.YELLOW + "请检查物品名称是否正确，或使用Tab补全查看可用物品。");
            return true;
        }

        // 检查物品是否存在
        if (!shootHelper.itemExists(type, itemId)) {
            player.sendMessage(ChatColor.RED + "物品 " + type + "_" + itemId + " 在Shoot插件中不存在！");
            plugin.getLogger().warning("物品不存在: type=" + type + ", itemId=" + itemId);
            return true;
        }

        // 检查是否使用悬浮文字
        if (plugin.isDecentHologramsAvailable()) {
            // 使用悬浮文字创建购买点
            String typeAndId = type + "_" + itemId;
            return plugin.getShopHologramManager().addBuyPoint(player, typeAndId, gameName);
        } else {
            // 使用传统NPC创建购买点
            return createBuyPointNPC(player, gameName, type, itemId, itemName);
        }
    }

    /**
     * 转换中文物品类型到英文缩写
     *
     * @param itemType 物品类型（中文或英文）
     * @return 英文缩写，如果无效则返回null
     */
    private String convertItemTypeToEnglish(String itemType) {
        if (itemType == null) return null;

        String lowerType = itemType.toLowerCase();
        switch (lowerType) {
            case "护甲":
            case "armor":
            case "ar":
                return "ar";
            case "武器":
            case "weapon":
            case "wp":
                return "wp";
            case "道具":
            case "item":
            case "it":
                return "it";
            case "特殊功能":
            case "特殊":
            case "special":
            case "sp":
                return "sp";
            default:
                return null;
        }
    }

    /**
     * 创建购买点NPC（从handleBuyCommandWithNPC方法提取的通用逻辑）
     *
     * @param player 执行命令的玩家
     * @param gameName 游戏名称
     * @param type 物品类型
     * @param itemId 物品ID
     * @param itemName 物品名称
     * @return 命令执行结果
     */
    private boolean createBuyPointNPC(Player player, String gameName, String type, String itemId, String itemName) {
        // 获取玩家当前位置
        Location location = player.getLocation();

        // 获取Shoot插件帮助类
        org.Ver_zhzh.deathZombieV4.utils.ShootPluginHelper shootHelper = plugin.getShootPluginHelper();

        // 如果没有提供物品名称，从Shoot插件获取
        if (itemName == null) {
            itemName = shootHelper.getItemName(type, itemId);
            if (itemName == null) {
                itemName = type + "_" + itemId;
            }
        }
        String npcName = "购买 " + itemName;

        // 创建NPC
        net.citizensnpcs.api.npc.NPC npc = shootHelper.createBuyPointNPC(location, npcName, type, itemId);
        if (npc == null) {
            player.sendMessage(ChatColor.RED + "创建购买点NPC失败！");
            return true;
        }

        // 更新游戏配置
        File gameFile = new File(plugin.getDataFolder(), "game/" + gameName + ".yml");
        if (!gameFile.exists()) {
            player.sendMessage(ChatColor.RED + "游戏配置文件不存在！路径: " + gameFile.getAbsolutePath());
            return true;
        }

        FileConfiguration gameConfig = YamlConfiguration.loadConfiguration(gameFile);

        // 设置购买点信息
        String npcUUID = npc.getUniqueId().toString();
        ConfigurationSection buyPointsSection = gameConfig.getConfigurationSection("buyPoints");
        if (buyPointsSection == null) {
            buyPointsSection = gameConfig.createSection("buyPoints");
        }

        ConfigurationSection buyPointSection = buyPointsSection.createSection(npcUUID);
        buyPointSection.set("type", type);
        buyPointSection.set("itemId", itemId);
        buyPointSection.set("name", itemName);
        buyPointSection.set("location.world", location.getWorld().getName());
        buyPointSection.set("location.x", location.getX());
        buyPointSection.set("location.y", location.getY());
        buyPointSection.set("location.z", location.getZ());
        buyPointSection.set("location.yaw", location.getYaw());
        buyPointSection.set("location.pitch", location.getPitch());

        try {
            gameConfig.save(gameFile);
            player.sendMessage(ChatColor.GREEN + "成功创建购买点 " + ChatColor.GOLD + itemName + ChatColor.GREEN + " 在游戏 " + ChatColor.YELLOW + gameName + ChatColor.GREEN + " 中！");
        } catch (IOException e) {
            player.sendMessage(ChatColor.RED + "保存游戏配置时出错：" + e.getMessage());
            e.printStackTrace();
        }

        return true;
    }

    /**
     * 处理enable命令，启用游戏
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @return 操作是否成功
     */
    private boolean handleEnableCommand(Player player, String gameName) {
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 " + gameName + " 不存在！");
            return true;
        }

        // 先检查游戏配置是否完整
        Map<String, Boolean> configStatus = gameManager.checkGameConfig(gameName);
        boolean allConfigured = true;
        StringBuilder missingConfig = new StringBuilder();

        for (Map.Entry<String, Boolean> entry : configStatus.entrySet()) {
            if (!entry.getValue()) {
                allConfigured = false;
                missingConfig.append("\n- ").append(entry.getKey());
            }
        }

        if (!allConfigured) {
            player.sendMessage(ChatColor.RED + "游戏 " + gameName + " 的配置不完整，无法启用！"
                    + ChatColor.YELLOW + "缺少以下配置:" + missingConfig.toString());
            return true;
        } // 启用游戏
        boolean result = gameManager.setGameEnabled(gameName, true);

        if (result) {
            player.sendMessage(ChatColor.GREEN + "游戏 " + gameName + " 已成功启用！玩家现在可以使用 /dzs join 命令加入游戏。");

            // 如果游戏会话不存在，创建一个新的
            if (plugin.getGameSessionManager().getGameState(gameName) == null) {
                plugin.getGameSessionManager().createGame(gameName);
                player.sendMessage(ChatColor.GREEN + "已为 " + gameName + " 创建游戏会话。");
            }
        } else {
            player.sendMessage(ChatColor.RED + "无法启用游戏 " + gameName + "！");
        }

        return true;
    }

    /**
     * 处理join命令，加入游戏
     *
     * @param player 玩家
     * @param gameName 游戏名称，如果为null则自动选择人数最多的游戏
     * @return 操作是否成功
     */
    private boolean handleJoinCommand(Player player, String gameName) {
        // 记录调试信息
        plugin.getLogger().info("玩家 " + player.getName() + " 尝试加入游戏: " + gameName);

        // 如果玩家已经在游戏中，提示玩家
        if (plugin.getGameSessionManager().isPlayerInGame(player)) {
            String currentGame = plugin.getGameSessionManager().getPlayerGame(player);
            player.sendMessage(ChatColor.YELLOW + "你已经在游戏 " + currentGame + " 中！");
            return true;
        }

        // 如果未指定游戏名称，自动选择人数最多的启用游戏
        if (gameName == null) {
            gameName = gameManager.getMostPopulatedGame();

            if (gameName == null) {
                player.sendMessage(ChatColor.RED + "没有可用的游戏！请联系管理员启用游戏。");
                return true;
            }

            player.sendMessage(ChatColor.YELLOW + "自动加入人数最多的游戏: " + gameName);
        }

        // 检查游戏是否存在且已启用
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 " + gameName + " 不存在！");
            plugin.getLogger().warning("玩家尝试加入不存在的游戏: " + gameName);
            return true;
        }

        if (!gameManager.isGameEnabled(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 " + gameName + " 尚未启用！");
            plugin.getLogger().warning("玩家尝试加入未启用的游戏: " + gameName);
            return true;
        } // 尝试加入游戏
        boolean result = plugin.getGameSessionManager().addPlayerToGame(player, gameName);

        if (result) {
            player.sendMessage(ChatColor.GREEN + "你已成功加入游戏 " + gameName + "！");

            // 传送玩家到游戏出生点
            FileConfiguration config = gameManager.getGameConfig(gameName);
            if (config != null && config.contains("spawn")) {
                String worldName = config.getString("spawn.world");
                double x = config.getDouble("spawn.x");
                double y = config.getDouble("spawn.y");
                double z = config.getDouble("spawn.z");
                float yaw = (float) config.getDouble("spawn.yaw");
                float pitch = (float) config.getDouble("spawn.pitch");

                World world = plugin.getServer().getWorld(worldName);
                if (world != null) {
                    Location spawnLoc = new Location(world, x, y, z, yaw, pitch);
                    player.teleport(spawnLoc);
                    player.sendMessage(ChatColor.GREEN + "已传送到游戏出生点！");
                } else {
                    plugin.getLogger().warning("游戏 " + gameName + " 的世界 " + worldName + " 不存在！");
                }
            } else {
                plugin.getLogger().warning("游戏 " + gameName + " 没有设置出生点！");
            }

            // 广播玩家加入消息
            plugin.getServer().broadcastMessage(ChatColor.YELLOW + player.getName() + " 加入了游戏 " + gameName + "！"
                    + ChatColor.GRAY + "(" + plugin.getGameSessionManager().getPlayerCount(gameName) + " 人)");
        } else {
            player.sendMessage(ChatColor.RED + "无法加入游戏 " + gameName + "！");
            plugin.getLogger().warning("玩家 " + player.getName() + " 无法加入游戏 " + gameName + "，可能是游戏状态不正确或人数已满");
        }

        return true;
    }

    /**
     * 处理jump命令 - 强制开始游戏（管理员测试用）
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @return 是否成功处理
     */
    private boolean handleJumpCommand(Player player, String gameName) {
        // 检查游戏是否存在
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return true;
        }

        // 检查游戏是否已启用
        if (!gameManager.isGameEnabled(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 未启用！请先使用 /dzs enable " + gameName + " 启用游戏。");
            return true;
        }

        // 获取游戏会话管理器
        GameSessionManager gameSessionManager = plugin.getGameSessionManager();

        // 检查游戏状态
        GameSessionManager.GameState state = gameSessionManager.getGameState(gameName);
        if (state == null) {
            // 游戏会话不存在，创建一个新会话
            gameSessionManager.createGame(gameName);
        } else if (state != GameSessionManager.GameState.WAITING) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 已经开始或已结束！");
            return true;
        }

        // 将玩家加入到游戏中（如果还没有加入）
        if (!gameSessionManager.isPlayerInGame(player, gameName)) {
            gameSessionManager.addPlayerToGame(player, gameName);
        }

        // 强制开始游戏
        if (gameSessionManager.startGame(gameName)) {
            player.sendMessage(ChatColor.GREEN + "已强制开始游戏 '" + gameName + "'！");

            // 广播消息给所有玩家
            for (Player p : Bukkit.getOnlinePlayers()) {
                if (p != player && gameSessionManager.isPlayerInGame(p, gameName)) {
                    p.sendMessage(ChatColor.YELLOW + "管理员 " + player.getName() + " 强制开始了游戏 '" + gameName + "'！");
                }
            }
        } else {
            player.sendMessage(ChatColor.RED + "强制开始游戏 '" + gameName + "' 失败！请检查游戏配置是否完整。");
        }

        return true;
    }

    /**
     * 处理setminplayer命令 - 设置游戏的最小玩家数量
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param minPlayers 最小玩家数量
     * @return 是否成功处理
     */
    private boolean handleSetMinPlayersCommand(Player player, String gameName, int minPlayers) {
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return true;
        }

        if (minPlayers < 1) {
            player.sendMessage(ChatColor.RED + "最小玩家数量不能小于1！");
            return true;
        }

        int maxPlayers = gameManager.getGameConfig(gameName).getInt("maxPlayers", 10);
        if (minPlayers > maxPlayers) {
            player.sendMessage(ChatColor.YELLOW + "警告：最小玩家数量(" + minPlayers + ")大于最大玩家数量(" + maxPlayers + ")，将设置为 " + maxPlayers);
            minPlayers = maxPlayers;
        }

        if (gameManager.setMinPlayers(gameName, minPlayers)) {
            player.sendMessage(ChatColor.GREEN + "成功设置游戏 '" + gameName + "' 的最小玩家数量为 " + minPlayers);
        } else {
            player.sendMessage(ChatColor.RED + "设置游戏 '" + gameName + "' 的最小玩家数量失败！");
        }

        return true;
    }

    /**
     * 处理stop命令，强制结束游戏并清理资源
     *
     * @param player 执行命令的玩家
     * @param gameName 游戏名称
     * @return 命令执行结果
     */
    private boolean handleStopCommand(Player player, String gameName) {
        // 检查游戏是否存在
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return true;
        }

        // 获取游戏会话管理器
        GameSessionManager gameSessionManager = plugin.getGameSessionManager();

        // 检查游戏状态
        GameSessionManager.GameState state = gameSessionManager.getGameState(gameName);
        if (state == null) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不在进行中！");
            return true;
        } // 记录游戏启用状态
        boolean isGameEnabled = gameManager.isGameEnabled(gameName);

        // 强制结束游戏并清理
        player.sendMessage(ChatColor.YELLOW + "正在强制结束游戏 '" + gameName + "' 并清理资源...");

        // 使用ZombieSpawnManager的endGameAndCleanup方法结束游戏并清理
        plugin.getZombieSpawnManager().endGameAndCleanup(gameName);

        player.sendMessage(ChatColor.GREEN + "游戏 '" + gameName + "' 已成功结束！");

        // 如果游戏仍然启用，告知玩家游戏将重新初始化
        if (isGameEnabled) {
            // 延迟2tick发送消息，确保重置过程完成
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                // 再次检查游戏状态
                GameSessionManager.GameState newState = gameSessionManager.getGameState(gameName);
                if (newState == GameSessionManager.GameState.WAITING) {
                    // 游戏已经成功重置
                    player.sendMessage(ChatColor.GREEN + "游戏 '" + gameName + "' 已重新初始化，现在处于等待玩家加入状态！");

                    // 通知所有在线玩家
                    for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                        if (player != onlinePlayer) {  // 避免给命令执行者发送重复消息
                            onlinePlayer.sendMessage(ChatColor.YELLOW + "管理员已强制结束游戏 '" + gameName + "'");
                            onlinePlayer.sendMessage(ChatColor.GREEN + "游戏 '" + gameName + "' 已重新初始化，现在可以再次加入！");
                        }
                    }
                } else {
                    // 如果游戏状态不正确，尝试手动重置
                    player.sendMessage(ChatColor.YELLOW + "正在尝试手动重置游戏状态...");

                    // 使用新的resetGame方法
                    boolean success = gameSessionManager.resetGame(gameName);
                    if (success) {
                        player.sendMessage(ChatColor.GREEN + "游戏 '" + gameName + "' 已手动重置，现在可以再次加入！");

                        // 通知所有在线玩家
                        for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                            if (player != onlinePlayer) {  // 避免给命令执行者发送重复消息
                                onlinePlayer.sendMessage(ChatColor.YELLOW + "管理员已强制结束并重置游戏 '" + gameName + "'");
                                onlinePlayer.sendMessage(ChatColor.GREEN + "游戏 '" + gameName + "' 已重新初始化，现在可以再次加入！");
                            }
                        }
                    } else {
                        player.sendMessage(ChatColor.RED + "无法重置游戏状态。请联系服务器管理员检查日志。");
                    }
                }
            }, 2L);
        } else {
            // 通知所有在线玩家
            for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                if (player != onlinePlayer) {  // 避免给命令执行者发送重复消息
                    onlinePlayer.sendMessage(ChatColor.YELLOW + "管理员已强制结束游戏 '" + gameName + "'");
                }
            }
        }

        return true;
    }

    /**
     * 公共方法，用于暴露测试命令功能给其他类调用
     *
     * @param player 执行生成的玩家
     * @param round 回合数
     * @param spawnName 生成点名称，可以为空
     * @param gameName 游戏名称，不能为空
     * @return 是否成功执行
     */
    public boolean handleTestSpawns(Player player, int round, String spawnName, String gameName) {
        if (player == null || gameName == null) {
            plugin.getLogger().severe("handleTestSpawns调用失败：玩家或游戏名称为空");
            return false;
        }

        plugin.getLogger().info("通过API调用测试命令：游戏=" + gameName + ", 回合=" + round + ", 生成点=" + (spawnName != null ? spawnName : "所有"));

        try {
            // 检查游戏是否存在
            if (!gameManager.gameExists(gameName)) {
                plugin.getLogger().warning("游戏 '" + gameName + "' 不存在");
                return false;
            }

            // 检查ZombieHelper实例状态
            ZombieHelper zombieHelper = plugin.getZombieHelper();
            if (zombieHelper == null || !zombieHelper.isCustomZombieAvailable()) {
                plugin.getLogger().severe("ZombieHelper 实例未初始化或CustomZombie不可用，无法生成怪物！");
                return false;
            }

            // 获取游戏配置
            Map<String, Map<String, Object>> roundModesMap = null;

            // 尝试从游戏配置中读取回合模式
            FileConfiguration gameConfig = YamlConfiguration.loadConfiguration(new File(plugin.getDataFolder(), "game/" + gameName + ".yml"));
            if (gameConfig.contains("roundModes.round" + round)) {
                plugin.getLogger().info("获取游戏 '" + gameName + "' 第 " + round + " 回合的怪物生成模式");
                roundModesMap = gameManager.getRoundModes(gameName, round);
            }

            if (roundModesMap == null || roundModesMap.isEmpty()) {
                plugin.getLogger().warning("警告: 无法找到第 " + round + " 回合的配置，使用默认配置");
                // 创建默认配置用于测试
                roundModesMap = new HashMap<>();
                Map<String, Object> defaultSpawnConfig = new HashMap<>();
                defaultSpawnConfig.put("monsterType", "zombie");
                defaultSpawnConfig.put("monsterId", "id1");
                defaultSpawnConfig.put("count", "5");

                roundModesMap.put("default", defaultSpawnConfig);
            }

            // 获取指定出生点的配置
            // 检查是否是组合键（生成点_刷怪逻辑）
            boolean foundConfig = false;
            Map<String, Object> spawnConfig = null;
            String actualSpawnName = spawnName;

            if (spawnName != null) {
                // 直接匹配
                if (roundModesMap.containsKey(spawnName)) {
                    spawnConfig = roundModesMap.get(spawnName);
                    foundConfig = true;
                    plugin.getLogger().info("直接找到生成点配置: " + spawnName);
                } else {
                    // 尝试查找匹配的组合键
                    for (String key : roundModesMap.keySet()) {
                        if (key.startsWith(spawnName + "_")) {
                            spawnConfig = roundModesMap.get(key);
                            foundConfig = true;
                            actualSpawnName = spawnName; // 保留原始生成点名称用于获取坐标
                            plugin.getLogger().info("找到生成点的刷怪逻辑配置: " + key);
                            break;
                        }
                    }
                }
            }

            if (foundConfig) {
                // 尝试从游戏配置中获取出生点坐标
                Location spawnLocation = gameManager.getZombieSpawnLocation(gameName, actualSpawnName);
                if (spawnLocation == null) {
                    // 如果找不到出生点位置，使用玩家位置
                    spawnLocation = player.getLocation();
                    plugin.getLogger().warning("无法找到出生点 '" + actualSpawnName + "' 的位置，使用玩家位置");
                }

                // 添加坐标信息到配置中
                spawnConfig.put("spawnLocation", spawnLocation);
                spawnConfig.put("spawnName", actualSpawnName);

                // 生成该出生点的怪物
                testSpawnMonsters(player, new MemoryConfiguration().createSection("test", spawnConfig), gameName);
            } else {
                // 测试所有出生点
                if (spawnName != null) {
                    plugin.getLogger().warning("找不到出生点 '" + spawnName + "'，将测试所有出生点");
                }

                // 处理所有生成点配置
                for (Map.Entry<String, Map<String, Object>> entry : roundModesMap.entrySet()) {
                    String configKey = entry.getKey();
                    Map<String, Object> configValue = entry.getValue();

                    // 确定实际的生成点名称
                    String spawnPointName = configKey;
                    // 如果是组合键（生成点_刷怪逻辑），提取生成点名称
                    if (configKey.contains("_")) {
                        spawnPointName = configKey.substring(0, configKey.indexOf("_"));
                    }

                    // 尝试从游戏配置中获取出生点坐标
                    Location spawnLocation = gameManager.getZombieSpawnLocation(gameName, spawnPointName);

                    if (spawnLocation == null) {
                        // 如果找不到出生点位置，使用玩家位置加随机偏移
                        spawnLocation = player.getLocation().clone();
                        // 添加随机偏移避免实体堆叠
                        Random random = new Random();
                        double offsetX = (random.nextDouble() - 0.5) * 5;
                        double offsetZ = (random.nextDouble() - 0.5) * 5;
                        spawnLocation.add(offsetX, 0, offsetZ);

                        plugin.getLogger().warning("无法找到出生点 '" + spawnPointName + "' 的位置，使用玩家位置加随机偏移");
                    }

                    // 创建配置副本，避免修改原始配置
                    Map<String, Object> configCopy = new HashMap<>(configValue);

                    // 添加坐标信息到配置中
                    configCopy.put("spawnLocation", spawnLocation);
                    configCopy.put("spawnName", spawnPointName);

                    plugin.getLogger().info("生成点 '" + configKey + "' 配置: " + formatConfigInfo(configCopy));

                    // 生成怪物
                    testSpawnMonsters(player, new MemoryConfiguration().createSection("test", configCopy), gameName);
                }
            }

            return true;
        } catch (Exception e) {
            plugin.getLogger().severe("测试命令API执行失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            // 补全主命令
            String partialCommand = args[0].toLowerCase();
            for (String cmd : COMMANDS) {
                if (cmd.startsWith(partialCommand)) {
                    completions.add(cmd);
                }
            }
        } else if (args.length == 2) {
            // 补全第二个参数（通常是游戏名称）
            String subCommand = args[0].toLowerCase();
            if (subCommand.equals("buy")) {
                // 补全物品类型和ID
                String partialItem = args[1].toLowerCase();
                for (String item : ITEMS) {
                    if (item.startsWith(partialItem)) {
                        completions.add(item);
                    }
                }
            } else if (subCommand.equals("enable") || subCommand.equals("join") || subCommand.equals("jump")
                    || subCommand.equals("stop") || subCommand.equals("kit") || subCommand.equals("kitgui")
                    || subCommand.equals("buff") || subCommand.equals("SetWaitTime") || subCommand.equals("lucky")
                    || subCommand.equals("clean")) {
                // 补全游戏名称
                List<String> games = new ArrayList<>(gameManager.getGameNames());
                for (String game : games) {
                    if (game.startsWith(args[1].toLowerCase())) {
                        completions.add(game);
                    }
                }
            } else if (subCommand.equals("cleanup")) {
                // 补全cleanup命令的操作类型
                String partialAction = args[1].toLowerCase();
                List<String> actions = Arrays.asList("player", "all");
                for (String action : actions) {
                    if (action.startsWith(partialAction)) {
                        completions.add(action);
                    }
                }
            } else if (subCommand.equals("top")) {
                // 补全top命令的统计类型
                String partialType = args[1].toLowerCase();
                // 同时支持中文和英文别名
                List<String> statTypes = new ArrayList<>();
                statTypes.addAll(LeaderboardManager.StatType.getAllDisplayNames());
                statTypes.addAll(LeaderboardManager.StatType.getAllAliases());

                for (String statType : statTypes) {
                    if (statType.toLowerCase().startsWith(partialType)) {
                        completions.add(statType);
                    }
                }
            } else if (subCommand.equals("topholo")) {
                // 补全topHolo命令的操作类型
                String partialAction = args[1].toLowerCase();
                List<String> actions = Arrays.asList("set", "remove", "list");
                for (String action : actions) {
                    if (action.startsWith(partialAction)) {
                        completions.add(action);
                    }
                }
            }
        } else if (args.length == 3) {
            // 补全第三个参数
            String subCommand = args[0].toLowerCase();
            if (subCommand.equals("kit")) {
                // 补全槽位位置（1-36）
                String partialSlot = args[2].toLowerCase();
                for (int i = 1; i <= 36; i++) {
                    String slotStr = String.valueOf(i);
                    if (slotStr.startsWith(partialSlot)) {
                        completions.add(slotStr);
                    }
                }
            } else if (subCommand.equals("buff")) {
                // 补全buff ID
                String partialBuff = args[2].toLowerCase();
                List<String> buffIds = Arrays.asList(
                        "speed1", "speed2", "KeepFood", "jump1",
                        "health1", "health2", "health3", "health+", "health++", "health+++"
                );
                for (String buffId : buffIds) {
                    if (buffId.toLowerCase().startsWith(partialBuff)) {
                        completions.add(buffId);
                    }
                }
            } else if (subCommand.equals("cleanup") && args[1].equalsIgnoreCase("player")) {
                // 补全玩家名称（仅在player操作时）
                String partialPlayerName = args[2].toLowerCase();
                for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                    if (onlinePlayer.getName().toLowerCase().startsWith(partialPlayerName)) {
                        completions.add(onlinePlayer.getName());
                    }
                }
            } else if (subCommand.equals("lucky")) {
                // 补全lucky命令的操作类型
                String partialAction = args[2].toLowerCase();
                List<String> actions = Arrays.asList("Change", "Add", "Set", "open", "remove");
                for (String action : actions) {
                    if (action.toLowerCase().startsWith(partialAction)) {
                        completions.add(action);
                    }
                }
            } else if (subCommand.equals("top")) {
                // 补全top命令的第三个参数（地图名称或查询范围）
                String partialParam = args[2].toLowerCase();

                // 获取统计类型
                LeaderboardManager.StatType statType = LeaderboardManager.StatType.fromDisplayName(args[1]);

                if (statType == LeaderboardManager.StatType.MAP_MAX_ROUNDS ||
                    statType == LeaderboardManager.StatType.MAP_FASTEST_TIME) {
                    // 地图相关统计，补全地图名称
                    List<String> gameNames = new ArrayList<>(gameManager.getGameNames());
                    for (String gameName : gameNames) {
                        if (gameName.toLowerCase().startsWith(partialParam)) {
                            completions.add(gameName);
                        }
                    }
                } else {
                    // 非地图相关统计，补全查询范围数字
                    List<String> ranges = Arrays.asList("5", "10", "15", "20");
                    for (String range : ranges) {
                        if (range.startsWith(partialParam)) {
                            completions.add(range);
                        }
                    }
                }
            } else if (subCommand.equals("topholo")) {
                String action = args[1].toLowerCase();
                if (action.equals("remove")) {
                    // 补全全息图名称
                    String partialName = args[2].toLowerCase();
                    HologramLeaderboardManager holoManager = plugin.getHologramLeaderboardManager();
                    if (holoManager != null) {
                        for (String holoName : holoManager.getAllHologramNames()) {
                            if (holoName.toLowerCase().startsWith(partialName)) {
                                completions.add(holoName);
                            }
                        }
                    }
                } else if (action.equals("set")) {
                    // 补全topHolo命令的统计类型
                    String partialType = args[2].toLowerCase();
                    List<String> statTypes = new ArrayList<>();
                    statTypes.addAll(LeaderboardManager.StatType.getAllDisplayNames());
                    statTypes.addAll(LeaderboardManager.StatType.getAllAliases());
                    statTypes.add("全部数据");
                    statTypes.add("all");

                    for (String statType : statTypes) {
                        if (statType.toLowerCase().startsWith(partialType)) {
                            completions.add(statType);
                        }
                    }
                }
            }
        } else if (args.length == 4) {
            // 补全第四个参数
            String subCommand = args[0].toLowerCase();
            if (subCommand.equals("kit")) {
                // 补全操作类型（set/remove）
                String partialAction = args[3].toLowerCase();
                List<String> actions = Arrays.asList("set", "remove");
                for (String action : actions) {
                    if (action.startsWith(partialAction)) {
                        completions.add(action);
                    }
                }
            } else if (subCommand.equals("top")) {
                // 补全top命令的第四个参数
                String partialParam = args[3].toLowerCase();

                // 获取统计类型
                LeaderboardManager.StatType statType = LeaderboardManager.StatType.fromDisplayName(args[1]);

                if (statType == LeaderboardManager.StatType.MAP_MAX_ROUNDS ||
                    statType == LeaderboardManager.StatType.MAP_FASTEST_TIME) {
                    // 地图相关统计，第四个参数是查询范围
                    List<String> ranges = Arrays.asList("5", "10", "15", "20");
                    for (String range : ranges) {
                        if (range.startsWith(partialParam)) {
                            completions.add(range);
                        }
                    }
                }
            } else if (subCommand.equals("topholo")) {
                String action = args[1].toLowerCase();
                if (action.equals("set")) {
                    // 补全topHolo set命令的第四个参数（全息图名称）
                    // 这里不提供补全，因为是用户自定义的名称
                    // 但可以提供一些示例
                    String partialName = args[3].toLowerCase();
                    List<String> examples = Arrays.asList("TopGames", "TopKills", "TopRounds", "AllStats");
                    for (String example : examples) {
                        if (example.toLowerCase().startsWith(partialName)) {
                            completions.add(example);
                        }
                    }
                }
            }
        } else if (args.length == 5) {
            // 补全第五个参数
            String subCommand = args[0].toLowerCase();
            if (subCommand.equals("kit")) {
                String action = args[3].toLowerCase();
                if (action.equals("set")) {
                    // 补全物品ID（id1-id69）
                    String partialId = args[4].toLowerCase();
                    // 添加idx格式的ID
                    for (int i = 1; i <= 69; i++) {
                        String idStr = "id" + i;
                        if (idStr.startsWith(partialId)) {
                            completions.add(idStr);
                        }
                    }
                    // 添加数字格式的ID（向后兼容）
                    for (int i = 1; i <= 69; i++) {
                        String idStr = String.valueOf(i);
                        if (idStr.startsWith(partialId)) {
                            completions.add(idStr);
                        }
                    }
                }
            } else if (subCommand.equals("topholo")) {
                String action = args[1].toLowerCase();
                if (action.equals("set")) {
                    // 补全topHolo set命令的第五个参数
                    String partialParam = args[4].toLowerCase();

                    // 获取统计类型
                    LeaderboardManager.StatType statType = LeaderboardManager.StatType.fromDisplayName(args[2]);

                    if (statType == LeaderboardManager.StatType.MAP_MAX_ROUNDS ||
                        statType == LeaderboardManager.StatType.MAP_FASTEST_TIME) {
                        // 地图相关统计，第五个参数是地图名称
                        List<String> gameNames = new ArrayList<>(gameManager.getGameNames());
                        for (String gameName : gameNames) {
                            if (gameName.toLowerCase().startsWith(partialParam)) {
                                completions.add(gameName);
                            }
                        }
                    } else {
                        // 非地图相关统计，第五个参数是显示范围
                        List<String> ranges = Arrays.asList("5", "10", "15", "20");
                        for (String range : ranges) {
                            if (range.startsWith(partialParam)) {
                                completions.add(range);
                            }
                        }
                    }
                }
            }
        } else if (args.length == 6) {
            // 补全第六个参数
            String subCommand = args[0].toLowerCase();
            if (subCommand.equals("topholo")) {
                String action = args[1].toLowerCase();
                if (action.equals("set")) {
                    // 补全topHolo set命令的第六个参数（显示范围，仅地图相关统计）
                    String partialParam = args[5].toLowerCase();

                    // 获取统计类型
                    LeaderboardManager.StatType statType = LeaderboardManager.StatType.fromDisplayName(args[2]);

                    if (statType == LeaderboardManager.StatType.MAP_MAX_ROUNDS ||
                        statType == LeaderboardManager.StatType.MAP_FASTEST_TIME) {
                        // 地图相关统计，第六个参数是显示范围
                        List<String> ranges = Arrays.asList("5", "10", "15", "20");
                        for (String range : ranges) {
                            if (range.startsWith(partialParam)) {
                                completions.add(range);
                            }
                        }
                    }
                }
            }
        }

        return completions;
    }

    /**
     * 处理reload命令，重新加载所有配置文件
     *
     * @param player 执行命令的玩家
     * @return 命令执行结果
     */
    private boolean handleReloadCommand(Player player) {
        player.sendMessage(ChatColor.YELLOW + "正在重新加载DeathZombieV4插件的所有配置文件...");

        try {
            // 重新加载主配置文件
            plugin.reloadConfig();
            player.sendMessage(ChatColor.GREEN + "主配置文件已重新加载！");

            // 重新加载大厅位置
            plugin.getLobbyManager().reloadLobbyLocation();
            player.sendMessage(ChatColor.GREEN + "大厅位置已重新加载！");

            // 检查大厅位置是否已设置
            if (plugin.getLobbyManager().isLobbySet()) {
                player.sendMessage(ChatColor.GREEN + "大厅位置已成功加载。");
            } else {
                player.sendMessage(ChatColor.YELLOW + "未设置大厅位置，玩家在游戏结束后将不会被传送。");
            }

            // 重新加载所有游戏配置
            plugin.getGameManager().loadAllGames();
            player.sendMessage(ChatColor.GREEN + "所有游戏配置文件已重新加载！");

            // 重新初始化游戏会话管理器
            // 注意：我们不能直接调用loadSessionData()，因为它是私有的
            // 但我们可以通过保存当前数据并重新初始化来实现类似效果
            plugin.getGameSessionManager().saveSessionData();
            player.sendMessage(ChatColor.GREEN + "游戏会话数据已保存！");

            // 保存游戏会话数据（替代原来的玩家状态保存）
            plugin.getGameSessionManager().saveSessionData();
            player.sendMessage(ChatColor.GREEN + "游戏会话数据已保存！");

            // 重新初始化怪物ID映射器
            // 注意：MonsterIdMapper没有loadMappings方法，但它在初始化时会加载映射
            player.sendMessage(ChatColor.GREEN + "怪物ID映射已重新加载！");

            // 重新加载消息配置
            plugin.getMessageManager().reloadConfig();
            player.sendMessage(ChatColor.GREEN + "消息配置文件已重新加载！");

            // 重新加载双CustomZombie系统配置
            if (plugin.getDualZombieSystemManager() != null) {
                plugin.getDualZombieSystemManager().reloadConfig();
                player.sendMessage(ChatColor.GREEN + "双CustomZombie系统配置已重新加载！");
            } else {
                player.sendMessage(ChatColor.YELLOW + "双CustomZombie系统未初始化，跳过配置重载");
            }

            player.sendMessage(ChatColor.GOLD + "所有配置文件已成功重新加载！");
            return true;
        } catch (Exception e) {
            player.sendMessage(ChatColor.RED + "重新加载配置文件时出错: " + e.getMessage());
            plugin.getLogger().severe("重新加载配置文件时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 处理设置初始装备的命令
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param slot 物品栏位置
     * @param action 操作类型（set/remove/change）
     * @param itemId 物品ID（仅在set操作时使用）
     * @param slotType 槽位类型（仅在change操作时使用）
     * @return 命令是否成功
     */
    private boolean handleKitCommand(Player player, String gameName, int slot, String action, String itemId, String slotType) {
        // 检查游戏是否存在
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return true;
        }

        // 检查物品栏位置是否有效 - 使用1-36的新格式
        if (slot < 1 || slot > 36) {
            player.sendMessage(ChatColor.RED + "物品栏位置必须在1-36之间！");
            return true;
        }

        // 获取GameSessionManager实例
        GameSessionManager gameSessionManager = plugin.getGameSessionManager();

        // 使用新格式的槽位编号(1-36)
        int newSlot = slot;

        // 记录槽位信息
        plugin.getLogger().info("使用槽位: " + newSlot);

        // 根据不同的操作类型处理
        if (action.equals("set")) {
            // 设置初始装备
            if (itemId == null || itemId.isEmpty()) {
                player.sendMessage(ChatColor.RED + "物品ID不能为空！");
                return true;
            }

            // 检查物品ID格式 - 僵尸末日的武器ID，不是Shoot插件的ID
            if (!itemId.startsWith("id") && !itemId.startsWith("wp") && !itemId.startsWith("it") && !itemId.startsWith("ar") && !itemId.startsWith("sp") && !itemId.equals("white_dye")) {
                player.sendMessage(ChatColor.YELLOW + "警告：物品ID格式不符合标准格式(id/wp/it/ar/sp)，但仍将继续设置。");
            }

            // 检查物品栏位置限制 - 使用旧格式的槽位编号进行检查
            if (slot == 0) {
                // 0号位置固定为铁剑
                if (!itemId.equals("id1")) {
                    player.sendMessage(ChatColor.YELLOW + "警告：0号位置通常固定为铁剑(id1)，但您设置了 " + itemId);
                }
            } else if (slot == 1) {
                // 1号位置固定为枪
                if (!itemId.startsWith("id") && !itemId.startsWith("wp")) {
                    player.sendMessage(ChatColor.YELLOW + "警告：1号位置通常固定为枪(id类型)，但您设置了 " + itemId);
                }
            } else if (slot >= 6 && slot <= 8) {
                // 6-8号位置固定为物品
                if (!itemId.startsWith("id") && !itemId.startsWith("it")) {
                    player.sendMessage(ChatColor.YELLOW + "警告：6-8号位置通常固定为物品(id/it类型)，但您设置了 " + itemId);
                }
            }

            // 设置初始装备 - 直接使用新格式的槽位编号(1-36)
            boolean success = gameSessionManager.setGameInitialEquipment(gameName, newSlot, itemId);

            if (success) {
                player.sendMessage(ChatColor.GREEN + "成功设置游戏 '" + gameName + "' 的初始装备：位置 " + slot + " (新格式: " + newSlot + ") = " + itemId);
            } else {
                player.sendMessage(ChatColor.RED + "设置游戏 '" + gameName + "' 的初始装备失败！");
            }
        } else if (action.equals("remove")) {
            // 移除初始装备设置，将其变为默认锁定槽位
            boolean success = gameSessionManager.setGameInitialEquipment(gameName, newSlot, "white_dye");

            if (success) {
                player.sendMessage(ChatColor.GREEN + "成功移除游戏 '" + gameName + "' 的初始装备设置，槽位 " + slot + " (新格式: " + newSlot + ") 已变为锁定状态");
            } else {
                player.sendMessage(ChatColor.RED + "移除游戏 '" + gameName + "' 的初始装备设置失败！");
            }
        } else if (action.equals("change")) {
            // 改变槽位类型
            if (slotType == null || slotType.isEmpty()) {
                player.sendMessage(ChatColor.RED + "槽位类型不能为空！");
                return true;
            }

            // 根据槽位类型设置不同的标识符
            String typeIdentifier;
            if (slotType.equals("武器") || slotType.equalsIgnoreCase("weapon")) {
                typeIdentifier = "weapon_slot";
            } else if (slotType.equals("道具") || slotType.equalsIgnoreCase("item")) {
                typeIdentifier = "item_slot";
            } else {
                player.sendMessage(ChatColor.RED + "无效的槽位类型！必须是 '武器' 或 '道具'");
                return true;
            }

            // 设置槽位类型标识符
            // 注意：这里我们使用一个特殊的格式来存储槽位类型信息
            // 例如：slot_type_X，其中X是槽位位置
            FileConfiguration config = gameManager.getGameConfig(gameName);
            if (config != null) {
                // 使用新格式的槽位编号
                config.set("initialEquipment.slot_type_" + newSlot, typeIdentifier);
                try {
                    gameManager.saveGameConfig(gameName);
                    player.sendMessage(ChatColor.GREEN + "成功设置游戏 '" + gameName + "' 的槽位 " + slot + " (新格式: " + newSlot + ") 类型为 " + slotType);
                } catch (Exception e) {
                    player.sendMessage(ChatColor.RED + "保存游戏配置失败：" + e.getMessage());
                    return true;
                }
            } else {
                player.sendMessage(ChatColor.RED + "无法获取游戏配置！");
                return true;
            }
        } else {
            player.sendMessage(ChatColor.RED + "无效的操作类型！必须是 set、remove 或 change");
        }

        return true;
    }

    /**
     * 处理设置游戏等待时间的命令
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param waitTime 等待时间（秒）
     * @return 命令是否成功
     */
    private boolean handleSetWaitTimeCommand(Player player, String gameName, int waitTime) {
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return true;
        }

        if (waitTime < 5) {
            player.sendMessage(ChatColor.RED + "等待时间不能小于5秒！");
            return true;
        }

        if (gameManager.setWaitTime(gameName, waitTime)) {
            player.sendMessage(ChatColor.GREEN + "成功设置游戏 '" + gameName + "' 的等待时间为 " + waitTime + " 秒！");
        } else {
            player.sendMessage(ChatColor.RED + "设置游戏 '" + gameName + "' 的等待时间失败！");
        }
        return true;
    }

    /**
     * 处理设置游戏buff的命令
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param action 操作类型（add或remove）
     * @param buffId buff的ID
     * @return 命令是否成功
     */
    private boolean handleBuffCommand(Player player, String gameName, String action, String buffId) {
        // 检查游戏是否存在
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return true;
        }

        // 检查操作类型
        if (!action.equalsIgnoreCase("add") && !action.equalsIgnoreCase("remove")) {
            player.sendMessage(ChatColor.RED + "无效的操作类型！必须是 'add' 或 'remove'");
            return true;
        }

        if (action.equalsIgnoreCase("add")) {
            // 添加buff
            if (gameManager.setBuff(gameName, buffId)) {
                player.sendMessage(ChatColor.GREEN + "成功为游戏 '" + gameName + "' 添加buff: " + buffId);
            } else {
                player.sendMessage(ChatColor.RED + "添加buff失败！请检查buff ID是否有效。");
                player.sendMessage(ChatColor.YELLOW + "可用的buff ID: speed1(速度1), speed2(速度2), KeepFood(饱食度), jump1(跳跃1)");
                player.sendMessage(ChatColor.YELLOW + "生命相关: health1(生命恢复1), health2(生命恢复2), health3(生命恢复3)");
                player.sendMessage(ChatColor.YELLOW + "额外生命: health+(额外生命1), health++(额外生命2), health+++(额外生命3)");
            }
        } else {
            // 移除buff
            if (gameManager.removeBuff(gameName, buffId)) {
                player.sendMessage(ChatColor.GREEN + "成功从游戏 '" + gameName + "' 移除buff: " + buffId);
            } else {
                player.sendMessage(ChatColor.RED + "移除buff失败！请检查游戏是否存在该buff。");
            }
        }

        return true;
    }

    /**
     * 处理选择NPC的命令
     *
     * @param player 玩家
     * @param npcId NPC ID
     * @return 命令是否成功
     */
    private boolean handleSelectNPCCommand(Player player, int npcId) {
        try {
            // 使用Citizens API直接选择NPC
            if (Bukkit.getPluginManager().isPluginEnabled("Citizens")) {
                // 直接使用命令选择NPC
                player.performCommand("npc sel " + npcId);
                player.sendMessage(ChatColor.GREEN + "已尝试选择ID为 " + npcId + " 的NPC");
                player.sendMessage(ChatColor.YELLOW + "如果选择失败，请确认NPC ID是否正确");

                // 广播选择命令，方便管理员看到
                Bukkit.broadcastMessage("§a[DeathZombieV4] §e管理员正在尝试选择NPC ID: §b" + npcId);
                Bukkit.broadcastMessage("§a[DeathZombieV4] §e如需手动选择，请使用命令 §b/npc sel " + npcId);

                return true;
            } else {
                player.sendMessage(ChatColor.RED + "Citizens插件未启用，无法选择NPC。");
            }
        } catch (Exception e) {
            player.sendMessage(ChatColor.RED + "选择NPC时发生错误: " + e.getMessage());
            plugin.getLogger().severe("选择NPC时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
        return true;
    }

    /**
     * 处理玩家离开游戏的命令
     *
     * @param player 玩家
     * @return 命令是否成功
     */
    private boolean handleLeaveCommand(Player player) {
        // 获取玩家当前所在的游戏
        String gameName = plugin.getGameSessionManager().getPlayerGame(player);

        if (gameName == null) {
            player.sendMessage(ChatColor.RED + "你当前没有加入任何游戏！");
            return true;
        }

        // 检查游戏状态，只允许在等待状态下离开
        GameSessionManager.GameState gameState = plugin.getGameSessionManager().getGameState(gameName);
        if (gameState != GameSessionManager.GameState.WAITING) {
            player.sendMessage(ChatColor.RED + "游戏已经开始，无法离开！请等待游戏结束。");
            return true;
        }

        // 获取当前游戏的最小玩家数
        int minPlayers = gameManager.getMinPlayers(gameName);

        // 获取当前游戏的玩家数量（包括即将离开的玩家）
        int currentPlayerCount = plugin.getGameSessionManager().getPlayerCount(gameName);

        // 从游戏中移除玩家
        if (plugin.getGameSessionManager().removePlayerFromGame(player, gameName)) {
            player.sendMessage(ChatColor.GREEN + "你已成功离开游戏 '" + gameName + "'！");

            // 尝试传送玩家到设置的大厅位置
            if (plugin.getLobbyManager().isLobbySet()) {
                // 传送到大厅位置
                if (plugin.getLobbyManager().teleportPlayerToLobby(player)) {
                    plugin.getLogger().info("玩家 " + player.getName() + " 离开游戏后已传送到大厅");
                } else {
                    // 如果传送到大厅失败，传送到世界出生点作为备用
                    player.teleport(player.getWorld().getSpawnLocation());
                    player.sendMessage(ChatColor.YELLOW + "传送到大厅失败，已传送到出生点");
                    plugin.getLogger().warning("玩家 " + player.getName() + " 传送到大厅失败，已传送到出生点");
                }
            } else {
                // 如果没有设置大厅位置，传送到世界出生点
                player.teleport(player.getWorld().getSpawnLocation());
                player.sendMessage(ChatColor.YELLOW + "未设置大厅位置，已传送到出生点");
                plugin.getLogger().info("玩家 " + player.getName() + " 离开游戏，未设置大厅位置，已传送到出生点");
            }

            // 清理玩家数据
            plugin.getPlayerInteractionManager().resetPlayerData(player);

            // 清理玩家的幸运箱资源（额外保障）
            if (plugin.getLuckyBoxManager() != null) {
                plugin.getLuckyBoxManager().onPlayerLeaveGame(player);
            }

            // 更新计分板
            plugin.getScoreboardManager().updatePlayerScoreboard(player);

            // 检查剩余玩家数量是否满足最小要求
            // 玩家已经被移除，所以当前玩家数量是currentPlayerCount-1
            if (currentPlayerCount - 1 < minPlayers) {
                // 玩家数量不足，重置倒计时
                plugin.getScoreboardManager().resetGameCountdown(gameName);

                // 通知剩余玩家
                for (UUID playerId : plugin.getGameSessionManager().getGameParticipants(gameName)) {
                    Player p = Bukkit.getPlayer(playerId);
                    if (p != null && p.isOnline()) {
                        p.sendMessage(ChatColor.YELLOW + "由于玩家离开，当前人数不足 " + minPlayers + " 人，游戏倒计时已重置。");
                    }
                }

                plugin.getLogger().info("玩家 " + player.getName() + " 离开游戏 '" + gameName + "' 后，人数不足最小要求，已重置倒计时");
            }
        } else {
            player.sendMessage(ChatColor.RED + "离开游戏失败！");
        }
        return true;
    }

    /**
     * 处理玩家重新连接游戏的命令
     *
     * @param player 玩家
     * @return 命令是否成功
     */
    private boolean handleRejoinCommand(Player player) {
        // 检查玩家当前状态
        PlayerStatus currentStatus = plugin.getPlayerInteractionManager().getPlayerStatus(player);

        // 检查玩家是否有保存的游戏会话
        String savedGameName = plugin.getGameSessionManager().getPlayerGame(player);

        if (savedGameName == null) {
            player.sendMessage(ChatColor.RED + "你没有保存的游戏会话！请使用 /dzs join 加入游戏。");
            return true;
        }

        // 检查游戏是否仍然存在且已启用
        if (!gameManager.gameExists(savedGameName)) {
            player.sendMessage(ChatColor.RED + "你之前的游戏 '" + savedGameName + "' 已不存在！");
            // 清理无效的游戏会话
            plugin.getGameSessionManager().removePlayerFromCurrentGame(player);
            return true;
        }

        if (!gameManager.isGameEnabled(savedGameName)) {
            player.sendMessage(ChatColor.RED + "你之前的游戏 '" + savedGameName + "' 已被禁用！");
            // 清理无效的游戏会话
            plugin.getGameSessionManager().removePlayerFromCurrentGame(player);
            return true;
        }

        // 检查玩家当前状态，只有在非暂时离开状态且已在游戏中时才提示
        if (currentStatus != PlayerStatus.TEMPORARILY_LEFT
                && plugin.getGameSessionManager().isPlayerInGame(player)) {
            String currentGame = plugin.getGameSessionManager().getPlayerGame(player);

            // 如果玩家状态是等待或游戏中，说明已经正常在游戏中
            if (currentStatus == PlayerStatus.WAITING
                    || currentStatus == PlayerStatus.IN_GAME) {
                player.sendMessage(ChatColor.YELLOW + "你已经在游戏 '" + currentGame + "' 中！");
                return true;
            }
        }

        // 尝试重新连接到游戏
        boolean success = plugin.getGameSessionManager().rejoinPlayerToGame(player, savedGameName);

        if (success) {
            // 如果玩家之前是暂时离开状态，将状态转换为游戏中
            if (currentStatus == PlayerStatus.TEMPORARILY_LEFT) {
                plugin.getPlayerInteractionManager().setPlayerStatus(player, PlayerStatus.IN_GAME, true);
                plugin.getLogger().info("玩家 " + player.getName() + " 重新连接成功，状态从暂时离开转换为游戏中");
            }

            player.sendMessage(ChatColor.GREEN + "成功重新连接到游戏 '" + savedGameName + "'！");

            // 广播玩家重新连接消息
            plugin.getServer().broadcastMessage(ChatColor.YELLOW + player.getName() + " 重新连接到游戏 " + savedGameName + "！"
                    + ChatColor.GRAY + "(" + plugin.getGameSessionManager().getPlayerCount(savedGameName) + " 人)");
        } else {
            player.sendMessage(ChatColor.RED + "重新连接到游戏 '" + savedGameName + "' 失败！可能是游戏状态不正确或人数已满。");
            player.sendMessage(ChatColor.YELLOW + "请使用 /dzs join 重新加入游戏。");
        }

        return true;
    }

    /**
     * 处理设置全局大厅位置的命令
     *
     * @param player 玩家
     * @return 命令是否成功
     */
    private boolean handleLobbyCommand(Player player) {
        // 获取玩家当前位置
        Location location = player.getLocation();

        // 设置大厅位置
        if (plugin.getLobbyManager().setLobbyLocation(location)) {
            player.sendMessage(ChatColor.GREEN + "成功设置全局大厅位置！");
            player.sendMessage(ChatColor.YELLOW + "所有游戏结束后，玩家将被传送到此位置。");
            player.sendMessage(ChatColor.YELLOW + "位置已保存到config.yml文件中。");

            // 强制重新加载大厅位置，确保设置生效
            plugin.getLobbyManager().reloadLobbyLocation();

            // 记录日志
            plugin.getLogger().info("管理员 " + player.getName() + " 设置了全局大厅位置: "
                    + location.getWorld().getName() + " ["
                    + location.getX() + ", "
                    + location.getY() + ", "
                    + location.getZ() + "]");

            // 检查设置后的状态
            if (plugin.getLobbyManager().isLobbySet()) {
                plugin.getLogger().info("大厅位置设置成功并已验证。");
            } else {
                plugin.getLogger().warning("大厅位置设置后验证失败，请检查配置文件。");
                player.sendMessage(ChatColor.RED + "警告：大厅位置设置后验证失败，请联系管理员检查配置文件。");
            }
        } else {
            player.sendMessage(ChatColor.RED + "设置全局大厅位置失败！");
        }

        return true;
    }

    /**
     * 处理幸运箱命令
     *
     * @param player 玩家
     * @param args 命令参数
     * @return 命令是否成功
     */
    private boolean handleLuckyCommand(Player player, String[] args) {
        if (args.length < 3) {
            player.sendMessage(ChatColor.RED + "用法: /dzs lucky <游戏名称> <Change|Add|Set|open|remove> <箱子名称> [参数A] [参数B]");
            return true;
        }

        String gameName = args[1];
        String action = args[2].toLowerCase();

        // remove操作可以没有箱子名称参数
        if (!action.equals("remove") && args.length < 4) {
            player.sendMessage(ChatColor.RED + "用法: /dzs lucky <游戏名称> <Change|Add|Set|open|remove> <箱子名称> [参数A] [参数B]");
            return true;
        }

        String boxName = args.length > 3 ? args[3] : null;

        // 检查游戏是否存在
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return true;
        }

        // 根据不同的操作类型处理命令
        switch (action) {
            case "set":
                return handleLuckyBoxSet(player, gameName, boxName, args);

            case "add":
                return handleLuckyBoxAdd(player, gameName, boxName, args);

            case "open":
                return handleLuckyBoxOpen(player, gameName, boxName, args);

            case "change":
                return handleLuckyBoxChange(player, gameName, boxName, args);

            case "remove":
                return handleLuckyBoxRemove(player, gameName, boxName, args);

            default:
                player.sendMessage(ChatColor.RED + "操作类型必须是 'Change'、'Add'、'Set'、'open' 或 'remove'！");
                return true;
        }
    }

    /**
     * 处理幸运箱的Set命令
     */
    private boolean handleLuckyBoxSet(Player player, String gameName, String boxName, String[] args) {
        LuckyBoxManager luckyBoxManager = plugin.getLuckyBoxManager();

        // 如果没有其他参数，设置玩家为等待破坏箱子状态
        if (args.length == 4) {
            plugin.getPlayerInteractionManager().setEditingGame(player, gameName);
            plugin.getPlayerInteractionManager().setPlayerData(player, "setting_lucky_box", "true");
            plugin.getPlayerInteractionManager().setPlayerData(player, "lucky_box_name", boxName);
            plugin.getPlayerInteractionManager().setPlayerData(player, "lucky_box_type", "local"); // 默认为local类型
            plugin.getPlayerInteractionManager().setPlayerData(player, "lucky_box_round", "1"); // 默认第1回合开启
            plugin.getPlayerInteractionManager().setPlayerData(player, "lucky_box_cost", "1000"); // 默认消耗1000金钱

            player.sendMessage(ChatColor.GREEN + "请破坏一个箱子来设置幸运箱位置！");
            player.sendMessage(ChatColor.YELLOW + "箱子名称: " + boxName + ", 类型: local, 开启回合: 1, 消耗金钱: 1000");
            return true;
        }

        // 设置箱子类型为local或random
        if (args.length >= 5) {
            String type = args[4].toLowerCase();
            if (type.equals("local") || type.equals("random")) {
                plugin.getPlayerInteractionManager().setEditingGame(player, gameName);
                plugin.getPlayerInteractionManager().setPlayerData(player, "setting_lucky_box", "true");
                plugin.getPlayerInteractionManager().setPlayerData(player, "lucky_box_name", boxName);
                plugin.getPlayerInteractionManager().setPlayerData(player, "lucky_box_type", type);
                plugin.getPlayerInteractionManager().setPlayerData(player, "lucky_box_round", "1"); // 默认第1回合开启
                plugin.getPlayerInteractionManager().setPlayerData(player, "lucky_box_cost", "1000"); // 默认消耗1000金钱

                player.sendMessage(ChatColor.GREEN + "请破坏一个箱子来设置幸运箱位置！");
                player.sendMessage(ChatColor.YELLOW + "箱子名称: " + boxName + ", 类型: " + type + ", 开启回合: 1, 消耗金钱: 1000");
                return true;
            } else {
                player.sendMessage(ChatColor.RED + "箱子类型必须是 'local' 或 'random'！");
                return true;
            }
        }

        return true;
    }

    /**
     * 处理幸运箱的Add命令
     */
    private boolean handleLuckyBoxAdd(Player player, String gameName, String boxName, String[] args) {
        LuckyBoxManager luckyBoxManager = plugin.getLuckyBoxManager();

        if (!boxName.startsWith("id")) {
            player.sendMessage(ChatColor.RED + "添加武器时必须指定武器ID，格式: id+数字 (例如: id1)");
            return true;
        }

        // 获取武器概率
        double probability = 50.0; // 默认50%
        if (args.length >= 5) {
            try {
                probability = Double.parseDouble(args[4]);
                if (probability < 0 || probability > 100) {
                    player.sendMessage(ChatColor.RED + "概率必须在0到100之间！");
                    return true;
                }
            } catch (NumberFormatException e) {
                player.sendMessage(ChatColor.RED + "概率必须是一个有效的小数！");
                return true;
            }
        }

        // 添加武器到全局奖池
        if (luckyBoxManager.addWeaponToGlobalLuckyBoxPool(boxName, probability)) {
            player.sendMessage(ChatColor.GREEN + "成功将武器 " + boxName + " 添加到全局奖池，概率: " + probability + "%");
        } else {
            player.sendMessage(ChatColor.RED + "添加武器到全局奖池失败！");
        }

        return true;
    }

    /**
     * 处理幸运箱的Open命令
     */
    private boolean handleLuckyBoxOpen(Player player, String gameName, String boxName, String[] args) {
        LuckyBoxManager luckyBoxManager = plugin.getLuckyBoxManager();

        if (!luckyBoxManager.luckyBoxExists(gameName, boxName)) {
            player.sendMessage(ChatColor.RED + "幸运箱 '" + boxName + "' 不存在于游戏 '" + gameName + "' 中！");
            return true;
        }

        if (args.length < 5) {
            player.sendMessage(ChatColor.RED + "用法: /dzs lucky " + gameName + " open " + boxName + " <openRound|openCost> <数值>");
            return true;
        }

        String paramType = args[4].toLowerCase();
        if (!paramType.equals("openround") && !paramType.equals("opencost")) {
            player.sendMessage(ChatColor.RED + "参数类型必须是 'openRound' 或 'openCost'！");
            return true;
        }

        if (args.length < 6) {
            player.sendMessage(ChatColor.RED + "请指定 " + paramType + " 的数值！");
            return true;
        }

        try {
            int value = Integer.parseInt(args[5]);
            if (value < 0) {
                player.sendMessage(ChatColor.RED + "数值不能为负数！");
                return true;
            }

            if (luckyBoxManager.setLuckyBoxOpenCondition(gameName, boxName, paramType, value)) {
                String paramName = paramType.equals("openround") ? "开启回合" : "消耗金钱";
                player.sendMessage(ChatColor.GREEN + "成功设置幸运箱 '" + boxName + "' 的" + paramName + "为: " + value);
            } else {
                player.sendMessage(ChatColor.RED + "设置幸运箱开启条件失败！");
            }
        } catch (NumberFormatException e) {
            player.sendMessage(ChatColor.RED + "数值必须是一个有效的整数！");
        }

        return true;
    }

    /**
     * 处理幸运箱的Change命令
     */
    private boolean handleLuckyBoxChange(Player player, String gameName, String boxName, String[] args) {
        LuckyBoxManager luckyBoxManager = plugin.getLuckyBoxManager();

        if (!luckyBoxManager.luckyBoxExists(gameName, boxName)) {
            player.sendMessage(ChatColor.RED + "幸运箱 '" + boxName + "' 不存在于游戏 '" + gameName + "' 中！");
            return true;
        }

        if (args.length < 5) {
            player.sendMessage(ChatColor.RED + "用法: /dzs lucky " + gameName + " change " + boxName + " <Local|Random|openRound|openCost> [数值]");
            return true;
        }

        String paramType = args[4].toLowerCase();

        if (paramType.equals("local") || paramType.equals("random")) {
            // 修改箱子类型
            if (luckyBoxManager.changeLuckyBoxType(gameName, boxName, paramType)) {
                player.sendMessage(ChatColor.GREEN + "成功将幸运箱 '" + boxName + "' 的类型修改为: " + paramType);
            } else {
                player.sendMessage(ChatColor.RED + "修改幸运箱类型失败！");
            }
        } else if (paramType.equals("openround") || paramType.equals("opencost")) {
            // 修改开启条件
            if (args.length < 6) {
                player.sendMessage(ChatColor.RED + "请指定 " + paramType + " 的数值！");
                return true;
            }

            try {
                int value = Integer.parseInt(args[5]);
                if (value < 0) {
                    player.sendMessage(ChatColor.RED + "数值不能为负数！");
                    return true;
                }

                if (luckyBoxManager.changeLuckyBoxOpenCondition(gameName, boxName, paramType, value)) {
                    String paramName = paramType.equals("openround") ? "开启回合" : "消耗金钱";
                    player.sendMessage(ChatColor.GREEN + "成功修改幸运箱 '" + boxName + "' 的" + paramName + "为: " + value);
                } else {
                    player.sendMessage(ChatColor.RED + "修改幸运箱开启条件失败！");
                }
            } catch (NumberFormatException e) {
                player.sendMessage(ChatColor.RED + "数值必须是一个有效的整数！");
            }
        } else {
            player.sendMessage(ChatColor.RED + "参数类型必须是 'Local'、'Random'、'openRound' 或 'openCost'！");
        }

        return true;
    }



    /**
     * 处理清理命令
     *
     * @param player 玩家
     * @param args 命令参数
     * @return 命令是否成功
     */
    private boolean handleCleanupCommand(Player player, String[] args) {
        String action = args[1].toLowerCase();
        LuckyBoxManager luckyBoxManager = plugin.getLuckyBoxManager();

        if (luckyBoxManager == null) {
            player.sendMessage(ChatColor.RED + "幸运箱管理器未初始化！");
            return true;
        }

        switch (action) {
            case "player":
                if (args.length < 3) {
                    player.sendMessage(ChatColor.RED + "用法: /dzs cleanup player <玩家名称>");
                    return true;
                }

                String targetPlayerName = args[2];
                Player targetPlayer = Bukkit.getPlayer(targetPlayerName);

                if (targetPlayer == null) {
                    player.sendMessage(ChatColor.RED + "玩家 '" + targetPlayerName + "' 不在线或不存在！");
                    return true;
                }

                // 清理指定玩家的幸运箱抽奖全息图
                luckyBoxManager.onPlayerLeaveGame(targetPlayer);
                player.sendMessage(ChatColor.GREEN + "已清理玩家 " + targetPlayer.getName() + " 的幸运箱抽奖全息图！");
                targetPlayer.sendMessage(ChatColor.YELLOW + "管理员已清理了你的幸运箱抽奖全息图。");
                break;

            case "all":
                // 清理所有在线玩家的幸运箱抽奖全息图
                int cleanedCount = 0;
                for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                    luckyBoxManager.onPlayerLeaveGame(onlinePlayer);
                    cleanedCount++;
                }

                // 清理所有抽奖任务
                luckyBoxManager.clearAllDrawings();

                player.sendMessage(ChatColor.GREEN + "已清理所有玩家的幸运箱抽奖全息图！共处理 " + cleanedCount + " 个玩家。");

                // 向所有在线玩家发送通知
                for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                    if (!onlinePlayer.equals(player)) {
                        onlinePlayer.sendMessage(ChatColor.YELLOW + "管理员已清理了所有玩家的幸运箱抽奖全息图。");
                    }
                }
                break;

            default:
                player.sendMessage(ChatColor.RED + "无效的操作类型！使用 'player' 或 'all'。");
                return true;
        }

        return true;
    }

    /**
     * 处理幸运箱的Remove命令
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param boxName 箱子名称（可为null）
     * @param args 命令参数
     * @return 命令是否成功
     */
    private boolean handleLuckyBoxRemove(Player player, String gameName, String boxName, String[] args) {
        LuckyBoxManager luckyBoxManager = plugin.getLuckyBoxManager();

        if (boxName == null) {
            // 没有指定箱子名称，移除离玩家最近的箱子
            String closestBoxId = luckyBoxManager.findClosestLuckyBox(player, gameName);
            if (closestBoxId == null) {
                player.sendMessage(ChatColor.RED + "附近没有找到幸运箱！");
                return true;
            }

            if (luckyBoxManager.removeLuckyBox(gameName, closestBoxId)) {
                player.sendMessage(ChatColor.GREEN + "成功移除了最近的幸运箱 '" + closestBoxId + "'！");
            } else {
                player.sendMessage(ChatColor.RED + "移除幸运箱失败！");
            }
        } else {
            // 指定了箱子名称，移除指定的箱子
            if (!luckyBoxManager.luckyBoxExists(gameName, boxName)) {
                player.sendMessage(ChatColor.RED + "幸运箱 '" + boxName + "' 不存在于游戏 '" + gameName + "' 中！");
                return true;
            }

            if (luckyBoxManager.removeLuckyBox(gameName, boxName)) {
                player.sendMessage(ChatColor.GREEN + "成功移除了幸运箱 '" + boxName + "'！");
            } else {
                player.sendMessage(ChatColor.RED + "移除幸运箱失败！");
            }
        }

        return true;
    }

    /**
     * 处理游戏清理命令
     *
     * @param player 玩家
     * @param args 命令参数
     * @return 命令是否成功
     */
    private boolean handleCleanCommand(Player player, String[] args) {
        if (args.length < 2) {
            // 清理所有游戏
            return cleanAllGames(player);
        } else {
            // 清理指定游戏
            String gameName = args[1];
            if (!gameManager.gameExists(gameName)) {
                player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
                return true;
            }
            return cleanSpecificGame(player, gameName);
        }
    }

    /**
     * 清理所有游戏
     *
     * @param player 玩家
     * @return 命令是否成功
     */
    private boolean cleanAllGames(Player player) {
        Set<String> allGames = gameManager.getAllGames();
        if (allGames.isEmpty()) {
            player.sendMessage(ChatColor.RED + "没有找到任何游戏！");
            return true;
        }

        int cleanedCount = 0;
        int totalGames = allGames.size();

        player.sendMessage(ChatColor.YELLOW + "开始清理所有游戏（共 " + totalGames + " 个）...");

        for (String gameName : allGames) {
            if (cleanSpecificGameInternal(gameName)) {
                cleanedCount++;
                player.sendMessage(ChatColor.GREEN + "[清理进度] " + cleanedCount + "/" + totalGames + " - 游戏 '" + gameName + "' 清理完成");
            } else {
                player.sendMessage(ChatColor.RED + "[清理进度] " + cleanedCount + "/" + totalGames + " - 游戏 '" + gameName + "' 清理失败");
            }
        }

        player.sendMessage(ChatColor.GREEN + "所有游戏清理完成！成功清理 " + cleanedCount + "/" + totalGames + " 个游戏。");
        return true;
    }

    /**
     * 清理指定游戏
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @return 命令是否成功
     */
    private boolean cleanSpecificGame(Player player, String gameName) {
        player.sendMessage(ChatColor.YELLOW + "开始清理游戏 '" + gameName + "'...");

        if (cleanSpecificGameInternal(gameName)) {
            player.sendMessage(ChatColor.GREEN + "游戏 '" + gameName + "' 清理完成！");
            return true;
        } else {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 清理失败！");
            return false;
        }
    }

    /**
     * 清理指定游戏的内部实现
     *
     * @param gameName 游戏名称
     * @return 是否清理成功
     */
    private boolean cleanSpecificGameInternal(String gameName) {
        try {
            plugin.getLogger().info("开始清理游戏 '" + gameName + "' 的所有资源...");

            // 获取游戏状态
            GameSessionManager.GameState gameState = plugin.getGameSessionManager().getGameState(gameName);

            if (gameState == GameSessionManager.GameState.RUNNING) {
                // 游戏进行中，先结束游戏
                plugin.getGameSessionManager().endGame(gameName);
                plugin.getLogger().info("游戏 '" + gameName + "' 已结束");
            }

            // 明确清理奖励箱悬浮文字（在重置游戏之前）
            if (plugin.getLuckyBoxManager() != null) {
                plugin.getLuckyBoxManager().clearLuckyBoxes(gameName);
                plugin.getLogger().info("已清理游戏 '" + gameName + "' 的奖励箱悬浮文字");
            }

            // 清理全局电源按钮效果
            if (plugin.isDecentHologramsAvailable() && plugin.getPowerButtonEffectManager() != null) {
                plugin.getPowerButtonEffectManager().removePowerButtonEffects(gameName);
                plugin.getLogger().info("已清理游戏 '" + gameName + "' 的全局电源按钮效果");
            }

            // 重置游戏为等待状态
            boolean resetSuccess = plugin.getGameSessionManager().resetGame(gameName);
            if (resetSuccess) {
                plugin.getLogger().info("游戏 '" + gameName + "' 已重置为等待状态");
                return true;
            } else {
                plugin.getLogger().warning("重置游戏 '" + gameName + "' 状态失败");
                return false;
            }
        } catch (Exception e) {
            plugin.getLogger().severe("清理游戏 '" + gameName + "' 时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 处理显示设置命令
     *
     * @param player 玩家
     * @param type 显示类型 (kill|Shoot|hit)
     * @param action 操作 (on|off)
     * @return 命令是否成功
     */
    private boolean handleDisplaySettingsCommand(Player player, String type, String action) {
        if (!action.equalsIgnoreCase("on") && !action.equalsIgnoreCase("off")) {
            player.sendMessage(ChatColor.RED + "操作必须是 'on' 或 'off'！");
            return true;
        }

        boolean enabled = action.equalsIgnoreCase("on");
        String typeDisplay;

        switch (type.toLowerCase()) {
            case "kill":
                plugin.getPlayerDisplaySettingsManager().setKillTitleEnabled(player, enabled);
                typeDisplay = "击杀金钱";
                break;

            case "shoot":
                plugin.getPlayerDisplaySettingsManager().setShootTitleEnabled(player, enabled);
                typeDisplay = "射击";
                break;

            case "hit":
                plugin.getPlayerDisplaySettingsManager().setHitTitleEnabled(player, enabled);
                typeDisplay = "命中";
                break;

            default:
                player.sendMessage(ChatColor.RED + "无效的显示类型！使用 'kill'、'Shoot' 或 'hit'。");
                return true;
        }

        String statusText = enabled ? ChatColor.GREEN + "开启" : ChatColor.RED + "关闭";
        player.sendMessage(ChatColor.YELLOW + "已" + statusText + ChatColor.YELLOW + " " + typeDisplay + "title显示！");

        return true;
    }

    /**
     * 处理跳过当前回合命令
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @return 命令是否成功
     */
    private boolean handleNextRoundCommand(Player player, String gameName) {
        // 检查游戏是否存在
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 " + ChatColor.YELLOW + gameName + ChatColor.RED + " 不存在！");
            return true;
        }

        // 检查游戏是否正在运行
        GameSessionManager.GameState gameState = plugin.getGameSessionManager().getGameState(gameName);
        if (gameState != GameSessionManager.GameState.RUNNING) {
            player.sendMessage(ChatColor.RED + "游戏 " + ChatColor.YELLOW + gameName + ChatColor.RED + " 当前不在运行状态！");
            player.sendMessage(ChatColor.YELLOW + "当前状态: " + gameState);
            return true;
        }

        try {
            // 清除当前回合的所有僵尸
            plugin.getZombieSpawnManager().clearAllZombies(gameName);
            player.sendMessage(ChatColor.GREEN + "已清除游戏 " + ChatColor.YELLOW + gameName + ChatColor.GREEN + " 当前回合的所有僵尸");

            // 强制触发回合完成检查，这会自动进入下一回合
            plugin.getZombieSpawnManager().checkRoundCompletion(gameName);
            player.sendMessage(ChatColor.GREEN + "已触发游戏 " + ChatColor.YELLOW + gameName + ChatColor.GREEN + " 的回合完成检查");

            // 向游戏中的所有玩家发送通知
            Set<UUID> participants = plugin.getGameSessionManager().getGameParticipants(gameName);
            for (UUID uuid : participants) {
                Player gamePlayer = Bukkit.getPlayer(uuid);
                if (gamePlayer != null && gamePlayer.isOnline() && !gamePlayer.equals(player)) {
                    gamePlayer.sendMessage(ChatColor.GOLD + "管理员 " + player.getName() + " 跳过了当前回合");
                }
            }

            plugin.getLogger().info("管理员 " + player.getName() + " 使用nextRound命令跳过了游戏 " + gameName + " 的当前回合");
            return true;

        } catch (Exception e) {
            player.sendMessage(ChatColor.RED + "跳过回合时发生错误: " + e.getMessage());
            plugin.getLogger().severe("nextRound命令执行失败: " + e.getMessage());
            e.printStackTrace();
            return true;
        }
    }

    /**
     * 处理设置全局电源按钮命令
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param unlockPrice 解锁价格
     * @return 命令是否成功
     */
    private boolean handlePowerCommand(Player player, String gameName, int unlockPrice) {
        // 检查游戏是否存在
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 " + ChatColor.YELLOW + gameName + ChatColor.RED + " 不存在！");
            return true;
        }

        player.sendMessage(ChatColor.GREEN + "请破坏一个方块来设置全局电源按钮位置");
        player.sendMessage(ChatColor.YELLOW + "这个按钮将作为全局电源按钮，只有解锁后玩家才能购买sp类型的全体增益效果");
        player.sendMessage(ChatColor.YELLOW + "设置解锁价格: " + unlockPrice + " 金币");

        // 设置玩家正在编辑的游戏和特殊标记
        plugin.getPlayerInteractionManager().setEditingGame(player, gameName);
        plugin.getPlayerInteractionManager().setPlayerData(player, "setting_power_button", "true");
        plugin.getPlayerInteractionManager().setPlayerData(player, "power_button_price", String.valueOf(unlockPrice));

        return true;
    }

    /**
     * 处理clean命令，允许任何用户（包括终端）执行
     *
     * @param sender 命令发送者
     * @param args 命令参数
     * @return 命令是否成功
     */
    private boolean handleCleanCommandForAnyUser(CommandSender sender, String[] args) {
        // 检查权限 - 只有管理员或终端可以使用clean命令
        if (sender instanceof Player) {
            Player player = (Player) sender;
            if (!player.hasPermission("deathzombiev4.admin")) {
                player.sendMessage(ChatColor.RED + "你需要管理员权限来使用此指令！");
                return true;
            }
            // 玩家执行，使用原有的处理逻辑
            return handleCleanCommand(player, args);
        } else {
            // 终端执行
            if (args.length < 2) {
                // 清理所有游戏
                return cleanAllGamesForConsole(sender);
            } else {
                // 清理指定游戏
                String gameName = args[1];
                if (!gameManager.gameExists(gameName)) {
                    sender.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
                    return true;
                }
                return cleanSpecificGameForConsole(sender, gameName);
            }
        }
    }

    /**
     * 为终端清理所有游戏
     *
     * @param sender 命令发送者
     * @return 命令是否成功
     */
    private boolean cleanAllGamesForConsole(CommandSender sender) {
        Set<String> allGames = gameManager.getAllGames();
        if (allGames.isEmpty()) {
            sender.sendMessage(ChatColor.RED + "没有找到任何游戏！");
            return true;
        }

        int cleanedCount = 0;
        int totalGames = allGames.size();

        sender.sendMessage(ChatColor.YELLOW + "开始清理所有游戏（共 " + totalGames + " 个）...");

        for (String gameName : allGames) {
            if (cleanSpecificGameInternal(gameName)) {
                cleanedCount++;
                sender.sendMessage(ChatColor.GREEN + "[清理进度] " + cleanedCount + "/" + totalGames + " - 游戏 '" + gameName + "' 清理完成");
            } else {
                sender.sendMessage(ChatColor.RED + "[清理进度] " + cleanedCount + "/" + totalGames + " - 游戏 '" + gameName + "' 清理失败");
            }
        }

        sender.sendMessage(ChatColor.GREEN + "所有游戏清理完成！成功清理 " + cleanedCount + "/" + totalGames + " 个游戏。");
        return true;
    }

    /**
     * 为终端清理指定游戏
     *
     * @param sender 命令发送者
     * @param gameName 游戏名称
     * @return 命令是否成功
     */
    private boolean cleanSpecificGameForConsole(CommandSender sender, String gameName) {
        sender.sendMessage(ChatColor.YELLOW + "开始清理游戏 '" + gameName + "'...");

        if (cleanSpecificGameInternal(gameName)) {
            sender.sendMessage(ChatColor.GREEN + "游戏 '" + gameName + "' 清理完成！");
            return true;
        } else {
            sender.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 清理失败！");
            return false;
        }
    }

    /**
     * 将怪物中文名称转换为对应的ID
     * 如果输入的已经是ID格式，则直接返回
     *
     * @param input 输入的名称或ID
     * @return 对应的怪物ID
     */
    private String convertMonsterNameToId(String input) {
        if (input == null || input.isEmpty()) {
            return "id1"; // 默认返回普通僵尸
        }

        // 如果输入已经是ID格式，直接返回
        if (input.matches("^(id\\d+|idc\\d+|idn\\d+)$")) {
            return input;
        }

        // 创建名称到ID的映射
        Map<String, String> nameToId = new HashMap<>();

        // 僵尸类型 (id系列)
        nameToId.put("普通僵尸", "id1");
        nameToId.put("小僵尸", "id2");
        nameToId.put("路障僵尸", "id3");
        nameToId.put("钻斧僵尸", "id4");
        nameToId.put("剧毒僵尸", "id5");
        nameToId.put("双生僵尸", "id6");
        nameToId.put("骷髅僵尸", "id7");
        nameToId.put("武装僵尸", "id8");
        nameToId.put("肥胖僵尸", "id9");
        nameToId.put("法师僵尸", "id10");
        nameToId.put("自爆僵尸", "id11");
        nameToId.put("毒箭僵尸", "id12");
        nameToId.put("电击僵尸", "id13");
        nameToId.put("冰冻僵尸", "id14");
        nameToId.put("暗影僵尸", "id15");
        nameToId.put("毁灭僵尸", "id16");
        nameToId.put("雷霆僵尸", "id17");
        nameToId.put("变异科学家", "id18");
        nameToId.put("变异法师", "id19");
        nameToId.put("气球僵尸", "id20");
        nameToId.put("迷雾僵尸", "id21");
        nameToId.put("变异雷霆僵尸", "id22");
        nameToId.put("终极毁灭僵尸", "id23");
        nameToId.put("变异暗影僵尸", "id24");
        nameToId.put("变异博士", "id25");
        nameToId.put("尖叫僵尸", "id26");

        // 实体类型 (idc系列)
        nameToId.put("变异僵尸01", "idc1");
        nameToId.put("变异僵尸02", "idc2");
        nameToId.put("变异烈焰人", "idc3");
        nameToId.put("变异爬行者", "idc4");
        nameToId.put("变异末影螨", "idc5");
        nameToId.put("变异蜘蛛", "idc6");
        nameToId.put("灾厄卫道士", "idc7");
        nameToId.put("灾厄唤魔者", "idc8");
        nameToId.put("灾厄劫掠兽", "idc9");
        nameToId.put("变异僵尸马", "idc10");
        nameToId.put("变异岩浆怪", "idc11");
        nameToId.put("变异尸壳", "idc12");  // 修复：应该是变异尸壳，不是变异骷髅
        nameToId.put("变异僵尸3", "idc13");
        nameToId.put("变异僵尸04", "idc14");
        nameToId.put("鲜血猪灵", "idc15");  // 修复：idc15对应鲜血猪灵
        nameToId.put("暗影潜影贝", "idc16");  // 修复：idc16对应暗影潜影贝
        nameToId.put("变异雪傀儡", "idc17");  // 修复：去掉"2"
        nameToId.put("变异铁傀儡", "idc18");
        nameToId.put("变异僵尸Max", "idc19");
        nameToId.put("灵魂坚守者", "idc20");
        nameToId.put("凋零领主", "idc21");
        nameToId.put("异变之王", "idc22");

        // NPC类型 (idn系列)
        nameToId.put("感染者史蒂夫", "idn1");
        nameToId.put("感染者艾利克斯", "idn2");
        nameToId.put("感染者农民", "idn3");
        nameToId.put("感染者居民", "idn4");
        nameToId.put("感染猪", "idn5");

        // 查找对应的ID
        String id = nameToId.get(input);
        if (id != null) {
            return id;
        }

        // 如果找不到对应的ID，返回默认值
        plugin.getLogger().warning("未找到怪物名称 '" + input + "' 对应的ID，使用默认值 id1");
        return "id1";
    }

    /**
     * 根据怪物ID获取中文名称
     *
     * @param id 怪物ID
     * @return 对应的中文名称，如果找不到则返回null
     */
    private String getMonsterNameById(String id) {
        // 创建ID到名称的映射
        Map<String, String> idToName = new HashMap<>();

        // 僵尸类型 (id系列)
        idToName.put("id1", "普通僵尸");
        idToName.put("id2", "小僵尸");
        idToName.put("id3", "路障僵尸");
        idToName.put("id4", "钻斧僵尸");
        idToName.put("id5", "剧毒僵尸");
        idToName.put("id6", "双生僵尸");
        idToName.put("id7", "骷髅僵尸");
        idToName.put("id8", "武装僵尸");
        idToName.put("id9", "肥胖僵尸");
        idToName.put("id10", "法师僵尸");
        idToName.put("id11", "自爆僵尸");
        idToName.put("id12", "毒箭僵尸");
        idToName.put("id13", "电击僵尸");
        idToName.put("id14", "冰冻僵尸");
        idToName.put("id15", "暗影僵尸");
        idToName.put("id16", "毁灭僵尸");
        idToName.put("id17", "雷霆僵尸");
        idToName.put("id18", "变异科学家");
        idToName.put("id19", "变异法师");
        idToName.put("id20", "气球僵尸");
        idToName.put("id21", "迷雾僵尸");
        idToName.put("id22", "变异雷霆僵尸");
        idToName.put("id23", "终极毁灭僵尸");
        idToName.put("id24", "变异暗影僵尸");
        idToName.put("id25", "变异博士");
        idToName.put("id26", "尖叫僵尸");

        // 实体类型 (idc系列)
        idToName.put("idc1", "变异僵尸01");
        idToName.put("idc2", "变异僵尸02");
        idToName.put("idc3", "变异烈焰人");
        idToName.put("idc4", "变异爬行者");
        idToName.put("idc5", "变异末影螨");
        idToName.put("idc6", "变异蜘蛛");
        idToName.put("idc7", "灾厄卫道士");
        idToName.put("idc8", "灾厄唤魔者");
        idToName.put("idc9", "灾厄劫掠兽");
        idToName.put("idc10", "变异僵尸马");
        idToName.put("idc11", "变异岩浆怪");
        idToName.put("idc12", "变异尸壳");  // 修复：应该是变异尸壳，不是变异骷髅
        idToName.put("idc13", "变异僵尸3");
        idToName.put("idc14", "变异僵尸04");
        idToName.put("idc15", "鲜血猪灵");  // 修复：idc15对应鲜血猪灵
        idToName.put("idc16", "暗影潜影贝");  // 修复：idc16对应暗影潜影贝
        idToName.put("idc17", "变异雪傀儡");  // 修复：去掉"2"
        idToName.put("idc18", "变异铁傀儡");
        idToName.put("idc19", "变异僵尸Max");
        idToName.put("idc20", "灵魂坚守者");
        idToName.put("idc21", "凋零领主");
        idToName.put("idc22", "异变之王");

        // NPC类型 (idn系列)
        idToName.put("idn1", "感染者史蒂夫");
        idToName.put("idn2", "感染者艾利克斯");
        idToName.put("idn3", "感染者农民");
        idToName.put("idn4", "感染者居民");
        idToName.put("idn5", "感染猪");

        return idToName.get(id);
    }

    /**
     * 处理测试金钱获取方式命令
     *
     * @param player 玩家
     * @param mode 测试模式 (shoot/kill)
     * @return 命令是否成功
     */
    private boolean handleTestMoneyCommand(Player player, String mode) {
        String currentMode = plugin.getConfig().getString("game.money.getting_money_way", "kill");

        if (mode.equalsIgnoreCase("shoot")) {
            // 切换到射击模式
            plugin.getConfig().set("game.money.getting_money_way", "shoot");
            plugin.saveConfig();

            player.sendMessage(ChatColor.GREEN + "已切换到射击命中金钱奖励模式！");
            player.sendMessage(ChatColor.YELLOW + "现在玩家将通过射击命中僵尸获得金钱奖励");
            player.sendMessage(ChatColor.YELLOW + "头部命中倍数: " + plugin.getConfig().getDouble("game.money.headshot_multiplier", 2.0));
            player.sendMessage(ChatColor.YELLOW + "身体命中倍数: " + plugin.getConfig().getDouble("game.money.bodyshot_multiplier", 1.0));
            player.sendMessage(ChatColor.GRAY + "击杀奖励已禁用（除非在配置中设置了shoot模式下的击杀奖励）");

        } else if (mode.equalsIgnoreCase("kill")) {
            // 切换到击杀模式
            plugin.getConfig().set("game.money.getting_money_way", "kill");
            plugin.saveConfig();

            player.sendMessage(ChatColor.GREEN + "已切换到击杀金钱奖励模式！");
            player.sendMessage(ChatColor.YELLOW + "现在玩家将通过击杀僵尸获得金钱奖励");
            player.sendMessage(ChatColor.GRAY + "射击命中奖励已禁用");

        } else if (mode.equalsIgnoreCase("debug")) {
            // 切换头部命中检测调试模式
            boolean currentDebug = plugin.getConfig().getBoolean("game.money.headshot_debug", false);
            plugin.getConfig().set("game.money.headshot_debug", !currentDebug);
            plugin.saveConfig();

            if (!currentDebug) {
                player.sendMessage(ChatColor.GREEN + "已启用头部命中检测调试模式！");
                player.sendMessage(ChatColor.YELLOW + "现在每次射击命中都会在控制台输出详细的检测信息");
                player.sendMessage(ChatColor.GRAY + "请查看服务器控制台或日志文件");
            } else {
                player.sendMessage(ChatColor.GREEN + "已禁用头部命中检测调试模式！");
                player.sendMessage(ChatColor.GRAY + "不再输出头部命中检测调试信息");
            }

            // 显示当前检测参数
            player.sendMessage(ChatColor.AQUA + "=== 当前头部检测参数 ===");
            player.sendMessage(ChatColor.YELLOW + "头部区域百分比: " + (plugin.getConfig().getDouble("game.money.head_region_percentage", 0.2) * 100) + "%");
            player.sendMessage(ChatColor.YELLOW + "角度容差: " + plugin.getConfig().getDouble("game.money.angle_tolerance", 8.0) + " 度");
            player.sendMessage(ChatColor.GRAY + "提示: 头部区域越小越严格，角度容差越小越精确");

        } else {
            player.sendMessage(ChatColor.RED + "无效的模式！请使用 'shoot'、'kill' 或 'debug'");
            return true;
        }

        // 显示当前配置信息
        player.sendMessage(ChatColor.AQUA + "=== 当前金钱奖励配置 ===");
        player.sendMessage(ChatColor.YELLOW + "获取方式: " + plugin.getConfig().getString("game.money.getting_money_way"));

        if ("shoot".equalsIgnoreCase(plugin.getConfig().getString("game.money.getting_money_way"))) {
            player.sendMessage(ChatColor.YELLOW + "射击命中奖励:");
            player.sendMessage(ChatColor.WHITE + "  普通僵尸: " + plugin.getConfig().getDouble("game.money.zombie_hit", 2));
            player.sendMessage(ChatColor.WHITE + "  特殊僵尸: " + plugin.getConfig().getDouble("game.money.special_zombie_hit", 4));
            player.sendMessage(ChatColor.WHITE + "  变异怪物: " + plugin.getConfig().getDouble("game.money.mutant_hit", 6));
            player.sendMessage(ChatColor.WHITE + "  感染者NPC: " + plugin.getConfig().getDouble("game.money.npc_hit", 10));
        } else {
            player.sendMessage(ChatColor.YELLOW + "击杀奖励:");
            player.sendMessage(ChatColor.WHITE + "  普通僵尸: " + plugin.getConfig().getDouble("game.money.zombie_kill", 10));
            player.sendMessage(ChatColor.WHITE + "  特殊僵尸: " + plugin.getConfig().getDouble("game.money.special_zombie_kill", 20));
            player.sendMessage(ChatColor.WHITE + "  变异怪物: " + plugin.getConfig().getDouble("game.money.mutant_kill", 30));
            player.sendMessage(ChatColor.WHITE + "  感染者NPC: " + plugin.getConfig().getDouble("game.money.npc_kill", 50));
        }

        return true;
    }

    /**
     * 处理top排行榜命令
     */
    private boolean handleTopCommand(Player player, String[] args) {
        String statTypeStr = args[1];
        String mapName = null;
        int limit = 10; // 默认显示前10名

        // 解析参数
        if (args.length > 2) {
            // 检查第三个参数是否是数字（查询范围）
            try {
                limit = Integer.parseInt(args[2]);
                if (limit <= 0 || limit > 50) {
                    player.sendMessage(ChatColor.RED + "查询范围必须在1-50之间！");
                    return true;
                }
            } catch (NumberFormatException e) {
                // 第三个参数不是数字，可能是地图名称
                mapName = args[2];
                if (args.length > 3) {
                    try {
                        limit = Integer.parseInt(args[3]);
                        if (limit <= 0 || limit > 50) {
                            player.sendMessage(ChatColor.RED + "查询范围必须在1-50之间！");
                            return true;
                        }
                    } catch (NumberFormatException e2) {
                        player.sendMessage(ChatColor.RED + "查询范围必须是有效的数字！");
                        return true;
                    }
                }
            }
        }

        // 转换统计类型
        LeaderboardManager.StatType statType = LeaderboardManager.StatType.fromDisplayName(statTypeStr);
        if (statType == null) {
            player.sendMessage(ChatColor.RED + "无效的统计类型！");
            player.sendMessage(ChatColor.YELLOW + "可用类型: 总游戏次数, 累计击杀僵尸数, 累计生存回合数, 累计救援队友数, 累计最高金钱数, 指定地图生存回合最大值, 最快单地图游戏耗时");
            return true;
        }

        // 检查地图相关统计是否提供了地图名称
        if ((statType == LeaderboardManager.StatType.MAP_MAX_ROUNDS ||
             statType == LeaderboardManager.StatType.MAP_FASTEST_TIME) &&
            (mapName == null || mapName.isEmpty())) {
            player.sendMessage(ChatColor.RED + "地图相关统计需要指定地图名称！");
            player.sendMessage(ChatColor.YELLOW + "用法: /dzs top " + statTypeStr + " <地图名称> [查询范围]");
            return true;
        }

        // 显示排行榜
        LeaderboardManager leaderboardManager = new LeaderboardManager(plugin);
        leaderboardManager.showLeaderboard(player, statType, mapName, limit);

        return true;
    }

    /**
     * 处理topHolo全息排行榜命令
     */
    private boolean handleTopHoloCommand(Player player, String[] args) {
        String action = args[1].toLowerCase();

        if (action.equals("remove")) {
            if (args.length < 3) {
                player.sendMessage(ChatColor.RED + "用法: /dzs topHolo remove <全息图名称>");
                return true;
            }
            return handleTopHoloRemove(player, args[2]);
        }

        if (action.equals("list")) {
            HologramLeaderboardManager holoManager = plugin.getHologramLeaderboardManager();
            if (holoManager == null) {
                player.sendMessage(ChatColor.RED + "全息排行榜管理器未初始化！");
                return true;
            }
            holoManager.listHologramLeaderboards(player);
            return true;
        }

        if (!action.equals("set")) {
            player.sendMessage(ChatColor.RED + "无效的操作类型！必须是 set, remove 或 list");
            return true;
        }

        if (args.length < 4) {
            player.sendMessage(ChatColor.RED + "用法: /dzs topHolo set <统计类型> <全息图名称> [地图名称] [显示范围]");
            return true;
        }

        String statTypeStr = args[2];
        String holoName = args[3];
        String mapName = null;
        int displayRange = 10; // 默认显示前10名

        // 解析可选参数
        if (args.length > 4) {
            // 检查第五个参数是否是数字（显示范围）
            try {
                displayRange = Integer.parseInt(args[4]);
                if (displayRange <= 0 || displayRange > 20) {
                    player.sendMessage(ChatColor.RED + "显示范围必须在1-20之间！");
                    return true;
                }
            } catch (NumberFormatException e) {
                // 第五个参数不是数字，可能是地图名称
                mapName = args[4];
                if (args.length > 5) {
                    try {
                        displayRange = Integer.parseInt(args[5]);
                        if (displayRange <= 0 || displayRange > 20) {
                            player.sendMessage(ChatColor.RED + "显示范围必须在1-20之间！");
                            return true;
                        }
                    } catch (NumberFormatException e2) {
                        player.sendMessage(ChatColor.RED + "显示范围必须是有效的数字！");
                        return true;
                    }
                }
            }
        }

        return handleTopHoloSet(player, statTypeStr, holoName, mapName, displayRange);
    }

    /**
     * 处理创建全息排行榜
     */
    private boolean handleTopHoloSet(Player player, String statTypeStr, String holoName, String mapName, int displayRange) {
        // 验证全息图名称
        if (!HologramLeaderboardManager.isValidHologramName(holoName)) {
            player.sendMessage(ChatColor.RED + "全息图名称只能包含英文字母和数字，且长度不超过32个字符！");
            player.sendMessage(ChatColor.YELLOW + "有效示例: GameRanking, TopPlayers, Map01Ranking");
            return true;
        }

        // 检查是否是全部数据
        boolean showAllData = statTypeStr.equals("全部数据") || statTypeStr.equalsIgnoreCase("all");

        LeaderboardManager.StatType statType = null;
        if (!showAllData) {
            statType = LeaderboardManager.StatType.fromDisplayName(statTypeStr);
            if (statType == null) {
                player.sendMessage(ChatColor.RED + "无效的统计类型！");
                player.sendMessage(ChatColor.YELLOW + "支持的类型:");
                player.sendMessage(ChatColor.WHITE + "中文: 总游戏次数, 累计击杀僵尸数, 累计生存回合数, 累计救援队友数, 累计最高金钱数, 指定地图生存回合最大值, 最快单地图游戏耗时, 全部数据");
                player.sendMessage(ChatColor.WHITE + "英文: games, kills, rounds, rescues, money, maprounds, maptime, all");
                return true;
            }

            // 检查地图相关统计是否提供了地图名称
            if ((statType == LeaderboardManager.StatType.MAP_MAX_ROUNDS ||
                 statType == LeaderboardManager.StatType.MAP_FASTEST_TIME) &&
                (mapName == null || mapName.isEmpty())) {
                player.sendMessage(ChatColor.RED + "地图相关统计需要指定地图名称！");
                player.sendMessage(ChatColor.YELLOW + "用法: /dzs topHolo set " + statTypeStr + " " + holoName + " <地图名称> [显示范围]");
                return true;
            }
        }

        // 创建全息排行榜
        HologramLeaderboardManager holoManager = plugin.getHologramLeaderboardManager();
        if (holoManager == null) {
            player.sendMessage(ChatColor.RED + "全息排行榜管理器未初始化！请确保DecentHolograms插件已安装。");
            return true;
        }

        return holoManager.createHologramLeaderboard(player, holoName, statType, mapName, displayRange, showAllData);
    }

    /**
     * 处理移除全息排行榜
     */
    private boolean handleTopHoloRemove(Player player, String holoName) {
        HologramLeaderboardManager holoManager = plugin.getHologramLeaderboardManager();
        if (holoManager == null) {
            player.sendMessage(ChatColor.RED + "全息排行榜管理器未初始化！");
            return true;
        }

        return holoManager.removeHologramLeaderboard(player, holoName);
    }

    /**
     * 显示top命令帮助
     */
    private void showTopCommandHelp(Player player) {
        player.sendMessage(ChatColor.GOLD + "========== 排行榜查询帮助 ==========");
        player.sendMessage(ChatColor.YELLOW + "用法: /dzs top <统计类型> [地图名称] [查询范围]");
        player.sendMessage("");
        player.sendMessage(ChatColor.AQUA + "统计类型 (支持中文和英文):");
        player.sendMessage(ChatColor.WHITE + "• 总游戏次数 (games) - 玩家总游戏次数");
        player.sendMessage(ChatColor.WHITE + "• 累计击杀僵尸数 (kills) - 累计击杀僵尸数量");
        player.sendMessage(ChatColor.WHITE + "• 累计生存回合数 (rounds) - 累计生存回合数");
        player.sendMessage(ChatColor.WHITE + "• 累计救援队友数 (rescues) - 累计救援队友次数");
        player.sendMessage(ChatColor.WHITE + "• 累计最高金钱数 (money) - 累计获得的最高金钱");
        player.sendMessage(ChatColor.WHITE + "• 指定地图生存回合最大值 (maprounds) - 指定地图最高生存回合");
        player.sendMessage(ChatColor.WHITE + "• 最快单地图游戏耗时 (maptime) - 指定地图最快完成时间");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "示例:");
        player.sendMessage(ChatColor.WHITE + "/dzs top games 10 - 查看总游戏次数前10名");
        player.sendMessage(ChatColor.WHITE + "/dzs top maprounds 监狱风云 5 - 查看监狱风云地图前5名");
        player.sendMessage(ChatColor.WHITE + "/dzs top 累计击杀僵尸数 - 查看击杀数排行榜");
    }

    /**
     * 显示topHolo命令帮助
     */
    private void showTopHoloCommandHelp(Player player) {
        player.sendMessage(ChatColor.GOLD + "========== 全息排行榜管理帮助 ==========");
        player.sendMessage(ChatColor.YELLOW + "用法: /dzs topHolo <操作> [参数...]");
        player.sendMessage("");
        player.sendMessage(ChatColor.AQUA + "操作类型:");
        player.sendMessage(ChatColor.WHITE + "• set <统计类型> <全息图名称> [地图名称] [显示范围] - 创建全息排行榜");
        player.sendMessage(ChatColor.WHITE + "• remove <全息图名称> - 移除全息排行榜");
        player.sendMessage(ChatColor.WHITE + "• list - 列出所有全息排行榜");
        player.sendMessage("");
        player.sendMessage(ChatColor.AQUA + "统计类型 (支持中文和英文):");
        player.sendMessage(ChatColor.WHITE + "• 所有top命令支持的统计类型");
        player.sendMessage(ChatColor.WHITE + "• 全部数据 (all) - 显示综合统计数据");
        player.sendMessage("");
        player.sendMessage(ChatColor.AQUA + "全息图名称规则:");
        player.sendMessage(ChatColor.WHITE + "• 只能包含英文字母和数字");
        player.sendMessage(ChatColor.WHITE + "• 长度不超过32个字符");
        player.sendMessage(ChatColor.WHITE + "• 例如: GameRanking, TopPlayers, Map01Ranking");
        player.sendMessage("");
        player.sendMessage(ChatColor.GREEN + "示例:");
        player.sendMessage(ChatColor.WHITE + "/dzs topHolo set games GameRanking 10");
        player.sendMessage(ChatColor.WHITE + "/dzs topHolo set maprounds PrisonRanking 监狱风云 15");
        player.sendMessage(ChatColor.WHITE + "/dzs topHolo set all AllStats 10");
        player.sendMessage(ChatColor.WHITE + "/dzs topHolo remove GameRanking");
        player.sendMessage(ChatColor.WHITE + "/dzs topHolo list");
        player.sendMessage("");
        player.sendMessage(ChatColor.YELLOW + "交互说明:");
        player.sendMessage(ChatColor.WHITE + "• 左键: 切换数据类型 (全部数据模式) 或 上一页 (单项数据模式)");
        player.sendMessage(ChatColor.WHITE + "• 右键: 切换数据类型 (全部数据模式) 或 下一页 (单项数据模式)");
    }


}
