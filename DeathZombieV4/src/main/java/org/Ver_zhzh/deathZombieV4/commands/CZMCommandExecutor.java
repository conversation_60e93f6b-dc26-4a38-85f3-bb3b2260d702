package org.Ver_zhzh.deathZombieV4.commands;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.utils.ZombieHelper;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.entity.Zombie;
import java.util.Map;

/**
 * 处理/czm命令的执行器类
 */
public class CZMCommandExecutor implements CommandExecutor {

    private final DeathZombieV4 plugin;
    private final ZombieHelper zombieHelper;
    private final CZMTabCompleter tabCompleter;

    /**
     * 构造函数
     *
     * @param plugin DeathZombieV4插件实例
     */
    public CZMCommandExecutor(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.zombieHelper = plugin.getZombieHelper();
        this.tabCompleter = new CZMTabCompleter(plugin);
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // 允许非玩家（控制台、命令方块等）使用此命令
        Player player = null;
        if (sender instanceof Player) {
            player = (Player) sender;
        }

        if (args.length < 1) {
            sendHelpMessage(player);
            return true;
        }

        switch (args[0].toLowerCase()) {
            case "zombie":
                if (args.length < 2) {
                    sender.sendMessage(ChatColor.RED + "请指定僵尸名称或ID! 用法: /czm zombie <名称|ID>");
                    sender.sendMessage(ChatColor.YELLOW + "示例: /czm zombie 普通僵尸 或 /czm zombie id1");
                    return true;
                }

                if (player == null) {
                    sender.sendMessage(ChatColor.RED + "此子命令需要玩家执行，因为需要获取玩家位置!");
                    return true;
                }

                String zombieInput = args[1];
                String zombieId = convertZombieNameToId(zombieInput);

                if (zombieId == null) {
                    sender.sendMessage(ChatColor.RED + "未知的僵尸名称或ID: " + zombieInput);
                    sender.sendMessage(ChatColor.YELLOW + "请使用Tab补全查看可用的僵尸名称");
                    return true;
                }

                Zombie zombie = zombieHelper.spawnCustomZombie(player.getLocation(), zombieId);

                if (zombie != null) {
                    String zombieDisplayName = getZombieDisplayName(zombieInput, zombieId);
                    sender.sendMessage(ChatColor.GREEN + "成功生成自定义僵尸: " + zombieDisplayName);
                } else {
                    sender.sendMessage(ChatColor.RED + "生成自定义僵尸失败");
                }
                return true;

            case "other":
                if (args.length < 2) {
                    sender.sendMessage(ChatColor.RED + "请指定实体名称或ID! 用法: /czm other <名称|IDC>");
                    sender.sendMessage(ChatColor.YELLOW + "示例: /czm other 变异僵尸01 或 /czm other idc1");
                    return true;
                }

                if (player == null) {
                    sender.sendMessage(ChatColor.RED + "此子命令需要玩家执行，因为需要获取玩家位置!");
                    return true;
                }

                String entityInput = args[1];
                String entityId = convertEntityNameToId(entityInput);

                if (entityId == null) {
                    sender.sendMessage(ChatColor.RED + "未知的实体名称或ID: " + entityInput);
                    sender.sendMessage(ChatColor.YELLOW + "请使用Tab补全查看可用的实体名称");
                    return true;
                }

                // 调用CustomZombie的spawnOtherEntity方法，传入Player参数
                zombieHelper.getCustomZombie().spawnOtherEntity(player, entityId);
                String entityDisplayName = getEntityDisplayName(entityInput, entityId);
                sender.sendMessage(ChatColor.GREEN + "成功生成实体: " + entityDisplayName);
                return true;

            case "npc":
                if (args.length < 2) {
                    sender.sendMessage(ChatColor.RED + "请指定NPC名称或ID! 用法: /czm npc <名称|IDN>");
                    sender.sendMessage(ChatColor.YELLOW + "示例: /czm npc 感染者史蒂夫 或 /czm npc idn1");
                    return true;
                }

                if (player == null) {
                    sender.sendMessage(ChatColor.RED + "此子命令需要玩家执行，因为需要获取玩家位置!");
                    return true;
                }

                // 检查Citizens插件是否可用
                if (Bukkit.getPluginManager().getPlugin("Citizens") == null) {
                    sender.sendMessage(ChatColor.RED + "无法生成NPC! 服务器未安装Citizens插件。");
                    return true;
                }

                String npcInput = args[1];
                String npcId = convertNpcNameToId(npcInput);

                if (npcId == null) {
                    sender.sendMessage(ChatColor.RED + "未知的NPC名称或ID: " + npcInput);
                    sender.sendMessage(ChatColor.YELLOW + "请使用Tab补全查看可用的NPC名称");
                    return true;
                }

                boolean npcSuccess = spawnNPC(player, npcId);

                if (npcSuccess) {
                    String npcDisplayName = getNpcDisplayName(npcInput, npcId);
                    sender.sendMessage(ChatColor.GREEN + "成功生成NPC: " + npcDisplayName);
                } else {
                    sender.sendMessage(ChatColor.RED + "生成NPC失败: " + npcId);
                }
                return true;

            case "gui":
                if (player == null) {
                    sender.sendMessage(ChatColor.RED + "此子命令需要玩家执行，因为需要打开GUI界面!");
                    return true;
                }

                // 尝试打开GUI菜单
                boolean guiOpened = zombieHelper.openGUI(player);
                if (!guiOpened) {
                    sender.sendMessage(ChatColor.RED + "GUI管理器未初始化，无法打开GUI界面");
                }
                return true;

            case "help":
            default:
                sendHelpMessage(sender);
                return true;
        }
    }

    /**
     * 根据NPC ID生成相应的NPC
     *
     * @param player 玩家
     * @param npcId NPC ID
     * @return 是否成功生成
     */
    private boolean spawnNPC(Player player, String npcId) {
        switch (npcId) {
            case "idn1":
                // 感染者1
                return zombieHelper.createInfectedNPC1(player.getLocation());
            case "idn2":
                // 感染者2号
                return zombieHelper.createInfectedNPC2(player.getLocation());
            case "idn3":
                // 感染者农民
                return zombieHelper.createInfectedFarmer(player.getLocation());
            case "idn4":
                // 感染者居民
                return zombieHelper.createInfectedResident(player.getLocation());
            case "idn5":
                // 感染猪
                return zombieHelper.createInfectedPig(player.getLocation());
            default:
                return false;
        }
    }

    /**
     * 发送帮助信息
     *
     * @param sender 命令发送者
     */
    private void sendHelpMessage(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "======= CustomZombie 帮助 =======");
        sender.sendMessage(ChatColor.GREEN + "/czm zombie <名称|ID> - 生成指定的僵尸 (需要玩家执行)");
        sender.sendMessage(ChatColor.YELLOW + "  支持中文名称: 普通僵尸, 小僵尸, 路障僵尸, 钻斧僵尸...");
        sender.sendMessage(ChatColor.YELLOW + "  示例: /czm zombie 普通僵尸 或 /czm zombie id1");
        sender.sendMessage(ChatColor.GREEN + "/czm other <名称|IDC> - 生成指定的其他实体 (需要玩家执行)");
        sender.sendMessage(ChatColor.YELLOW + "  支持中文名称: 变异僵尸01, 变异僵尸02, 变异烈焰人...");
        sender.sendMessage(ChatColor.YELLOW + "  示例: /czm other 变异僵尸01 或 /czm other idc1");
        sender.sendMessage(ChatColor.GREEN + "/czm npc <名称|IDN> - 生成指定的NPC (需要玩家执行)");
        sender.sendMessage(ChatColor.YELLOW + "  支持中文名称: 感染者史蒂夫, 感染者艾利克斯, 感染者农民...");
        sender.sendMessage(ChatColor.YELLOW + "  示例: /czm npc 感染者史蒂夫 或 /czm npc idn1");
        sender.sendMessage(ChatColor.GREEN + "/czm gui - 打开自定义僵尸GUI菜单 (需要玩家执行)");
        sender.sendMessage(ChatColor.GREEN + "/czm help - 显示帮助信息");
        sender.sendMessage(ChatColor.GRAY + "提示: 使用Tab键可以自动补全中文名称");
    }

    /**
     * 转换僵尸名称到ID
     *
     * @param input 用户输入的名称或ID
     * @return 对应的ID，如果无效则返回null
     */
    private String convertZombieNameToId(String input) {
        // 如果输入已经是ID格式，直接返回
        if (input.toLowerCase().startsWith("id")) {
            return input.toLowerCase();
        }

        // 尝试从中文名称转换
        String id = tabCompleter.getZombieIdByName(input);
        return id;
    }

    /**
     * 转换实体名称到ID
     *
     * @param input 用户输入的名称或ID
     * @return 对应的ID，如果无效则返回null
     */
    private String convertEntityNameToId(String input) {
        // 如果输入已经是ID格式，直接返回
        if (input.toLowerCase().startsWith("idc")) {
            return input.toLowerCase();
        }

        // 尝试从中文名称转换
        String id = tabCompleter.getEntityIdByName(input);
        return id;
    }

    /**
     * 转换NPC名称到ID
     *
     * @param input 用户输入的名称或ID
     * @return 对应的ID，如果无效则返回null
     */
    private String convertNpcNameToId(String input) {
        // 如果输入已经是ID格式，直接返回
        if (input.toLowerCase().startsWith("idn")) {
            return input.toLowerCase();
        }

        // 尝试从中文名称转换
        String id = tabCompleter.getNpcIdByName(input);
        return id;
    }

    /**
     * 获取僵尸显示名称
     *
     * @param input 用户输入
     * @param id 实际ID
     * @return 显示名称
     */
    private String getZombieDisplayName(String input, String id) {
        // 如果输入是中文名称，直接返回
        if (!input.toLowerCase().startsWith("id")) {
            return input;
        }

        // 如果输入是ID，尝试转换为中文名称
        for (Map.Entry<String, String> entry : tabCompleter.zombieNameToId.entrySet()) {
            if (entry.getValue().equals(id)) {
                return entry.getKey() + " (" + id + ")";
            }
        }

        return id;
    }

    /**
     * 获取实体显示名称
     *
     * @param input 用户输入
     * @param id 实际ID
     * @return 显示名称
     */
    private String getEntityDisplayName(String input, String id) {
        // 如果输入是中文名称，直接返回
        if (!input.toLowerCase().startsWith("idc")) {
            return input;
        }

        // 如果输入是ID，尝试转换为中文名称
        for (Map.Entry<String, String> entry : tabCompleter.entityNameToId.entrySet()) {
            if (entry.getValue().equals(id)) {
                return entry.getKey() + " (" + id + ")";
            }
        }

        return id;
    }

    /**
     * 获取NPC显示名称
     *
     * @param input 用户输入
     * @param id 实际ID
     * @return 显示名称
     */
    private String getNpcDisplayName(String input, String id) {
        // 如果输入是中文名称，直接返回
        if (!input.toLowerCase().startsWith("idn")) {
            return input;
        }

        // 如果输入是ID，尝试转换为中文名称
        for (Map.Entry<String, String> entry : tabCompleter.npcNameToId.entrySet()) {
            if (entry.getValue().equals(id)) {
                return entry.getKey() + " (" + id + ")";
            }
        }

        return id;
    }
}
