package org.Ver_zhzh.deathZombieV4.gui;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.GameKitManager;
import org.Ver_zhzh.deathZombieV4.game.GameManager;
import org.Ver_zhzh.deathZombieV4.game.GameSessionManager;
import org.Ver_zhzh.deathZombieV4.utils.ShootPluginHelper;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

/**
 * 初始装备GUI编辑器
 */
public class KitEditorGUI implements Listener {

    private final DeathZombieV4 plugin;
    private final GameManager gameManager;
    private final GameSessionManager gameSessionManager;
    private final GameKitManager gameKitManager;

    // 存储玩家正在编辑的游戏名称
    private final Map<Player, String> editingGames = new HashMap<>();

    // 存储GUI界面的标题前缀，用于识别
    private static final String GUI_TITLE_PREFIX = ChatColor.DARK_GREEN + "初始装备编辑器 - ";
    private static final String SLOT_OPTIONS_TITLE_PREFIX = ChatColor.DARK_BLUE + "槽位 ";
    private static final String SLOT_TYPE_SELECTION_TITLE = ChatColor.BLUE + "选择槽位类型";
    private static final String ITEM_SELECTION_TITLE = ChatColor.AQUA + "选择物品";
    private static final String ITEM_CATEGORY_TITLE = ChatColor.LIGHT_PURPLE + "选择物品类别";
    private static final String CONFIRM_TITLE = ChatColor.YELLOW + "确认选择";
    private static final String CHANGE_TYPE_CONFIRM_TITLE = ChatColor.RED + "确认更改类型";

    // 物品选择相关
    private final Map<Player, Integer> currentPage = new HashMap<>();
    private final Map<Player, String> currentCategory = new HashMap<>();
    private static final int ITEMS_PER_PAGE = 21; // 每页显示21个物品（3行x7列）

    // 存储槽位类型按钮的位置
    private static final int WEAPON_SLOT_BUTTON = 45;
    private static final int ITEM_SLOT_BUTTON = 46;
    private static final int LOCKED_SLOT_BUTTON = 47;
    private static final int SAVE_BUTTON = 53;

    // 存储玩家正在编辑的槽位信息
    private final Map<Player, Integer> editingSlots = new HashMap<>();

    public KitEditorGUI(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.gameManager = plugin.getGameManager();
        this.gameSessionManager = plugin.getGameSessionManager();
        this.gameKitManager = plugin.getGameKitManager();

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }

    /**
     * 打开初始装备编辑器GUI（包含临时更改）
     */
    public void openKitEditorWithChanges(Player player, String gameName) {
        openKitEditor(player, gameName, true);
    }

    /**
     * 打开初始装备编辑器GUI
     *
     * @param player 玩家
     * @param gameName 游戏名称
     */
    public void openKitEditor(Player player, String gameName) {
        openKitEditor(player, gameName, false);
    }

    /**
     * 打开初始装备编辑器GUI的内部实现
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param includeChanges 是否包含临时更改
     */
    private void openKitEditor(Player player, String gameName, boolean includeChanges) {
        // 检查游戏是否存在
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 '" + gameName + "' 不存在！");
            return;
        }

        // 创建一个6行的GUI（54格）- 前4行为背包槽位，第5行为护甲槽位，第6行为控制按钮
        Inventory inventory = Bukkit.createInventory(null, 54, GUI_TITLE_PREFIX + gameName);

        // 获取游戏的初始装备配置
        Map<Integer, String> initialEquipment = gameSessionManager.getGameInitialEquipment(gameName);

        // 获取游戏配置
        FileConfiguration config = gameManager.getGameConfig(gameName);

        // 先用玻璃板填充所有背包槽位（前36格）
        for (int i = 0; i < 36; i++) {
            inventory.setItem(i, createDetailedSlotItem(null, null));
        }

        // 设置护甲区域（第5行，36-39槽位）
        setupArmorSection(inventory, gameName);

        // 设置分隔线（第5行剩余槽位）
        for (int i = 40; i < 45; i++) {
            inventory.setItem(i, createSeparatorItem());
        }

        // 填充物品栏
        if (initialEquipment != null && !initialEquipment.isEmpty()) {
            for (Map.Entry<Integer, String> entry : initialEquipment.entrySet()) {
                int slot = entry.getKey() - 1; // 配置从1开始，物品栏从0开始
                String itemId = entry.getValue();

                // 如果包含临时更改，检查是否有临时覆盖
                if (includeChanges) {
                    String tempKey = "temp.kit_changes." + player.getUniqueId() + "." + slot;
                    String tempItemId = plugin.getConfig().getString(tempKey);
                    if (tempItemId != null) {
                        itemId = tempItemId;
                    }
                }

                if (slot >= 0 && slot < 36) { // 只处理前36个槽位（玩家背包大小）
                    if (itemId.equals("white_dye") || itemId.equals("id69")) {
                        // 检查是否有槽位类型配置
                        String slotTypeKey = "initialEquipment.slot_type_" + entry.getKey();
                        String slotType = config != null ? config.getString(slotTypeKey) : null;

                        if (slotType != null) {
                            // 创建带有详细信息的槽位物品（没有初始值）
                            inventory.setItem(slot, createDetailedSlotItem(slotType, null));
                        } else {
                            // 默认锁定槽位
                            inventory.setItem(slot, createDetailedSlotItem("locked_slot", null));
                        }
                    } else {
                        // 获取槽位类型
                        String slotTypeKey = "initialEquipment.slot_type_" + entry.getKey();
                        String slotType = config != null ? config.getString(slotTypeKey) : null;

                        // 如果没有设置槽位类型，根据物品ID猜测类型
                        if (slotType == null) {
                            if (itemId.startsWith("id")) {
                                // 假设id开头的是武器
                                slotType = "weapon_slot";
                            } else {
                                // 其他物品默认为道具槽位
                                slotType = "item_slot";
                            }
                        }

                        // 创建带有详细信息的槽位物品
                        inventory.setItem(slot, createDetailedSlotItem(slotType, itemId));
                    }
                }
            }
        }

        // 如果包含临时更改，还需要处理新增的槽位（原本没有配置的槽位）
        if (includeChanges) {
            for (int slot = 0; slot < 36; slot++) {
                String tempKey = "temp.kit_changes." + player.getUniqueId() + "." + slot;
                String tempItemId = plugin.getConfig().getString(tempKey);
                if (tempItemId != null) {
                    // 检查当前槽位是否已经被设置
                    ItemStack currentItem = inventory.getItem(slot);
                    if (currentItem == null || currentItem.getType() == Material.GLASS_PANE) {
                        // 这是一个新增的槽位或空槽位，设置临时更改的物品
                        String slotType = tempItemId.startsWith("id") ? "weapon_slot" : "item_slot";
                        inventory.setItem(slot, createDetailedSlotItem(slotType, tempItemId));
                    } else {
                        // 槽位已有物品，但需要更新为临时更改的物品
                        String slotType = tempItemId.startsWith("id") ? "weapon_slot" : "item_slot";
                        inventory.setItem(slot, createDetailedSlotItem(slotType, tempItemId));
                    }
                }
            }
        }

        // 添加底部控制按钮（只保留保存按钮）

        inventory.setItem(SAVE_BUTTON, createControlButton(Material.EMERALD_BLOCK, "保存并关闭",
                ChatColor.GREEN + "保存并关闭",
                ChatColor.GRAY + "点击此按钮保存所有更改并关闭编辑器"));

        // 记录玩家正在编辑的游戏
        editingGames.put(player, gameName);

        // 打开物品栏
        player.openInventory(inventory);
    }

    /**
     * 创建详细信息的槽位物品
     *
     * @param slotType 槽位类型 (weapon_slot, item_slot, locked_slot)
     * @param itemId 物品ID，如果没有则为null
     * @return 创建的物品
     */
    private ItemStack createDetailedSlotItem(String slotType, String itemId) {
        ItemStack item;
        String displayName;
        // 修复判断逻辑：只要itemId不为null、不为空字符串、不是占位符就认为有初始值
        boolean hasInitial = (itemId != null && !itemId.trim().isEmpty() &&
                             !itemId.equals("white_dye") && !itemId.equals("id69"));

        // 如果有初始值，直接使用实际物品的材质和属性
        if (hasInitial) {
            ItemStack actualItem = createItemById(itemId);
            if (actualItem != null) {
                // 使用实际物品作为基础
                item = actualItem.clone();
                displayName = getItemDisplayName(itemId);
            } else {
                // 回退到默认材质
                item = getDefaultItemBySlotType(slotType);
                displayName = getItemDisplayName(itemId);
            }
        } else {
            // 没有初始值，使用默认材质
            item = getDefaultItemBySlotType(slotType);
            displayName = "空闲设置";
        }

        // 设置物品信息
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(ChatColor.GOLD + displayName);
            List<String> lore = new ArrayList<>();

            if (hasInitial) {
                lore.add(ChatColor.GRAY + "初始值: " + ChatColor.YELLOW + itemId);
            }

            lore.add("");
            lore.add(ChatColor.AQUA + "点击设置此槽位");

            meta.setLore(lore);
            item.setItemMeta(meta);
        }

        return item;
    }

    /**
     * 根据槽位类型获取默认物品
     */
    private ItemStack getDefaultItemBySlotType(String slotType) {
        if (slotType == null || slotType.isEmpty()) {
            // 未设置槽位类型，使用玻璃板
            return new ItemStack(Material.GLASS_PANE);
        } else if (slotType.equals("weapon_slot")) {
            return new ItemStack(Material.IRON_SWORD);
        } else if (slotType.equals("item_slot")) {
            return new ItemStack(Material.CHEST);
        } else {
            return new ItemStack(Material.WHITE_DYE);
        }
    }

    /**
     * 获取物品显示名称
     *
     * @param itemId 物品ID
     * @return 物品的中文显示名称
     */
    private String getItemDisplayName(String itemId) {
        if (itemId == null || itemId.trim().isEmpty()) {
            return "未知物品";
        }

        // 尝试使用GameKitManager获取物品名称
        if (gameKitManager != null) {
            String itemName = gameKitManager.getItemName(itemId);
            if (itemName != null && !itemName.equals(itemId)) {
                // 移除颜色代码，返回纯文本名称
                String cleanName = ChatColor.stripColor(itemName);
                return cleanName;
            }
        }

        // 如果GameKitManager不可用或未找到物品名称，尝试创建物品实例获取名称
        try {
            ItemStack tempItem = createItemById(itemId);
            if (tempItem != null && tempItem.getItemMeta() != null && tempItem.getItemMeta().hasDisplayName()) {
                String displayName = ChatColor.stripColor(tempItem.getItemMeta().getDisplayName());
                return displayName;
            }
        } catch (Exception e) {
            plugin.getLogger().warning("获取物品显示名称时发生错误: " + e.getMessage());
        }

        // 回退到原始ID
        return itemId;
    }

    /**
     * 根据物品ID推断槽位类型
     *
     * @param itemId 物品ID
     * @return 槽位类型 (weapon_slot, item_slot, locked_slot)
     */
    private String inferSlotTypeFromItemId(String itemId) {
        if (itemId == null || itemId.isEmpty()) {
            return "locked_slot";
        }

        // 处理数字ID
        if (itemId.startsWith("id") && itemId.length() > 2) {
            try {
                int numId = Integer.parseInt(itemId.substring(2));
                if (numId >= 1 && numId <= 24) {
                    return "weapon_slot"; // 武器
                } else if (numId >= 25 && numId <= 37) {
                    return "item_slot"; // 护甲（归类为物品）
                } else if (numId >= 38 && numId <= 70) {
                    return "item_slot"; // 物品和特殊物品
                }
            } catch (NumberFormatException e) {
                // 不是有效的数字ID
            }
        }

        // 默认为道具槽位
        return "item_slot";
    }

    /**
     * 设置护甲区域
     */
    private void setupArmorSection(Inventory inventory, String gameName) {
        // 获取游戏配置以检查是否已设置护甲套装
        FileConfiguration config = gameManager.getGameConfig(gameName);

        // 上身护甲套装（36）- 包含头盔和胸甲
        String upperArmorId = config != null ? config.getString("armorEquipment.upper") : null;
        if (upperArmorId != null) {
            inventory.setItem(36, createArmorSetItemWithValue("upper", "上身护甲套装", upperArmorId));
        } else {
            inventory.setItem(36, createArmorSetItem("upper", "上身护甲套装"));
        }

        // 下身护甲套装（37）- 包含护腿和靴子
        String lowerArmorId = config != null ? config.getString("armorEquipment.lower") : null;
        if (lowerArmorId != null) {
            inventory.setItem(37, createArmorSetItemWithValue("lower", "下身护甲套装", lowerArmorId));
        } else {
            inventory.setItem(37, createArmorSetItem("lower", "下身护甲套装"));
        }

        // 空槽位（38-39）用于分隔
        inventory.setItem(38, createSeparatorItem());
        inventory.setItem(39, createSeparatorItem());
    }

    /**
     * 创建护甲套装槽位物品
     */
    private ItemStack createArmorSetItem(String setType, String displayName) {
        Material material;
        List<String> lore = new ArrayList<>();

        switch (setType) {
            case "upper":
                material = Material.IRON_CHESTPLATE;
                lore.add(ChatColor.GRAY + "包含: " + ChatColor.YELLOW + "头盔 + 胸甲");
                lore.add(ChatColor.GRAY + "状态: " + ChatColor.RED + "未设置");
                lore.add("");
                lore.add(ChatColor.AQUA + "点击设置上身护甲套装");
                break;
            case "lower":
                material = Material.IRON_LEGGINGS;
                lore.add(ChatColor.GRAY + "包含: " + ChatColor.YELLOW + "护腿 + 靴子");
                lore.add(ChatColor.GRAY + "状态: " + ChatColor.RED + "未设置");
                lore.add("");
                lore.add(ChatColor.AQUA + "点击设置下身护甲套装");
                break;
            default:
                material = Material.BARRIER;
                lore.add(ChatColor.RED + "未知护甲类型");
        }

        lore.add(ChatColor.YELLOW + "注意: 护甲设置独立于背包槽位");

        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(ChatColor.BLUE + displayName);
            meta.setLore(lore);
            item.setItemMeta(meta);
        }
        return item;
    }

    /**
     * 创建分隔线物品
     */
    private ItemStack createSeparatorItem() {
        ItemStack item = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(ChatColor.GRAY + "分隔线");
            item.setItemMeta(meta);
        }
        return item;
    }

    /**
     * 创建武器槽位物品
     */
    private ItemStack createWeaponSlotItem() {
        return createDetailedSlotItem("weapon_slot", null);
    }

    /**
     * 创建道具槽位物品
     */
    private ItemStack createItemSlotItem() {
        return createDetailedSlotItem("item_slot", null);
    }

    /**
     * 创建锁定槽位物品
     */
    private ItemStack createLockedSlotItem() {
        return createDetailedSlotItem("locked_slot", null);
    }

    /**
     * 创建控制按钮
     */
    private ItemStack createControlButton(Material material, String name, String... lore) {
        ItemStack button = new ItemStack(material);
        ItemMeta meta = button.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(name);
            if (lore.length > 0) {
                List<String> loreList = new ArrayList<>();
                for (String line : lore) {
                    loreList.add(line);
                }
                meta.setLore(loreList);
            }
            button.setItemMeta(meta);
        }
        return button;
    }

    /**
     * 处理物品栏点击事件
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        Inventory inventory = event.getInventory();

        // 获取GUI标题
        String title = event.getView().getTitle();
        if (title == null) {
            return;
        }

        // 添加调试信息
        plugin.getLogger().info("GUI点击事件 - 玩家: " + player.getName() + ", 标题: '" + title + "', 槽位: " + event.getRawSlot());

        // 阻止玩家拖动物品
        event.setCancelled(true);

        // 获取点击的槽位
        int slot = event.getRawSlot();

        // 处理主编辑器GUI
        if (title.startsWith(GUI_TITLE_PREFIX)) {
            handleMainEditorClick(player, inventory, slot);
            return;
        }

        // 处理槽位选项菜单
        if (title.startsWith(SLOT_OPTIONS_TITLE_PREFIX)) {
            handleSlotOptionsClick(player, inventory, slot);
            return;
        }



        // 处理统一的物品选择菜单（包含类别切换和翻页）
        if (title.startsWith(ITEM_SELECTION_TITLE)) {
            handleUnifiedItemSelectionClick(player, inventory, slot);
            return;
        }

        // 处理确认菜单
        if (title.equals(CONFIRM_TITLE)) {
            handleConfirmClick(player, inventory, slot);
            return;
        }

        // 处理护甲套装选择菜单
        if (title.startsWith(ChatColor.BLUE + "选择") && title.contains("护甲套装")) {
            handleArmorSetSelectionClick(player, inventory, slot);
            return;
        }
    }

    /**
     * 处理主编辑器GUI的点击事件
     */
    private void handleMainEditorClick(Player player, Inventory inventory, int slot) {
        // 获取玩家正在编辑的游戏
        String gameName = editingGames.get(player);
        if (gameName == null) {
            player.closeInventory();
            return;
        }

        // 添加调试信息
        plugin.getLogger().info("处理主编辑器点击，玩家: " + player.getName() + ", 点击的槽位: " + slot);
        plugin.getLogger().info("编辑的游戏: " + gameName);

        try {
            // 处理控制按钮点击
            if (slot == SAVE_BUTTON) {
                // 保存并关闭
                plugin.getLogger().info("玩家点击了保存按钮");
                saveChanges(player, inventory, gameName);
                player.closeInventory();
                player.sendMessage(ChatColor.GREEN + "已保存初始装备设置！");
            } else if (slot >= 0 && slot < 36) {
                // 玩家点击了背包槽位
                plugin.getLogger().info("玩家点击了背包槽位: " + slot);
                ItemStack clickedItem = inventory.getItem(slot);

                // 记录玩家正在编辑的槽位和游戏
                editingSlots.put(player, slot);
                editingGames.put(player, gameName);

                // 直接显示物品类别选择菜单，不区分是否已设置
                plugin.getLogger().info("玩家点击了背包槽位，直接显示物品类别选择菜单");
                showItemCategoryMenu(player, slot, gameName);
            } else if (slot >= 36 && slot <= 37) {
                // 玩家点击了护甲套装槽位（只有36和37是护甲套装）
                plugin.getLogger().info("玩家点击了护甲套装槽位: " + slot);
                handleArmorSetClick(player, slot, gameName);
            }
        } catch (Exception e) {
            plugin.getLogger().severe("处理主编辑器点击时发生错误: " + e.getMessage());
            e.printStackTrace();
            player.sendMessage(ChatColor.RED + "处理菜单点击时发生错误，请联系管理员！");
        }
    }

    /**
     * 处理槽位选项菜单的点击事件
     */
    private void handleSlotOptionsClick(Player player, Inventory inventory, int slot) {
        // 获取玩家正在编辑的游戏和槽位
        String gameName = editingGames.get(player);
        Integer editingSlot = editingSlots.get(player);

        if (gameName == null || editingSlot == null) {
            player.closeInventory();
            return;
        }

        // 添加调试信息
        plugin.getLogger().info("处理槽位选项菜单点击，玩家: " + player.getName() + ", 点击的槽位: " + slot);
        plugin.getLogger().info("编辑的游戏: " + gameName + ", 编辑的槽位: " + editingSlot);

        try {
            switch (slot) {
                case 2: // 设置为锁定槽位
                    // 直接设置为锁定槽位
                    plugin.getLogger().info("玩家选择了设置为锁定槽位");
                    openKitEditor(player, gameName);
                    player.getOpenInventory().getTopInventory().setItem(editingSlot, createLockedSlotItem());
                    player.sendMessage(ChatColor.GREEN + "已将槽位 " + (editingSlot + 1) + " 设置为锁定槽位");
                    break;
                case 4: // 设置初始物品
                    // 显示物品类别选择菜单
                    plugin.getLogger().info("玩家选择了设置初始物品");
                    showItemCategoryMenu(player, editingSlot, gameName);
                    break;
                case 8: // 返回
                    // 返回主编辑器
                    plugin.getLogger().info("玩家选择了返回");
                    openKitEditor(player, gameName);
                    break;
                default:
                    // 未知选项，返回主编辑器
                    plugin.getLogger().warning("未知的槽位选项: " + slot);
                    openKitEditor(player, gameName);
                    break;
            }
        } catch (Exception e) {
            plugin.getLogger().severe("处理槽位选项菜单点击时发生错误: " + e.getMessage());
            e.printStackTrace();
            player.sendMessage(ChatColor.RED + "处理菜单点击时发生错误，请联系管理员！");
            // 尝试返回主编辑器
            try {
                openKitEditor(player, gameName);
            } catch (Exception ex) {
                player.closeInventory();
            }
        }
    }

    /**
     * 处理槽位类型选择菜单的点击事件
     */
    private void handleSlotTypeSelectionClick(Player player, Inventory inventory, int slot) {
        // 获取玩家正在编辑的游戏和槽位
        String gameName = editingGames.get(player);
        Integer editingSlot = editingSlots.get(player);

        if (gameName == null || editingSlot == null) {
            player.closeInventory();
            return;
        }

        switch (slot) {
            case 0: // 武器槽位
                openKitEditor(player, gameName);
                player.getOpenInventory().getTopInventory().setItem(editingSlot, createWeaponSlotItem());
                break;
            case 1: // 道具槽位
                openKitEditor(player, gameName);
                player.getOpenInventory().getTopInventory().setItem(editingSlot, createItemSlotItem());
                break;
            case 2: // 锁定槽位
                openKitEditor(player, gameName);
                player.getOpenInventory().getTopInventory().setItem(editingSlot, createLockedSlotItem());
                break;
            case 8: // 返回
                showSlotOptions(player, editingSlot, gameName);
                break;
        }
    }



    /**
     * 处理确认菜单的点击事件
     */
    private void handleConfirmClick(Player player, Inventory inventory, int slot) {
        // 获取玩家正在编辑的游戏和槽位
        String gameName = editingGames.get(player);
        Integer editingSlot = editingSlots.get(player);

        if (gameName == null || editingSlot == null) {
            player.closeInventory();
            return;
        }

        String itemId = plugin.getConfig().getString("temp.selected_item_id." + player.getUniqueId());

        switch (slot) {
            case 2: // 确认
                if (itemId != null) {
                    // 创建物品并临时保存到内存中
                    ItemStack item = createItemById(itemId);
                    if (item != null) {
                        // 保存到临时存储中，而不是立即重新加载GUI
                        String tempKey = "temp.kit_changes." + player.getUniqueId() + "." + editingSlot;
                        plugin.getConfig().set(tempKey, itemId);

                        // 重新打开编辑器，但这次会包含临时更改
                        openKitEditorWithChanges(player, gameName);
                        player.sendMessage(ChatColor.GREEN + "已设置物品: " + itemId);
                    }
                }
                break;
            case 6: // 取消
                showItemCategoryMenu(player, editingSlot, gameName);
                break;
        }
    }

    /**
     * 处理确认更改类型菜单的点击事件
     */
    private void handleChangeTypeConfirmClick(Player player, Inventory inventory, int slot) {
        // 获取玩家正在编辑的游戏和槽位
        String gameName = editingGames.get(player);
        Integer editingSlot = editingSlots.get(player);

        if (gameName == null || editingSlot == null) {
            player.closeInventory();
            return;
        }

        switch (slot) {
            case 2: // 是，更改类型
                // 显示槽位类型选择菜单
                showSlotTypeSelectionMenu(player, editingSlot, gameName);
                break;
            case 6: // 否，返回
                // 返回主编辑器
                openKitEditor(player, gameName);
                break;
        }
    }

    /**
     * 处理槽位类型选择
     */
    private void handleSlotTypeSelection(Player player, Inventory inventory, String slotType) {
        // 获取玩家当前选中的槽位
        int selectedSlot = -1;
        for (int i = 0; i < 36; i++) {
            ItemStack item = inventory.getItem(i);
            if (item != null && item.getType() == Material.LIME_DYE) {
                selectedSlot = i;
                break;
            }
        }

        if (selectedSlot == -1) {
            player.sendMessage(ChatColor.RED + "请先选择一个槽位！");
            return;
        }

        // 更新槽位类型
        if (slotType.equals("weapon_slot")) {
            inventory.setItem(selectedSlot, createWeaponSlotItem());
        } else if (slotType.equals("item_slot")) {
            inventory.setItem(selectedSlot, createItemSlotItem());
        } else {
            inventory.setItem(selectedSlot, createLockedSlotItem());
        }

        player.sendMessage(ChatColor.GREEN + "已设置槽位 " + selectedSlot + " 的类型！");
    }

    /**
     * 显示槽位选项菜单
     */
    private void showSlotOptions(Player player, int slot, String gameName) {
        // 添加调试信息
        plugin.getLogger().info("准备打开槽位选项菜单，玩家: " + player.getName() + ", 槽位: " + slot + ", 游戏: " + gameName);

        try {
            // 记录玩家正在编辑的槽位和游戏（在创建菜单之前记录）
            editingSlots.put(player, slot);
            editingGames.put(player, gameName);

            // 创建选项菜单
            Inventory optionsMenu = Bukkit.createInventory(null, 9, SLOT_OPTIONS_TITLE_PREFIX + slot + " 选项");

            // 添加选项
            optionsMenu.setItem(2, createControlButton(Material.WHITE_DYE, ChatColor.GRAY + "设置为锁定槽位",
                    ChatColor.GRAY + "将此槽位设置为锁定槽位"));

            optionsMenu.setItem(4, createControlButton(Material.DIAMOND_SWORD, ChatColor.AQUA + "设置初始物品",
                    ChatColor.GRAY + "为此槽位设置初始物品"));

            optionsMenu.setItem(8, createControlButton(Material.BARRIER, ChatColor.RED + "返回",
                    ChatColor.GRAY + "返回到初始装备编辑器"));

            // 打开选项菜单
            player.openInventory(optionsMenu);

            // 添加调试信息
            plugin.getLogger().info("成功打开槽位选项菜单，标题: " + SLOT_OPTIONS_TITLE_PREFIX + slot + " 选项");
        } catch (Exception e) {
            plugin.getLogger().severe("打开槽位选项菜单时发生错误: " + e.getMessage());
            e.printStackTrace();
            player.sendMessage(ChatColor.RED + "打开菜单时发生错误，请联系管理员！");
        }
    }

    /**
     * 显示确认更改类型的菜单
     */
    private void showChangeTypeConfirmMenu(Player player, int slot, String gameName) {
        // 添加调试信息
        plugin.getLogger().info("准备打开确认更改类型菜单，玩家: " + player.getName() + ", 槽位: " + slot + ", 游戏: " + gameName);

        try {
            // 记录玩家正在编辑的槽位和游戏（在创建菜单之前记录）
            editingSlots.put(player, slot);
            editingGames.put(player, gameName);

            // 创建确认菜单
            Inventory confirmMenu = Bukkit.createInventory(null, 9, CHANGE_TYPE_CONFIRM_TITLE);

            // 获取当前槽位的物品
            ItemStack currentItem = null;
            Inventory mainInventory = player.getOpenInventory().getTopInventory();
            if (mainInventory != null && mainInventory.getSize() > slot) {
                currentItem = mainInventory.getItem(slot);
            }

            // 显示当前槽位的物品
            if (currentItem != null) {
                confirmMenu.setItem(4, currentItem.clone());
            }

            // 添加确认和取消按钮
            confirmMenu.setItem(2, createControlButton(Material.LIME_CONCRETE, ChatColor.GREEN + "是",
                    ChatColor.GRAY + "是，我要更改此槽位的类型"));

            confirmMenu.setItem(6, createControlButton(Material.RED_CONCRETE, ChatColor.RED + "否",
                    ChatColor.GRAY + "否，返回上一级菜单"));

            // 打开确认菜单
            player.openInventory(confirmMenu);

            // 添加调试信息
            plugin.getLogger().info("成功打开确认更改类型菜单，标题: " + CHANGE_TYPE_CONFIRM_TITLE);
            plugin.getLogger().info("玩家: " + player.getName() + ", 槽位: " + slot + ", 游戏: " + gameName);
        } catch (Exception e) {
            plugin.getLogger().severe("打开确认更改类型菜单时发生错误: " + e.getMessage());
            e.printStackTrace();
            player.sendMessage(ChatColor.RED + "打开菜单时发生错误，请联系管理员！");
        }
    }

    /**
     * 显示槽位类型选择菜单
     */
    private void showSlotTypeSelectionMenu(Player player, int slot, String gameName) {
        // 添加调试信息
        plugin.getLogger().info("准备打开槽位类型选择菜单，玩家: " + player.getName() + ", 槽位: " + slot + ", 游戏: " + gameName);

        try {
            // 记录玩家正在编辑的槽位和游戏（在创建菜单之前记录）
            editingSlots.put(player, slot);
            editingGames.put(player, gameName);

            // 创建类型选择菜单
            Inventory typeMenu = Bukkit.createInventory(null, 9, SLOT_TYPE_SELECTION_TITLE);

            // 添加选项
            typeMenu.setItem(0, createControlButton(Material.IRON_SWORD, ChatColor.GOLD + "武器槽位",
                    ChatColor.GRAY + "此槽位用于放置武器",
                    ChatColor.GRAY + "点击设置为武器槽位"));

            typeMenu.setItem(1, createControlButton(Material.CHEST, ChatColor.GOLD + "道具槽位",
                    ChatColor.GRAY + "此槽位用于放置道具",
                    ChatColor.GRAY + "点击设置为道具槽位"));

            typeMenu.setItem(2, createControlButton(Material.WHITE_DYE, ChatColor.GRAY + "锁定槽位",
                    ChatColor.RED + "此槽位已锁定",
                    ChatColor.GRAY + "点击设置为锁定槽位"));

            typeMenu.setItem(8, createControlButton(Material.BARRIER, ChatColor.RED + "返回",
                    ChatColor.GRAY + "返回到上一级菜单"));

            // 打开类型选择菜单
            player.openInventory(typeMenu);

            // 添加调试信息
            plugin.getLogger().info("成功打开槽位类型选择菜单，标题: " + SLOT_TYPE_SELECTION_TITLE);
            plugin.getLogger().info("玩家: " + player.getName() + ", 槽位: " + slot + ", 游戏: " + gameName);
        } catch (Exception e) {
            plugin.getLogger().severe("打开槽位类型选择菜单时发生错误: " + e.getMessage());
            e.printStackTrace();
            player.sendMessage(ChatColor.RED + "打开菜单时发生错误，请联系管理员！");
        }
    }



    /**
     * 显示确认菜单
     */
    private void showConfirmMenu(Player player, int slot, String gameName, String itemId) {
        // 添加调试信息
        plugin.getLogger().info("准备打开确认菜单，玩家: " + player.getName() + ", 槽位: " + slot + ", 游戏: " + gameName + ", 物品ID: " + itemId);

        try {
            // 记录玩家正在编辑的槽位、游戏和物品ID（在创建菜单之前记录）
            editingSlots.put(player, slot);
            editingGames.put(player, gameName);
            plugin.getConfig().set("temp.selected_item_id." + player.getUniqueId(), itemId);

            // 创建确认菜单
            Inventory confirmMenu = Bukkit.createInventory(null, 9, CONFIRM_TITLE);

            // 显示选中的物品
            ItemStack selectedItem = createItemById(itemId);
            if (selectedItem != null) {
                confirmMenu.setItem(4, createItemWithId(selectedItem, itemId));
            }

            // 添加确认和取消按钮
            confirmMenu.setItem(2, createControlButton(Material.LIME_CONCRETE, ChatColor.GREEN + "确认",
                    ChatColor.GRAY + "确认选择此物品"));

            confirmMenu.setItem(6, createControlButton(Material.RED_CONCRETE, ChatColor.RED + "取消",
                    ChatColor.GRAY + "取消选择"));

            // 打开确认菜单
            player.openInventory(confirmMenu);

            // 添加调试信息
            plugin.getLogger().info("成功打开确认菜单，标题: " + CONFIRM_TITLE);
            plugin.getLogger().info("玩家: " + player.getName() + ", 槽位: " + slot + ", 游戏: " + gameName + ", 物品ID: " + itemId);
        } catch (Exception e) {
            plugin.getLogger().severe("打开确认菜单时发生错误: " + e.getMessage());
            e.printStackTrace();
            player.sendMessage(ChatColor.RED + "打开菜单时发生错误，请联系管理员！");
        }
    }

    /**
     * 创建带有ID信息的物品
     */
    private ItemStack createItemWithId(ItemStack item, String itemId) {
        if (item == null) {
            return null;
        }

        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            List<String> lore = meta.hasLore() ? meta.getLore() : new ArrayList<>();
            // 确保不重复添加物品ID信息
            boolean hasIdInfo = false;
            for (String line : lore) {
                if (line.startsWith(ChatColor.GRAY + "物品ID: ")) {
                    hasIdInfo = true;
                    break;
                }
            }

            if (!hasIdInfo) {
                lore.add(ChatColor.GRAY + "物品ID: " + ChatColor.YELLOW + itemId);
                meta.setLore(lore);
                item.setItemMeta(meta);
            }
        }

        return item;
    }

    /**
     * 保存更改
     */
    private void saveChanges(Player player, Inventory inventory, String gameName) {
        // 获取游戏配置
        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (config == null) {
            player.sendMessage(ChatColor.RED + "无法获取游戏配置！");
            return;
        }

        // 添加调试信息
        plugin.getLogger().info("开始保存游戏 '" + gameName + "' 的初始装备配置");

        // 清除现有的初始装备配置
        ConfigurationSection section = config.getConfigurationSection("initialEquipment");
        if (section != null) {
            for (String key : section.getKeys(false)) {
                config.set("initialEquipment." + key, null);
            }
            plugin.getLogger().info("已清除游戏 '" + gameName + "' 的现有初始装备配置");
        }

        // 保存新的初始装备配置
        int savedCount = 0;
        for (int i = 0; i < 36; i++) {
            ItemStack item = inventory.getItem(i);
            int configSlot = i + 1; // 配置从1开始，物品栏从0开始

            // 首先检查是否有临时更改
            String tempKey = "temp.kit_changes." + player.getUniqueId() + "." + i;
            String tempItemId = plugin.getConfig().getString(tempKey);

            if (tempItemId != null) {
                // 有临时更改，直接保存临时更改的物品ID
                config.set("initialEquipment." + configSlot, tempItemId);
                savedCount++;
                plugin.getLogger().info("设置槽位 " + configSlot + " 的物品ID为 " + tempItemId + " (来自临时更改)");
            } else if (item != null) {
                // 没有临时更改，处理GUI中的物品
                // 通过材质类型识别槽位类型，而不是依赖显示名称
                if (item.getType() == Material.IRON_SWORD && item.getItemMeta() != null
                        && item.getItemMeta().getDisplayName().equals(ChatColor.GOLD + "空闲设置")) {
                    // 武器槽位（空闲状态） - 使用id69作为占位符
                    config.set("initialEquipment." + configSlot, "id69");
                    config.set("initialEquipment.slot_type_" + configSlot, "weapon_slot");
                    savedCount++;
                    plugin.getLogger().info("设置槽位 " + configSlot + " 为武器槽位");
                } else if (item.getType() == Material.CHEST && item.getItemMeta() != null
                        && item.getItemMeta().getDisplayName().equals(ChatColor.GOLD + "空闲设置")) {
                    // 道具槽位（空闲状态） - 使用id69作为占位符
                    config.set("initialEquipment." + configSlot, "id69");
                    config.set("initialEquipment.slot_type_" + configSlot, "item_slot");
                    savedCount++;
                    plugin.getLogger().info("设置槽位 " + configSlot + " 为道具槽位");
                } else if (item.getType() == Material.WHITE_DYE && item.getItemMeta() != null
                        && item.getItemMeta().getDisplayName().equals(ChatColor.GOLD + "空闲设置")) {
                    // 锁定槽位（空闲状态） - 使用id69作为占位符
                    config.set("initialEquipment." + configSlot, "id69");
                    config.set("initialEquipment.slot_type_" + configSlot, "locked_slot");
                    savedCount++;
                    plugin.getLogger().info("设置槽位 " + configSlot + " 为锁定槽位");
                } else {
                    // 其他物品，尝试获取物品ID
                    String itemId = getItemId(item);
                    if (itemId != null) {
                        config.set("initialEquipment." + configSlot, itemId);
                        // 根据物品ID推断槽位类型
                        String slotType = inferSlotTypeFromItemId(itemId);
                        if (slotType != null) {
                            config.set("initialEquipment.slot_type_" + configSlot, slotType);
                        }
                        savedCount++;
                        plugin.getLogger().info("设置槽位 " + configSlot + " 的物品ID为 " + itemId + "，槽位类型为 " + slotType);
                    }
                }
            }
        }

        // 清除临时更改
        for (int i = 0; i < 36; i++) {
            String tempKey = "temp.kit_changes." + player.getUniqueId() + "." + i;
            plugin.getConfig().set(tempKey, null);
        }

        // 保存配置
        try {
            // 直接保存配置文件，而不是通过GameManager
            File gameFile = new File(plugin.getDataFolder(), "game/" + gameName + ".yml");
            config.save(gameFile);
            plugin.getLogger().info("成功保存游戏 '" + gameName + "' 的配置文件，共设置了 " + savedCount + " 个槽位");
            player.sendMessage(ChatColor.GREEN + "已保存初始装备设置！共设置了 " + savedCount + " 个槽位");
        } catch (Exception e) {
            player.sendMessage(ChatColor.RED + "保存配置失败：" + e.getMessage());
            plugin.getLogger().severe("保存游戏 '" + gameName + "' 的配置文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 从物品中获取物品ID
     */
    private String getItemId(ItemStack item) {
        if (item == null || item.getItemMeta() == null || !item.getItemMeta().hasLore()) {
            return null;
        }

        List<String> lore = item.getItemMeta().getLore();
        for (String line : lore) {
            // 尝试多种格式的物品ID标识
            if (line.startsWith(ChatColor.GRAY + "物品ID: ")) {
                String itemId = line.substring((ChatColor.GRAY + "物品ID: " + ChatColor.YELLOW).length());
                return itemId.trim();
            } else if (line.startsWith(ChatColor.GRAY + "初始值: ")) {
                String itemId = line.substring((ChatColor.GRAY + "初始值: " + ChatColor.YELLOW).length());
                return itemId.trim();
            } else if (line.startsWith(ChatColor.YELLOW + "物品ID: ")) {
                String itemId = line.substring((ChatColor.YELLOW + "物品ID: ").length());
                return itemId.trim();
            }
        }

        return null;
    }

    /**
     * 根据ID创建物品
     *
     * @param itemId 物品ID
     * @return 创建的物品
     */
    private ItemStack createItemById(String itemId) {
        if (itemId == null || itemId.isEmpty()) {
            return null;
        }

        // 首先尝试使用GameKitManager获取物品信息
        if (gameKitManager != null) {
            Map<String, Object> itemInfo = gameKitManager.getItemInfo(itemId);
            if (itemInfo != null) {
                String materialName = (String) itemInfo.get("material");
                String name = (String) itemInfo.get("name");
                String description = (String) itemInfo.get("description");

                try {
                    Material material = Material.valueOf(materialName);
                    ItemStack item = new ItemStack(material);
                    ItemMeta meta = item.getItemMeta();
                    if (meta != null) {
                        meta.setDisplayName(ChatColor.GOLD + name);
                        List<String> lore = new ArrayList<>();
                        if (description != null && !description.isEmpty()) {
                            lore.add(ChatColor.GRAY + description);
                        }
                        lore.add("");
                        lore.add(ChatColor.YELLOW + "物品ID: " + itemId);
                        meta.setLore(lore);
                        item.setItemMeta(meta);
                    }
                    return item;
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("无效的材质名称: " + materialName + " 对于物品ID: " + itemId);
                }
            }
        }

        // 回退到旧的逻辑
        switch (itemId.toLowerCase()) {
            case "white_dye": // 白色染料（用于锁定槽位）
                ItemStack whiteDye = new ItemStack(Material.WHITE_DYE);
                ItemMeta dyeMeta = whiteDye.getItemMeta();
                dyeMeta.setDisplayName(ChatColor.GRAY + "锁定槽位");
                List<String> dyeLore = new ArrayList<>();
                dyeLore.add(ChatColor.RED + "此槽位已锁定");
                dyeMeta.setLore(dyeLore);
                whiteDye.setItemMeta(dyeMeta);
                return whiteDye;

            default:
                // 检查是否是Shoot插件的枪支ID
                if (itemId.startsWith("id") && itemId.length() > 2) {
                    try {
                        int gunId = Integer.parseInt(itemId.substring(2));
                        ShootPluginHelper shootHelperForGun = plugin.getShootPluginHelper();
                        if (shootHelperForGun != null) {
                            return shootHelperForGun.createGun(gunId);
                        }
                    } catch (NumberFormatException e) {
                        // 不是有效的数字ID，继续处理
                    }
                }

                plugin.getLogger().warning("未知的物品ID: " + itemId);
                return null;
        }
    }

    /**
     * 处理物品栏关闭事件
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getPlayer();

        // 获取GUI标题
        String title = event.getView().getTitle();
        if (title == null) {
            return;
        }

        // 添加调试信息
        plugin.getLogger().info("GUI关闭事件 - 玩家: " + player.getName() + ", 标题: '" + title + "'");

        // 首先检查关闭的GUI是否与KitEditor相关
        boolean isKitEditorRelated = title.startsWith(GUI_TITLE_PREFIX) ||
                                   title.startsWith(SLOT_OPTIONS_TITLE_PREFIX) ||
                                   title.equals(ITEM_CATEGORY_TITLE) ||
                                   title.startsWith(ITEM_SELECTION_TITLE) ||
                                   title.equals(CONFIRM_TITLE) ||
                                   (title.startsWith(ChatColor.BLUE + "选择") && title.contains("护甲套装"));

        // 如果关闭的GUI与KitEditor无关，且玩家不在编辑状态中，直接返回
        if (!isKitEditorRelated && !editingGames.containsKey(player)) {
            return;
        }

        // 延迟检查是否真的需要清除编辑状态
        // 如果玩家在短时间内打开了另一个相关的GUI，则不清除状态
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            // 检查玩家是否仍然在线
            if (!player.isOnline()) {
                // 玩家已离线，清除编辑状态
                plugin.getLogger().info("玩家 " + player.getName() + " 已离线，清除编辑状态");
                clearPlayerEditingState(player);
                return;
            }

            // 检查玩家当前是否还在编辑相关的GUI中
            if (player.getOpenInventory() != null) {
                String currentTitle = player.getOpenInventory().getTitle();
                if (currentTitle != null && (
                    currentTitle.startsWith(GUI_TITLE_PREFIX) ||
                    currentTitle.startsWith(SLOT_OPTIONS_TITLE_PREFIX) ||
                    currentTitle.equals(ITEM_CATEGORY_TITLE) ||
                    currentTitle.startsWith(ITEM_SELECTION_TITLE) ||
                    currentTitle.equals(CONFIRM_TITLE) ||
                    (currentTitle.startsWith(ChatColor.BLUE + "选择") && currentTitle.contains("护甲套装")))) {
                    // 玩家还在相关的GUI中，不清除编辑状态
                    plugin.getLogger().info("玩家 " + player.getName() + " 还在相关GUI中，保持编辑状态");
                    return;
                }
            }

            // 只有当玩家确实在编辑状态中时才清除编辑状态
            if (editingGames.containsKey(player)) {
                plugin.getLogger().info("玩家 " + player.getName() + " 已退出编辑器，清除编辑状态");
                clearPlayerEditingState(player);
            }
        }, 5L); // 延迟5个tick（0.25秒）
    }

    /**
     * 清除玩家的编辑状态
     */
    private void clearPlayerEditingState(Player player) {
        editingGames.remove(player);
        editingSlots.remove(player);
        currentPage.remove(player);
        currentCategory.remove(player);
        // 清除临时数据
        plugin.getConfig().set("temp.selected_item_id." + player.getUniqueId(), null);
    }

    /**
     * 处理护甲套装槽位点击
     */
    private void handleArmorSetClick(Player player, int slot, String gameName) {
        // 记录玩家正在编辑的槽位和游戏
        editingSlots.put(player, slot);
        editingGames.put(player, gameName);

        // 根据槽位确定护甲套装类型
        String armorSetType = (slot == 36) ? "upper" : "lower";

        // 设置护甲类别并显示统一的物品选择菜单
        currentCategory.put(player, "armors");
        currentPage.put(player, 0);
        showUnifiedItemSelectionMenu(player, slot, gameName, "armors", 0);
    }

    /**
     * 显示统一的物品选择菜单（合并了类别选择和物品选择）
     */
    private void showItemCategoryMenu(Player player, int slot, String gameName) {
        try {
            // 记录玩家正在编辑的槽位和游戏
            editingSlots.put(player, slot);
            editingGames.put(player, gameName);

            // 直接显示统一的物品选择菜单，从武器类别开始
            currentCategory.put(player, "weapons");
            currentPage.put(player, 0);
            showUnifiedItemSelectionMenu(player, slot, gameName, "weapons", 0);

        } catch (Exception e) {
            plugin.getLogger().severe("打开物品选择菜单时发生错误: " + e.getMessage());
            e.printStackTrace();
            player.sendMessage(ChatColor.RED + "打开菜单时发生错误，请联系管理员！");
        }
    }

    /**
     * 显示统一的物品选择菜单（包含类别切换和翻页功能）
     */
    private void showUnifiedItemSelectionMenu(Player player, int slot, String gameName, String category, int page) {
        try {
            // 记录玩家正在编辑的槽位、游戏、类别和页码
            editingSlots.put(player, slot);
            editingGames.put(player, gameName);
            currentCategory.put(player, category);
            currentPage.put(player, page);

            // 获取该类别的所有物品
            List<String> itemIds = getItemIdsByCategory(category);

            // 计算总页数
            int totalPages = (int) Math.ceil((double) itemIds.size() / ITEMS_PER_PAGE);
            if (totalPages == 0) totalPages = 1;

            // 创建物品选择菜单
            String title = ITEM_SELECTION_TITLE + " - " + getCategoryDisplayName(category) + " (" + (page + 1) + "/" + totalPages + ")";
            Inventory itemMenu = Bukkit.createInventory(null, 54, title);

            // 计算当前页的物品范围
            int startIndex = page * ITEMS_PER_PAGE;
            int endIndex = Math.min(startIndex + ITEMS_PER_PAGE, itemIds.size());

            // 添加物品到菜单（前3行，21个物品）
            int menuSlot = 0;
            for (int i = startIndex; i < endIndex; i++) {
                if (menuSlot >= 21) break; // 只显示前21个物品（3行x7列）

                String itemId = itemIds.get(i);
                ItemStack item = createItemById(itemId);
                if (item != null) {
                    itemMenu.setItem(menuSlot, createItemWithId(item, itemId));
                }
                menuSlot++;
            }

            // 第4行：类别切换按钮
            itemMenu.setItem(27, createControlButton(Material.IRON_SWORD, ChatColor.GOLD + "武器类",
                    ChatColor.GRAY + "查看所有武器 (id1-id24)",
                    category.equals("weapons") ? ChatColor.GREEN + "当前类别" : ChatColor.YELLOW + "点击切换"));

            itemMenu.setItem(28, createControlButton(Material.IRON_CHESTPLATE, ChatColor.BLUE + "护甲类",
                    ChatColor.GRAY + "查看所有护甲 (id25-id37)",
                    category.equals("armors") ? ChatColor.GREEN + "当前类别" : ChatColor.YELLOW + "点击切换"));

            itemMenu.setItem(29, createControlButton(Material.POTION, ChatColor.GREEN + "物品类",
                    ChatColor.GRAY + "查看所有物品 (id38-id66)",
                    category.equals("items") ? ChatColor.GREEN + "当前类别" : ChatColor.YELLOW + "点击切换"));

            itemMenu.setItem(30, createControlButton(Material.NETHER_STAR, ChatColor.LIGHT_PURPLE + "特殊物品",
                    ChatColor.GRAY + "查看特殊物品 (id67-id70)",
                    category.equals("special") ? ChatColor.GREEN + "当前类别" : ChatColor.YELLOW + "点击切换"));

            // 第5行：翻页和控制按钮
            if (page > 0) {
                itemMenu.setItem(45, createControlButton(Material.ARROW, ChatColor.YELLOW + "上一页",
                        ChatColor.GRAY + "第 " + page + " 页"));
            }

            itemMenu.setItem(49, createControlButton(Material.BARRIER, ChatColor.RED + "返回",
                    ChatColor.GRAY + "返回到主编辑器"));

            if (page < totalPages - 1) {
                itemMenu.setItem(53, createControlButton(Material.ARROW, ChatColor.YELLOW + "下一页",
                        ChatColor.GRAY + "第 " + (page + 2) + " 页"));
            }

            // 页码信息
            itemMenu.setItem(48, createControlButton(Material.BOOK, ChatColor.AQUA + "页码信息",
                    ChatColor.GRAY + "当前: 第 " + (page + 1) + " 页",
                    ChatColor.GRAY + "总计: " + totalPages + " 页",
                    ChatColor.GRAY + "类别: " + getCategoryDisplayName(category)));

            // 打开物品选择菜单
            player.openInventory(itemMenu);

        } catch (Exception e) {
            plugin.getLogger().severe("打开统一物品选择菜单时发生错误: " + e.getMessage());
            e.printStackTrace();
            player.sendMessage(ChatColor.RED + "打开菜单时发生错误，请联系管理员！");
        }
    }

    /**
     * 处理统一物品选择菜单的点击事件
     */
    private void handleUnifiedItemSelectionClick(Player player, Inventory inventory, int slot) {
        // 获取玩家正在编辑的游戏和槽位
        String gameName = editingGames.get(player);
        Integer editingSlot = editingSlots.get(player);
        String category = currentCategory.get(player);
        Integer page = currentPage.get(player);

        if (gameName == null || editingSlot == null || category == null || page == null) {
            player.closeInventory();
            return;
        }

        // 处理物品选择（前21个槽位）
        if (slot >= 0 && slot < 21) {
            // 获取选中的物品
            ItemStack clickedItem = inventory.getItem(slot);
            if (clickedItem == null) {
                return;
            }

            // 获取物品ID
            String itemId = getItemId(clickedItem);
            if (itemId == null) {
                return;
            }

            // 检查是否是护甲设置
            if (category.equals("armors") && (editingSlot == 36 || editingSlot == 37)) {
                // 护甲设置直接生效，不需要确认菜单
                saveArmorSetConfiguration(player, gameName, editingSlot, itemId);
                updateArmorSlotAndReturnToEditor(player, gameName, editingSlot, itemId);
            } else {
                // 普通物品显示确认菜单
                showConfirmMenu(player, editingSlot, gameName, itemId);
            }
            return;
        }

        // 处理类别切换按钮（第4行）
        if (slot >= 27 && slot <= 30) {
            String newCategory = null;
            switch (slot) {
                case 27: // 武器类
                    newCategory = "weapons";
                    break;
                case 28: // 护甲类
                    newCategory = "armors";
                    break;
                case 29: // 物品类
                    newCategory = "items";
                    break;
                case 30: // 特殊物品
                    newCategory = "special";
                    break;
            }

            if (newCategory != null && !newCategory.equals(category)) {
                // 切换到新类别，重置页码
                currentCategory.put(player, newCategory);
                currentPage.put(player, 0);
                showUnifiedItemSelectionMenu(player, editingSlot, gameName, newCategory, 0);
            }
            return;
        }

        // 处理控制按钮（第5行）
        if (slot == 45) { // 上一页
            if (page > 0) {
                showUnifiedItemSelectionMenu(player, editingSlot, gameName, category, page - 1);
            }
            return;
        } else if (slot == 53) { // 下一页
            List<String> itemIds = getItemIdsByCategory(category);
            int totalPages = (int) Math.ceil((double) itemIds.size() / ITEMS_PER_PAGE);
            if (page < totalPages - 1) {
                showUnifiedItemSelectionMenu(player, editingSlot, gameName, category, page + 1);
            }
            return;
        } else if (slot == 49) { // 返回到主编辑器
            openKitEditor(player, gameName);
            return;
        } else if (slot == 48) { // 页码信息，不做任何操作
            return;
        }
    }

    /**
     * 显示物品选择菜单（新版本，支持分类和翻页）
     */
    private void showItemSelectionMenu(Player player, int slot, String gameName, String category, int page) {
        try {
            // 记录玩家正在编辑的槽位、游戏、类别和页码
            editingSlots.put(player, slot);
            editingGames.put(player, gameName);
            currentCategory.put(player, category);
            currentPage.put(player, page);

            // 获取该类别的所有物品
            List<String> itemIds = getItemIdsByCategory(category);

            // 计算总页数
            int totalPages = (int) Math.ceil((double) itemIds.size() / ITEMS_PER_PAGE);
            if (totalPages == 0) totalPages = 1;

            // 创建物品选择菜单
            String title = ITEM_SELECTION_TITLE + " - " + getCategoryDisplayName(category) + " (" + (page + 1) + "/" + totalPages + ")";
            Inventory itemMenu = Bukkit.createInventory(null, 54, title);

            // 计算当前页的物品范围
            int startIndex = page * ITEMS_PER_PAGE;
            int endIndex = Math.min(startIndex + ITEMS_PER_PAGE, itemIds.size());

            // 添加物品到菜单中（前3行，每行7个物品）
            int menuSlot = 0;
            for (int i = startIndex; i < endIndex; i++) {
                if (menuSlot >= ITEMS_PER_PAGE) break;

                // 跳过每行的第8和第9个槽位（索引7和8）
                if (menuSlot % 9 >= 7) {
                    menuSlot += 2;
                }

                String itemId = itemIds.get(i);
                ItemStack item = createItemById(itemId);
                if (item != null) {
                    itemMenu.setItem(menuSlot, createItemWithId(item, itemId));
                }
                menuSlot++;
            }

            // 添加控制按钮（第4行）
            if (page > 0) {
                itemMenu.setItem(45, createControlButton(Material.ARROW, ChatColor.GREEN + "上一页",
                        ChatColor.GRAY + "第 " + page + " 页"));
            }

            if (page < totalPages - 1) {
                itemMenu.setItem(53, createControlButton(Material.ARROW, ChatColor.GREEN + "下一页",
                        ChatColor.GRAY + "第 " + (page + 2) + " 页"));
            }

            itemMenu.setItem(49, createControlButton(Material.BARRIER, ChatColor.RED + "返回",
                    ChatColor.GRAY + "返回到类别选择"));

            // 显示页码信息
            itemMenu.setItem(48, createControlButton(Material.BOOK, ChatColor.YELLOW + "页码信息",
                    ChatColor.GRAY + "当前: 第 " + (page + 1) + " 页",
                    ChatColor.GRAY + "总计: " + totalPages + " 页",
                    ChatColor.GRAY + "物品: " + itemIds.size() + " 个"));

            // 打开物品选择菜单
            player.openInventory(itemMenu);

        } catch (Exception e) {
            plugin.getLogger().severe("打开物品选择菜单时发生错误: " + e.getMessage());
            e.printStackTrace();
            player.sendMessage(ChatColor.RED + "打开菜单时发生错误，请联系管理员！");
        }
    }

    /**
     * 根据类别获取物品ID列表
     */
    private List<String> getItemIdsByCategory(String category) {
        List<String> itemIds = new ArrayList<>();

        if (gameKitManager == null) {
            return itemIds;
        }

        switch (category) {
            case "weapons":
                // 武器 id1-id24
                for (int i = 1; i <= 24; i++) {
                    itemIds.add("id" + i);
                }
                break;
            case "armors":
                // 护甲 id25-id37
                for (int i = 25; i <= 37; i++) {
                    itemIds.add("id" + i);
                }
                break;
            case "items":
                // 物品 id38-id66
                for (int i = 38; i <= 66; i++) {
                    itemIds.add("id" + i);
                }
                break;
            case "special":
                // 特殊物品 id67-id70
                for (int i = 67; i <= 70; i++) {
                    itemIds.add("id" + i);
                }
                break;

        }

        return itemIds;
    }

    /**
     * 获取类别显示名称
     */
    private String getCategoryDisplayName(String category) {
        switch (category) {
            case "weapons":
                return "武器类";
            case "armors":
                return "护甲类";
            case "items":
                return "物品类";
            case "special":
                return "特殊物品";
            default:
                return "未知类别";
        }
    }

    /**
     * 显示护甲套装选择菜单
     */
    private void showArmorSetSelectionMenu(Player player, int slot, String gameName, String armorSetType) {
        try {
            // 记录玩家正在编辑的槽位、游戏和类型
            editingSlots.put(player, slot);
            editingGames.put(player, gameName);

            // 创建护甲套装选择菜单
            String title = ChatColor.BLUE + "选择" + (armorSetType.equals("upper") ? "上身" : "下身") + "护甲套装";
            Inventory armorMenu = Bukkit.createInventory(null, 27, title);

            // 获取护甲ID范围
            List<String> armorIds = getArmorIdsBySetType(armorSetType);

            // 添加护甲套装选项
            int menuSlot = 0;
            for (String armorId : armorIds) {
                if (menuSlot >= 21) break; // 最多显示21个选项

                ItemStack armorItem = createItemById(armorId);
                if (armorItem != null) {
                    armorMenu.setItem(menuSlot, createItemWithId(armorItem, armorId));
                }
                menuSlot++;
            }

            // 添加返回按钮
            armorMenu.setItem(22, createControlButton(Material.BARRIER, ChatColor.RED + "返回",
                    ChatColor.GRAY + "返回到主编辑器"));

            // 打开护甲套装选择菜单
            player.openInventory(armorMenu);

        } catch (Exception e) {
            plugin.getLogger().severe("打开护甲套装选择菜单时发生错误: " + e.getMessage());
            e.printStackTrace();
            player.sendMessage(ChatColor.RED + "打开菜单时发生错误，请联系管理员！");
        }
    }

    /**
     * 根据护甲套装类型获取护甲ID列表
     */
    private List<String> getArmorIdsBySetType(String setType) {
        List<String> armorIds = new ArrayList<>();

        if (setType.equals("upper")) {
            // 上身护甲套装：头盔和胸甲类型的护甲
            for (int i = 25; i <= 31; i++) { // 假设id25-id31是头盔和胸甲
                armorIds.add("id" + i);
            }
        } else if (setType.equals("lower")) {
            // 下身护甲套装：护腿和靴子类型的护甲
            for (int i = 32; i <= 37; i++) { // 假设id32-id37是护腿和靴子
                armorIds.add("id" + i);
            }
        }

        return armorIds;
    }

    /**
     * 处理护甲套装选择菜单的点击事件
     */
    private void handleArmorSetSelectionClick(Player player, Inventory inventory, int slot) {
        // 获取玩家正在编辑的游戏和槽位
        String gameName = editingGames.get(player);
        Integer editingSlot = editingSlots.get(player);

        plugin.getLogger().info("护甲套装选择点击 - 玩家: " + player.getName() + ", 槽位: " + slot + ", 游戏: " + gameName + ", 编辑槽位: " + editingSlot);

        if (gameName == null || editingSlot == null) {
            plugin.getLogger().warning("游戏名称或编辑槽位为空，关闭界面");
            player.closeInventory();
            return;
        }

        // 处理返回按钮
        if (slot == 22) {
            plugin.getLogger().info("玩家点击了返回按钮");
            openKitEditor(player, gameName);
            return;
        }

        // 获取选中的护甲套装
        ItemStack clickedItem = inventory.getItem(slot);
        if (clickedItem == null) {
            plugin.getLogger().warning("点击的物品为空，槽位: " + slot);
            return;
        }

        plugin.getLogger().info("点击的物品: " + clickedItem.getType() + ", 显示名称: " +
                               (clickedItem.getItemMeta() != null ? clickedItem.getItemMeta().getDisplayName() : "无"));

        // 获取护甲ID
        String armorId = getItemId(clickedItem);
        plugin.getLogger().info("提取的护甲ID: " + armorId);

        if (armorId == null) {
            plugin.getLogger().warning("无法从物品中提取护甲ID");
            player.sendMessage(ChatColor.RED + "无法识别选中的护甲，请重试！");
            return;
        }

        plugin.getLogger().info("开始保存护甲套装配置: " + armorId);

        // 直接设置护甲套装（护甲不需要确认菜单，因为它们不占用背包槽位）
        // 这里可以保存护甲套装配置到游戏配置文件中
        saveArmorSetConfiguration(player, gameName, editingSlot, armorId);

        // 更新护甲槽位显示并返回主编辑器
        updateArmorSlotAndReturnToEditor(player, gameName, editingSlot, armorId);
    }

    /**
     * 保存护甲套装配置
     */
    private void saveArmorSetConfiguration(Player player, String gameName, int armorSlot, String armorId) {
        try {
            FileConfiguration config = gameManager.getGameConfig(gameName);
            if (config == null) {
                player.sendMessage(ChatColor.RED + "无法获取游戏配置！");
                return;
            }

            // 根据槽位确定护甲套装类型
            String armorSetType = (armorSlot == 36) ? "upper" : "lower";

            // 保存护甲套装配置到armorEquipment节点（与游戏实际读取的配置保持一致）
            config.set("armorEquipment." + armorSetType, armorId);

            // 保存配置文件
            File gameFile = new File(plugin.getDataFolder(), "game/" + gameName + ".yml");
            config.save(gameFile);

            plugin.getLogger().info("已保存游戏 '" + gameName + "' 的" + armorSetType + "护甲装备配置: " + armorId);

        } catch (Exception e) {
            plugin.getLogger().severe("保存护甲套装配置时发生错误: " + e.getMessage());
            e.printStackTrace();
            player.sendMessage(ChatColor.RED + "保存护甲配置失败！");
        }
    }

    /**
     * 更新护甲槽位显示并返回主编辑器
     */
    private void updateArmorSlotAndReturnToEditor(Player player, String gameName, int armorSlot, String armorId) {
        plugin.getLogger().info("开始更新护甲槽位显示并返回主编辑器");

        try {
            // 先关闭当前GUI
            player.closeInventory();

            // 延迟执行，确保当前GUI操作完成
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                try {
                    plugin.getLogger().info("延迟任务执行 - 重新打开主编辑器");

                    // 确保玩家仍然在线
                    if (!player.isOnline()) {
                        plugin.getLogger().warning("玩家已离线，取消GUI更新");
                        return;
                    }

                    // 重新打开主编辑器，包含护甲更新
                    openKitEditor(player, gameName);

                    // 发送成功消息
                    String armorDisplayName = getArmorDisplayName(armorId);
                    player.sendMessage(ChatColor.GREEN + "已设置护甲套装: " + armorDisplayName);
                    plugin.getLogger().info("成功更新护甲槽位显示，护甲: " + armorDisplayName);

                } catch (Exception e) {
                    plugin.getLogger().severe("更新护甲槽位显示时发生错误: " + e.getMessage());
                    e.printStackTrace();
                    player.sendMessage(ChatColor.RED + "更新护甲显示失败！");
                    // 回退到重新打开编辑器
                    try {
                        openKitEditor(player, gameName);
                    } catch (Exception ex) {
                        plugin.getLogger().severe("回退打开编辑器也失败: " + ex.getMessage());
                        player.closeInventory();
                    }
                }
            }, 3L); // 延迟3个tick确保GUI操作完成

        } catch (Exception e) {
            plugin.getLogger().severe("更新护甲槽位时发生错误: " + e.getMessage());
            e.printStackTrace();
            // 回退到重新打开编辑器
            try {
                openKitEditor(player, gameName);
                player.sendMessage(ChatColor.GREEN + "已设置护甲套装: " + armorId);
            } catch (Exception ex) {
                plugin.getLogger().severe("回退操作失败: " + ex.getMessage());
                player.closeInventory();
            }
        }
    }

    /**
     * 创建带有已设置值的护甲套装槽位物品
     */
    private ItemStack createArmorSetItemWithValue(String setType, String displayName, String armorId) {
        Material material;
        List<String> lore = new ArrayList<>();

        switch (setType) {
            case "upper":
                material = Material.IRON_CHESTPLATE;
                lore.add(ChatColor.GRAY + "包含: " + ChatColor.YELLOW + "头盔 + 胸甲");
                lore.add(ChatColor.GRAY + "状态: " + ChatColor.GREEN + "已设置");
                lore.add(ChatColor.GRAY + "当前: " + ChatColor.AQUA + getArmorDisplayName(armorId));
                lore.add("");
                lore.add(ChatColor.AQUA + "点击重新设置上身护甲套装");
                break;
            case "lower":
                material = Material.IRON_LEGGINGS;
                lore.add(ChatColor.GRAY + "包含: " + ChatColor.YELLOW + "护腿 + 靴子");
                lore.add(ChatColor.GRAY + "状态: " + ChatColor.GREEN + "已设置");
                lore.add(ChatColor.GRAY + "当前: " + ChatColor.AQUA + getArmorDisplayName(armorId));
                lore.add("");
                lore.add(ChatColor.AQUA + "点击重新设置下身护甲套装");
                break;
            default:
                material = Material.BARRIER;
                lore.add(ChatColor.RED + "未知护甲类型");
        }

        lore.add(ChatColor.YELLOW + "注意: 护甲设置独立于背包槽位");

        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(ChatColor.BLUE + displayName);
            meta.setLore(lore);
            item.setItemMeta(meta);
        }
        return item;
    }

    /**
     * 获取护甲的显示名称
     */
    private String getArmorDisplayName(String armorId) {
        if (gameKitManager != null) {
            String itemName = gameKitManager.getItemName(armorId);
            if (itemName != null && !itemName.equals(armorId)) {
                return ChatColor.stripColor(itemName);
            }
        }
        return armorId;
    }

}
