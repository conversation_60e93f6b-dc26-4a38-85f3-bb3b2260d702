package org.Ver_zhzh.customZombie.UserCustomZombie;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.entity.Zombie;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.plugin.Plugin;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.Ver_zhzh.customZombie.CustomZombie;
import org.Ver_zhzh.customZombie.UserCustomZombie.AdvancedSkillHandler;

import java.io.File;
import java.io.IOException;

/**
 * 用户自定义僵尸类
 * 基于配置文件的僵尸生成系统，支持覆盖默认CustomZombie的设置
 * 符合阿里巴巴编码规范
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22
 */
public class UserCustomZombie {

    // 插件实例和日志记录器
    private final Plugin plugin;
    private final Logger logger;

    // 原有CustomZombie实例的引用，用于技能共享
    private final CustomZombie originalCustomZombie;

    // 高级技能处理器
    private final AdvancedSkillHandler advancedSkillHandler;

    // 配置文件相关
    private FileConfiguration zombieConfig;
    private File zombieConfigFile;

    // 配置缓存
    private final Map<String, ZombieOverrideConfig> configCache = new HashMap<>();

    // 系统设置缓存
    private boolean useDefaultSettings = true;
    private boolean useUserCustomSettings = false;
    private String priorityStrategy = "default_first";
    private boolean debugMode = false;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     * @param originalCustomZombie 原有CustomZombie实例
     */
    public UserCustomZombie(Plugin plugin, CustomZombie originalCustomZombie) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.originalCustomZombie = originalCustomZombie;

        // 初始化高级技能处理器
        this.advancedSkillHandler = new AdvancedSkillHandler(plugin, logger, originalCustomZombie, debugMode);

        // 初始化配置文件
        initializeConfig();

        // 加载配置
        loadConfig();

        logger.info("UserCustomZombie系统初始化完成");
    }

    /**
     * 初始化配置文件
     */
    private void initializeConfig() {
        zombieConfigFile = new File(plugin.getDataFolder(), "zombie.yml");

        if (!zombieConfigFile.exists()) {
            // 如果配置文件不存在，从resources复制
            plugin.saveResource("zombie.yml", false);
            logger.info("已创建默认zombie.yml配置文件");
        }

        zombieConfig = YamlConfiguration.loadConfiguration(zombieConfigFile);
    }

    /**
     * 加载配置文件
     */
    public void loadConfig() {
        try {
            zombieConfig = YamlConfiguration.loadConfiguration(zombieConfigFile);

            // 加载系统设置
            ConfigurationSection systemSettings = zombieConfig.getConfigurationSection("system_settings");
            if (systemSettings != null) {
                useDefaultSettings = systemSettings.getBoolean("use_default_settings", true);
                useUserCustomSettings = systemSettings.getBoolean("use_user_custom_settings", false);
                priorityStrategy = systemSettings.getString("priority_strategy", "default_first");
                debugMode = systemSettings.getBoolean("debug_mode", false);
            }

            // 清空并重新加载配置缓存
            configCache.clear();
            loadOverrideConfigs();

            if (debugMode) {
                logger.info("UserCustomZombie配置加载完成:");
                logger.info("- 使用默认设置: " + useDefaultSettings);
                logger.info("- 使用用户自定义设置: " + useUserCustomSettings);
                logger.info("- 优先级策略: " + priorityStrategy);
                logger.info("- 已加载覆盖配置数量: " + configCache.size());
            }

        } catch (Exception e) {
            logger.severe("加载zombie.yml配置文件时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 加载覆盖配置
     */
    private void loadOverrideConfigs() {
        ConfigurationSection overrides = zombieConfig.getConfigurationSection("user_custom_overrides.specific_overrides");
        if (overrides == null) {
            logger.warning("未找到specific_overrides配置节");
            return;
        }

        // 加载ID1-ID25的配置
        for (String zombieId : new String[]{"id1", "id2", "id3", "id4", "id5", "id6", "id7", "id8", "id9", "id10", "id11", "id12", "id13", "id14", "id15", "id16", "id17", "id18", "id19", "id20", "id21", "id22", "id23", "id24", "id25"}) {
            ConfigurationSection zombieSection = overrides.getConfigurationSection(zombieId);
            if (zombieSection != null && zombieSection.getBoolean("enabled", false)) {
                ZombieOverrideConfig config = new ZombieOverrideConfig();
                config.loadFromConfig(zombieSection);
                configCache.put(zombieId, config);

                if (debugMode) {
                    logger.info("已加载" + zombieId + "的覆盖配置");
                }
            }
        }
    }

    /**
     * 生成用户自定义僵尸（主要入口方法）
     *
     * @param player 触发生成的玩家
     * @param zombieId 僵尸ID
     * @return 生成的僵尸实体，失败时返回null
     */
    public Zombie spawnUserCustomZombie(Player player, String zombieId) {
        Location spawnLocation = player.getLocation().add(player.getLocation().getDirection().multiply(2));
        return spawnUserCustomZombieDirect(spawnLocation, zombieId);
    }

    /**
     * 直接在指定位置生成用户自定义僵尸
     *
     * @param location 生成位置
     * @param zombieId 僵尸ID
     * @return 生成的僵尸实体，失败时返回null
     */
    public Zombie spawnUserCustomZombieDirect(Location location, String zombieId) {
        try {
            if (debugMode) {
                logger.info("尝试生成用户自定义僵尸: " + zombieId + " 在位置: " + formatLocation(location));
            }

            // 检查是否启用用户自定义设置
            if (!useUserCustomSettings) {
                if (debugMode) {
                    logger.info("用户自定义设置未启用，跳过生成");
                }
                return null;
            }

            // 检查是否有对应的覆盖配置
            ZombieOverrideConfig overrideConfig = configCache.get(zombieId);
            if (overrideConfig == null) {
                if (debugMode) {
                    logger.info("未找到" + zombieId + "的覆盖配置");
                }
                return null;
            }

            // 根据优先级策略决定生成方式
            switch (priorityStrategy) {
                case "user_first":
                    return spawnWithUserCustomSettings(location, zombieId, overrideConfig);

                case "default_first":
                    // 先尝试默认设置，失败时使用用户自定义
                    if (useDefaultSettings && originalCustomZombie != null) {
                        try {
                            Zombie defaultZombie = originalCustomZombie.spawnCustomZombieDirect(location, zombieId);
                            if (defaultZombie != null) {
                                // 应用用户自定义覆盖到默认僵尸
                                applyOverridesToZombie(defaultZombie, overrideConfig);
                                return defaultZombie;
                            }
                        } catch (Exception e) {
                            logger.warning("默认僵尸生成失败，尝试用户自定义: " + e.getMessage());
                        }
                    }
                    return spawnWithUserCustomSettings(location, zombieId, overrideConfig);

                case "both":
                    // 实验性功能：同时生成两种僵尸
                    Zombie defaultZombie = null;
                    Zombie userZombie = null;

                    if (useDefaultSettings && originalCustomZombie != null) {
                        try {
                            defaultZombie = originalCustomZombie.spawnCustomZombieDirect(location, zombieId);
                        } catch (Exception e) {
                            logger.warning("默认僵尸生成失败: " + e.getMessage());
                        }
                    }

                    // 在稍微偏移的位置生成用户自定义僵尸
                    Location offsetLocation = location.clone().add(1, 0, 1);
                    userZombie = spawnWithUserCustomSettings(offsetLocation, zombieId, overrideConfig);

                    // 返回成功生成的僵尸（优先返回用户自定义）
                    return userZombie != null ? userZombie : defaultZombie;

                default:
                    logger.warning("未知的优先级策略: " + priorityStrategy);
                    return null;
            }

        } catch (Exception e) {
            logger.severe("生成用户自定义僵尸时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 使用用户自定义设置生成僵尸
     *
     * @param location 生成位置
     * @param zombieId 僵尸ID
     * @param overrideConfig 覆盖配置
     * @return 生成的僵尸实体，失败时返回null
     */
    private Zombie spawnWithUserCustomSettings(Location location, String zombieId, ZombieOverrideConfig overrideConfig) {
        try {
            // 生成基础僵尸实体
            Zombie zombie = (Zombie) location.getWorld().spawnEntity(location, EntityType.ZOMBIE);

            // 应用覆盖配置
            applyOverridesToZombie(zombie, overrideConfig);

            // 添加用户自定义标记
            zombie.setMetadata("userCustomZombie", new FixedMetadataValue(plugin, true));
            zombie.setMetadata("zombieId", new FixedMetadataValue(plugin, zombieId));
            zombie.setMetadata("dualZombieSystem", new FixedMetadataValue(plugin, "user_custom"));

            // 如果原有CustomZombie有特殊技能，尝试启用
            enableOriginalSkills(zombie, zombieId);

            if (debugMode) {
                logger.info("成功生成用户自定义僵尸: " + zombieId);
            }

            return zombie;

        } catch (Exception e) {
            logger.severe("使用用户自定义设置生成僵尸失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 应用覆盖配置到僵尸实体
     *
     * @param zombie 僵尸实体
     * @param config 覆盖配置
     */
    private void applyOverridesToZombie(Zombie zombie, ZombieOverrideConfig config) {
        // 设置生命值
        if (config.healthOverride > 0) {
            zombie.setMaxHealth(config.healthOverride);
            zombie.setHealth(config.healthOverride);
        }

        // 设置自定义名称
        if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
            zombie.setCustomName(config.customNameOverride);
            zombie.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
        }

        // 设置武器
        if (config.weaponOverride != null) {
            try {
                Material weaponMaterial = Material.valueOf(config.weaponOverride);
                ItemStack weapon = new ItemStack(weaponMaterial);
                zombie.getEquipment().setItemInMainHand(weapon);
            } catch (IllegalArgumentException e) {
                logger.warning("无效的武器材料: " + config.weaponOverride);
            }
        }

        // 设置护甲（包括特殊僵尸的默认装备）
        applyArmorOverridesWithDefaults(zombie, config);

        // 应用药水效果
        applyPotionEffects(zombie, config);

        // 设置移动速度
        if (config.speedMultiplier > 0) {
            try {
                // 尝试不同的属性名称，兼容不同版本的API
                org.bukkit.attribute.AttributeInstance speedAttr = null;

                // 尝试新版本的属性名称
                try {
                    speedAttr = zombie.getAttribute(org.bukkit.attribute.Attribute.valueOf("MOVEMENT_SPEED"));
                } catch (Exception e1) {
                    try {
                        speedAttr = zombie.getAttribute(org.bukkit.attribute.Attribute.valueOf("GENERIC_MOVEMENT_SPEED"));
                    } catch (Exception e2) {
                        logger.warning("无法找到移动速度属性，跳过设置");
                    }
                }

                if (speedAttr != null) {
                    speedAttr.setBaseValue(speedAttr.getBaseValue() * config.speedMultiplier);
                }
            } catch (Exception e) {
                logger.warning("设置移动速度失败: " + e.getMessage());
            }
        }

        // 设置攻击伤害
        if (config.damageOverride > 0) {
            try {
                // 尝试不同的属性名称，兼容不同版本的API
                org.bukkit.attribute.AttributeInstance damageAttr = null;

                // 尝试新版本的属性名称
                try {
                    damageAttr = zombie.getAttribute(org.bukkit.attribute.Attribute.valueOf("ATTACK_DAMAGE"));
                } catch (Exception e1) {
                    try {
                        damageAttr = zombie.getAttribute(org.bukkit.attribute.Attribute.valueOf("GENERIC_ATTACK_DAMAGE"));
                    } catch (Exception e2) {
                        logger.warning("无法找到攻击伤害属性，跳过设置");
                    }
                }

                if (damageAttr != null) {
                    damageAttr.setBaseValue(config.damageOverride);
                }
            } catch (Exception e) {
                logger.warning("设置攻击伤害失败: " + e.getMessage());
            }
        }
    }

    /**
     * 应用护甲覆盖（包括特殊僵尸的默认装备）
     *
     * @param zombie 僵尸实体
     * @param config 覆盖配置
     */
    private void applyArmorOverridesWithDefaults(Zombie zombie, ZombieOverrideConfig config) {
        // 获取僵尸ID以确定默认装备
        String zombieId = null;
        if (zombie.hasMetadata("zombieId")) {
            zombieId = zombie.getMetadata("zombieId").get(0).asString();
        }

        // 为特殊僵尸设置默认装备（如果用户没有覆盖的话）
        applyDefaultEquipmentForSpecialZombies(zombie, zombieId, config);

        // 然后应用用户的装备覆盖
        applyArmorOverrides(zombie, config);
    }

    /**
     * 为特殊僵尸应用默认装备
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     * @param config 覆盖配置
     */
    private void applyDefaultEquipmentForSpecialZombies(Zombie zombie, String zombieId, ZombieOverrideConfig config) {
        if (zombieId == null) return;

        switch (zombieId) {
            case "id11": // 自爆僵尸
                // 如果用户没有配置头盔覆盖，使用默认的TNT头盔
                if (config.helmetOverride == null || config.helmetOverride.isEmpty()) {
                    ItemStack tntHelmet = new ItemStack(Material.TNT);
                    zombie.getEquipment().setHelmet(tntHelmet);
                    if (debugMode) {
                        logger.info("为自爆僵尸设置默认TNT头盔");
                    }
                }
                break;

            case "id12": // 毒箭僵尸
                // 如果用户没有配置武器覆盖，使用默认的弓
                if (config.weaponOverride == null || config.weaponOverride.isEmpty()) {
                    ItemStack bow = new ItemStack(Material.BOW);
                    zombie.getEquipment().setItemInMainHand(bow);
                    if (debugMode) {
                        logger.info("为毒箭僵尸设置默认弓");
                    }
                }
                break;

            case "id9": // 肥胖僵尸
                // 如果用户没有配置头盔覆盖，使用默认的煤炭块头盔
                if (config.helmetOverride == null || config.helmetOverride.isEmpty()) {
                    ItemStack coalHelmet = new ItemStack(Material.COAL_BLOCK);
                    zombie.getEquipment().setHelmet(coalHelmet);
                    if (debugMode) {
                        logger.info("为肥胖僵尸设置默认煤炭块头盔");
                    }
                }
                // 如果用户没有配置胸甲覆盖，使用默认的皮革胸甲（黑色）
                if (config.chestplateOverride == null || config.chestplateOverride.isEmpty()) {
                    ItemStack leatherChestplate = new ItemStack(Material.LEATHER_CHESTPLATE);
                    zombie.getEquipment().setChestplate(leatherChestplate);
                    if (debugMode) {
                        logger.info("为肥胖僵尸设置默认皮革胸甲");
                    }
                }
                // 如果用户没有配置靴子覆盖，使用默认的皮革靴子（红色）
                if (config.bootsOverride == null || config.bootsOverride.isEmpty()) {
                    ItemStack leatherBoots = new ItemStack(Material.LEATHER_BOOTS);
                    zombie.getEquipment().setBoots(leatherBoots);
                    if (debugMode) {
                        logger.info("为肥胖僵尸设置默认皮革靴子");
                    }
                }
                break;

            case "id13": // 电击僵尸
                // 如果用户没有配置武器覆盖，使用默认的木棍
                if (config.weaponOverride == null || config.weaponOverride.isEmpty()) {
                    ItemStack stick = new ItemStack(Material.STICK);
                    zombie.getEquipment().setItemInMainHand(stick);
                    if (debugMode) {
                        logger.info("为电击僵尸设置默认木棍");
                    }
                }
                break;

            case "id14": // 冰冻僵尸
                // 如果用户没有配置武器覆盖，使用默认的钻石
                if (config.weaponOverride == null || config.weaponOverride.isEmpty()) {
                    ItemStack diamond = new ItemStack(Material.DIAMOND);
                    zombie.getEquipment().setItemInMainHand(diamond);
                    if (debugMode) {
                        logger.info("为冰冻僵尸设置默认钻石");
                    }
                }
                // 如果用户没有配置头盔覆盖，使用默认的冰块头盔
                if (config.helmetOverride == null || config.helmetOverride.isEmpty()) {
                    ItemStack iceHelmet = new ItemStack(Material.ICE);
                    zombie.getEquipment().setHelmet(iceHelmet);
                    if (debugMode) {
                        logger.info("为冰冻僵尸设置默认冰块头盔");
                    }
                }
                break;

            case "id15": // 暗影僵尸
                // 如果用户没有配置头盔覆盖，使用默认的黑曜石头盔
                if (config.helmetOverride == null || config.helmetOverride.isEmpty()) {
                    ItemStack obsidianHelmet = new ItemStack(Material.OBSIDIAN);
                    zombie.getEquipment().setHelmet(obsidianHelmet);
                    if (debugMode) {
                        logger.info("为暗影僵尸设置默认黑曜石头盔");
                    }
                }
                break;

            case "id16": // 毁灭僵尸
                // 如果用户没有配置武器覆盖，使用默认的钻石剑
                if (config.weaponOverride == null || config.weaponOverride.isEmpty()) {
                    ItemStack diamondSword = new ItemStack(Material.DIAMOND_SWORD);
                    zombie.getEquipment().setItemInMainHand(diamondSword);
                    if (debugMode) {
                        logger.info("为毁灭僵尸设置默认钻石剑");
                    }
                }
                break;

            case "id17": // 雷霆僵尸
                // 如果用户没有配置武器覆盖，使用默认的弓
                if (config.weaponOverride == null || config.weaponOverride.isEmpty()) {
                    ItemStack bow = new ItemStack(Material.BOW);
                    zombie.getEquipment().setItemInMainHand(bow);
                    if (debugMode) {
                        logger.info("为雷霆僵尸设置默认弓");
                    }
                }
                break;

            case "id18": // 变异科学家
                // 如果用户没有配置武器覆盖，使用默认的钻石剑（火焰附加2）
                if (config.weaponOverride == null || config.weaponOverride.isEmpty()) {
                    ItemStack scientistSword = new ItemStack(Material.DIAMOND_SWORD);
                    ItemMeta scientistSwordMeta = scientistSword.getItemMeta();
                    scientistSwordMeta.addEnchant(Enchantment.FIRE_ASPECT, 2, true);
                    scientistSword.setItemMeta(scientistSwordMeta);
                    zombie.getEquipment().setItemInMainHand(scientistSword);
                    if (debugMode) {
                        logger.info("为变异科学家设置默认火焰附加2钻石剑");
                    }
                }
                // 如果用户没有配置头盔覆盖，使用默认的玩家头颅
                if (config.helmetOverride == null || config.helmetOverride.isEmpty()) {
                    ItemStack scientistHead = new ItemStack(Material.PLAYER_HEAD);
                    zombie.getEquipment().setHelmet(scientistHead);
                    if (debugMode) {
                        logger.info("为变异科学家设置默认玩家头颅");
                    }
                }
                break;

            case "id19": // 变异法师
                // 如果用户没有配置武器覆盖，使用默认的木棍
                if (config.weaponOverride == null || config.weaponOverride.isEmpty()) {
                    ItemStack stick = new ItemStack(Material.STICK);
                    zombie.getEquipment().setItemInMainHand(stick);
                    if (debugMode) {
                        logger.info("为变异法师设置默认木棍");
                    }
                }
                // 如果用户没有配置头盔覆盖，使用默认的艾利克斯头颅
                if (config.helmetOverride == null || config.helmetOverride.isEmpty()) {
                    ItemStack alexHead = new ItemStack(Material.PLAYER_HEAD);
                    // 这里可以设置为艾利克斯头颅，但需要特殊处理
                    zombie.getEquipment().setHelmet(alexHead);
                    if (debugMode) {
                        logger.info("为变异法师设置默认艾利克斯头颅");
                    }
                }
                // 如果用户没有配置其他装备，设置默认的皮革装备
                if (config.chestplateOverride == null || config.chestplateOverride.isEmpty()) {
                    ItemStack leatherChestplate = new ItemStack(Material.LEATHER_CHESTPLATE);
                    zombie.getEquipment().setChestplate(leatherChestplate);
                }
                if (config.leggingsOverride == null || config.leggingsOverride.isEmpty()) {
                    ItemStack leatherLeggings = new ItemStack(Material.LEATHER_LEGGINGS);
                    zombie.getEquipment().setLeggings(leatherLeggings);
                }
                if (config.bootsOverride == null || config.bootsOverride.isEmpty()) {
                    ItemStack leatherBoots = new ItemStack(Material.LEATHER_BOOTS);
                    zombie.getEquipment().setBoots(leatherBoots);
                }
                break;

            case "id22": // 变异雷霆僵尸
                // 如果用户没有配置武器覆盖，使用默认的弓
                if (config.weaponOverride == null || config.weaponOverride.isEmpty()) {
                    ItemStack bow = new ItemStack(Material.BOW);
                    zombie.getEquipment().setItemInMainHand(bow);
                    if (debugMode) {
                        logger.info("为变异雷霆僵尸设置默认弓");
                    }
                }
                // 如果用户没有配置头盔覆盖，使用默认的僵尸头颅
                if (config.helmetOverride == null || config.helmetOverride.isEmpty()) {
                    ItemStack zombieHead = new ItemStack(Material.ZOMBIE_HEAD);
                    zombie.getEquipment().setHelmet(zombieHead);
                    if (debugMode) {
                        logger.info("为变异雷霆僵尸设置默认僵尸头颅");
                    }
                }
                // 如果用户没有配置其他装备，设置默认的锁链装备
                if (config.chestplateOverride == null || config.chestplateOverride.isEmpty()) {
                    ItemStack chainChestplate = new ItemStack(Material.CHAINMAIL_CHESTPLATE);
                    ItemMeta chestMeta = chainChestplate.getItemMeta();
                    chestMeta.addEnchant(Enchantment.PROTECTION, 2, true);
                    chainChestplate.setItemMeta(chestMeta);
                    zombie.getEquipment().setChestplate(chainChestplate);
                }
                if (config.leggingsOverride == null || config.leggingsOverride.isEmpty()) {
                    ItemStack chainLeggings = new ItemStack(Material.CHAINMAIL_LEGGINGS);
                    ItemMeta legMeta = chainLeggings.getItemMeta();
                    legMeta.addEnchant(Enchantment.PROTECTION, 2, true);
                    chainLeggings.setItemMeta(legMeta);
                    zombie.getEquipment().setLeggings(chainLeggings);
                }
                if (config.bootsOverride == null || config.bootsOverride.isEmpty()) {
                    ItemStack chainBoots = new ItemStack(Material.CHAINMAIL_BOOTS);
                    ItemMeta bootMeta = chainBoots.getItemMeta();
                    bootMeta.addEnchant(Enchantment.PROTECTION, 2, true);
                    chainBoots.setItemMeta(bootMeta);
                    zombie.getEquipment().setBoots(chainBoots);
                }
                break;

            case "id23": // 终极毁灭僵尸
                // 如果用户没有配置武器覆盖，使用默认的锋利3钻石剑
                if (config.weaponOverride == null || config.weaponOverride.isEmpty()) {
                    ItemStack diamondSword = new ItemStack(Material.DIAMOND_SWORD);
                    ItemMeta swordMeta = diamondSword.getItemMeta();
                    swordMeta.addEnchant(Enchantment.SHARPNESS, 3, true);
                    diamondSword.setItemMeta(swordMeta);
                    zombie.getEquipment().setItemInMainHand(diamondSword);
                    if (debugMode) {
                        logger.info("为终极毁灭僵尸设置默认锋利3钻石剑");
                    }
                }
                // 如果用户没有配置头盔覆盖，使用默认的史蒂夫头颅
                if (config.helmetOverride == null || config.helmetOverride.isEmpty()) {
                    ItemStack steveHead = new ItemStack(Material.PLAYER_HEAD);
                    zombie.getEquipment().setHelmet(steveHead);
                    if (debugMode) {
                        logger.info("为终极毁灭僵尸设置默认史蒂夫头颅");
                    }
                }
                // 如果用户没有配置其他装备，设置默认的钻石装备
                if (config.chestplateOverride == null || config.chestplateOverride.isEmpty()) {
                    ItemStack diamondChestplate = new ItemStack(Material.DIAMOND_CHESTPLATE);
                    ItemMeta chestMeta = diamondChestplate.getItemMeta();
                    chestMeta.addEnchant(Enchantment.PROTECTION, 3, true);
                    chestMeta.addEnchant(Enchantment.UNBREAKING, 3, true);
                    diamondChestplate.setItemMeta(chestMeta);
                    zombie.getEquipment().setChestplate(diamondChestplate);
                }
                if (config.leggingsOverride == null || config.leggingsOverride.isEmpty()) {
                    ItemStack diamondLeggings = new ItemStack(Material.DIAMOND_LEGGINGS);
                    ItemMeta legMeta = diamondLeggings.getItemMeta();
                    legMeta.addEnchant(Enchantment.PROTECTION, 3, true);
                    legMeta.addEnchant(Enchantment.UNBREAKING, 3, true);
                    diamondLeggings.setItemMeta(legMeta);
                    zombie.getEquipment().setLeggings(diamondLeggings);
                }
                if (config.bootsOverride == null || config.bootsOverride.isEmpty()) {
                    ItemStack diamondBoots = new ItemStack(Material.DIAMOND_BOOTS);
                    ItemMeta bootMeta = diamondBoots.getItemMeta();
                    bootMeta.addEnchant(Enchantment.PROTECTION, 3, true);
                    bootMeta.addEnchant(Enchantment.UNBREAKING, 3, true);
                    diamondBoots.setItemMeta(bootMeta);
                    zombie.getEquipment().setBoots(diamondBoots);
                }
                break;

            case "id25": // 变异博士
                // 如果用户没有配置武器覆盖，使用默认的火焰附加2钻石剑
                if (config.weaponOverride == null || config.weaponOverride.isEmpty()) {
                    ItemStack diamondSword = new ItemStack(Material.DIAMOND_SWORD);
                    ItemMeta swordMeta = diamondSword.getItemMeta();
                    swordMeta.addEnchant(Enchantment.FIRE_ASPECT, 2, true);
                    swordMeta.addEnchant(Enchantment.SHARPNESS, 3, true);
                    swordMeta.addEnchant(Enchantment.UNBREAKING, 3, true);
                    diamondSword.setItemMeta(swordMeta);
                    zombie.getEquipment().setItemInMainHand(diamondSword);
                    if (debugMode) {
                        logger.info("为变异博士设置默认火焰附加2钻石剑");
                    }
                }
                // 如果用户没有配置头盔覆盖，使用默认的史蒂夫头颅
                if (config.helmetOverride == null || config.helmetOverride.isEmpty()) {
                    ItemStack steveHead = new ItemStack(Material.PLAYER_HEAD);
                    zombie.getEquipment().setHelmet(steveHead);
                    if (debugMode) {
                        logger.info("为变异博士设置默认史蒂夫头颅");
                    }
                }
                // 如果用户没有配置其他装备，设置默认的钻石装备
                if (config.chestplateOverride == null || config.chestplateOverride.isEmpty()) {
                    ItemStack diamondChestplate = new ItemStack(Material.DIAMOND_CHESTPLATE);
                    ItemMeta chestMeta = diamondChestplate.getItemMeta();
                    chestMeta.addEnchant(Enchantment.PROTECTION, 3, true);
                    chestMeta.addEnchant(Enchantment.UNBREAKING, 3, true);
                    diamondChestplate.setItemMeta(chestMeta);
                    zombie.getEquipment().setChestplate(diamondChestplate);
                }
                if (config.leggingsOverride == null || config.leggingsOverride.isEmpty()) {
                    ItemStack diamondLeggings = new ItemStack(Material.DIAMOND_LEGGINGS);
                    ItemMeta legMeta = diamondLeggings.getItemMeta();
                    legMeta.addEnchant(Enchantment.PROTECTION, 3, true);
                    legMeta.addEnchant(Enchantment.UNBREAKING, 3, true);
                    diamondLeggings.setItemMeta(legMeta);
                    zombie.getEquipment().setLeggings(diamondLeggings);
                }
                if (config.bootsOverride == null || config.bootsOverride.isEmpty()) {
                    ItemStack diamondBoots = new ItemStack(Material.DIAMOND_BOOTS);
                    ItemMeta bootMeta = diamondBoots.getItemMeta();
                    bootMeta.addEnchant(Enchantment.PROTECTION, 3, true);
                    bootMeta.addEnchant(Enchantment.UNBREAKING, 3, true);
                    diamondBoots.setItemMeta(bootMeta);
                    zombie.getEquipment().setBoots(diamondBoots);
                }
                break;

            // 其他特殊僵尸可以在这里添加默认装备
        }
    }

    /**
     * 应用护甲覆盖
     *
     * @param zombie 僵尸实体
     * @param config 覆盖配置
     */
    private void applyArmorOverrides(Zombie zombie, ZombieOverrideConfig config) {
        // 应用头盔
        applyArmorPiece(zombie, config.helmetOverride, config.helmetEnchantments, "helmet");

        // 应用胸甲
        applyArmorPiece(zombie, config.chestplateOverride, config.chestplateEnchantments, "chestplate");

        // 应用护腿
        applyArmorPiece(zombie, config.leggingsOverride, config.leggingsEnchantments, "leggings");

        // 应用靴子
        applyArmorPiece(zombie, config.bootsOverride, config.bootsEnchantments, "boots");
    }

    /**
     * 应用单个护甲部件
     *
     * @param zombie 僵尸实体
     * @param materialName 材料名称
     * @param enchantments 附魔Map
     * @param armorType 护甲类型（用于日志）
     */
    private void applyArmorPiece(Zombie zombie, String materialName, Map<String, Integer> enchantments, String armorType) {
        if (materialName == null) {
            return;
        }

        try {
            Material armorMaterial = Material.valueOf(materialName);
            ItemStack armorPiece = new ItemStack(armorMaterial);

            // 应用附魔
            if (enchantments != null && !enchantments.isEmpty()) {
                ItemMeta meta = armorPiece.getItemMeta();
                for (Map.Entry<String, Integer> entry : enchantments.entrySet()) {
                    try {
                        Enchantment enchantment = Enchantment.getByName(entry.getKey().toUpperCase());
                        if (enchantment != null) {
                            meta.addEnchant(enchantment, entry.getValue(), true);
                        }
                    } catch (Exception e) {
                        logger.warning("无效的附魔: " + entry.getKey() + " 在 " + armorType);
                    }
                }
                armorPiece.setItemMeta(meta);
            }

            // 设置护甲到对应部位
            switch (armorType.toLowerCase()) {
                case "helmet":
                    zombie.getEquipment().setHelmet(armorPiece);
                    break;
                case "chestplate":
                    zombie.getEquipment().setChestplate(armorPiece);
                    break;
                case "leggings":
                    zombie.getEquipment().setLeggings(armorPiece);
                    break;
                case "boots":
                    zombie.getEquipment().setBoots(armorPiece);
                    break;
            }

        } catch (IllegalArgumentException e) {
            logger.warning("无效的" + armorType + "材料: " + materialName);
        }
    }

    /**
     * 应用药水效果
     *
     * @param zombie 僵尸实体
     * @param config 覆盖配置
     */
    private void applyPotionEffects(Zombie zombie, ZombieOverrideConfig config) {
        if (config.potionEffects != null && !config.potionEffects.isEmpty()) {
            if (debugMode) {
                logger.info("开始应用药水效果，共 " + config.potionEffects.size() + " 个效果");
            }

            for (Map.Entry<String, PotionEffectConfig> entry : config.potionEffects.entrySet()) {
                try {
                    PotionEffectType effectType = PotionEffectType.getByName(entry.getKey().toUpperCase());
                    if (effectType != null) {
                        PotionEffectConfig effectConfig = entry.getValue();
                        int duration = effectConfig.duration == -1 ? Integer.MAX_VALUE : effectConfig.duration;
                        PotionEffect effect = new PotionEffect(effectType, duration, effectConfig.level);
                        zombie.addPotionEffect(effect);

                        if (debugMode) {
                            logger.info("成功应用药水效果: " + entry.getKey() + " 等级:" + effectConfig.level +
                                      " 持续时间:" + (effectConfig.duration == -1 ? "永久" : effectConfig.duration + "tick"));
                        }
                    } else {
                        logger.warning("无法找到药水效果类型: " + entry.getKey());
                    }
                } catch (Exception e) {
                    logger.warning("应用药水效果失败: " + entry.getKey() + " - " + e.getMessage());
                }
            }
        } else {
            if (debugMode) {
                logger.info("没有配置药水效果或药水效果列表为空");
            }
        }
    }

    /**
     * 启用原有CustomZombie的技能
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     */
    private void enableOriginalSkills(Zombie zombie, String zombieId) {
        if (originalCustomZombie == null) {
            return;
        }

        try {
            // 根据僵尸ID启用对应的技能
            switch (zombieId) {
                case "id4": // 钻斧僵尸（无特殊技能）
                    // 钻斧僵尸没有特殊技能，只是高攻击力的近战僵尸
                    if (debugMode) {
                        logger.info("已为" + zombieId + "设置钻斧僵尸属性（无特殊技能）");
                    }
                    break;

                case "id5": // 剧毒僵尸
                    enablePoisonZombieSkills(zombie, zombieId);
                    break;

                case "id6": // 双生僵尸（新版本）
                    enableTwinZombieSkills(zombie, zombieId);
                    break;

                case "id7": // 骷髅僵尸
                    enableSkeletonZombieSkills(zombie, zombieId);
                    break;

                case "id8": // 武装僵尸
                    enableArmedZombieSkills(zombie, zombieId);
                    break;

                case "id9": // 肥胖僵尸
                    enableFatZombieSkills(zombie, zombieId);
                    break;

                case "id10": // 法师僵尸
                    enableMageZombieSkills(zombie, zombieId);
                    break;

                case "id11": // 自爆僵尸
                    ZombieOverrideConfig config11 = configCache.get(zombieId);
                    advancedSkillHandler.enableExplosionZombieSkills(zombie, zombieId, config11);
                    break;

                case "id12": // 毒箭僵尸
                    ZombieOverrideConfig config12 = configCache.get(zombieId);
                    advancedSkillHandler.enablePoisonArrowZombieSkills(zombie, zombieId, config12);
                    break;

                case "id13": // 电击僵尸
                    ZombieOverrideConfig config13 = configCache.get(zombieId);
                    advancedSkillHandler.enableElectricZombieSkills(zombie, zombieId, config13);
                    break;

                case "id14": // 冰冻僵尸
                    ZombieOverrideConfig config14 = configCache.get(zombieId);
                    advancedSkillHandler.enableFreezingZombieSkills(zombie, zombieId, config14);
                    break;

                case "id15": // 暗影僵尸
                    ZombieOverrideConfig config15 = configCache.get(zombieId);
                    advancedSkillHandler.enableShadowZombieSkills(zombie, zombieId, config15);
                    break;

                case "id16": // 毁灭僵尸
                    enableDestructionZombieSkills(zombie, zombieId);
                    break;

                case "id17": // 雷霆僵尸
                    ZombieOverrideConfig config17 = configCache.get(zombieId);
                    advancedSkillHandler.enableLightningZombieSkills(zombie, zombieId, config17);
                    break;

                case "id18": // 变异科学家
                    ZombieOverrideConfig config18 = configCache.get(zombieId);
                    advancedSkillHandler.enableMutantScientistSkills(zombie, zombieId, config18);
                    break;

                case "id19": // 变异法师
                    ZombieOverrideConfig config19 = configCache.get(zombieId);
                    advancedSkillHandler.enableMutantMageSkills(zombie, zombieId, config19);
                    break;

                case "id20": // 气球僵尸
                    ZombieOverrideConfig config20 = configCache.get(zombieId);
                    advancedSkillHandler.enableBalloonZombieSkills(zombie, zombieId, config20);
                    break;

                case "id21": // 迷雾僵尸
                    ZombieOverrideConfig config21 = configCache.get(zombieId);
                    advancedSkillHandler.enableFogZombieSkills(zombie, zombieId, config21);
                    break;

                case "id22": // 变异雷霆僵尸
                    ZombieOverrideConfig config22 = configCache.get(zombieId);
                    advancedSkillHandler.enableMutantThunderZombieSkills(zombie, zombieId, config22);
                    break;

                case "id23": // 终极毁灭僵尸
                    ZombieOverrideConfig config23 = configCache.get(zombieId);
                    advancedSkillHandler.enableUltimateDestructionZombieSkills(zombie, zombieId, config23);
                    break;

                case "id24": // 变异暗影僵尸
                    ZombieOverrideConfig config24 = configCache.get(zombieId);
                    advancedSkillHandler.enableMutantShadowZombieSkills(zombie, zombieId, config24);
                    break;

                case "id25": // 变异博士
                    ZombieOverrideConfig config25 = configCache.get(zombieId);
                    advancedSkillHandler.enableMutantDoctorZombieSkills(zombie, zombieId, config25);
                    break;

                default:
                    if (debugMode) {
                        logger.info(zombieId + "没有特殊技能需要启用");
                    }
                    break;
            }
        } catch (Exception e) {
            logger.warning("启用原有技能时出错: " + e.getMessage());
        }
    }

    /**
     * 启用剧毒僵尸技能
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     */
    private void enablePoisonZombieSkills(Zombie zombie, String zombieId) {
        try {
            // 添加剧毒僵尸标记
            zombie.setMetadata("poisonZombie", new FixedMetadataValue(plugin, true));

            // 从配置获取毒性参数
            ZombieOverrideConfig config = configCache.get(zombieId);
            if (debugMode) {
                logger.info("检查剧毒僵尸配置 - zombieId: " + zombieId);
                logger.info("配置对象: " + (config != null ? "存在" : "null"));
                if (config != null) {
                    logger.info("specialAbilities: " + (config.specialAbilities != null ? "存在，大小=" + config.specialAbilities.size() : "null"));
                    if (config.specialAbilities != null) {
                        for (Map.Entry<String, Object> entry : config.specialAbilities.entrySet()) {
                            logger.info("  - " + entry.getKey() + ": " + entry.getValue() + " (类型: " + entry.getValue().getClass().getSimpleName() + ")");
                        }
                    }
                }
            }

            if (config != null && config.specialAbilities != null) {
                // 设置毒性攻击参数
                Object poisonEnabled = config.specialAbilities.get("poison_attack_enabled");
                if (poisonEnabled instanceof Boolean && (Boolean) poisonEnabled) {
                    zombie.setMetadata("poisonAttackEnabled", new FixedMetadataValue(plugin, true));

                    // 设置毒性持续时间
                    Object poisonDuration = config.specialAbilities.get("poison_duration");
                    if (poisonDuration instanceof Integer) {
                        zombie.setMetadata("poisonDuration", new FixedMetadataValue(plugin, (Integer) poisonDuration));
                    }

                    // 设置毒性等级
                    Object poisonLevel = config.specialAbilities.get("poison_level");
                    if (poisonLevel instanceof Integer) {
                        zombie.setMetadata("poisonLevel", new FixedMetadataValue(plugin, (Integer) poisonLevel));
                    }
                }
            }

            if (debugMode) {
                logger.info("已为" + zombieId + "启用剧毒技能");
            }

        } catch (Exception e) {
            logger.warning("启用剧毒僵尸技能失败: " + e.getMessage());
        }
    }

    /**
     * 启用双生僵尸技能（ID6）
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     */
    private void enableTwinZombieSkills(Zombie zombie, String zombieId) {
        try {
            // 通过反射调用原有CustomZombie的startTwinZombieTasks方法
            java.lang.reflect.Method startTwinZombieTasksMethod =
                originalCustomZombie.getClass().getDeclaredMethod("startTwinZombieTasks", Zombie.class);
            startTwinZombieTasksMethod.setAccessible(true);

            // 从配置获取技能参数
            ZombieOverrideConfig config = configCache.get(zombieId);
            if (debugMode) {
                logger.info("检查双生僵尸配置 - zombieId: " + zombieId);
                logger.info("配置对象: " + (config != null ? "存在" : "null"));
                if (config != null) {
                    logger.info("skillCooldownOverrides: " + (config.skillCooldownOverrides != null ? "存在，大小=" + config.skillCooldownOverrides.size() : "null"));
                    if (config.skillCooldownOverrides != null) {
                        for (Map.Entry<String, Integer> entry : config.skillCooldownOverrides.entrySet()) {
                            logger.info("  - " + entry.getKey() + ": " + entry.getValue());
                        }
                    }
                }
            }

            // 设置双生僵尸的配置元数据，供死亡时使用
            if (config != null && config.specialAbilities != null) {
                Object spawnCount = config.specialAbilities.get("twin_spawn_count");
                if (spawnCount instanceof Integer) {
                    zombie.setMetadata("twin_spawn_count", new FixedMetadataValue(plugin, (Integer) spawnCount));
                    if (debugMode) {
                        logger.info("设置双生僵尸生成数量: " + spawnCount);
                    }
                }

                Object spawnOnDeath = config.specialAbilities.get("twin_spawn_on_death");
                if (spawnOnDeath instanceof Boolean) {
                    zombie.setMetadata("twin_spawn_on_death", new FixedMetadataValue(plugin, (Boolean) spawnOnDeath));
                    if (debugMode) {
                        logger.info("设置双生僵尸生成开关: " + spawnOnDeath);
                    }
                }
            }

            if (config != null && config.skillCooldownOverrides != null) {
                // 如果有自定义的任务间隔，我们需要创建自定义的任务
                Integer customInterval = config.skillCooldownOverrides.get("twin_task_interval");
                if (customInterval != null) {
                    startCustomTwinZombieTask(zombie, customInterval);
                } else {
                    // 使用原有的默认实现
                    startTwinZombieTasksMethod.invoke(originalCustomZombie, zombie);
                }
            } else {
                // 使用原有的默认实现
                startTwinZombieTasksMethod.invoke(originalCustomZombie, zombie);
            }

            if (debugMode) {
                logger.info("已为" + zombieId + "启用双生僵尸技能");
            }

        } catch (Exception e) {
            logger.warning("启用双生僵尸技能失败，尝试备用方案: " + e.getMessage());
            // 备用方案：直接设置元数据
            zombie.setMetadata("twinZombie", new FixedMetadataValue(plugin, true));
        }
    }

    /**
     * 启用骷髅僵尸技能（ID7）
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     */
    private void enableSkeletonZombieSkills(Zombie zombie, String zombieId) {
        try {
            // 通过反射调用原有CustomZombie的startShootingArrows方法
            java.lang.reflect.Method startShootingArrowsMethod =
                originalCustomZombie.getClass().getDeclaredMethod("startShootingArrows", Zombie.class);
            startShootingArrowsMethod.setAccessible(true);

            // 从配置获取技能参数
            ZombieOverrideConfig config = configCache.get(zombieId);
            if (config != null && config.skillCooldownOverrides != null) {
                // 如果有自定义的射箭参数，创建自定义的射箭任务
                Integer customCooldown = config.skillCooldownOverrides.get("arrow_shooting");
                Integer arrowCount = config.skillCooldownOverrides.get("arrow_burst_count");

                if (customCooldown != null || arrowCount != null) {
                    startCustomShootingArrowsTask(zombie,
                        customCooldown != null ? customCooldown : 60,
                        arrowCount != null ? arrowCount : 3,
                        config);
                } else {
                    // 使用原有的默认实现
                    startShootingArrowsMethod.invoke(originalCustomZombie, zombie);
                }
            } else {
                // 使用原有的默认实现
                startShootingArrowsMethod.invoke(originalCustomZombie, zombie);
            }

            if (debugMode) {
                logger.info("已为" + zombieId + "启用骷髅僵尸射箭技能");
            }

        } catch (Exception e) {
            logger.warning("启用骷髅僵尸技能失败: " + e.getMessage());
        }
    }

    /**
     * 启动自定义双生僵尸任务
     *
     * @param zombie 僵尸实体
     * @param interval 任务间隔（tick）
     */
    private void startCustomTwinZombieTask(Zombie zombie, int interval) {
        // 添加元数据标记
        zombie.setMetadata("twinZombie", new FixedMetadataValue(plugin, true));

        // 创建自定义任务
        org.bukkit.scheduler.BukkitTask task = new org.bukkit.scheduler.BukkitRunnable() {
            @Override
            public void run() {
                // 检查僵尸是否还存活
                if (zombie == null || zombie.isDead()) {
                    this.cancel();
                    return;
                }

                // 这个任务主要是为了保持与原有系统的兼容性
                // 实际的双生逻辑在僵尸死亡时触发
            }
        }.runTaskTimer(plugin, 20, interval);

        if (debugMode) {
            logger.info("已启动自定义双生僵尸任务，间隔: " + interval + " ticks");
        }
    }

    /**
     * 启动自定义射箭任务
     *
     * @param zombie 僵尸实体
     * @param cooldown 射箭冷却时间（tick）
     * @param arrowCount 每次射击的箭矢数量
     * @param config 配置对象
     */
    private void startCustomShootingArrowsTask(Zombie zombie, int cooldown, int arrowCount, ZombieOverrideConfig config) {
        org.bukkit.scheduler.BukkitTask task = new org.bukkit.scheduler.BukkitRunnable() {
            @Override
            public void run() {
                if (zombie.isDead()) {
                    this.cancel();
                    return;
                }

                org.bukkit.Location loc = zombie.getLocation();

                // 获取目标搜索范围
                double targetRange = 50.0; // 默认范围
                if (config.specialAbilities != null) {
                    Object rangeObj = config.specialAbilities.get("target_range");
                    if (rangeObj instanceof Number) {
                        targetRange = ((Number) rangeObj).doubleValue();
                    }
                }

                // 找到最近的玩家
                org.bukkit.entity.Player target = null;
                double minDistance = Double.MAX_VALUE;
                for (org.bukkit.entity.Player player : zombie.getWorld().getPlayers()) {
                    double distance = player.getLocation().distance(loc);
                    if (distance < minDistance && distance <= targetRange) {
                        minDistance = distance;
                        target = player;
                    }
                }

                if (target != null) {
                    // 发射指定数量的箭矢
                    for (int i = 0; i < arrowCount; i++) {
                        org.bukkit.entity.Arrow arrow = zombie.launchProjectile(org.bukkit.entity.Arrow.class);
                        arrow.setShooter(zombie);

                        // 获取箭矢消失时间
                        int despawnTime = 200; // 默认10秒
                        if (config.specialAbilities != null) {
                            Object despawnObj = config.specialAbilities.get("arrow_despawn_time");
                            if (despawnObj instanceof Integer) {
                                despawnTime = (Integer) despawnObj;
                            }
                        }

                        // 设置箭矢消失时间
                        new org.bukkit.scheduler.BukkitRunnable() {
                            @Override
                            public void run() {
                                if (!arrow.isDead()) {
                                    arrow.remove();
                                }
                            }
                        }.runTaskLater(plugin, despawnTime);
                    }

                    if (debugMode) {
                        logger.info("骷髅僵尸发射了 " + arrowCount + " 支箭矢，目标: " + target.getName());
                    }
                }
            }
        }.runTaskTimer(plugin, 0, cooldown);

        if (debugMode) {
            logger.info("已启动自定义射箭任务，冷却: " + cooldown + " ticks，箭矢数: " + arrowCount);
        }
    }

    /**
     * 格式化位置信息
     *
     * @param location 位置
     * @return 格式化的位置字符串
     */
    private String formatLocation(Location location) {
        return String.format("%s(%.1f,%.1f,%.1f)",
            location.getWorld().getName(),
            location.getX(),
            location.getY(),
            location.getZ());
    }

    /**
     * 重载配置文件
     */
    public void reloadConfig() {
        logger.info("正在重载UserCustomZombie配置...");
        loadConfig();
        logger.info("UserCustomZombie配置重载完成");
    }

    /**
     * 检查是否启用用户自定义设置
     *
     * @return 是否启用
     */
    public boolean isUserCustomEnabled() {
        return useUserCustomSettings;
    }

    /**
     * 检查是否启用默认设置
     *
     * @return 是否启用
     */
    public boolean isDefaultEnabled() {
        return useDefaultSettings;
    }

    /**
     * 获取优先级策略
     *
     * @return 优先级策略
     */
    public String getPriorityStrategy() {
        return priorityStrategy;
    }

    /**
     * 僵尸覆盖配置类
     * 存储单个僵尸的覆盖设置
     */
    public static class ZombieOverrideConfig {
        public boolean enabled = false;
        public double healthOverride = -1;
        public double damageOverride = -1;
        public double speedMultiplier = -1;
        public String customNameOverride = null;
        public String weaponOverride = null;
        public String helmetOverride = null;
        public String chestplateOverride = null;
        public String leggingsOverride = null;
        public String bootsOverride = null;
        public Map<String, Integer> helmetEnchantments = new HashMap<>();
        public Map<String, Integer> chestplateEnchantments = new HashMap<>();
        public Map<String, Integer> leggingsEnchantments = new HashMap<>();
        public Map<String, Integer> bootsEnchantments = new HashMap<>();
        public Map<String, PotionEffectConfig> potionEffects = new HashMap<>();
        public Map<String, Integer> skillCooldownOverrides = new HashMap<>();
        public Map<String, Object> specialAbilities = new HashMap<>();

        /**
         * 从配置节加载设置
         *
         * @param section 配置节
         */
        public void loadFromConfig(ConfigurationSection section) {
            enabled = section.getBoolean("enabled", false);
            healthOverride = section.getDouble("health_override", -1);
            damageOverride = section.getDouble("damage_override", -1);
            speedMultiplier = section.getDouble("speed_multiplier", -1);
            customNameOverride = section.getString("custom_name_override");
            weaponOverride = section.getString("weapon_override");

            // 加载护甲覆盖
            ConfigurationSection armorSection = section.getConfigurationSection("armor_overrides");
            if (armorSection != null) {
                helmetOverride = armorSection.getString("helmet");
                chestplateOverride = armorSection.getString("chestplate");
                leggingsOverride = armorSection.getString("leggings");
                bootsOverride = armorSection.getString("boots");

                // 加载各部位附魔
                loadArmorEnchantments(armorSection, "helmet_enchantments", helmetEnchantments);
                loadArmorEnchantments(armorSection, "chestplate_enchantments", chestplateEnchantments);
                loadArmorEnchantments(armorSection, "leggings_enchantments", leggingsEnchantments);
                loadArmorEnchantments(armorSection, "boots_enchantments", bootsEnchantments);
            }

            // 加载药水效果
            ConfigurationSection potionSection = section.getConfigurationSection("potion_effects");
            if (potionSection != null) {
                for (String effectName : potionSection.getKeys(false)) {
                    ConfigurationSection effectSection = potionSection.getConfigurationSection(effectName);
                    if (effectSection != null) {
                        PotionEffectConfig effectConfig = new PotionEffectConfig();
                        effectConfig.level = effectSection.getInt("level", 0);
                        effectConfig.duration = effectSection.getInt("duration", -1);
                        potionEffects.put(effectName, effectConfig);
                    }
                }
            }

            // 加载技能冷却时间覆盖
            ConfigurationSection skillSection = section.getConfigurationSection("skill_cooldown_overrides");
            if (skillSection != null) {
                for (String skillName : skillSection.getKeys(false)) {
                    int value = skillSection.getInt(skillName);
                    skillCooldownOverrides.put(skillName, value);
                    System.out.println("[DEBUG] 加载技能冷却覆盖: " + skillName + " = " + value);
                }
            } else {
                System.out.println("[DEBUG] 没有找到skill_cooldown_overrides配置节");
            }

            // 加载特殊能力
            ConfigurationSection abilitySection = section.getConfigurationSection("special_abilities");
            if (abilitySection != null) {
                for (String abilityName : abilitySection.getKeys(false)) {
                    Object value = abilitySection.get(abilityName);
                    specialAbilities.put(abilityName, value);
                    System.out.println("[DEBUG] 加载特殊能力: " + abilityName + " = " + value + " (类型: " + value.getClass().getSimpleName() + ")");
                }
            } else {
                System.out.println("[DEBUG] 没有找到special_abilities配置节");
            }
        }

        /**
         * 加载护甲附魔配置
         *
         * @param armorSection 护甲配置节
         * @param enchantmentKey 附魔配置键名
         * @param enchantmentMap 存储附魔的Map
         */
        private void loadArmorEnchantments(ConfigurationSection armorSection, String enchantmentKey, Map<String, Integer> enchantmentMap) {
            ConfigurationSection enchantSection = armorSection.getConfigurationSection(enchantmentKey);
            if (enchantSection != null) {
                for (String enchantName : enchantSection.getKeys(false)) {
                    enchantmentMap.put(enchantName, enchantSection.getInt(enchantName));
                }
            }
        }
    }

    /**
     * 药水效果配置类
     */
    private static class PotionEffectConfig {
        public int level = 0;
        public int duration = -1; // -1表示永久
    }

    /**
     * 启用武装僵尸技能（ID8）
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     */
    private void enableArmedZombieSkills(Zombie zombie, String zombieId) {
        try {
            // 武装僵尸主要是高血量和速度效果，没有复杂的技能
            // 添加武装僵尸标记
            zombie.setMetadata("armedZombie", new FixedMetadataValue(plugin, true));

            // 从配置获取技能参数
            ZombieOverrideConfig config = configCache.get(zombieId);
            if (debugMode) {
                logger.info("检查武装僵尸配置 - zombieId: " + zombieId);
                logger.info("配置对象: " + (config != null ? "存在" : "null"));
            }

            // 应用速度效果（如果配置中没有覆盖）
            if (config == null || config.potionEffects == null || !config.potionEffects.containsKey("speed")) {
                // 应用默认的速度1效果
                zombie.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 0));
                if (debugMode) {
                    logger.info("为武装僵尸应用默认速度1效果");
                }
            }

            if (debugMode) {
                logger.info("已为" + zombieId + "启用武装僵尸技能");
            }

        } catch (Exception e) {
            logger.warning("启用武装僵尸技能失败: " + e.getMessage());
        }
    }

    /**
     * 启用肥胖僵尸技能（ID9）
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     */
    private void enableFatZombieSkills(Zombie zombie, String zombieId) {
        try {
            // 添加肥胖僵尸标记
            zombie.setMetadata("fatZombie", new FixedMetadataValue(plugin, true));

            // 从配置获取技能参数
            ZombieOverrideConfig config = configCache.get(zombieId);
            if (debugMode) {
                logger.info("检查肥胖僵尸配置 - zombieId: " + zombieId);
                logger.info("配置对象: " + (config != null ? "存在" : "null"));
            }

            // 应用默认效果（如果配置中没有覆盖）
            if (config == null || config.potionEffects == null) {
                // 应用默认的缓慢2和生命提升3效果
                zombie.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, Integer.MAX_VALUE, 1));
                zombie.addPotionEffect(new PotionEffect(PotionEffectType.HEALTH_BOOST, Integer.MAX_VALUE, 2));
                if (debugMode) {
                    logger.info("为肥胖僵尸应用默认效果：缓慢2和生命提升3");
                }
            } else {
                // 检查是否需要应用默认效果
                if (!config.potionEffects.containsKey("slowness")) {
                    zombie.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, Integer.MAX_VALUE, 1));
                }
                if (!config.potionEffects.containsKey("health_boost")) {
                    zombie.addPotionEffect(new PotionEffect(PotionEffectType.HEALTH_BOOST, Integer.MAX_VALUE, 2));
                }
            }

            if (debugMode) {
                logger.info("已为" + zombieId + "启用肥胖僵尸技能");
            }

        } catch (Exception e) {
            logger.warning("启用肥胖僵尸技能失败: " + e.getMessage());
        }
    }

    /**
     * 启用法师僵尸技能（ID10）- 这是难点
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     */
    private void enableMageZombieSkills(Zombie zombie, String zombieId) {
        try {
            // 添加法师僵尸标记
            zombie.setMetadata("mageZombie", new FixedMetadataValue(plugin, true));

            // 从配置获取技能参数
            ZombieOverrideConfig config = configCache.get(zombieId);
            if (debugMode) {
                logger.info("检查法师僵尸配置 - zombieId: " + zombieId);
                logger.info("配置对象: " + (config != null ? "存在" : "null"));
                if (config != null) {
                    logger.info("skillCooldownOverrides: " + (config.skillCooldownOverrides != null ? "存在，大小=" + config.skillCooldownOverrides.size() : "null"));
                    logger.info("specialAbilities: " + (config.specialAbilities != null ? "存在，大小=" + config.specialAbilities.size() : "null"));
                }
            }

            // 通过反射调用原有CustomZombie的startMutantMageSummonTasks方法
            java.lang.reflect.Method startMageSummonMethod =
                originalCustomZombie.getClass().getDeclaredMethod("startMutantMageSummonTasks", Zombie.class);
            startMageSummonMethod.setAccessible(true);

            // 检查是否有自定义的召唤参数
            if (config != null && (config.skillCooldownOverrides != null || config.specialAbilities != null)) {
                // 设置配置元数据供召唤任务使用
                if (config.skillCooldownOverrides != null) {
                    Integer summonInterval = config.skillCooldownOverrides.get("summon_interval");
                    Integer summonCount = config.skillCooldownOverrides.get("summon_count");
                    Integer maxNearbyZombies = config.skillCooldownOverrides.get("max_nearby_zombies");

                    if (summonInterval != null) {
                        zombie.setMetadata("mage_summon_interval", new FixedMetadataValue(plugin, summonInterval));
                        if (debugMode) {
                            logger.info("设置法师召唤间隔: " + summonInterval);
                        }
                    }
                    if (summonCount != null) {
                        zombie.setMetadata("mage_summon_count", new FixedMetadataValue(plugin, summonCount));
                        if (debugMode) {
                            logger.info("设置法师召唤数量: " + summonCount);
                        }
                    }
                    if (maxNearbyZombies != null) {
                        zombie.setMetadata("mage_max_nearby", new FixedMetadataValue(plugin, maxNearbyZombies));
                        if (debugMode) {
                            logger.info("设置法师最大附近僵尸数: " + maxNearbyZombies);
                        }
                    }
                }

                if (config.specialAbilities != null) {
                    Object summonEnabled = config.specialAbilities.get("summon_enabled");
                    Object summonType = config.specialAbilities.get("summon_zombie_type");

                    if (summonEnabled instanceof Boolean) {
                        zombie.setMetadata("mage_summon_enabled", new FixedMetadataValue(plugin, (Boolean) summonEnabled));
                        if (debugMode) {
                            logger.info("设置法师召唤开关: " + summonEnabled);
                        }
                    }
                    if (summonType instanceof String) {
                        zombie.setMetadata("mage_summon_type", new FixedMetadataValue(plugin, (String) summonType));
                        if (debugMode) {
                            logger.info("设置法师召唤类型: " + summonType);
                        }
                    }
                }

                // 启动自定义召唤任务
                startCustomMageSummonTask(zombie, config);
            } else {
                // 使用原有的默认实现
                startMageSummonMethod.invoke(originalCustomZombie, zombie);
            }

            if (debugMode) {
                logger.info("已为" + zombieId + "启用法师僵尸召唤技能");
            }

        } catch (Exception e) {
            logger.warning("启用法师僵尸技能失败，尝试备用方案: " + e.getMessage());
            // 备用方案：直接设置元数据
            zombie.setMetadata("mageZombie", new FixedMetadataValue(plugin, true));
        }
    }

    /**
     * 启动自定义法师召唤任务
     *
     * @param zombie 法师僵尸
     * @param config 配置
     */
    private void startCustomMageSummonTask(Zombie zombie, ZombieOverrideConfig config) {
        // 获取配置参数
        int summonInterval = 200; // 默认10秒
        int summonCount = 2; // 默认召唤2个
        int maxNearbyZombies = 5; // 默认最大5个附近僵尸
        String summonType = "id4"; // 默认召唤钻斧僵尸
        boolean summonEnabled = true; // 默认启用

        if (config.skillCooldownOverrides != null) {
            summonInterval = config.skillCooldownOverrides.getOrDefault("summon_interval", summonInterval);
            summonCount = config.skillCooldownOverrides.getOrDefault("summon_count", summonCount);
            maxNearbyZombies = config.skillCooldownOverrides.getOrDefault("max_nearby_zombies", maxNearbyZombies);
        }

        if (config.specialAbilities != null) {
            Object enabledObj = config.specialAbilities.get("summon_enabled");
            if (enabledObj instanceof Boolean) {
                summonEnabled = (Boolean) enabledObj;
            }
            Object typeObj = config.specialAbilities.get("summon_zombie_type");
            if (typeObj instanceof String) {
                summonType = (String) typeObj;
            }
        }

        if (!summonEnabled) {
            if (debugMode) {
                logger.info("法师召唤功能已禁用");
            }
            return;
        }

        final int finalSummonCount = summonCount;
        final int finalMaxNearbyZombies = maxNearbyZombies;
        final String finalSummonType = summonType;

        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    return;
                }

                // 检查周围僵尸数量
                int nearbyZombiesCount = 0;
                for (Entity entity : zombie.getNearbyEntities(10, 10, 10)) {
                    if (entity instanceof Zombie) {
                        nearbyZombiesCount++;
                    }
                }

                // 如果周围僵尸少于限制，才继续召唤
                if (nearbyZombiesCount < finalMaxNearbyZombies) {
                    // 召唤粒子效果
                    Location summonLoc = zombie.getLocation().add(0, 1, 0);
                    zombie.getWorld().spawnParticle(Particle.FLAME, summonLoc, 20, 1.5, 0.5, 1.5, 0.1);

                    // 播放召唤音效
                    zombie.getWorld().playSound(zombie.getLocation(), Sound.ENTITY_ILLUSIONER_CAST_SPELL, 1.0f, 1.0f);

                    // 召唤僵尸
                    for (int i = 0; i < finalSummonCount; i++) {
                        // 计算随机位置偏移
                        double offsetX = (Math.random() - 0.5) * 3;
                        double offsetZ = (Math.random() - 0.5) * 3;
                        Location spawnLoc = zombie.getLocation().clone().add(offsetX, 0, offsetZ);

                        // 确保生成位置有效
                        while (!spawnLoc.getBlock().getType().isSolid() && spawnLoc.getBlockY() > 0) {
                            spawnLoc.setY(spawnLoc.getY() - 1);
                        }
                        spawnLoc.setY(spawnLoc.getY() + 1);

                        // 生成指定类型的僵尸
                        if (originalCustomZombie != null) {
                            try {
                                originalCustomZombie.spawnCustomZombieDirect(spawnLoc, finalSummonType);
                                // 显示召唤效果
                                zombie.getWorld().spawnParticle(Particle.PORTAL, spawnLoc, 20, 0.5, 0.5, 0.5, 0.1);
                            } catch (Exception e) {
                                logger.warning("法师召唤僵尸失败: " + e.getMessage());
                            }
                        }
                    }

                    if (debugMode) {
                        logger.info("法师僵尸召唤了 " + finalSummonCount + " 个 " + finalSummonType + " 类型的僵尸");
                    }
                }
            }
        }.runTaskTimer(plugin, 60, summonInterval); // 3秒后开始，按配置间隔执行

        if (debugMode) {
            logger.info("已启动自定义法师召唤任务，间隔: " + summonInterval + " ticks");
        }
    }

    /**
     * 启用毁灭僵尸技能（ID16）
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     */
    private void enableDestructionZombieSkills(Zombie zombie, String zombieId) {
        try {
            // 添加毁灭僵尸标记
            zombie.setMetadata("destructionZombie", new FixedMetadataValue(plugin, true));

            // 毁灭僵尸主要是高血量高攻击，没有复杂的技能
            // 可以在这里添加特殊效果，比如力量效果
            zombie.addPotionEffect(new PotionEffect(PotionEffectType.STRENGTH, Integer.MAX_VALUE, 1));

            if (debugMode) {
                logger.info("已为" + zombieId + "启用毁灭僵尸技能（力量效果）");
            }

        } catch (Exception e) {
            logger.warning("启用毁灭僵尸技能失败: " + e.getMessage());
        }
    }

    /**
     * 获取高级技能处理器
     *
     * @return AdvancedSkillHandler实例
     */
    public AdvancedSkillHandler getAdvancedSkillHandler() {
        return advancedSkillHandler;
    }
}
