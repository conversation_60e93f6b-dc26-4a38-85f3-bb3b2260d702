# IDC1-IDC2完整实现指南

## 概述

我们已经完成了IDC1（变异僵尸01）和IDC2（变异僵尸02）的完整实现，包括：
- 基础属性配置
- 装备系统
- 特殊技能系统
- 粒子效果
- 攻击机制

## IDC1 - 变异僵尸01

### 基础配置
- **实体类型**: 僵尸猪人 (ZOMBIFIED_PIGLIN)
- **生命值**: 100点
- **伤害**: 2点（每次攻击）
- **名称**: §6变异僵尸01

### 装备配置
- **武器**: 铁剑 + 锋利1附魔
- **护甲**: 全套皮革装备
  - 皮革头盔
  - 皮革胸甲
  - 皮革护腿
  - 皮革靴子

### 药水效果
- **速度2**: 永久效果
- **跳跃提升2**: 永久效果

### 特殊能力
1. **黑色烟雾粒子气场**
   - 形状: 正方体
   - 大小: 3.0格范围
   - 粒子类型: SMOKE_NORMAL
   - 更新间隔: 5tick

2. **毒性攻击**
   - 攻击概率: 100%
   - 毒性等级: 剧毒1
   - 持续时间: 3秒（60tick）
   - 伤害: 固定2点

## IDC2 - 变异僵尸02

### 基础配置
- **实体类型**: 骷髅 (SKELETON)
- **生命值**: 100点
- **伤害**: 6点（弓箭伤害）
- **名称**: §7变异僵尸02

### 装备配置
- **武器**: 弓 + 冲击2附魔
- **头盔**: 苦力怕头颅

### 药水效果
- **速度2**: 永久效果
- **跳跃提升4**: 永久效果

### 特殊能力
1. **黑色烟雾粒子气场**
   - 与IDC1相同的粒子效果
   - 形状: 正方体
   - 大小: 3.0格范围

2. **增强射箭能力**
   - 射击间隔: 2秒（40tick）
   - 箭矢速度: 1.2倍
   - 射击精度: 0.8
   - 射击范围: 16格

## 配置文件示例

```yaml
system_settings:
  use_idc_type_support: true
  use_user_custom_settings: true
  priority_strategy: "user_first"
  debug_mode: true

user_custom_overrides:
  idc_overrides:
    idc1:
      enabled: true
      health_override: 100.0
      damage_override: 2.0
      custom_name_override: "§6变异僵尸01"
      entity_type_override: "ZOMBIFIED_PIGLIN"
      equipment:
        weapon: "IRON_SWORD"
        weapon_enchantments:
          sharpness: 1
        helmet: "LEATHER_HELMET"
        chestplate: "LEATHER_CHESTPLATE"
        leggings: "LEATHER_LEGGINGS"
        boots: "LEATHER_BOOTS"
      potion_effects:
        speed:
          level: 1
          duration: -1
        jump_boost:
          level: 1
          duration: -1
      special_abilities:
        particle_enabled: true
        particle_type: "SMOKE_NORMAL"
        particle_shape: "CUBE"
        particle_size: 3.0
        poison_attack_enabled: true
        poison_duration: 60
        poison_level: 0
      skill_cooldown_overrides:
        particle_interval: 5
        poison_attack_chance: 100

    idc2:
      enabled: true
      health_override: 100.0
      damage_override: 6.0
      custom_name_override: "§7变异僵尸02"
      entity_type_override: "SKELETON"
      equipment:
        weapon: "BOW"
        weapon_enchantments:
          punch: 2
        helmet: "CREEPER_HEAD"
      potion_effects:
        speed:
          level: 1
          duration: -1
        jump_boost:
          level: 3
          duration: -1
      special_abilities:
        particle_enabled: true
        particle_type: "SMOKE_NORMAL"
        particle_shape: "CUBE"
        particle_size: 3.0
        shooting_enabled: true
        arrow_speed: 1.2
        arrow_accuracy: 0.8
      skill_cooldown_overrides:
        particle_interval: 5
        shooting_interval: 40
```

## 集成步骤

### 1. 初始化系统

```java
// 在主插件的onEnable方法中
UserCustomEntityManager entityManager = new UserCustomEntityManager(this);
entityManager.initialize(customZombieInstance);

// 注册监听器
getServer().getPluginManager().registerEvents(new UserCustomEntityListener(this), this);
```

### 2. 生成实体

```java
// 生成IDC1
Entity idc1 = entityManager.spawnCustomEntity(location, "idc1");

// 生成IDC2
Entity idc2 = entityManager.spawnCustomEntity(location, "idc2");
```

### 3. 命令集成示例

```java
if (args[0].equalsIgnoreCase("spawnidc")) {
    String entityId = args[1];
    Location spawnLocation = player.getLocation().add(player.getLocation().getDirection().multiply(2));
    
    Entity entity = entityManager.spawnCustomEntity(spawnLocation, entityId);
    if (entity != null) {
        player.sendMessage("§a成功生成IDC实体: " + entityId);
    } else {
        player.sendMessage("§c生成IDC实体失败: " + entityId);
    }
}
```

## 技术特性

### 粒子效果系统
- 自动生成正方体形状的黑色烟雾粒子
- 粒子跟随实体移动
- 实体死亡时自动停止粒子效果

### 攻击系统
- **IDC1**: 近战毒性攻击，100%概率施加剧毒效果
- **IDC2**: 远程弓箭攻击，带有冲击2效果

### 装备系统
- 支持完整的装备配置
- 支持附魔系统
- 自动应用到生成的实体

### 元数据系统
- 完整的实体标记系统
- 支持技能参数存储
- 兼容现有的识别系统

## 调试和测试

### 启用调试模式
在zombie.yml中设置：
```yaml
system_settings:
  debug_mode: true
```

### 测试命令
```
/czm spawnidc idc1  # 生成IDC1
/czm spawnidc idc2  # 生成IDC2
```

### 验证功能
1. **IDC1测试**:
   - 检查是否为僵尸猪人实体
   - 验证装备（铁剑+皮革套装）
   - 测试毒性攻击效果
   - 观察黑色烟雾粒子

2. **IDC2测试**:
   - 检查是否为骷髅实体
   - 验证装备（弓+苦力怕头颅）
   - 测试自动射箭功能
   - 观察黑色烟雾粒子

## 故障排除

### 常见问题
1. **实体生成失败**: 检查IDC类型支持是否启用
2. **粒子效果不显示**: 确认particle_enabled为true
3. **攻击效果无效**: 检查监听器是否正确注册
4. **装备未应用**: 验证equipment配置格式

### 日志检查
启用调试模式后，查看控制台输出：
- 实体生成日志
- 装备应用日志
- 技能启用日志
- 粒子效果日志

## 性能优化

### 粒子效果优化
- 粒子更新间隔可调整（默认5tick）
- 粒子数量已优化为最小必要数量

### 射箭系统优化
- 自动检测目标距离和有效性
- 任务自动清理机制

### 内存管理
- 实体死亡时自动清理相关任务
- 元数据自动回收

这个实现提供了完整的IDC1和IDC2功能，包括所有原版特性和增强功能。系统具有良好的扩展性，可以轻松添加更多IDC类型实体。
