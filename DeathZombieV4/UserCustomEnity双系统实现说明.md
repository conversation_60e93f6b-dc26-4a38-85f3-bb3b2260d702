# UserCustomEnity双系统实现说明

## 概述

基于UserCustomZombie的成功实现，我们为IDC类型实体系列创建了UserCustomEnity双系统更新。这个系统允许用户通过配置文件自定义IDC类型实体的属性，同时保持与原有CustomZombie系统的兼容性。

## 系统架构

### 核心类结构

1. **UserCustomEnity.java** - 主要的实体管理类
   - 负责IDC类型实体的生成和配置管理
   - 支持双系统优先级策略
   - 提供配置覆盖功能

2. **UserCustomEntityManager.java** - 实体管理器
   - 提供统一的管理接口
   - 处理系统初始化和配置重载
   - 封装对UserCustomEnity的访问

3. **EntityOverrideConfig** - 实体覆盖配置类
   - 存储单个IDC实体的覆盖设置
   - 支持生命值、伤害、速度等基础属性
   - 支持药水效果和特殊能力配置

## 配置文件更新

### zombie.yml新增配置

```yaml
system_settings:
  # 新增IDC类型支持开关
  use_idc_type_support: true

user_custom_overrides:
  # 新增IDC类型实体覆盖配置节
  idc_overrides:
    idc1:
      enabled: true
      health_override: 80.0
      damage_override: 8.0
      speed_multiplier: 1.3
      custom_name_override: "§c§l强化变异僵尸01"
      potion_effects:
        speed:
          level: 2
          duration: -1
      special_abilities:
        particle_enabled: true
        mutation_enabled: true
```

## 支持的IDC类型实体

### 已配置的实体类型

- **idc1-idc2**: 变异僵尸系列
- **idc3**: 变异烈焰人
- **idc4**: 变异爬行者
- **idc5**: 变异末影螨
- **idc6**: 变异蜘蛛
- **idc18**: 变异铁傀儡
- **idc20**: 灵魂坚守者
- **idc21**: 凋零领主
- **idc22**: 异变之王

### 实体类型映射

系统自动将IDC ID映射到对应的Minecraft实体类型：
- idc1, idc2 → ZOMBIE
- idc3 → BLAZE
- idc4 → CREEPER
- idc5 → ENDERMITE
- idc6 → SPIDER
- idc18 → IRON_GOLEM
- idc20 → WARDEN (如果支持) 或 IRON_GOLEM
- idc21 → WITHER
- idc22 → ENDER_DRAGON

## 功能特性

### 双系统支持

1. **优先级策略**
   - `user_first`: 优先使用用户自定义设置
   - `default_first`: 优先使用默认设置，失败时使用用户自定义
   - `both`: 同时生成两种实体（实验性）

2. **配置覆盖**
   - 基础属性：生命值、伤害值、移动速度
   - 外观：自定义名称
   - 效果：药水效果（类型、等级、持续时间）
   - 能力：特殊能力开关和参数

3. **元数据标记**
   - `userCustomEntity`: 标记为用户自定义实体
   - `entityId`: 记录实体ID
   - `dualEntitySystem`: 标记双系统来源
   - `idcZombieEntity`: 兼容现有系统的标记

## 使用方法

### 基本使用

```java
// 初始化管理器
UserCustomEntityManager manager = new UserCustomEntityManager(plugin);
manager.initialize(originalCustomZombie);

// 生成自定义实体
Entity entity = manager.spawnCustomEntity(location, "idc1");

// 检查系统状态
boolean idcSupport = manager.isIdcTypeSupportEnabled();
boolean userCustom = manager.isUserCustomSettingsEnabled();
```

### 配置管理

```java
// 重载配置
manager.reloadConfig();

// 检查调试模式
if (manager.isDebugMode()) {
    logger.info("调试模式已启用");
}
```

## 兼容性说明

### 向后兼容

- 原有CustomZombie的所有功能保持不变
- 现有IDC实体生成逻辑无需修改
- 默认情况下IDC类型支持为启用状态

### 性能影响

- 配置缓存机制减少文件读取
- 双系统管理器采用懒加载
- 调试模式可关闭以提升性能

## 扩展计划

### 后续功能

1. **完整技能系统**
   - 实现IDC实体的特殊技能覆盖
   - 支持技能冷却时间自定义
   - 添加新的特殊能力

2. **更多实体类型**
   - 支持剩余的IDC7-IDC19实体
   - 添加新的变异实体类型
   - 支持完全自定义的新实体

3. **高级配置**
   - 游戏级别的配置开关
   - 条件性配置加载
   - 动态配置更新

## 故障排除

### 常见问题

1. **IDC类型支持未启用**
   - 检查`use_idc_type_support: true`
   - 确认配置文件格式正确

2. **实体生成失败**
   - 检查实体ID是否在支持列表中
   - 确认`enabled: true`在对应配置中
   - 查看控制台错误日志

3. **配置不生效**
   - 使用重载命令刷新配置
   - 检查YAML格式是否正确
   - 确认缩进使用空格而非制表符

### 调试建议

1. 启用调试模式：`debug_mode: true`
2. 查看详细日志输出
3. 使用配置重载命令测试更改
4. 检查实体元数据标记是否正确设置

## 总结

UserCustomEnity双系统为IDC类型实体提供了强大的自定义功能，同时保持了与现有系统的完全兼容性。通过配置文件驱动的方式，用户可以轻松调整实体属性，而开发者可以继续扩展更多高级功能。
